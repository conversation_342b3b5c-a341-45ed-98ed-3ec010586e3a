# 重构方案：前后端分离架构 (Node.js + MySQL)

## 1. 系统架构设计

### 前端架构
- 保留现有Vue 3框架，采用前后端分离架构
- 使用Axios处理API请求
- 保留Element Plus作为UI组件库
- 保留Pinia进行状态管理

### 后端架构
- 使用Node.js + Express作为服务端框架
- 使用MySQL作为数据库
- 使用Sequelize作为ORM
- 使用JWT进行身份验证
- RESTful API设计

## 2. 数据库设计

### 主要数据表
1. **products表**
   - id (主键)
   - name (商品名称)
   - category (商品类别)
   - lyPrice (光年币价格)
   - rmbPrice (人民币价格)
   - description (商品描述)
   - stock (库存)
   - exchangeCount (兑换次数)
   - isHot (是否热门)
   - isNew (是否新品)
   - status (状态：active/inactive)
   - createdAt
   - updatedAt

2. **product_images表**
   - id (主键)
   - productId (外键关联products表)
   - imageUrl (图片URL)
   - sortOrder (排序顺序)
   - createdAt
   - updatedAt

3. **users表** (新增)
   - id (主键)
   - username
   - password (加密存储)
   - role (角色：admin/user)
   - points (光年币数量)
   - createdAt
   - updatedAt

4. **exchanges表** (新增)
   - id (主键)
   - userId (关联用户)
   - productId (关联商品)
   - quantity (数量)
   - location (兑换地点)
   - status (状态：pending/completed/cancelled)
   - createdAt
   - updatedAt

5. **feedback表** (新增)
   - id (主键)
   - userId (可选，关联用户)
   - content (反馈内容)
   - createdAt
   - updatedAt

6. **announcements表** (新增)
   - id (主键)
   - title (公告标题)
   - content (公告内容)
   - type (公告类型：新品/促销/系统更新)
   - status (状态：active/inactive)
   - createdBy (创建者ID)
   - createdAt
   - updatedAt

7. **categories表** (新增)
   - id (主键)
   - name (类别名称)
   - description (类别描述)
   - sortOrder (排序顺序)
   - createdAt
   - updatedAt

## 3. 功能需求

### 用户端需求
1. **商品浏览**
   - 商品分类展示
   - 商品搜索与筛选
   - 商品详情查看
   - 多图片预览
   - 意见反馈

### 管理端需求
1. **用户管理**
   - 管理员登录到管理后台
   - 用户添加、编辑、删除、查看

2. **商品管理**
   - 商品添加、编辑、删除、修改
   - 商品分类管理
   - 新品爆款管理（设置新品/爆款标记，控制首页展示）

3. **公告管理**
   - 发布系统公告（新品上新、年度大促玩法、系统更新等）
   - 管理历史公告（上线、下线、修改）

## 4. 主要界面

### 商品展示页
1. **顶部导航栏**
   - Logo
   - 搜索框
   - 意见反馈按钮

2. **分类筛选区**
   - 商品类别
   - 排序选项

3. **商品列表区**
   - 商品卡片网格布局
   - 商品卡片：图片、名称、价格（光年币和人民币）

### 商品弹窗
- 商品信息展示：图片、名称、商品介绍、价格（光年币和人民币）、关闭按钮

### 管理后台
1. **登录界面**
   - 账号密码登录表单

2. **主界面布局**
   - 侧边导航栏：各管理模块入口
   - 内容区：根据选择的模块显示相应功能界面

3. **数据管理界面**
   - 数据表格：展示列表数据
   - 表单：添加/编辑数据

## 5. 后端API设计

### 认证相关
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册（仅管理员可用）
- `GET /api/auth/profile` - 获取用户资料

### 用户管理
- `GET /api/users` - 获取用户列表（管理员）
- `GET /api/users/:id` - 获取用户详情
- `POST /api/users` - 添加用户（管理员）
- `PUT /api/users/:id` - 更新用户信息
- `DELETE /api/users/:id` - 删除用户（管理员）

### 商品相关
- `GET /api/products` - 获取商品列表（支持筛选、排序和分页）
- `GET /api/products/:id` - 获取单个商品详情
- `POST /api/products` - 添加新商品（管理员）
- `PUT /api/products/:id` - 更新商品信息（管理员）
- `DELETE /api/products/:id` - 删除商品（管理员）
- `PUT /api/products/:id/status` - 更新商品状态（上/下线）

### 分类相关
- `GET /api/categories` - 获取所有分类
- `GET /api/categories/:id` - 获取单个分类详情
- `POST /api/categories` - 添加新分类（管理员）
- `PUT /api/categories/:id` - 更新分类信息（管理员）
- `DELETE /api/categories/:id` - 删除分类（管理员）

### 公告相关
- `GET /api/announcements` - 获取公告列表
- `GET /api/announcements/:id` - 获取单个公告详情
- `POST /api/announcements` - 发布新公告（管理员）
- `PUT /api/announcements/:id` - 更新公告信息（管理员）
- `PUT /api/announcements/:id/status` - 更新公告状态（上/下线）
- `DELETE /api/announcements/:id` - 删除公告（管理员）

### 兑换相关
- `POST /api/exchanges` - 创建新的兑换订单
- `GET /api/exchanges` - 获取用户的兑换历史
- `GET /api/exchanges/:id` - 获取单个兑换详情
- `PUT /api/exchanges/:id/status` - 更新兑换状态（管理员）

### 反馈相关
- `POST /api/feedback` - 提交反馈
- `GET /api/feedback` - 获取反馈列表（管理员）
- `DELETE /api/feedback/:id` - 删除反馈（管理员）

## 6. 前端重构方案

### 目录结构调整
```
src/
├── api/                  # API请求模块
│   ├── index.js          # API基础配置
│   ├── auth.js           # 认证相关API
│   ├── products.js       # 商品相关API
│   ├── categories.js     # 分类相关API
│   ├── announcements.js  # 公告相关API
│   ├── exchanges.js      # 兑换相关API
│   ├── feedback.js       # 反馈相关API
│   └── users.js          # 用户相关API
├── stores/               # Pinia状态管理
│   ├── auth.js           # 认证状态
│   ├── products.js       # 商品状态
│   ├── categories.js     # 分类状态
│   ├── announcements.js  # 公告状态
│   ├── exchanges.js      # 兑换状态
│   └── users.js          # 用户状态
├── components/           # 保留现有组件结构
│   ├── common/           # 通用组件
│   ├── product/          # 商品相关组件
│   └── admin/            # 管理后台组件
├── views/                # 页面视图组件（新增）
│   ├── Home.vue          # 商品展示主页
│   ├── Login.vue         # 登录页
│   ├── Profile.vue       # 个人资料页
│   ├── Feedback.vue      # 反馈表单页
│   └── Admin/            # 管理员页面
│       ├── Dashboard.vue # 管理概览
│       ├── Products.vue  # 商品管理
│       ├── Categories.vue # 分类管理
│       ├── Users.vue     # 用户管理
│       ├── Announcements.vue # 公告管理
│       └── Feedback.vue  # 反馈管理
├── router/               # Vue Router（新增）
│   └── index.js
├── utils/                # 工具函数
├── assets/               # 静态资源
└── App.vue               # 根组件（简化，主要负责路由）
```

### API层改造
从原来直接使用本地数据改为通过API获取数据：
- 创建axios实例配置拦截器处理认证和错误
- 将所有数据操作转换为API调用
- 实现数据缓存策略提升性能

### 状态管理改造
- 重构products store来使用API获取数据
- 新增auth store管理用户认证状态
- 新增categories store管理分类数据
- 新增announcements store管理公告数据
- 新增exchanges store管理兑换记录
- 新增users store管理用户数据

### 组件改造
- 将App.vue逻辑拆分到多个view组件
- 在需要认证的组件中添加权限检查
- 添加加载状态和错误处理
- 实现商品多图片预览功能
- 实现公告展示和管理功能

## 7. 后端实现方案

### 项目结构
```
server/
├── config/               # 配置文件
│   ├── config.js         # 主配置文件
│   └── database.js       # 数据库配置
├── models/               # 数据库模型
│   ├── index.js          # 模型关联
│   ├── user.js           # 用户模型
│   ├── product.js        # 商品模型
│   ├── category.js       # 分类模型
│   ├── productImage.js   # 商品图片模型
│   ├── announcement.js   # 公告模型
│   ├── exchange.js       # 兑换记录模型
│   └── feedback.js       # 反馈模型
├── controllers/          # 控制器
│   ├── authController.js # 认证控制器
│   ├── userController.js # 用户控制器
│   ├── productController.js  # 商品控制器
│   ├── categoryController.js # 分类控制器
│   ├── announcementController.js # 公告控制器
│   ├── exchangeController.js # 兑换控制器
│   └── feedbackController.js # 反馈控制器
├── routes/               # 路由定义
│   ├── auth.js           # 认证路由
│   ├── users.js          # 用户路由
│   ├── products.js       # 商品路由
│   ├── categories.js     # 分类路由
│   ├── announcements.js  # 公告路由
│   ├── exchanges.js      # 兑换路由
│   └── feedback.js       # 反馈路由
├── middlewares/          # 中间件
│   ├── auth.js           # 认证中间件
│   ├── error.js          # 错误处理
│   ├── upload.js         # 文件上传
│   └── validation.js     # 数据验证
├── utils/                # 工具函数
│   ├── jwt.js            # JWT工具
│   └── logger.js         # 日志工具
├── app.js                # Express应用
└── server.js             # 服务器入口
```

### 数据库初始化
- 使用Sequelize迁移功能创建数据库表
- 开发数据库种子脚本导入初始商品数据
- 实现数据库关系映射

### 认证与授权
- 实现JWT认证机制
- 基于角色的访问控制
- 密码加盐哈希存储

### 文件上传
- 实现商品图片上传功能
- 支持多张图片上传
- 图片存储方案（本地或云存储）

## 8. 部署方案

### 开发环境
- 前端: `npm run dev` (Vite)
- 后端: `npm run dev` (Nodemon)
- 数据库: 本地MySQL

### 生产环境
- 前端: 静态文件托管 (Nginx/CDN)
- 后端: Node.js应用部署 (PM2)
- 数据库: MySQL服务器
- HTTPS配置
- 负载均衡(可选)

## 9. 重构迭代开发计划

### 第一阶段：基础架构与核心功能 (2周)

1. **项目初始化与配置** (2天)
   - 搭建前端项目框架
   - 搭建后端项目框架
   - 数据库设计与创建
   - 配置开发环境

2. **用户认证系统** (3天)
   - 实现JWT认证
   - 登录功能
   - 权限控制

3. **商品管理核心功能** (5天)
   - 商品数据模型
   - 商品列表API
   - 商品详情API
   - 商品增删改查

4. **前端基础UI构建** (4天)
   - 主页布局
   - 商品列表组件
   - 商品详情组件
   - 登录界面

### 第二阶段：分类与筛选功能 (1周)

1. **分类系统** (3天)
   - 分类数据模型
   - 分类API实现
   - 分类管理界面

2. **筛选与搜索功能** (4天)
   - 搜索API
   - 筛选API
   - 前端搜索组件
   - 前端筛选组件

### 第三阶段：管理后台 (2周)

1. **用户管理模块** (3天)
   - 用户数据API
   - 用户管理界面
   - 用户权限控制

2. **商品管理模块** (4天)
   - 商品管理界面
   - 商品分类管理
   - 新品/爆款标记管理

3. **公告管理模块** (3天)
   - 公告数据模型
   - 公告API
   - 公告管理界面

4. **反馈管理模块** (2天)
   - 反馈数据模型
   - 反馈API
   - 反馈管理界面

### 第四阶段：高级功能与UI优化 (1周)

1. **多图片上传与预览** (3天)
   - 图片上传API
   - 图片存储方案
   - 前端图片预览组件

2. **UI/UX优化** (4天)
   - 响应式设计优化
   - 动画效果
   - 主题定制
   - 用户体验改进

### 第五阶段：测试与部署 (1周)

1. **测试** (3天)
   - 单元测试
   - 集成测试
   - 用户界面测试

2. **部署与上线** (4天)
   - 环境配置
   - 数据迁移
   - 服务器部署
   - 监控配置

## 10. 安全性考虑

- API接口限流防止DOS攻击
- JWT令牌过期机制
- 密码加盐哈希存储
- SQL注入防护
- CORS配置
- 前端敏感数据保护
- 管理员操作日志记录

## 11. 性能优化

- 数据库索引优化
- API响应缓存
- 前端资源懒加载
- CDN加速静态资源
- 图片压缩与优化
- 数据分页加载

## 12. 扩展功能建议

- 用户评论与评分系统
- 积分获取系统
- 兑换记录导出功能
- 管理员数据统计看板
- 多语言支持
- 移动端适配 