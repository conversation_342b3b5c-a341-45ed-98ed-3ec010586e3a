/**
 * 环境配置加载器
 * 根据NODE_ENV自动加载对应的环境配置文件
 *
 * 支持的环境配置文件：
 * - .env.development (开发环境)
 * - .env.test (测试环境)
 * - .env.production (生产环境)
 */

const path = require('path');
const fs = require('fs');

/**
 * 加载环境配置文件
 * @param {string} nodeEnv - 环境类型 (development/test/production)
 * @returns {boolean} - 是否成功加载配置
 */
function loadEnvironmentConfig(nodeEnv = process.env.NODE_ENV || 'development') {
  console.log('====== 环境配置加载器 ======');
  console.log(`当前环境: ${nodeEnv}`);
  
  // 确定配置文件路径
  const serverRoot = path.join(__dirname, '..');
  let envFilePath;
  let envFileName;
  
  if (nodeEnv === 'production') {
    envFileName = '.env.production';
    envFilePath = path.join(serverRoot, '.env.production');
  } else if (nodeEnv === 'test') {
    envFileName = '.env.test';
    envFilePath = path.join(serverRoot, '.env.test');
  } else {
    envFileName = '.env.development';
    envFilePath = path.join(serverRoot, '.env.development');
  }
  
  console.log(`配置文件: ${envFileName}`);
  console.log(`配置路径: ${envFilePath}`);
  
  // 检查配置文件是否存在
  if (!fs.existsSync(envFilePath)) {
    console.error(`❌ 配置文件不存在: ${envFilePath}`);
    
    // 尝试使用备用配置文件
    if (nodeEnv === 'production') {
      const fallbackPath = path.join(serverRoot, '.env.development');
      if (fs.existsSync(fallbackPath)) {
        console.warn(`⚠️  使用开发环境配置作为备用: ${fallbackPath}`);
        envFilePath = fallbackPath;
        envFileName = '.env.development (fallback)';
      } else {
        console.error('❌ 开发环境配置也不存在，无法启动应用');
        return false;
      }
    } else if (nodeEnv === 'test') {
      const fallbackPath = path.join(serverRoot, '.env.development');
      if (fs.existsSync(fallbackPath)) {
        console.warn(`⚠️  使用开发环境配置作为备用: ${fallbackPath}`);
        envFilePath = fallbackPath;
        envFileName = '.env.development (fallback)';
      } else {
        console.error('❌ 开发环境配置也不存在，无法启动应用');
        return false;
      }
    } else {
      console.error('❌ 开发环境配置不存在，无法启动应用');
      return false;
    }
  }
  
  // 加载配置文件
  try {
    require('dotenv').config({ path: envFilePath });
    console.log(`✅ 成功加载配置文件: ${envFileName}`);
    
    // 验证关键配置项
    const requiredConfigs = [
      'NODE_ENV',
      'PORT',
      'SERVER_URL',
      'DB_HOST',
      'DB_NAME',
      'JWT_SECRET',
      'FEISHU_APP_ID',
      'FEISHU_APP_SECRET',
      'FEISHU_REDIRECT_URI'
    ];
    
    const missingConfigs = [];
    requiredConfigs.forEach(config => {
      if (!process.env[config]) {
        missingConfigs.push(config);
      }
    });
    
    if (missingConfigs.length > 0) {
      console.warn('⚠️  以下配置项缺失:');
      missingConfigs.forEach(config => {
        console.warn(`   - ${config}`);
      });
    }
    
    // 显示关键配置信息（隐藏敏感信息）
    console.log('====== 关键配置信息 ======');
    console.log(`NODE_ENV: ${process.env.NODE_ENV}`);
    console.log(`PORT: ${process.env.PORT}`);
    console.log(`SERVER_URL: ${process.env.SERVER_URL}`);
    console.log(`DB_HOST: ${process.env.DB_HOST}`);
    console.log(`DB_NAME: ${process.env.DB_NAME}`);
    console.log(`FEISHU_APP_ID: ${process.env.FEISHU_APP_ID}`);
    console.log(`FEISHU_REDIRECT_URI: ${process.env.FEISHU_REDIRECT_URI}`);
    console.log(`CORS_ORIGIN: ${process.env.CORS_ORIGIN}`);
    console.log(`JWT_SECRET: ${process.env.JWT_SECRET ? '***已设置***' : '未设置'}`);
    console.log(`FEISHU_APP_SECRET: ${process.env.FEISHU_APP_SECRET ? '***已设置***' : '未设置'}`);
    console.log('====== 配置加载完成 ======');
    
    return true;
  } catch (error) {
    console.error(`❌ 加载配置文件失败: ${error.message}`);
    return false;
  }
}

/**
 * 获取当前环境类型
 * @returns {string} - 环境类型
 */
function getCurrentEnvironment() {
  return process.env.NODE_ENV || 'development';
}

/**
 * 检查是否为生产环境
 * @returns {boolean}
 */
function isProduction() {
  return getCurrentEnvironment() === 'production';
}

/**
 * 检查是否为开发环境
 * @returns {boolean}
 */
function isDevelopment() {
  return getCurrentEnvironment() === 'development';
}

/**
 * 获取前端URL
 * @returns {string}
 */
function getFrontendUrl() {
  if (process.env.FRONTEND_URL) {
    return process.env.FRONTEND_URL;
  }
  
  // 根据环境自动推断
  if (isProduction()) {
    return 'https://store.chongyangqisi.com';
  } else {
    return 'http://localhost:5173';
  }
}

module.exports = {
  loadEnvironmentConfig,
  getCurrentEnvironment,
  isProduction,
  isDevelopment,
  getFrontendUrl
};
