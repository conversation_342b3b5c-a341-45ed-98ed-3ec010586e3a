#!/usr/bin/env node

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api';

// 测试支付偏好分析功能
async function testPaymentPreference() {
  console.log('=== 支付偏好分析功能测试 ===\n');

  try {
    // 1. 登录获取token
    console.log('1. 管理员登录...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: '超管',
        email: '<EMAIL>',
        password: '654321'
      })
    });

    const loginData = await loginResponse.json();
    if (!loginData.token) {
      throw new Error('登录失败');
    }
    console.log('✅ 登录成功');

    const token = loginData.token;
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // 2. 测试不同时间周期的API调用
    const periods = ['week', 'month', 'quarter', 'year'];
    
    for (const period of periods) {
      console.log(`\n2. 测试 ${period} 周期数据...`);
      
      const response = await fetch(`${BASE_URL}/exchanges/payment-preference?period=${period}`, {
        method: 'GET',
        headers
      });

      if (!response.ok) {
        throw new Error(`API调用失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(`API返回错误: ${data.message}`);
      }

      // 验证数据结构
      const requiredFields = ['overview', 'paymentTrend', 'categoryPreference', 'userSegments'];
      const missingFields = requiredFields.filter(field => !data.data.hasOwnProperty(field));
      
      if (missingFields.length > 0) {
        throw new Error(`缺少必要字段: ${missingFields.join(', ')}`);
      }

      // 验证概览数据
      const overview = data.data.overview;
      const overviewFields = ['totalOrders', 'rmbOrders', 'lyOrders', 'rmbPercentage', 'lyPercentage'];
      const missingOverviewFields = overviewFields.filter(field => typeof overview[field] !== 'number');
      
      if (missingOverviewFields.length > 0) {
        throw new Error(`概览数据字段类型错误: ${missingOverviewFields.join(', ')}`);
      }

      console.log(`✅ ${period} 周期数据结构验证通过`);
      console.log(`   - 总订单数: ${overview.totalOrders}`);
      console.log(`   - 人民币订单: ${overview.rmbOrders} (${overview.rmbPercentage}%)`);
      console.log(`   - 光年币订单: ${overview.lyOrders} (${overview.lyPercentage}%)`);
      console.log(`   - 趋势数据点: ${data.data.paymentTrend.length}`);
      console.log(`   - 分类数据: ${data.data.categoryPreference.length}`);
      console.log(`   - 用户分层: RMB=${data.data.userSegments.rmbOnly}, LY=${data.data.userSegments.lyOnly}, Mixed=${data.data.userSegments.mixed}`);
    }

    // 3. 测试数据一致性
    console.log('\n3. 测试数据一致性...');
    const monthResponse = await fetch(`${BASE_URL}/exchanges/payment-preference?period=month`, {
      method: 'GET',
      headers
    });
    
    const monthData = await monthResponse.json();
    const overview = monthData.data.overview;
    
    // 验证百分比计算
    const calculatedRmbPercentage = overview.totalOrders > 0 ? 
      Math.round((overview.rmbOrders / overview.totalOrders) * 100 * 100) / 100 : 0;
    const calculatedLyPercentage = overview.totalOrders > 0 ? 
      Math.round((overview.lyOrders / overview.totalOrders) * 100 * 100) / 100 : 0;
    
    if (Math.abs(overview.rmbPercentage - calculatedRmbPercentage) > 0.01) {
      throw new Error(`人民币百分比计算错误: 期望 ${calculatedRmbPercentage}, 实际 ${overview.rmbPercentage}`);
    }
    
    if (Math.abs(overview.lyPercentage - calculatedLyPercentage) > 0.01) {
      throw new Error(`光年币百分比计算错误: 期望 ${calculatedLyPercentage}, 实际 ${overview.lyPercentage}`);
    }
    
    // 验证订单总数
    if (overview.totalOrders !== overview.rmbOrders + overview.lyOrders) {
      throw new Error(`订单总数计算错误: ${overview.totalOrders} ≠ ${overview.rmbOrders} + ${overview.lyOrders}`);
    }
    
    console.log('✅ 数据一致性验证通过');

    // 4. 测试分类偏好数据
    console.log('\n4. 测试分类偏好数据...');
    const categoryPreference = monthData.data.categoryPreference;
    
    for (const category of categoryPreference) {
      // 验证分类数据完整性
      const requiredCategoryFields = ['categoryId', 'categoryName', 'rmbCount', 'lyCount', 'totalOrders', 'rmbPercentage', 'lyPercentage'];
      const missingCategoryFields = requiredCategoryFields.filter(field => category[field] === undefined);
      
      if (missingCategoryFields.length > 0) {
        throw new Error(`分类数据缺少字段: ${missingCategoryFields.join(', ')}`);
      }
      
      // 验证分类百分比计算
      if (category.totalOrders !== category.rmbCount + category.lyCount) {
        throw new Error(`分类 ${category.categoryName} 订单总数计算错误`);
      }
      
      const expectedRmbPercentage = category.totalOrders > 0 ? 
        Math.round((category.rmbCount / category.totalOrders) * 100 * 100) / 100 : 0;
      const expectedLyPercentage = category.totalOrders > 0 ? 
        Math.round((category.lyCount / category.totalOrders) * 100 * 100) / 100 : 0;
      
      if (Math.abs(category.rmbPercentage - expectedRmbPercentage) > 0.01) {
        throw new Error(`分类 ${category.categoryName} 人民币百分比计算错误`);
      }
      
      if (Math.abs(category.lyPercentage - expectedLyPercentage) > 0.01) {
        throw new Error(`分类 ${category.categoryName} 光年币百分比计算错误`);
      }
    }
    
    console.log(`✅ 分类偏好数据验证通过 (${categoryPreference.length} 个分类)`);

    // 5. 测试用户分层数据
    console.log('\n5. 测试用户分层数据...');
    const userSegments = monthData.data.userSegments;
    
    const requiredSegmentFields = ['rmbOnly', 'lyOnly', 'mixed', 'preferences'];
    const missingSegmentFields = requiredSegmentFields.filter(field => userSegments[field] === undefined);
    
    if (missingSegmentFields.length > 0) {
      throw new Error(`用户分层数据缺少字段: ${missingSegmentFields.join(', ')}`);
    }
    
    if (!userSegments.preferences.hasOwnProperty('highValueRmb') || 
        !userSegments.preferences.hasOwnProperty('frequentLy')) {
      throw new Error('用户偏好数据结构错误');
    }
    
    console.log('✅ 用户分层数据验证通过');
    console.log(`   - 仅人民币用户: ${userSegments.rmbOnly}`);
    console.log(`   - 仅光年币用户: ${userSegments.lyOnly}`);
    console.log(`   - 混合支付用户: ${userSegments.mixed}`);
    console.log(`   - 高价值人民币用户: ${userSegments.preferences.highValueRmb}`);
    console.log(`   - 频繁光年币用户: ${userSegments.preferences.frequentLy}`);

    console.log('\n🎉 所有测试通过！支付偏好分析功能正常工作。');
    
    return {
      success: true,
      message: '支付偏好分析功能测试通过',
      testResults: {
        apiCalls: periods.length,
        dataStructure: '✅ 正确',
        dataConsistency: '✅ 正确',
        categoryData: `✅ ${categoryPreference.length} 个分类`,
        userSegments: '✅ 正确'
      }
    };

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// 运行测试
testPaymentPreference().then(result => {
  if (result.success) {
    console.log('\n📋 测试总结:');
    Object.entries(result.testResults).forEach(([key, value]) => {
      console.log(`• ${key}: ${value}`);
    });
    process.exit(0);
  } else {
    console.error('\n💥 测试失败，请检查错误信息');
    process.exit(1);
  }
}).catch(error => {
  console.error('测试执行出错:', error);
  process.exit(1);
});
