# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=feishu_mall

# 应用配置
NODE_ENV=production
PORT=3000
SERVER_URL=http://**************:3000

# JWT配置
JWT_SECRET=development_secret_key_please_change_in_production
JWT_EXPIRES_IN=1h
JWT_LONG_EXPIRES_IN=30d

# 飞书应用配置
FEISHU_APP_ID=cli_a66b3b2dcab8d013
FEISHU_APP_SECRET=5Fa8aatAGZ2Dv6K5VZhAWhbhjzE4lT2r
FEISHU_REDIRECT_URI=http://**************:3000/api/feishu/callback

# 飞书机器人配置
FEISHU_BOT_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/e6cff700-4172-4039-a700-43c8f43765fc

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880

# CORS配置
CORS_ORIGIN=http://**************,http://localhost:5173,http://localhost:5174,http://localhost:8080,http://127.0.0.1:5173
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS

# 日志配置
LOG_LEVEL=info
