# 生产环境配置指南

本文档提供如何配置生产环境的详细步骤。

## 前端配置

在项目根目录下创建 `.env.production` 文件，添加以下内容：

```
# 生产环境API配置
VITE_API_URL=http://your-production-server:3000/api
```

注意：将 `your-production-server` 替换为您的实际服务器IP或域名。

## 后端配置

在服务器端目录下创建 `.env` 文件，添加以下内容：

```
# 服务器配置
PORT=3000
NODE_ENV=production

# 数据库配置
DB_NAME=feishu_mall
DB_USER=your_db_user
DB_PASSWORD=your_secure_password
DB_HOST=localhost
DB_PORT=3306

# JWT配置
JWT_SECRET=your_very_secure_random_string
JWT_EXPIRES_IN=1d

# 上传目录配置
UPLOAD_DIR=uploads

# CORS配置
CORS_ORIGIN=http://your-production-server
```

注意：
- 将 `your_db_user` 和 `your_secure_password` 替换为您的数据库用户名和密码
- 将 `your_very_secure_random_string` 替换为一个随机生成的安全密钥
- 将 `your-production-server` 替换为您的实际服务器IP或域名

## 重要提示

1. 环境文件包含敏感信息，请确保：
   - 不要将这些文件提交到版本控制系统
   - 限制这些文件的访问权限
   - 在生产环境中使用强密码和密钥

2. 构建前端代码前确保已正确配置 `.env.production` 文件：
   ```bash
   npm run build
   ```

3. 启动后端服务前确保已正确配置 `.env` 文件：
   ```bash
   cd server
   npm start
   ```

4. 检查配置是否生效：
   - 前端构建后检查生成的JS文件是否包含正确的API URL
   - 后端启动后检查日志确认环境变量是否正确加载