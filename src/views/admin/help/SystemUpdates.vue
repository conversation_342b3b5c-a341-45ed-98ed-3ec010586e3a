<template>
  <div class="help-doc system-updates">
    <h2>系统更新</h2>

    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>系统更新日志</h3>
        </div>
      </template>
      <div class="card-content">
        <p>本页面记录了系统各个版本的更新内容，帮助您了解系统的发展历程和最新变化。</p>

        <el-timeline>
          <el-timeline-item
            timestamp="2025年1月"
            placement="top"
            type="primary"
            size="large"
          >
            <el-card class="update-card">
              <template #header>
                <div class="version-header">
                  <h4>版本 1.4.1</h4>
                  <el-tag type="success">最新版本</el-tag>
                </div>
              </template>
              <h5>重要修复</h5>
              <ul>
                <li>🐛 修复管理后台系统设置模块前端渲染问题</li>
                <li>✨ 修复IntelligentSchedule.vue组件中onUnmounted钩子嵌套错误</li>
                <li>🔧 正确导入和使用Vue生命周期钩子函数</li>
                <li>⚡ 优化定时器清理机制，防止内存泄漏</li>
              </ul>
              <h5>功能增强</h5>
              <ul>
                <li>🛡️ 为高级功能组件添加ErrorBoundary错误边界包装</li>
                <li>🔄 增强API调用的错误处理和容错机制</li>
                <li>📊 添加默认值设置，避免页面崩溃</li>
                <li>🚀 增强路由错误处理机制</li>
                <li>✅ 添加路由跳转失败的捕获和处理</li>
              </ul>
              <h5>用户体验改进</h5>
              <ul>
                <li>🎯 解决"点击高级诊断后再点击其他菜单无反应变空白"的问题</li>
                <li>🔄 确保系统设置页面各功能模块间的正常切换</li>
                <li>⚡ 提升页面渲染稳定性和响应速度</li>
                <li>🧪 新增修复验证脚本，确保问题彻底解决</li>
              </ul>
            </el-card>
          </el-timeline-item>

          <el-timeline-item
            timestamp="2024年12月15日"
            placement="top"
            type="info"
          >
            <el-card class="update-card">
              <template #header>
                <div class="version-header">
                  <h4>版本 2.1.0</h4>
                </div>
              </template>
              <h5>新增功能</h5>
              <ul>
                <li>深度集成飞书生态，支持飞书登录认证和群消息推送</li>
                <li>新增双重支付系统，支持光年币和人民币两种支付方式</li>
                <li>完整的订单管理系统，支持订单状态跟踪和批量操作</li>
                <li>智能通知系统，自动推送每日、每周、每月销售报告</li>
                <li>里程碑庆祝功能，达成销售目标时自动发送庆祝消息</li>
                <li>自定义消息模板，支持个性化飞书群推送内容</li>
                <li>智能发送时间控制，避免非工作时间打扰</li>
              </ul>
              <h5>功能优化</h5>
              <ul>
                <li>全新的管理后台界面，采用现代化卡片式布局</li>
                <li>优化数据仪表盘，提供更直观的数据可视化</li>
                <li>改进商品管理流程，支持批量操作和数据导出</li>
                <li>增强用户管理，支持部门和职场信息管理</li>
                <li>完善日志系统，提供详细的操作记录和审计功能</li>
              </ul>
              <h5>问题修复</h5>
              <ul>
                <li>修复飞书登录在某些情况下的认证失败问题</li>
                <li>修复订单状态更新时的数据同步问题</li>
                <li>修复大量数据导出时的性能问题</li>
                <li>修复通知推送的时区计算错误</li>
              </ul>
            </el-card>
          </el-timeline-item>

          <el-timeline-item
            timestamp="2023年12月15日"
            placement="top"
            type="info"
          >
            <el-card class="update-card">
              <template #header>
                <div class="version-header">
                  <h4>版本 3.5.0</h4>
                </div>
              </template>
              <h5>新增功能</h5>
              <ul>
                <li>新增帮助中心模块，提供详细的系统使用指南</li>
                <li>新增数据导出功能，支持多种格式（Excel、PDF、CSV）</li>
                <li>新增批量操作功能，提高批量处理效率</li>
                <li>新增系统通知功能，实时接收重要消息</li>
              </ul>
              <h5>功能优化</h5>
              <ul>
                <li>优化商品管理界面，支持更多筛选条件</li>
                <li>改进数据统计报表，新增多种数据分析图表</li>
                <li>优化系统响应速度，提升整体性能</li>
              </ul>
              <h5>问题修复</h5>
              <ul>
                <li>修复商品导入时可能出现的数据格式问题</li>
                <li>修复部分浏览器兼容性问题</li>
                <li>修复用户权限设置中的逻辑错误</li>
              </ul>
            </el-card>
          </el-timeline-item>

          <el-timeline-item
            timestamp="2023年10月20日"
            placement="top"
            type="info"
          >
            <el-card class="update-card">
              <template #header>
                <div class="version-header">
                  <h4>版本 3.4.2</h4>
                </div>
              </template>
              <h5>功能优化</h5>
              <ul>
                <li>优化移动端适配，提升移动设备使用体验</li>
                <li>改进用户界面交互，简化操作流程</li>
              </ul>
              <h5>问题修复</h5>
              <ul>
                <li>修复数据统计中的计算错误</li>
                <li>修复部分UI组件在特定条件下的显示问题</li>
                <li>修复用户反馈处理流程中的状态更新问题</li>
              </ul>
            </el-card>
          </el-timeline-item>

          <el-timeline-item
            timestamp="2023年9月5日"
            placement="top"
            type="info"
          >
            <el-card class="update-card">
              <template #header>
                <div class="version-header">
                  <h4>版本 3.4.0</h4>
                </div>
              </template>
              <h5>新增功能</h5>
              <ul>
                <li>新增用户行为分析模块，提供用户操作的详细统计</li>
                <li>新增自定义报表功能，支持个性化数据报表生成</li>
                <li>新增API接口文档，方便第三方系统集成</li>
              </ul>
              <h5>功能优化</h5>
              <ul>
                <li>重构权限管理系统，支持更细粒度的权限控制</li>
                <li>优化数据库结构，提升数据查询和处理效率</li>
              </ul>
              <h5>问题修复</h5>
              <ul>
                <li>修复多用户同时操作时可能出现的数据冲突问题</li>
                <li>修复大数据量导出时的内存占用问题</li>
              </ul>
            </el-card>
          </el-timeline-item>

          <el-timeline-item
            timestamp="更早版本"
            placement="top"
            type="info"
          >
            <el-button type="primary" @click="showEarlierVersions = !showEarlierVersions">
              {{ showEarlierVersions ? '隐藏更早版本' : '查看更早版本' }}
            </el-button>

            <div v-if="showEarlierVersions" class="earlier-versions">
              <el-card class="update-card" v-for="(version, index) in earlierVersions" :key="index">
                <template #header>
                  <div class="version-header">
                    <h4>{{ version.name }}</h4>
                  </div>
                </template>
                <p class="version-date">{{ version.date }}</p>
                <div v-html="version.content"></div>
              </el-card>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>

    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>更新政策与流程</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>更新周期</h4>
        <p>我们采用以下更新周期发布系统更新：</p>
        <ul>
          <li><strong>大版本更新</strong>：每季度发布一次，包含重大功能更新和系统优化</li>
          <li><strong>小版本更新</strong>：每月发布一次，包含功能改进和问题修复</li>
          <li><strong>补丁更新</strong>：根据需要随时发布，主要用于修复重要问题</li>
        </ul>

        <h4>更新通知</h4>
        <p>系统更新前，我们会通过以下方式提前通知用户：</p>
        <ul>
          <li>系统内部公告</li>
          <li>管理员邮件通知</li>
          <li>登录页面提示</li>
        </ul>

        <div class="info-block">
          <h4>更新注意事项</h4>
          <ul>
            <li>大版本更新可能需要短暂的系统维护，通常安排在非工作时间进行</li>
            <li>更新后，建议清除浏览器缓存，以确保获得最佳体验</li>
            <li>如遇更新问题，请联系系统管理员或技术支持</li>
          </ul>
        </div>
      </div>
    </el-card>

    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>功能建议与反馈</h3>
        </div>
      </template>
      <div class="card-content">
        <p>我们非常重视用户的建议和反馈，它们是系统持续改进的重要依据。如果您有任何功能建议或问题反馈，请通过以下方式提交：</p>

        <div class="feedback-methods">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="method-item">
                <el-icon :size="40" color="#409EFF"><ChatDotRound /></el-icon>
                <h4>反馈系统</h4>
                <p>使用系统内置的反馈功能提交建议</p>
                <el-button type="primary">提交反馈</el-button>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="method-item">
                <el-icon :size="40" color="#67C23A"><Message /></el-icon>
                <h4>电子邮件</h4>
                <p>发送邮件至：<EMAIL></p>
                <el-button type="success">发送邮件</el-button>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="method-item">
                <el-icon :size="40" color="#E6A23C"><Service /></el-icon>
                <h4>联系客服</h4>
                <p>直接联系我们的客服团队</p>
                <el-button type="warning">联系客服</el-button>
              </div>
            </el-col>
          </el-row>
        </div>

        <p>我们会认真评估每一条建议，并在未来的版本更新中考虑采纳。感谢您对系统改进的贡献！</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ChatDotRound, Message, Service } from '@element-plus/icons-vue';

// 显示更早版本的控制变量
const showEarlierVersions = ref(false);

// 更早版本的数据
const earlierVersions = [
  {
    name: '版本 3.3.5',
    date: '2023年8月12日',
    content: `
      <h5>功能优化</h5>
      <ul>
        <li>优化搜索功能，提高搜索结果准确性</li>
        <li>改进数据导入功能，支持更多数据格式</li>
      </ul>
      <h5>问题修复</h5>
      <ul>
        <li>修复部分报表导出失败的问题</li>
        <li>修复用户列表分页显示错误</li>
      </ul>
    `
  },
  {
    name: '版本 3.3.0',
    date: '2023年7月1日',
    content: `
      <h5>新增功能</h5>
      <ul>
        <li>新增数据备份与恢复功能</li>
        <li>新增系统操作日志查询</li>
      </ul>
      <h5>功能优化</h5>
      <ul>
        <li>重新设计用户界面，提升用户体验</li>
        <li>优化数据加载速度，减少等待时间</li>
      </ul>
      <h5>问题修复</h5>
      <ul>
        <li>修复角色权限设置保存失败的问题</li>
        <li>修复图表统计数据不准确的问题</li>
      </ul>
    `
  },
  {
    name: '版本 3.2.0',
    date: '2023年5月15日',
    content: `
      <h5>新增功能</h5>
      <ul>
        <li>新增多语言支持，包括英文、日文、韩文</li>
        <li>新增数据导入导出功能</li>
      </ul>
      <h5>功能优化</h5>
      <ul>
        <li>优化后台管理界面，增强易用性</li>
        <li>改进系统安全性，加强数据保护</li>
      </ul>
    `
  }
];
</script>

<style scoped>
.system-updates h2 {
  margin-bottom: 20px;
  font-weight: bold;
  color: #303133;
}

.help-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.card-content {
  line-height: 1.6;
}

.card-content h4 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-size: 16px;
  color: #303133;
}

.card-content p {
  margin-bottom: 12px;
  color: #606266;
}

.card-content ul {
  padding-left: 20px;
  margin-bottom: 16px;
}

.card-content li {
  margin-bottom: 6px;
  color: #606266;
}

.update-card {
  margin-bottom: 15px;
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.version-header h4 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.version-date {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.update-card h5 {
  font-size: 15px;
  margin-top: 15px;
  margin-bottom: 8px;
  color: #303133;
}

.earlier-versions {
  margin-top: 15px;
}

.info-block {
  background-color: #f0f9ff;
  border-left: 4px solid #409EFF;
  padding: 12px 16px;
  margin: 16px 0;
  border-radius: 4px;
}

.feedback-methods {
  margin: 20px 0;
}

.method-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  height: 100%;
  background-color: #fafafa;
  transition: all 0.3s;
}

.method-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.method-item h4 {
  margin: 12px 0;
  color: #303133;
}

.method-item p {
  margin-bottom: 15px;
  color: #606266;
  font-size: 14px;
}

.method-item .el-button {
  width: 100%;
}
</style>
