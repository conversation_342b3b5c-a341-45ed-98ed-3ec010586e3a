<template>
  <div class="exchange-form-container">
    <!-- 第一步：填写兑换信息 -->
    <div v-if="currentStep === 1">
      <div class="step-indicator">
        <div class="step-title">第1步：填写信息</div>
        <el-steps :active="1" simple>
          <el-step title="填写信息" />
          <el-step title="支付确认" v-if="showPaymentStep" />
          <el-step title="提交申请" />
        </el-steps>
      </div>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-position="top"
        class="exchange-form"
      >
        <el-form-item label="联系方式（手机号码）" prop="contactInfo">
          <el-input v-model="form.contactInfo" placeholder="请输入您的手机号码" maxlength="11" />
        </el-form-item>

        <el-form-item label="所在职场位置" prop="location">
          <el-select
            v-model="form.location"
            placeholder="请选择您的职场"
            class="w-full"
            :loading="loadingWorkplaces"
            @change="handleWorkplaceChange"
          >
            <el-option
              v-for="item in workplaces"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="数量" prop="quantity">
          <el-input-number
            v-model="form.quantity"
            :min="1"
            :max="maxQuantity"
            :disabled="maxQuantity <= 0"
            class="w-full"
          />
          <div class="text-xs text-gray-500 mt-1">
            商品库存: {{ product.stock }}
          </div>
        </el-form-item>

        <el-form-item label="支付方式" prop="paymentMethod">
          <el-radio-group v-model="form.paymentMethod">
            <el-radio label="ly">光年币支付 ({{ product.lyPrice }})</el-radio>
            <el-radio label="rmb">人民币支付 (¥{{ product.rmbPrice }})</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="form.remarks"
            type="textarea"
            :rows="3"
            placeholder="可选填，如有特殊要求请在此说明"
          />
        </el-form-item>

        <div class="exchange-summary">
          <div class="flex justify-between mb-2">
            <div class="font-bold">商品名称:</div>
            <div>{{ product.name }}</div>
          </div>
          <div class="flex justify-between mb-2">
            <div class="font-bold">数量:</div>
            <div>{{ form.quantity }}</div>
          </div>
          <div class="flex justify-between mb-2">
            <div class="font-bold">应付:</div>
            <div v-if="form.paymentMethod === 'ly'">
              {{ form.quantity * product.lyPrice }} 光年币
            </div>
            <div v-else>
              ¥{{ (form.quantity * product.rmbPrice).toFixed(2) }}
            </div>
          </div>
        </div>

        <div class="form-actions">
          <el-button type="primary" @click="proceedToPayment">
            下一步
          </el-button>
          <el-button @click="$emit('cancel')">取消</el-button>
        </div>
      </el-form>
    </div>

    <!-- 第二步：支付确认（仅在使用rmb支付方式时显示） -->
    <div v-else-if="currentStep === 2 && showPaymentStep">
      <div class="step-indicator">
        <div class="step-title">第2步：支付确认</div>
        <el-steps :active="2" simple>
          <el-step title="填写信息" />
          <el-step title="支付确认" />
          <el-step title="提交申请" />
        </el-steps>
      </div>

      <PaymentQRCode
        :amount="totalAmount"
        @payment-confirmed="handlePaymentConfirmed"
        @cancel="backToStep(1)"
      />
    </div>

    <!-- 最后一步：确认信息和提交 -->
    <div v-else>
      <div class="step-indicator">
        <div class="step-title">{{ showPaymentStep ? '第3步：提交申请' : '第2步：提交申请' }}</div>
        <el-steps :active="showPaymentStep ? 3 : 2" simple>
          <el-step title="填写信息" />
          <el-step title="支付确认" v-if="showPaymentStep" />
          <el-step title="提交申请" />
        </el-steps>
      </div>

      <div class="confirmation-container">
        <h3 class="confirmation-title">确认订单信息</h3>

        <div class="confirmation-content">
          <div class="confirmation-item">
            <span class="item-label">商品名称:</span>
            <span class="item-value">{{ product.name }}</span>
          </div>
          <div class="confirmation-item">
            <span class="item-label">数量:</span>
            <span class="item-value">{{ form.quantity }}</span>
          </div>
          <div class="confirmation-item">
            <span class="item-label">支付方式:</span>
            <span class="item-value">{{ form.paymentMethod === 'ly' ? '光年币' : '人民币' }}</span>
          </div>
          <div class="confirmation-item">
            <span class="item-label">应付金额:</span>
            <span class="item-value" :class="{'rmb-amount': form.paymentMethod === 'rmb', 'ly-amount': form.paymentMethod === 'ly'}">
              {{ form.paymentMethod === 'ly'
                ? `${form.quantity * product.lyPrice} 光年币`
                : `¥${(form.quantity * product.rmbPrice).toFixed(2)}` }}
            </span>
          </div>
          <div class="confirmation-item">
            <span class="item-label">联系方式:</span>
            <span class="item-value">{{ form.contactInfo }}</span>
          </div>
          <div class="confirmation-item">
            <span class="item-label">所在职场:</span>
            <span class="item-value">{{ form.location }}</span>
          </div>
          <div class="confirmation-item" v-if="form.remarks">
            <span class="item-label">备注:</span>
            <span class="item-value">{{ form.remarks }}</span>
          </div>

          <!-- 显示支付凭证（如果有的话） -->
          <div class="confirmation-item" v-if="form.paymentProofUrl">
            <span class="item-label">支付凭证:</span>
            <div class="payment-proof-preview">
              <img :src="form.paymentProofUrl" @click="previewPaymentProof" alt="支付凭证" />
            </div>
          </div>
        </div>

        <div class="confirmation-actions">
          <el-button @click="backToStep(showPaymentStep ? 2 : 1)">上一步</el-button>
          <el-button type="primary" @click="submitForm" :loading="loading">
            提交申请
          </el-button>
        </div>
      </div>
    </div>

    <!-- 支付凭证预览对话框 -->
    <el-dialog v-model="proofDialogVisible" title="支付凭证预览">
      <img :src="form.paymentProofUrl" style="width: 100%;" alt="支付凭证预览" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { createExchange, getUserLastExchange } from '../api/exchanges';
import PaymentQRCode from './PaymentQRCode.vue';
import { getAllActiveWorkplaces } from '../api/system';
import { useAuthStore } from '../stores/auth';

const props = defineProps({
  product: {
    type: Object,
    required: true
  },
  defaultPaymentMethod: {
    type: String,
    default: 'ly',
    validator: (value) => ['ly', 'rmb'].includes(value)
  }
});

const emit = defineEmits(['success', 'cancel']);
const authStore = useAuthStore();

const formRef = ref(null);
const loading = ref(false);
const currentStep = ref(1);
const proofDialogVisible = ref(false);
const isLoadingUserData = ref(false);

// 最大可选数量
const maxQuantity = computed(() => props.product.stock);

// 表单数据
const workplaces = ref([]);
const loadingWorkplaces = ref(false);
const form = reactive({
  productId: props.product.id,
  contactInfo: '',
  location: '',
  workplaceId: null,
  quantity: 1,
  paymentMethod: props.defaultPaymentMethod,
  remarks: '',
  paymentProofUrl: ''
});

// 计算是否显示支付步骤（只在使用rmb支付方式时显示）
const showPaymentStep = computed(() => form.paymentMethod === 'rmb');

// 计算总金额
const totalAmount = computed(() => {
  return form.paymentMethod === 'rmb'
    ? form.quantity * props.product.rmbPrice
    : form.quantity * props.product.lyPrice;
});

// 表单验证规则
const rules = {
  contactInfo: [
    { required: true, message: '请输入联系方式', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码格式', trigger: 'blur' }
  ],
  location: [
    { required: true, message: '请选择您的职场位置', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: '请选择数量', trigger: 'change' },
    { type: 'number', min: 1, message: '数量不能小于1', trigger: 'change' }
  ],
  paymentMethod: [
    { required: true, message: '请选择支付方式', trigger: 'change' }
  ]
};

// 获取用户信息并自动填充表单
const autoFillUserInfo = async () => {
  isLoadingUserData.value = true;

  try {
    // 从用户个人资料中获取手机号码和职场信息
    if (authStore.user) {
      const { mobile, workplace } = authStore.user;

      // 如果用户资料中有手机号码，则自动填充
      if (mobile) {
        form.contactInfo = mobile;
      }

      // 如果用户资料中有职场信息，则自动填充
      if (workplace) {
        form.location = workplace;
      }
    }

    // 如果用户资料中缺少信息，则尝试从最近的兑换记录中获取
    if (!form.contactInfo || !form.location) {
      try {
        const response = await getUserLastExchange();

        if (response && response.data) {
          const lastExchange = response.data;

          // 如果还没有填充联系方式，则使用最近兑换记录中的联系方式
          if (!form.contactInfo && lastExchange.contactInfo) {
            form.contactInfo = lastExchange.contactInfo;
          }

          // 如果还没有填充职场位置，则使用最近兑换记录中的职场位置
          if (!form.location && lastExchange.location) {
            form.location = lastExchange.location;
          }
        }
      } catch (error) {
        console.error('获取最近兑换记录失败:', error);
        // 获取失败不影响表单填写，仅记录错误
      }
    }
  } catch (error) {
    console.error('自动填充用户信息失败:', error);
  } finally {
    isLoadingUserData.value = false;
  }
};

// 添加获取职场数据的方法
const fetchWorkplaces = async () => {
  loadingWorkplaces.value = true;
  try {
    const response = await getAllActiveWorkplaces();
    workplaces.value = response;
  } catch (error) {
    console.error('获取职场列表失败:', error);
    ElMessage.error('获取职场列表失败，请刷新重试');
  } finally {
    loadingWorkplaces.value = false;
  }
};

// 在onMounted中调用获取职场数据和自动填充用户信息
onMounted(async () => {
  // 根据传入的默认支付方式设置初始值
  form.paymentMethod = props.defaultPaymentMethod;

  // 获取职场数据
  await fetchWorkplaces();

  // 自动填充用户信息
  await autoFillUserInfo();

  // 处理职场ID
  if (form.location) {
    handleWorkplaceChange(form.location);
  }
});

// 处理进入下一步
const proceedToPayment = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid, fields) => {
    if (!valid) {
      console.error('表单验证失败:', fields);
      return;
    }

    // 跳转到下一步（支付或确认）
    currentStep.value = showPaymentStep.value ? 2 : 3;
  });
};

// 处理支付确认
const handlePaymentConfirmed = (paymentData) => {
  console.log('支付已确认:', paymentData);
  // 保存支付凭证URL
  form.paymentProofUrl = paymentData.paymentProofUrl;
  // 进入最后确认步骤
  currentStep.value = 3;
};

// 返回上一步
const backToStep = (step) => {
  currentStep.value = step;
};

// 预览支付凭证
const previewPaymentProof = () => {
  proofDialogVisible.value = true;
};

// 提交表单
const submitForm = async () => {
  try {
    loading.value = true;

    // 验证人民币支付是否有上传支付凭证
    if (form.paymentMethod === 'rmb' && !form.paymentProofUrl) {
      ElMessage.error('请先完成支付并上传支付凭证');
      loading.value = false;
      return;
    }

    // 发送请求创建兑换申请
    const response = await createExchange(form);

    ElMessage.success('兑换申请已提交成功！');
    emit('success', response.data);
  } catch (error) {
    console.error('创建兑换申请失败:', error);
    let errorMsg = '提交申请失败，请稍后重试';

    // 处理不同类型的错误
    if (error.response && error.response.data && error.response.data.message) {
      errorMsg = error.response.data.message;
    } else if (error.message) {
      // 处理网络超时等错误
      if (error.message.includes('timeout') || error.message.includes('Network Error')) {
        errorMsg = '网络请求超时，但申请可能已提交成功，请稍后查看订单页面确认';
        ElMessage.warning(errorMsg);
        // 对于超时错误，不直接显示失败，而是提示用户检查
        return;
      } else {
        errorMsg = error.message;
      }
    }

    ElMessage.error(errorMsg);
  } finally {
    loading.value = false;
  }
};

// 添加处理职场选择改变的方法
const handleWorkplaceChange = (value) => {
  if (value) {
    const selectedWorkplace = workplaces.value.find(wp => wp.name === value);
    if (selectedWorkplace) {
      form.workplaceId = selectedWorkplace.id;
    }
  } else {
    form.workplaceId = null;
  }
};
</script>

<style scoped>
.exchange-form-container {
  padding: 20px;
}

.step-indicator {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.step-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #303133;
}

.exchange-form {
  max-width: 600px;
  margin: 0 auto;
}

.exchange-summary {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
  border-left: 4px solid #409eff;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

.w-full {
  width: 100%;
}

/* 确认页面样式 */
.confirmation-container {
  max-width: 600px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.confirmation-title {
  font-size: 18px;
  color: #303133;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.confirmation-content {
  margin-bottom: 20px;
}

.confirmation-item {
  display: flex;
  padding: 10px 0;
  border-bottom: 1px dashed #ebeef5;
}

.item-label {
  width: 100px;
  color: #606266;
  font-weight: bold;
}

.item-value {
  flex: 1;
  color: #303133;
}

.rmb-amount {
  color: #f56c6c;
  font-weight: bold;
}

.ly-amount {
  color: #409eff;
  font-weight: bold;
}

.payment-proof-preview {
  margin-top: 10px;
}

.payment-proof-preview img {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #ebeef5;
  transition: all 0.3s;
}

.payment-proof-preview img:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.confirmation-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
}
</style>
