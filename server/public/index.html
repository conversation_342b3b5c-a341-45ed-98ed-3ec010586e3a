<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>光年小卖部 API 服务</title>
  <style>
    body {
      font-family: 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f8f9fa;
    }
    header {
      background-color: #4a86e8;
      color: white;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 30px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    h1, h2 {
      margin-top: 0;
    }
    .content {
      background-color: white;
      padding: 25px;
      border-radius: 5px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .btn {
      display: inline-block;
      background-color: #4a86e8;
      color: white;
      padding: 10px 20px;
      text-decoration: none;
      border-radius: 5px;
      font-weight: bold;
      margin-top: 15px;
      transition: background-color 0.3s;
    }
    .btn:hover {
      background-color: #3a76d8;
    }
    footer {
      margin-top: 30px;
      text-align: center;
      color: #666;
      font-size: 14px;
    }
    .api-section {
      margin-top: 20px;
      padding: 15px;
      border: 1px solid #e3e3e3;
      border-radius: 4px;
      background-color: #f9f9f9;
    }
    .api-path {
      font-family: monospace;
      background-color: #f0f0f0;
      padding: 2px 5px;
      border-radius: 3px;
    }
  </style>
</head>
<body>
  <header>
    <h1>光年小卖部 API 服务</h1>
  </header>

  <div class="content">
    <h2>欢迎使用光年小卖部 API</h2>
    <p>这是光年小卖部电商平台的后端API服务。使用本API可以管理商品、用户、订单等功能。</p>

    <div class="api-section">
      <h3>API 文档</h3>
      <p>完整的API文档可通过以下链接查看：</p>
      <a href="/api-docs" class="btn">查看 API 文档</a>
    </div>

    <div class="api-section">
      <h3>主要API路径</h3>
      <ul>
        <li><span class="api-path">/api/auth</span> - 认证相关API</li>
        <li><span class="api-path">/api/products</span> - 商品管理API</li>
        <li><span class="api-path">/api/users</span> - 用户管理API</li>
        <li><span class="api-path">/api/exchanges</span> - 订单管理API</li>
        <li><span class="api-path">/api/categories</span> - 分类管理API</li>
      </ul>
    </div>

    <div class="api-section">
      <h3>系统状态</h3>
      <ul>
        <li><span class="api-path">/api/health</span> - 服务健康状态检查</li>
        <li><span class="api-path">/api/env-check</span> - 环境配置检查</li>
      </ul>
    </div>
  </div>

  <footer>
    <p>© 2024 光年小卖部. 保留所有权利.</p>
  </footer>
</body>
</html>
