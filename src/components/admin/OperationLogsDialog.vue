<template>
  <el-dialog
    v-model="dialogVisible"
    title="库存操作日志"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="logs-content">
      <!-- 操作工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" icon="el-icon-refresh" @click="loadLogs" :loading="loading">
            刷新
          </el-button>
          <el-button type="success" icon="el-icon-download" @click="exportLogs" :disabled="logs.length === 0">
            导出
          </el-button>
          <el-button type="info" icon="el-icon-pie-chart" @click="showAnalysis">
            统计分析
          </el-button>
        </div>
        <div class="toolbar-right">
          <span class="record-count">共 {{ totalRecords }} 条记录</span>
        </div>
      </div>

      <!-- 筛选条件 -->
      <div class="filters">
        <div class="filter-row">
          <div class="filter-item">
            <label>商品搜索:</label>
            <el-input
              v-model="filters.productSearch"
              placeholder="输入商品名称或ID"
              style="width: 180px"
              clearable
              @keyup.enter="loadLogs"
            >
              <template #prepend>
                <el-select
                  v-model="searchType"
                  style="width: 80px"
                  @change="handleSearchTypeChange"
                >
                  <el-option label="名称" value="name" />
                  <el-option label="ID" value="id" />
                </el-select>
              </template>
            </el-input>
          </div>
          <div class="filter-item">
            <label>用户:</label>
            <el-select
              v-model="filters.userId"
              placeholder="选择用户"
              style="width: 150px"
              clearable
              filterable
            >
              <el-option
                v-for="user in users"
                :key="user.id"
                :label="user.name"
                :value="user.id"
              />
            </el-select>
          </div>
          <div class="filter-item">
            <label>职场:</label>
            <el-select
              v-model="filters.workplaceId"
              placeholder="选择职场"
              style="width: 150px"
              clearable
            >
              <el-option
                v-for="workplace in workplaces"
                :key="workplace.id"
                :label="workplace.name"
                :value="workplace.id"
              />
            </el-select>
          </div>
          <div class="filter-item">
            <label>操作类型:</label>
            <el-select
              v-model="filters.operationType"
              placeholder="选择操作类型"
              style="width: 150px"
              clearable
            >
              <el-option label="库存更新" value="stock_update" />
              <el-option label="库存转移" value="stock_transfer" />
              <el-option label="快速调整" value="quick_adjust" />
              <el-option label="批量调整" value="batch_quick_adjust" />
              <el-option label="批量编辑" value="batch_edit" />
              <el-option label="库存同步" value="sync" />
              <el-option label="兑换扣减" value="exchange_deduct" />
              <el-option label="兑换恢复" value="exchange_restore" />
              <el-option label="数据迁移" value="migration" />
            </el-select>
          </div>
        </div>
        <div class="filter-row">
          <div class="filter-item">
            <label>开始日期:</label>
            <el-date-picker
              v-model="filters.startDate"
              type="datetime"
              placeholder="选择开始时间"
              style="width: 180px"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </div>
          <div class="filter-item">
            <label>结束日期:</label>
            <el-date-picker
              v-model="filters.endDate"
              type="datetime"
              placeholder="选择结束时间"
              style="width: 180px"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </div>
          <div class="filter-item">
            <label>操作状态:</label>
            <el-select
              v-model="filters.status"
              placeholder="选择状态"
              style="width: 120px"
              clearable
            >
              <el-option label="成功" value="completed" />
              <el-option label="失败" value="failed" />
              <el-option label="进行中" value="processing" />
            </el-select>
          </div>
          <div class="filter-actions">
            <el-button type="primary" @click="loadLogs" :loading="loading">
              查询
            </el-button>
            <el-button @click="resetFilters">
              重置
            </el-button>
            <el-button type="text" @click="toggleAdvancedFilters">
              {{ showAdvancedFilters ? '收起' : '高级筛选' }}
            </el-button>
          </div>
        </div>

        <!-- 高级筛选 -->
        <div v-show="showAdvancedFilters" class="advanced-filters">
          <div class="filter-row">
            <div class="filter-item">
              <label>IP地址:</label>
              <el-input
                v-model="filters.ipAddress"
                placeholder="输入IP地址"
                style="width: 150px"
                clearable
              />
            </div>
            <div class="filter-item">
              <label>会话ID:</label>
              <el-input
                v-model="filters.sessionId"
                placeholder="输入会话ID"
                style="width: 180px"
                clearable
              />
            </div>
            <div class="filter-item">
              <label>库存变化:</label>
              <el-select
                v-model="filters.stockChange"
                placeholder="选择变化类型"
                style="width: 120px"
                clearable
              >
                <el-option label="增加" value="increase" />
                <el-option label="减少" value="decrease" />
                <el-option label="无变化" value="no_change" />
              </el-select>
            </div>
          </div>
        </div>
      </div>

      <!-- 日志表格 -->
      <div class="logs-table">
        <el-table
          :data="logs"
          v-loading="loading"
          stripe
          style="width: 100%"
          :default-sort="{ prop: 'createdAt', order: 'descending' }"
          :header-cell-style="{
            background: '#f8f9fa',
            color: '#606266',
            fontWeight: '600',
            fontSize: '13px',
            padding: '12px 0'
          }"
          :cell-style="{ padding: '12px 0' }"
          :row-style="{ height: '60px' }"
        >
          <!-- 商品信息列 -->
          <el-table-column label="商品信息" width="220" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="product-cell">
                <div class="product-main">
                  <div class="product-name" v-if="row.product">
                    <i class="el-icon-goods"></i>
                    {{ row.product.name }}
                  </div>
                  <div class="product-name unknown" v-else>
                    <i class="el-icon-warning-outline"></i>
                    未知商品
                  </div>
                </div>
                <div class="product-id">ID: {{ row.productId }}</div>
              </div>
            </template>
          </el-table-column>

          <!-- 职场信息列 -->
          <el-table-column label="职场" width="130" align="center">
            <template #default="{ row }">
              <div class="workplace-cell">
                <div v-if="row.workplaceId && row.workplace" class="workplace-info">
                  <div class="workplace-name">
                    <i class="el-icon-office-building"></i>
                    {{ row.workplace.name }}
                  </div>
                  <div class="workplace-code">{{ row.workplace.code }}</div>
                </div>
                <div v-else class="workplace-global">
                  <i class="el-icon-s-grid"></i>
                  <span>总库存</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 操作类型列 -->
          <el-table-column label="操作类型" width="110" align="center">
            <template #default="{ row }">
              <el-tag
                :type="getOperationTypeColor(row.operationType)"
                size="default"
                effect="light"
                class="operation-tag"
              >
                {{ getOperationTypeText(row.operationType) }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 库存变化列 -->
          <el-table-column label="库存变化" width="180" align="center">
            <template #default="{ row }">
              <div class="stock-change-cell">
                <div class="stock-flow">
                  <span class="stock-before">{{ row.beforeStock }}</span>
                  <i class="el-icon-right arrow"></i>
                  <span class="stock-after">{{ row.afterStock }}</span>
                </div>
                <div class="stock-delta">
                  <el-tag
                    :type="row.changeAmount > 0 ? 'success' : row.changeAmount < 0 ? 'danger' : 'info'"
                    size="small"
                    effect="light"
                    class="change-tag"
                  >
                    <i :class="row.changeAmount > 0 ? 'el-icon-top' : row.changeAmount < 0 ? 'el-icon-bottom' : 'el-icon-minus'"></i>
                    {{ row.changeAmount > 0 ? '+' : '' }}{{ row.changeAmount }}
                  </el-tag>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 操作员列 -->
          <el-table-column label="操作员" width="130" align="center">
            <template #default="{ row }">
              <div class="operator-cell">
                <div v-if="row.operatorName" class="operator-info">
                  <div class="operator-name">
                    <i class="el-icon-user"></i>
                    {{ row.operatorName }}
                  </div>
                  <div class="operator-id" v-if="row.operatorId">
                    ID: {{ row.operatorId }}
                  </div>
                </div>
                <div v-else class="operator-system">
                  <i class="el-icon-cpu"></i>
                  <span>系统</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 操作原因列 -->
          <el-table-column label="操作原因" min-width="200" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="reason-cell">
                <div class="reason-text">
                  <i class="el-icon-document"></i>
                  {{ row.reason || '无备注' }}
                </div>
                <div class="batch-info" v-if="row.batchId">
                  <el-tag size="mini" type="info" effect="plain">
                    批次: {{ row.batchId.split('_').pop() }}
                  </el-tag>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 操作时间列 -->
          <el-table-column label="操作时间" width="170" align="center">
            <template #default="{ row }">
              <div class="time-cell">
                <div class="time-main">
                  <i class="el-icon-time"></i>
                  {{ formatDateTime(row.createdAt) }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="100" align="center">
            <template #default="{ row }">
              <el-button
                type="text"
                size="small"
                @click="viewDetails(row)"
              >
                详情
              </el-button>
            </template>
          </el-table-column>
          <!-- 空状态 -->
          <template #empty>
            <div class="empty-state">
              <i class="el-icon-document-remove"></i>
              <div class="empty-text">暂无操作日志</div>
              <div class="empty-description">
                {{ Object.values(filters).some(v => v) ? '当前筛选条件下没有找到相关记录' : '还没有任何库存操作记录' }}
              </div>
            </div>
          </template>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[20, 50, 100, 200]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalCount"
          />
        </div>
      </div>
    </div>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="操作详情"
      width="600px"
      append-to-body
    >
      <div class="log-detail" v-if="selectedLog">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>操作ID:</label>
              <span>{{ selectedLog.id }}</span>
            </div>
            <div class="detail-item">
              <label>商品信息:</label>
              <div class="product-detail">
                <div class="product-name-detail" v-if="selectedLog.product">
                  <i class="el-icon-goods"></i>
                  {{ selectedLog.product.name }}
                </div>
                <div class="product-id-detail">ID: {{ selectedLog.productId }}</div>
              </div>
            </div>
            <div class="detail-item">
              <label>职场信息:</label>
              <div class="workplace-detail" v-if="selectedLog.workplaceId">
                <div class="workplace-name-detail" v-if="selectedLog.workplace">
                  <i class="el-icon-office-building"></i>
                  {{ selectedLog.workplace.name }}
                </div>
                <div class="workplace-id-detail">ID: {{ selectedLog.workplaceId }}</div>
              </div>
              <span v-else class="text-muted">总库存操作</span>
            </div>
            <div class="detail-item">
              <label>操作类型:</label>
              <el-tag :type="getOperationTypeColor(selectedLog.operationType)" size="small">
                {{ getOperationTypeText(selectedLog.operationType) }}
              </el-tag>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>库存变化</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>操作前库存:</label>
              <span>{{ selectedLog.beforeStock }}</span>
            </div>
            <div class="detail-item">
              <label>操作后库存:</label>
              <span>{{ selectedLog.afterStock }}</span>
            </div>
            <div class="detail-item">
              <label>变化数量:</label>
              <span :class="{ 
                'increase': selectedLog.changeAmount > 0, 
                'decrease': selectedLog.changeAmount < 0 
              }">
                {{ selectedLog.changeAmount > 0 ? '+' : '' }}{{ selectedLog.changeAmount }}
              </span>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>操作信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>操作员:</label>
              <span>{{ selectedLog.operatorName || '系统' }}</span>
            </div>
            <div class="detail-item">
              <label>操作原因:</label>
              <span>{{ selectedLog.reason || '无' }}</span>
            </div>
            <div class="detail-item">
              <label>批次ID:</label>
              <span>{{ selectedLog.batchId || '无' }}</span>
            </div>
            <div class="detail-item">
              <label>IP地址:</label>
              <span>{{ selectedLog.ipAddress || '无' }}</span>
            </div>
            <div class="detail-item">
              <label>操作时间:</label>
              <span>{{ formatDateTime(selectedLog.createdAt) }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section" v-if="selectedLog.metadata">
          <h4>扩展信息</h4>
          <div class="metadata">
            <pre>{{ JSON.stringify(selectedLog.metadata, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 统计分析对话框 -->
    <el-dialog
      v-model="showAnalysisDialog"
      title="操作统计分析"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="analysis-content">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-title">总操作数</div>
            <div class="stat-value">{{ analysisData.totalOperations || 0 }}</div>
          </div>

          <div class="stat-card">
            <div class="stat-title">操作类型分布</div>
            <div class="stat-list">
              <div
                v-for="(count, type) in analysisData.operationTypes"
                :key="type"
                class="stat-item"
              >
                <span class="stat-label">{{ getOperationTypeText(type) }}</span>
                <span class="stat-count">{{ count }}</span>
              </div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-title">用户操作统计</div>
            <div class="stat-list">
              <div
                v-for="(count, user) in analysisData.userStats"
                :key="user"
                class="stat-item"
              >
                <span class="stat-label">{{ user }}</span>
                <span class="stat-count">{{ count }}</span>
              </div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-title">职场操作统计</div>
            <div class="stat-list">
              <div
                v-for="(count, workplace) in analysisData.workplaceStats"
                :key="workplace"
                class="stat-item"
              >
                <span class="stat-label">{{ workplace }}</span>
                <span class="stat-count">{{ count }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAnalysisDialog = false">关闭</el-button>
          <el-button type="primary" @click="exportAnalysis">导出分析报告</el-button>
        </span>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getStockOperationLogs, getWorkplaces } from '@/api/stockManagement'
import { format } from 'date-fns'

export default {
  name: 'OperationLogsDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const dialogVisible = ref(false)
    const loading = ref(false)
    const logs = ref([])
    const workplaces = ref([])
    const totalCount = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(20)
    
    const showDetailDialog = ref(false)
    const selectedLog = ref(null)

    const searchType = ref('name') // 搜索类型：name 或 id

    // 新增的响应式数据
    const users = ref([])
    const totalRecords = ref(0)
    const showAdvancedFilters = ref(false)
    const showAnalysisDialog = ref(false)
    const analysisData = ref({})

    const filters = ref({
      productSearch: '', // 商品搜索（名称或ID）
      userId: null,
      workplaceId: null,
      operationType: null,
      startDate: null,
      endDate: null,
      status: null,
      ipAddress: '',
      sessionId: '',
      stockChange: null
    })

    // 操作类型映射
    const operationTypeMap = {
      add: { text: '增加', color: 'success' },
      subtract: { text: '减少', color: 'warning' },
      set: { text: '设置', color: 'primary' },
      transfer: { text: '转移', color: 'info' },
      sync: { text: '同步', color: 'info' },
      exchange_deduct: { text: '兑换扣减', color: 'danger' },
      exchange_restore: { text: '兑换恢复', color: 'success' },
      migration: { text: '迁移', color: 'info' }
    }

    const getOperationTypeText = (type) => {
      return operationTypeMap[type]?.text || type
    }

    const getOperationTypeColor = (type) => {
      return operationTypeMap[type]?.color || 'info'
    }

    const formatDateTime = (dateTime) => {
      if (!dateTime) return ''
      try {
        return format(new Date(dateTime), 'yyyy-MM-dd HH:mm:ss')
      } catch (error) {
        return dateTime
      }
    }

    const loadWorkplaces = async () => {
      try {
        const response = await getWorkplaces()
        workplaces.value = response.data || []
      } catch (error) {
        console.error('加载职场列表失败:', error)
      }
    }

    const loadLogs = async () => {
      loading.value = true
      try {
        const params = {
          page: currentPage.value,
          limit: pageSize.value,
          workplaceId: filters.value.workplaceId,
          operationType: filters.value.operationType,
          startDate: filters.value.startDate,
          endDate: filters.value.endDate
        }

        // 处理商品搜索
        if (filters.value.productSearch) {
          if (searchType.value === 'id') {
            // 按ID搜索
            params.productId = filters.value.productSearch
          } else {
            // 按名称搜索
            params.productName = filters.value.productSearch
          }
        }

        // 清理空值
        Object.keys(params).forEach(key => {
          if (params[key] === '' || params[key] === null || params[key] === undefined) {
            delete params[key]
          }
        })

        console.log('🔍 搜索参数:', params)

        const response = await getStockOperationLogs(params)
        logs.value = response.data?.logs || []
        totalCount.value = response.data?.pagination?.total || 0

      } catch (error) {
        console.error('加载操作日志失败:', error)
        ElMessage.error('加载操作日志失败')
      } finally {
        loading.value = false
      }
    }

    const resetFilters = () => {
      filters.value = {
        productSearch: '',
        workplaceId: null,
        operationType: null,
        startDate: null,
        endDate: null
      }
      searchType.value = 'name'
      currentPage.value = 1
      loadLogs()
    }

    const handleSearchTypeChange = () => {
      // 搜索类型切换时清空搜索内容
      filters.value.productSearch = ''
    }

    const handleSizeChange = (size) => {
      pageSize.value = size
      currentPage.value = 1
      loadLogs()
    }

    const handleCurrentChange = (page) => {
      currentPage.value = page
      loadLogs()
    }

    const viewDetails = (log) => {
      selectedLog.value = log
      showDetailDialog.value = true
    }

    const handleClose = () => {
      dialogVisible.value = false
      emit('update:modelValue', false)
    }

    // 监听器
    watch(() => props.modelValue, (newVal) => {
      dialogVisible.value = newVal
      if (newVal) {
        loadWorkplaces()
        loadLogs()
      }
    })

    // 切换高级筛选
    const toggleAdvancedFilters = () => {
      showAdvancedFilters.value = !showAdvancedFilters.value
    }

    // 导出日志
    const exportLogs = async () => {
      try {
        const exportData = logs.value.map(log => ({
          '操作时间': formatDateTime(log.timestamp),
          '用户': log.userName || '未知用户',
          '商品名称': log.productName || '未知商品',
          '商品ID': log.productId,
          '职场': log.workplaceName || '全局',
          '操作类型': getOperationTypeText(log.type),
          '操作': log.operation,
          '数量': log.quantity || '',
          '原库存': log.oldStock || '',
          '新库存': log.newStock || '',
          '原因': log.reason || '',
          'IP地址': log.ipAddress || '',
          '会话ID': log.sessionId || ''
        }))

        // 创建CSV内容
        const headers = Object.keys(exportData[0] || {})
        const csvContent = [
          headers.join(','),
          ...exportData.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
        ].join('\n')

        // 下载文件
        const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        const url = URL.createObjectURL(blob)
        link.setAttribute('href', url)
        link.setAttribute('download', `操作日志_${new Date().toISOString().slice(0, 10)}.csv`)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        ElMessage.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        ElMessage.error('导出失败')
      }
    }

    // 显示统计分析
    const showAnalysis = () => {
      // 计算统计数据
      const stats = {
        totalOperations: logs.value.length,
        operationTypes: {},
        userStats: {},
        workplaceStats: {},
        timeStats: {}
      }

      // 统计操作类型
      logs.value.forEach(log => {
        const type = log.type
        stats.operationTypes[type] = (stats.operationTypes[type] || 0) + 1

        // 统计用户操作
        const user = log.userName || '未知用户'
        stats.userStats[user] = (stats.userStats[user] || 0) + 1

        // 统计职场操作
        const workplace = log.workplaceName || '全局'
        stats.workplaceStats[workplace] = (stats.workplaceStats[workplace] || 0) + 1
      })

      analysisData.value = stats
      showAnalysisDialog.value = true
    }

    // 导出分析报告
    const exportAnalysis = () => {
      try {
        const stats = analysisData.value
        const reportContent = [
          '操作统计分析报告',
          `生成时间: ${new Date().toLocaleString()}`,
          '',
          `总操作数: ${stats.totalOperations || 0}`,
          '',
          '操作类型分布:',
          ...Object.entries(stats.operationTypes || {}).map(([type, count]) =>
            `  ${getOperationTypeText(type)}: ${count}`
          ),
          '',
          '用户操作统计:',
          ...Object.entries(stats.userStats || {}).map(([user, count]) =>
            `  ${user}: ${count}`
          ),
          '',
          '职场操作统计:',
          ...Object.entries(stats.workplaceStats || {}).map(([workplace, count]) =>
            `  ${workplace}: ${count}`
          )
        ].join('\n')

        const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8;' })
        const link = document.createElement('a')
        const url = URL.createObjectURL(blob)
        link.setAttribute('href', url)
        link.setAttribute('download', `操作统计分析_${new Date().toISOString().slice(0, 10)}.txt`)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        ElMessage.success('分析报告导出成功')
      } catch (error) {
        console.error('导出分析报告失败:', error)
        ElMessage.error('导出分析报告失败')
      }
    }

    watch(dialogVisible, (newVal) => {
      if (!newVal) {
        emit('update:modelValue', false)
      }
    })

    return {
      dialogVisible,
      loading,
      logs,
      workplaces,
      users,
      totalCount,
      totalRecords,
      currentPage,
      pageSize,
      showDetailDialog,
      selectedLog,
      showAdvancedFilters,
      showAnalysisDialog,
      analysisData,
      filters,
      searchType,
      getOperationTypeText,
      getOperationTypeColor,
      formatDateTime,
      loadLogs,
      resetFilters,
      handleSearchTypeChange,
      handleSizeChange,
      handleCurrentChange,
      viewDetails,
      handleClose,
      toggleAdvancedFilters,
      exportLogs,
      showAnalysis,
      exportAnalysis
    }
  }
}
</script>

<style scoped>
.logs-content {
  max-height: 80vh;
  overflow-y: auto;
}

/* 筛选条件 */
.filters {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.filter-actions {
  margin-left: auto;
  display: flex;
  gap: 8px;
}

/* 表格整体样式 */
.logs-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.logs-table :deep(.el-table) {
  border-radius: 8px;
}

.logs-table :deep(.el-table__header-wrapper) {
  border-radius: 8px 8px 0 0;
}

.logs-table :deep(.el-table__body-wrapper) {
  border-radius: 0 0 8px 8px;
}

.logs-table :deep(.el-table__row:hover > td) {
  background-color: #f8f9fa !important;
}

/* 商品信息单元格 */
.product-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 4px 0;
}

.product-main {
  display: flex;
  align-items: center;
}

.product-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.product-name.unknown {
  color: #f56c6c;
}

.product-name i {
  font-size: 14px;
  color: #409eff;
}

.product-name.unknown i {
  color: #f56c6c;
}

.product-id {
  font-size: 12px;
  color: #909399;
  margin-left: 20px;
}

/* 职场信息单元格 */
.workplace-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.workplace-info {
  text-align: center;
}

.workplace-name {
  font-weight: 600;
  color: #303133;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 4px;
  justify-content: center;
}

.workplace-name i {
  color: #67c23a;
  font-size: 14px;
}

.workplace-code {
  font-size: 11px;
  color: #909399;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 10px;
  margin-top: 2px;
}

.workplace-global {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: #909399;
}

.workplace-global i {
  font-size: 16px;
  color: #c0c4cc;
}

.workplace-global span {
  font-size: 12px;
}

/* 操作类型标签 */
.operation-tag {
  font-weight: 600;
  border-radius: 12px;
  padding: 4px 12px;
}

/* 库存变化单元格 */
.stock-change-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.stock-flow {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.stock-before,
.stock-after {
  font-weight: 600;
  color: #303133;
  background: #f5f7fa;
  padding: 2px 8px;
  border-radius: 6px;
  min-width: 30px;
  text-align: center;
}

.arrow {
  color: #909399;
  font-size: 12px;
}

.stock-delta {
  display: flex;
  justify-content: center;
}

.change-tag {
  font-weight: 600;
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.change-tag i {
  font-size: 12px;
}

/* 操作员单元格 */
.operator-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.operator-info {
  text-align: center;
}

.operator-name {
  font-weight: 600;
  color: #303133;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 4px;
  justify-content: center;
}

.operator-name i {
  color: #409eff;
  font-size: 14px;
}

.operator-id {
  font-size: 11px;
  color: #909399;
  background: #f0f9ff;
  padding: 2px 6px;
  border-radius: 10px;
  margin-top: 2px;
}

.operator-system {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: #909399;
}

.operator-system i {
  font-size: 16px;
  color: #c0c4cc;
}

.operator-system span {
  font-size: 12px;
}

/* 操作原因单元格 */
.reason-cell {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 4px 0;
}

.reason-text {
  color: #303133;
  font-size: 13px;
  display: flex;
  align-items: flex-start;
  gap: 6px;
  line-height: 1.4;
}

.reason-text i {
  color: #909399;
  font-size: 14px;
  margin-top: 1px;
  flex-shrink: 0;
}

.batch-info {
  margin-left: 20px;
}

/* 时间单元格 */
.time-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.time-main {
  font-size: 12px;
  color: #606266;
  display: flex;
  align-items: center;
  gap: 4px;
  text-align: center;
  line-height: 1.3;
}

.time-main i {
  color: #909399;
  font-size: 14px;
  flex-shrink: 0;
}

.text-muted {
  color: #c0c4cc;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-state i {
  font-size: 64px;
  color: #dcdfe6;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

.empty-description {
  font-size: 14px;
  color: #909399;
  text-align: center;
  line-height: 1.4;
}

/* 分页 */
.pagination {
  padding: 24px 20px;
  display: flex;
  justify-content: center;
  background: #fafbfc;
  border-top: 1px solid #e4e7ed;
  border-radius: 0 0 8px 8px;
}

.pagination :deep(.el-pagination) {
  font-weight: 500;
}

.pagination :deep(.el-pagination__total) {
  color: #606266;
  font-weight: 500;
}

.pagination :deep(.el-pager li.is-active) {
  background: #409eff;
  color: white;
  border-radius: 6px;
}

/* 详情对话框 */
.log-detail {
  max-height: 60vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-item label {
  font-size: 14px;
  color: #606266;
  min-width: 80px;
}

.detail-item span {
  color: #303133;
}

.detail-item span.increase {
  color: #67c23a;
}

.detail-item span.decrease {
  color: #f56c6c;
}

.metadata {
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
}

.metadata pre {
  margin: 0;
  font-size: 12px;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.record-count {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

/* 高级筛选样式 */
.advanced-filters {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

/* 统计分析对话框样式 */
.analysis-content {
  padding: 20px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.stat-card {
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
}

.stat-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #409eff;
  text-align: center;
}

.stat-list {
  max-height: 200px;
  overflow-y: auto;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e4e7ed;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  flex: 1;
}

.stat-count {
  font-size: 14px;
  font-weight: 600;
  color: #409eff;
  background: #ecf5ff;
  padding: 2px 8px;
  border-radius: 12px;
  min-width: 30px;
  text-align: center;
}

/* 响应式 */
@media (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-actions {
    margin-left: 0;
    justify-content: flex-end;
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }

  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .toolbar-left {
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* 详情对话框中的商品信息 */
.product-detail {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-name-detail {
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 6px;
}

.product-name-detail i {
  color: #409eff;
  font-size: 14px;
}

.product-id-detail {
  font-size: 12px;
  color: #909399;
  margin-left: 20px;
}

/* 详情对话框中的职场信息 */
.workplace-detail {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.workplace-name-detail {
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 6px;
}

.workplace-name-detail i {
  color: #67c23a;
  font-size: 14px;
}

.workplace-id-detail {
  font-size: 12px;
  color: #909399;
  margin-left: 20px;
}
</style>
