<template>
  <div class="help-doc data-analytics">
    <h2>数据统计和报表</h2>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>数据仪表盘</h3>
        </div>
      </template>
      <div class="card-content">
        <p>数据仪表盘是系统的核心数据展示中心，提供实时的业务数据概览和关键指标监控。通过直观的图表和统计数据，帮助管理员快速了解系统运营状况。</p>
        
        <div class="info-block">
          <h4>主要数据指标</h4>
          <ul>
            <li><strong>实时统计</strong>：今日订单数、销售额、活跃用户数</li>
            <li><strong>趋势分析</strong>：近7天、近30天的销售趋势图表</li>
            <li><strong>商品分析</strong>：热门商品排行、库存状态统计</li>
            <li><strong>用户分析</strong>：用户增长趋势、部门活跃度排行</li>
            <li><strong>支付分析</strong>：光年币与人民币支付比例</li>
          </ul>
        </div>
        
        <h4>数据更新频率</h4>
        <ul>
          <li>实时数据：每5分钟自动刷新</li>
          <li>图表数据：每小时更新一次</li>
          <li>统计报表：每日凌晨自动生成</li>
        </ul>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>销售数据分析</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>销售概览</h4>
        <p>销售数据分析提供了全面的销售业绩监控：</p>
        <ul>
          <li><strong>总体销售</strong>：总订单数、总销售额、平均客单价</li>
          <li><strong>时间维度</strong>：按日、周、月、季度的销售趋势</li>
          <li><strong>商品维度</strong>：各商品的销售排行和贡献度</li>
          <li><strong>用户维度</strong>：用户购买行为分析和复购率</li>
          <li><strong>部门维度</strong>：各部门的消费统计和活跃度</li>
        </ul>
        
        <h4>关键指标说明</h4>
        <el-table :data="salesMetrics" style="width: 100%">
          <el-table-column prop="metric" label="指标名称" width="150" />
          <el-table-column prop="description" label="说明" />
          <el-table-column prop="calculation" label="计算方式" width="200" />
        </el-table>
        
        <h4>数据筛选</h4>
        <p>系统支持多维度的数据筛选：</p>
        <ul>
          <li>时间范围：自定义开始和结束日期</li>
          <li>商品分类：按商品分类筛选数据</li>
          <li>支付方式：区分光年币和人民币订单</li>
          <li>用户部门：按用户所属部门筛选</li>
          <li>订单状态：按订单完成状态筛选</li>
        </ul>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>用户行为分析</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>用户统计</h4>
        <p>用户行为分析帮助了解用户使用习惯和偏好：</p>
        <ul>
          <li><strong>用户增长</strong>：新用户注册趋势和增长率</li>
          <li><strong>活跃度分析</strong>：日活跃、周活跃、月活跃用户数</li>
          <li><strong>购买行为</strong>：用户购买频次和偏好商品分析</li>
          <li><strong>部门分布</strong>：各部门用户数量和活跃度对比</li>
          <li><strong>登录方式</strong>：飞书登录与传统登录的使用比例</li>
        </ul>
        
        <h4>用户分群</h4>
        <p>系统自动将用户分为不同群体：</p>
        <ul>
          <li><strong>新用户</strong>：注册时间少于30天的用户</li>
          <li><strong>活跃用户</strong>：近30天内有购买行为的用户</li>
          <li><strong>沉睡用户</strong>：超过90天未购买的用户</li>
          <li><strong>高价值用户</strong>：累计消费金额排名前20%的用户</li>
        </ul>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>商品数据分析</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>商品表现</h4>
        <p>商品数据分析提供商品运营的关键洞察：</p>
        <ul>
          <li><strong>销量排行</strong>：按销量、销售额排序的商品排行榜</li>
          <li><strong>库存分析</strong>：库存周转率、滞销商品识别</li>
          <li><strong>价格分析</strong>：不同价格区间的商品销售表现</li>
          <li><strong>分类表现</strong>：各商品分类的销售贡献度</li>
          <li><strong>新品表现</strong>：新上架商品的销售情况跟踪</li>
        </ul>
        
        <h4>库存管理</h4>
        <p>基于数据分析的库存管理建议：</p>
        <ul>
          <li>自动识别热销商品，建议增加库存</li>
          <li>标记滞销商品，建议促销或下架</li>
          <li>库存预警，低库存商品自动提醒</li>
          <li>库存周转率分析，优化采购策略</li>
        </ul>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>报表导出和分享</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>报表类型</h4>
        <p>系统支持多种类型的报表导出：</p>
        <ul>
          <li><strong>销售报表</strong>：详细的销售数据和趋势分析</li>
          <li><strong>商品报表</strong>：商品销售表现和库存状况</li>
          <li><strong>用户报表</strong>：用户行为分析和统计数据</li>
          <li><strong>财务报表</strong>：收入统计和支付方式分析</li>
          <li><strong>运营报表</strong>：综合运营数据和KPI指标</li>
        </ul>
        
        <h4>导出格式</h4>
        <ul>
          <li><strong>Excel格式</strong>：适合进一步数据分析和处理</li>
          <li><strong>PDF格式</strong>：适合打印和正式报告</li>
          <li><strong>CSV格式</strong>：适合导入其他系统或工具</li>
        </ul>
        
        <h4>自动报告</h4>
        <p>系统支持自动生成和推送报告：</p>
        <ul>
          <li>每日销售简报：通过飞书群自动推送</li>
          <li>每周运营报告：详细的周度数据分析</li>
          <li>每月业务总结：月度业务表现和趋势分析</li>
          <li>自定义报告：按需配置报告内容和推送频率</li>
        </ul>
        
        <el-alert
          title="提示：所有报表数据都会保留历史记录，方便进行同期对比分析"
          type="info"
          :closable="false"
          show-icon
          style="margin: 15px 0;"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
// 销售指标数据
const salesMetrics = [
  {
    metric: '总订单数',
    description: '统计期间内的订单总数量',
    calculation: '所有状态订单计数'
  },
  {
    metric: '完成订单数',
    description: '已完成状态的订单数量',
    calculation: '状态为"已完成"的订单'
  },
  {
    metric: '总销售额',
    description: '所有完成订单的销售金额总和',
    calculation: '完成订单金额求和'
  },
  {
    metric: '平均客单价',
    description: '每个订单的平均金额',
    calculation: '总销售额 ÷ 完成订单数'
  },
  {
    metric: '用户转化率',
    description: '有购买行为的用户占总用户的比例',
    calculation: '购买用户数 ÷ 总用户数'
  },
  {
    metric: '复购率',
    description: '有多次购买行为的用户比例',
    calculation: '多次购买用户 ÷ 总购买用户'
  }
];
</script>

<style scoped>
.data-analytics h2 {
  margin-bottom: 20px;
  font-weight: bold;
  color: #303133;
}

.help-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.card-content {
  line-height: 1.6;
}

.card-content h4 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-size: 16px;
  color: #303133;
}

.card-content p {
  margin-bottom: 12px;
  color: #606266;
}

.card-content ul {
  padding-left: 20px;
  margin-bottom: 16px;
}

.card-content li {
  margin-bottom: 6px;
  color: #606266;
}

.info-block {
  background-color: #f0f9ff;
  border-left: 4px solid #409EFF;
  padding: 12px 16px;
  margin: 16px 0;
  border-radius: 4px;
}
</style>
