# 商品库存管理系统多职场库存分配功能优化设计

## 1. 项目概述

### 1.1 背景分析
当前系统商品管理只有单一库存字段，无法满足多职场独立库存管理的需求。系统设置中的职场管理包含多个职场（如：北京、武汉、长沙），需要将商品库存按职场进行分配管理。

### 1.2 功能目标
- 实现商品库存按职场分配管理
- 支持各职场库存独立修改
- 总库存自动计算，实时更新
- 兑换时根据选择职场扣减对应库存
- 确保数据一致性和业务完整性

## 2. 数据库表结构设计

### 2.1 新增表：product_workplace_stocks（商品职场库存表）

```sql
-- 商品职场库存表
CREATE TABLE product_workplace_stocks (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    productId INT NOT NULL COMMENT '商品ID',
    workplaceId INT NOT NULL COMMENT '职场ID',
    stock INT NOT NULL DEFAULT 0 COMMENT '职场库存数量',
    reservedStock INT NOT NULL DEFAULT 0 COMMENT '预留库存（待处理订单占用）',
    availableStock INT GENERATED ALWAYS AS (stock - reservedStock) STORED COMMENT '可用库存（虚拟字段）',
    minStockAlert INT DEFAULT 10 COMMENT '最低库存告警阈值',
    maxStockLimit INT DEFAULT NULL COMMENT '最大库存限制',
    lastStockUpdate DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后库存更新时间',
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引设计
    UNIQUE KEY uk_product_workplace (productId, workplaceId) COMMENT '商品-职场唯一索引',
    INDEX idx_product_id (productId) COMMENT '商品ID索引',
    INDEX idx_workplace_id (workplaceId) COMMENT '职场ID索引',
    INDEX idx_stock (stock) COMMENT '库存数量索引',
    INDEX idx_available_stock (availableStock) COMMENT '可用库存索引',
    INDEX idx_last_update (lastStockUpdate) COMMENT '更新时间索引',
    
    -- 外键约束
    CONSTRAINT fk_pws_product FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT fk_pws_workplace FOREIGN KEY (workplaceId) REFERENCES workplaces(id) ON DELETE CASCADE ON UPDATE CASCADE,
    
    -- 数据约束
    CONSTRAINT chk_stock_non_negative CHECK (stock >= 0),
    CONSTRAINT chk_reserved_stock_non_negative CHECK (reservedStock >= 0),
    CONSTRAINT chk_reserved_not_exceed_stock CHECK (reservedStock <= stock),
    CONSTRAINT chk_min_alert_positive CHECK (minStockAlert >= 0),
    CONSTRAINT chk_max_limit_positive CHECK (maxStockLimit IS NULL OR maxStockLimit > 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='商品职场库存表 - 管理每个商品在各职场的库存分配';
```

### 2.2 修改现有表结构

#### 2.2.1 products表字段调整
```sql
-- 为products表添加总库存虚拟字段和相关字段
ALTER TABLE products 
ADD COLUMN totalStock INT GENERATED ALWAYS AS (
    COALESCE((SELECT SUM(stock) FROM product_workplace_stocks WHERE productId = products.id), stock)
) STORED COMMENT '总库存（所有职场库存之和）',
ADD COLUMN stockManagementType ENUM('single', 'workplace') NOT NULL DEFAULT 'single' COMMENT '库存管理类型：single-单一库存，workplace-职场分配',
ADD COLUMN autoStockSync BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否自动同步库存到职场',
ADD COLUMN stockSyncedAt DATETIME NULL COMMENT '库存同步到职场的时间';

-- 添加索引
ALTER TABLE products 
ADD INDEX idx_total_stock (totalStock),
ADD INDEX idx_stock_management_type (stockManagementType),
ADD INDEX idx_stock_synced_at (stockSyncedAt);
```

#### 2.2.2 exchanges表字段调整
```sql
-- 为exchanges表添加职场相关字段
ALTER TABLE exchanges
ADD COLUMN targetWorkplaceId INT NULL COMMENT '目标兑换职场ID',
ADD COLUMN stockWorkplaceId INT NULL COMMENT '实际库存扣减的职场ID',
ADD COLUMN stockDeductedAt DATETIME NULL COMMENT '库存扣减时间',
ADD COLUMN needsStockAllocation BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否需要库存调配',
ADD COLUMN stockAllocationStatus ENUM('pending', 'allocated', 'failed') NULL COMMENT '库存调配状态',
ADD COLUMN stockAllocatedBy INT NULL COMMENT '库存调配操作人ID',
ADD COLUMN stockAllocatedAt DATETIME NULL COMMENT '库存调配时间',
ADD COLUMN allocationNotes TEXT NULL COMMENT '库存调配备注';

-- 添加外键和索引
ALTER TABLE exchanges
ADD CONSTRAINT fk_exchange_target_workplace FOREIGN KEY (targetWorkplaceId) REFERENCES workplaces(id) ON DELETE SET NULL ON UPDATE CASCADE,
ADD CONSTRAINT fk_exchange_stock_workplace FOREIGN KEY (stockWorkplaceId) REFERENCES workplaces(id) ON DELETE SET NULL ON UPDATE CASCADE,
ADD CONSTRAINT fk_exchange_allocator FOREIGN KEY (stockAllocatedBy) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
ADD INDEX idx_target_workplace (targetWorkplaceId),
ADD INDEX idx_stock_workplace (stockWorkplaceId),
ADD INDEX idx_needs_allocation (needsStockAllocation),
ADD INDEX idx_allocation_status (stockAllocationStatus),
ADD INDEX idx_stock_deducted_at (stockDeductedAt);
```

### 2.3 新增表：stock_operation_logs（库存操作日志表）

```sql
-- 库存操作日志表
CREATE TABLE stock_operation_logs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    productId INT NOT NULL COMMENT '商品ID',
    workplaceId INT NULL COMMENT '职场ID（NULL表示总库存操作）',
    operationType ENUM('add', 'subtract', 'set', 'transfer', 'sync', 'exchange_deduct', 'exchange_restore') NOT NULL COMMENT '操作类型',
    beforeStock INT NOT NULL COMMENT '操作前库存',
    afterStock INT NOT NULL COMMENT '操作后库存',
    changeAmount INT NOT NULL COMMENT '变更数量',
    relatedOrderId INT NULL COMMENT '关联订单ID（兑换扣减时）',
    operatorId INT NULL COMMENT '操作员ID',
    operatorName VARCHAR(50) NULL COMMENT '操作员姓名',
    reason VARCHAR(200) NULL COMMENT '操作原因',
    batchId VARCHAR(50) NULL COMMENT '批次ID（批量操作时）',
    ipAddress VARCHAR(45) NULL COMMENT '操作IP地址',
    userAgent TEXT NULL COMMENT '用户代理信息',
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 索引设计
    INDEX idx_product_id (productId),
    INDEX idx_workplace_id (workplaceId),
    INDEX idx_operation_type (operationType),
    INDEX idx_related_order (relatedOrderId),
    INDEX idx_operator (operatorId),
    INDEX idx_batch_id (batchId),
    INDEX idx_created_at (createdAt),
    INDEX idx_product_workplace_time (productId, workplaceId, createdAt),
    
    -- 外键约束
    CONSTRAINT fk_sol_product FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT fk_sol_workplace FOREIGN KEY (workplaceId) REFERENCES workplaces(id) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT fk_sol_order FOREIGN KEY (relatedOrderId) REFERENCES exchanges(id) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT fk_sol_operator FOREIGN KEY (operatorId) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='库存操作日志表 - 记录所有库存变更操作的详细日志';
```

## 3. 后端API接口设计

### 3.1 库存管理核心接口

#### 3.1.1 获取商品职场库存信息
```javascript
// GET /api/products/:id/workplace-stocks
// 响应格式
{
  "success": true,
  "data": {
    "productId": 1,
    "productName": "商品名称",
    "stockManagementType": "workplace",
    "totalStock": 150,
    "workplaceStocks": [
      {
        "workplaceId": 1,
        "workplaceName": "北京",
        "workplaceCode": "BJ",
        "stock": 50,
        "reservedStock": 5,
        "availableStock": 45,
        "minStockAlert": 10,
        "isLowStock": false,
        "lastStockUpdate": "2024-01-15T10:30:00Z"
      },
      {
        "workplaceId": 2,
        "workplaceName": "武汉",
        "workplaceCode": "WH",
        "stock": 60,
        "reservedStock": 3,
        "availableStock": 57,
        "minStockAlert": 10,
        "isLowStock": false,
        "lastStockUpdate": "2024-01-15T09:15:00Z"
      }
    ]
  }
}
```

#### 3.1.2 获取商品列表（包含职场库存）
```javascript
// GET /api/products?includeWorkplaceStocks=true
// 响应格式
{
  "success": true,
  "data": {
    "products": [
      {
        "id": 1,
        "name": "商品名称",
        "categoryId": 1,
        "categoryName": "分类名称",
        "lyPrice": 100,
        "rmbPrice": 10.00,
        "stock": 150,  // 原始库存字段（兼容性）
        "totalStock": 150,  // 总库存（所有职场之和）
        "stockManagementType": "workplace",
        "workplaceStocks": [
          {
            "workplaceId": 1,
            "workplaceName": "北京",
            "stock": 50,
            "availableStock": 45,
            "isLowStock": false
          },
          {
            "workplaceId": 2,
            "workplaceName": "武汉",
            "stock": 60,
            "availableStock": 57,
            "isLowStock": false
          },
          {
            "workplaceId": 3,
            "workplaceName": "长沙",
            "stock": 40,
            "availableStock": 38,
            "isLowStock": false
          }
        ]
      }
    ],
    "pagination": {
      "currentPage": 1,
      "pageSize": 20,
      "totalItems": 100,
      "totalPages": 5
    }
  }
}
```

#### 3.1.3 更新职场库存
```javascript
// PUT /api/products/:id/workplace-stocks
// 请求体
{
  "workplaceStocks": [
    {
      "workplaceId": 1,
      "stock": 55,
      "minStockAlert": 10
    },
    {
      "workplaceId": 2,
      "stock": 65,
      "minStockAlert": 15
    }
  ],
  "reason": "库存调整",
  "batchId": "BATCH_20240115_001"
}
```

#### 3.1.4 库存转移接口
```javascript
// POST /api/products/:id/transfer-stock
// 请求体
{
  "fromWorkplaceId": 1,
  "toWorkplaceId": 2,
  "quantity": 10,
  "reason": "职场间库存调配"
}
```

### 3.2 兑换相关接口调整

#### 3.2.1 创建兑换订单（支持职场选择）
```javascript
// POST /api/exchanges
// 请求体新增字段
{
  "productId": 1,
  "quantity": 2,
  "paymentMethod": "ly",
  "workplaceId": 1,  // 新增：指定兑换的目标职场
  "contactInfo": "联系方式",
  "location": "配送地址",
  "remarks": "备注"
}

// 响应格式
{
  "success": true,
  "message": "兑换申请提交成功",
  "data": {
    "exchangeId": 123,
    "status": "pending", // pending-待处理 或 approved-已确认
    "needsStockAllocation": true, // 是否需要库存调配
    "message": "您选择的职场库存不足，订单已进入待处理状态，管理员将安排库存调配"
  }
}
```

#### 3.2.2 管理员库存调配接口
```javascript
// GET /api/admin/exchanges/pending-allocation
// 获取需要库存调配的兑换订单
{
  "success": true,
  "data": {
    "orders": [
      {
        "id": 123,
        "productId": 1,
        "productName": "商品名称",
        "quantity": 2,
        "targetWorkplaceId": 1,
        "targetWorkplaceName": "北京",
        "currentStock": 0,
        "requiredStock": 2,
        "shortfall": 2,
        "userInfo": {
          "username": "用户名",
          "contactInfo": "联系方式"
        },
        "createdAt": "2024-01-15T10:30:00Z",
        "availableWorkplaces": [
          {
            "workplaceId": 2,
            "workplaceName": "武汉",
            "availableStock": 5
          }
        ]
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalItems": 10
    }
  }
}

// POST /api/admin/stock/transfer
// 职场间库存转移
{
  "productId": 1,
  "fromWorkplaceId": 2,
  "toWorkplaceId": 1,
  "quantity": 2,
  "reason": "满足兑换订单需求",
  "relatedExchangeId": 123
}

// PUT /api/admin/exchanges/:id/allocate-stock
// 完成库存调配，更新订单状态
{
  "stockWorkplaceId": 1,  // 实际扣减库存的职场
  "allocationNotes": "已从武汉调配库存到北京"
}
```

#### 3.2.3 兑换订单状态管理接口
```javascript
// GET /api/admin/exchanges
// 获取兑换订单列表（支持按调配状态筛选）
// 查询参数：
// - needsStockAllocation: true/false
// - stockAllocationStatus: pending/allocated/failed
// - targetWorkplaceId: 职场ID

// PUT /api/admin/exchanges/:id/status
// 更新兑换订单状态
{
  "status": "approved",  // pending, approved, rejected, shipped, completed
  "adminNotes": "库存调配完成，订单已确认"
}
```

## 4. 前端界面改造方案

### 4.1 商品管理页面改造

#### 4.1.1 商品列表表格调整

**动态职场库存列设计**：
- 保留"总库存"列显示所有职场库存的汇总
- 为每个活跃职场动态添加独立的库存列（如：北京库存、武汉库存、长沙库存）
- 职场新增时自动增加对应库存列，职场停用时隐藏对应列
- 新增"库存管理"操作按钮，打开职场库存管理弹窗
- 库存状态图标：绿色（充足）、黄色（偏低）、红色（不足）
- 支持表格横向滚动以适应多职场列显示

**表格列结构示例**：
```
商品名称 | 分类 | 总库存 | 北京库存 | 武汉库存 | 长沙库存 | 光年币价格 | 人民币价格 | 操作
```

#### 4.1.2 动态职场库存列实现

**Vue组件代码示例**：
```vue
<!-- ProductManagement.vue 表格部分 -->
<template>
  <div class="product-management">
    <!-- 商品列表表格 -->
    <el-table
      :data="products"
      border
      stripe
      :scroll-x="true"
      class="workplace-stock-table"
      @selection-change="handleSelectionChange"
    >
      <!-- 基础列 -->
      <el-table-column type="selection" width="55" fixed="left" />
      <el-table-column prop="name" label="商品名称" width="200" fixed="left" />
      <el-table-column prop="categoryName" label="分类" width="120" />

      <!-- 总库存列 -->
      <el-table-column label="总库存" width="100" align="center">
        <template #default="scope">
          <div class="stock-cell">
            <span class="stock-number">{{ scope.row.totalStock || 0 }}</span>
            <el-icon
              :class="getTotalStockStatusClass(scope.row)"
              class="stock-status-icon"
            >
              <CircleFilled />
            </el-icon>
          </div>
        </template>
      </el-table-column>

      <!-- 动态职场库存列 -->
      <el-table-column
        v-for="workplace in activeWorkplaces"
        :key="`workplace-${workplace.id}`"
        :label="`${workplace.name}库存`"
        width="120"
        align="center"
      >
        <template #default="scope">
          <div class="workplace-stock-cell">
            <span class="stock-number">
              {{ getWorkplaceStock(scope.row, workplace.id) }}
            </span>
            <el-icon
              :class="getWorkplaceStockStatusClass(scope.row, workplace.id)"
              class="stock-status-icon"
            >
              <CircleFilled />
            </el-icon>
          </div>
        </template>
      </el-table-column>

      <!-- 价格列 -->
      <el-table-column prop="lyPrice" label="光年币价格" width="120" align="center" />
      <el-table-column label="人民币价格" width="120" align="center">
        <template #default="scope">
          ¥{{ scope.row.rmbPrice }}
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <div class="operation-buttons">
            <el-tooltip content="编辑商品" placement="top">
              <el-button type="primary" size="small" @click="handleEdit(scope.row)">
                <el-icon><Edit /></el-icon>
              </el-button>
            </el-tooltip>

            <el-tooltip content="库存管理" placement="top">
              <el-button type="warning" size="small" @click="handleStockManagement(scope.row)">
                <el-icon><Box /></el-icon>
              </el-button>
            </el-tooltip>

            <el-tooltip content="图片管理" placement="top">
              <el-button type="success" size="small" @click="handleImages(scope.row)">
                <el-icon><Picture /></el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { CircleFilled, Edit, Box, Picture } from '@element-plus/icons-vue';
import { getWorkplaces } from '../../api/system';
import { getProducts, getProductWorkplaceStocks } from '../../api/products';

// 职场数据
const workplaces = ref([]);
const activeWorkplaces = computed(() =>
  workplaces.value.filter(w => w.isActive)
);

// 商品数据
const products = ref([]);

// 获取职场数据
const fetchWorkplaces = async () => {
  try {
    const response = await getWorkplaces();
    workplaces.value = response.data;
  } catch (error) {
    console.error('获取职场数据失败:', error);
  }
};

// 获取商品职场库存
const getWorkplaceStock = (product, workplaceId) => {
  if (!product.workplaceStocks) return 0;
  const workplaceStock = product.workplaceStocks.find(ws => ws.workplaceId === workplaceId);
  return workplaceStock ? workplaceStock.stock : 0;
};

// 获取总库存状态样式
const getTotalStockStatusClass = (product) => {
  const totalStock = product.totalStock || 0;
  if (totalStock === 0) return 'stock-status-empty';
  if (totalStock <= 10) return 'stock-status-low';
  return 'stock-status-normal';
};

// 获取职场库存状态样式
const getWorkplaceStockStatusClass = (product, workplaceId) => {
  const stock = getWorkplaceStock(product, workplaceId);
  if (stock === 0) return 'stock-status-empty';
  if (stock <= 5) return 'stock-status-low';
  return 'stock-status-normal';
};

// 处理库存管理
const handleStockManagement = (product) => {
  // 打开职场库存管理弹窗
  showWorkplaceStockDialog(product);
};

// 初始化数据
onMounted(async () => {
  await fetchWorkplaces();
  await fetchProducts();
});

// 获取商品数据（包含职场库存）
const fetchProducts = async () => {
  try {
    loading.value = true;
    const response = await getProducts({
      page: currentPage.value,
      pageSize: pageSize.value,
      includeWorkplaceStocks: true,
      ...filters.value
    });
    products.value = response.data.products;
    totalItems.value = response.data.pagination.totalItems;
  } catch (error) {
    console.error('获取商品数据失败:', error);
    ElMessage.error('获取商品数据失败');
  } finally {
    loading.value = false;
  }
};

// 监听职场变化，重新获取商品数据
watch(activeWorkplaces, async (newWorkplaces, oldWorkplaces) => {
  if (newWorkplaces.length !== oldWorkplaces.length) {
    await fetchProducts();
  }
}, { deep: true });
</script>

<style scoped>
.workplace-stock-table {
  width: 100%;
  min-width: 1200px; /* 确保表格有足够宽度 */
}

.stock-cell,
.workplace-stock-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.stock-number {
  font-weight: 500;
  font-size: 14px;
}

.stock-status-icon {
  font-size: 8px;
}

.stock-status-normal {
  color: #67c23a; /* 绿色 - 库存充足 */
}

.stock-status-low {
  color: #e6a23c; /* 黄色 - 库存偏低 */
}

.stock-status-empty {
  color: #f56c6c; /* 红色 - 库存不足 */
}

.operation-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .workplace-stock-table {
    overflow-x: auto;
  }
}

/* 表格滚动条样式优化 */
.workplace-stock-table :deep(.el-table__body-wrapper) {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

.workplace-stock-table :deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
}

.workplace-stock-table :deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 4px;
}

.workplace-stock-table :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 4px;
}

.workplace-stock-table :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}
</style>
```

**后端API实现示例**：
```javascript
// server/controllers/productController.js

/**
 * 获取商品列表（支持职场库存）
 */
exports.getProducts = async (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 20,
      includeWorkplaceStocks = false,
      ...filters
    } = req.query;

    const offset = (page - 1) * pageSize;
    const limit = parseInt(pageSize);

    // 构建查询条件
    const whereCondition = buildProductWhereCondition(filters);

    // 基础查询配置
    const queryConfig = {
      where: whereCondition,
      include: [
        {
          model: Category,
          attributes: ['id', 'name']
        }
      ],
      offset,
      limit,
      order: [['createdAt', 'DESC']]
    };

    // 如果需要包含职场库存信息
    if (includeWorkplaceStocks === 'true') {
      queryConfig.include.push({
        model: ProductWorkplaceStock,
        include: [{
          model: Workplace,
          attributes: ['id', 'name', 'code'],
          where: { isActive: true },
          required: false
        }],
        required: false
      });
    }

    const { count, rows: products } = await Product.findAndCountAll(queryConfig);

    // 处理职场库存数据格式
    const formattedProducts = products.map(product => {
      const productData = product.get({ plain: true });

      if (includeWorkplaceStocks === 'true' && productData.ProductWorkplaceStocks) {
        productData.workplaceStocks = productData.ProductWorkplaceStocks.map(pws => ({
          workplaceId: pws.workplaceId,
          workplaceName: pws.Workplace?.name || '未知职场',
          workplaceCode: pws.Workplace?.code || '',
          stock: pws.stock,
          reservedStock: pws.reservedStock || 0,
          availableStock: pws.stock - (pws.reservedStock || 0),
          minStockAlert: pws.minStockAlert || 10,
          isLowStock: (pws.stock - (pws.reservedStock || 0)) <= (pws.minStockAlert || 10),
          lastStockUpdate: pws.lastStockUpdate
        }));

        // 计算总库存
        productData.totalStock = productData.workplaceStocks.reduce((sum, ws) => sum + ws.stock, 0);

        // 清理原始关联数据
        delete productData.ProductWorkplaceStocks;
      }

      return productData;
    });

    res.json({
      success: true,
      data: {
        products: formattedProducts,
        pagination: {
          currentPage: parseInt(page),
          pageSize: limit,
          totalItems: count,
          totalPages: Math.ceil(count / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取商品列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取商品列表失败',
      error: error.message
    });
  }
};

/**
 * 获取商品职场库存详情
 */
exports.getProductWorkplaceStocks = async (req, res) => {
  try {
    const { id } = req.params;

    const product = await Product.findByPk(id, {
      include: [
        {
          model: ProductWorkplaceStock,
          include: [{
            model: Workplace,
            attributes: ['id', 'name', 'code', 'isActive'],
            required: true
          }]
        }
      ]
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: '商品不存在'
      });
    }

    // 获取所有活跃职场
    const activeWorkplaces = await Workplace.findAll({
      where: { isActive: true },
      order: [['name', 'ASC']]
    });

    // 构建职场库存数据
    const workplaceStocks = activeWorkplaces.map(workplace => {
      const existingStock = product.ProductWorkplaceStocks?.find(
        pws => pws.workplaceId === workplace.id
      );

      return {
        workplaceId: workplace.id,
        workplaceName: workplace.name,
        workplaceCode: workplace.code,
        stock: existingStock?.stock || 0,
        reservedStock: existingStock?.reservedStock || 0,
        availableStock: (existingStock?.stock || 0) - (existingStock?.reservedStock || 0),
        minStockAlert: existingStock?.minStockAlert || 10,
        maxStockLimit: existingStock?.maxStockLimit || null,
        isLowStock: ((existingStock?.stock || 0) - (existingStock?.reservedStock || 0)) <= (existingStock?.minStockAlert || 10),
        lastStockUpdate: existingStock?.lastStockUpdate || null
      };
    });

    // 计算汇总数据
    const totalStock = workplaceStocks.reduce((sum, ws) => sum + ws.stock, 0);
    const totalReservedStock = workplaceStocks.reduce((sum, ws) => sum + ws.reservedStock, 0);
    const totalAvailableStock = totalStock - totalReservedStock;

    res.json({
      success: true,
      data: {
        productId: product.id,
        productName: product.name,
        stockManagementType: product.stockManagementType || 'single',
        totalStock,
        totalReservedStock,
        totalAvailableStock,
        workplaceStocks
      }
    });

  } catch (error) {
    console.error('获取商品职场库存失败:', error);
    res.status(500).json({
      success: false,
      message: '获取商品职场库存失败',
      error: error.message
    });
  }
};
```

**表格性能优化和用户体验考虑**：

1. **动态列管理**：
   - 当职场数量较多时（超过5个），考虑提供列显示/隐藏的控制选项
   - 支持列宽度调整和列顺序拖拽
   - 提供"紧凑模式"切换，减少列宽度以显示更多职场

2. **数据加载优化**：
   - 职场库存数据采用懒加载策略，仅在需要时请求
   - 实现表格虚拟滚动，支持大量商品数据展示
   - 添加骨架屏加载效果，提升用户体验

3. **响应式设计**：
   - 移动端自动隐藏部分职场列，提供横向滑动查看
   - 平板设备优化列宽度分配
   - 提供表格全屏模式，便于查看大量数据

4. **交互优化**：
   - 职场库存列支持快速编辑（双击或点击编辑图标）
   - 批量操作支持（选中多个商品进行库存调整）
   - 库存状态提示信息（悬停显示详细库存信息）

#### 4.1.3 职场库存管理弹窗组件
```vue
<!-- WorkplaceStockManager.vue -->
<template>
  <el-dialog
    v-model="visible"
    title="职场库存管理"
    width="800px"
    :before-close="handleClose"
  >
    <div class="workplace-stock-manager">
      <!-- 商品基本信息 -->
      <div class="product-info">
        <h3>{{ product.name }}</h3>
        <div class="stock-summary">
          <el-statistic title="总库存" :value="totalStock" />
          <el-statistic title="可用库存" :value="availableStock" />
          <el-statistic title="预留库存" :value="reservedStock" />
        </div>
      </div>

      <!-- 职场库存表格 -->
      <el-table :data="workplaceStocks" border>
        <el-table-column prop="workplaceName" label="职场" width="120" />
        <el-table-column label="当前库存" width="120">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.stock"
              :min="scope.row.reservedStock"
              :max="9999"
              @change="handleStockChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="reservedStock" label="预留库存" width="100" />
        <el-table-column label="可用库存" width="100">
          <template #default="scope">
            <span :class="getStockStatusClass(scope.row)">
              {{ scope.row.availableStock }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="库存状态" width="100">
          <template #default="scope">
            <el-tag :type="getStockTagType(scope.row)">
              {{ getStockStatusText(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button size="small" @click="showTransferDialog(scope.row)">
              转移
            </el-button>
            <el-button size="small" @click="showStockLog(scope.row)">
              日志
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 操作按钮 -->
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="saveStockChanges" :loading="saving">
          保存更改
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>
```

### 4.2 兑换页面改造

#### 4.2.1 兑换表单新增职场选择
```vue
<!-- ExchangeForm.vue 部分代码 -->
<el-form-item label="选择职场" prop="workplaceId" v-if="showWorkplaceSelection">
  <el-select v-model="form.workplaceId" placeholder="请选择职场">
    <el-option
      v-for="workplace in availableWorkplaces"
      :key="workplace.id"
      :label="`${workplace.name} (库存: ${workplace.availableStock})`"
      :value="workplace.id"
      :disabled="workplace.availableStock < form.quantity"
    />
  </el-select>
  <div class="workplace-stock-info">
    <span v-if="selectedWorkplace">
      当前选择职场可用库存：{{ selectedWorkplace.availableStock }}
    </span>
  </div>
</el-form-item>
```

## 5. 数据迁移方案

### 5.1 迁移策略
1. **渐进式迁移**：支持单一库存和职场库存两种模式并存
2. **数据完整性保证**：迁移过程中确保数据不丢失
3. **回滚机制**：提供完整的回滚方案

### 5.2 迁移脚本
```sql
-- 第一阶段：数据准备和验证
-- 1. 备份现有数据
CREATE TABLE products_backup_20240115 AS SELECT * FROM products;
CREATE TABLE exchanges_backup_20240115 AS SELECT * FROM exchanges;

-- 2. 为现有商品创建职场库存记录
INSERT INTO product_workplace_stocks (productId, workplaceId, stock, createdAt, updatedAt)
SELECT 
    p.id as productId,
    w.id as workplaceId,
    CASE 
        WHEN w.id = (SELECT MIN(id) FROM workplaces WHERE isActive = 1) 
        THEN p.stock  -- 将现有库存分配给第一个活跃职场
        ELSE 0        -- 其他职场库存为0
    END as stock,
    NOW() as createdAt,
    NOW() as updatedAt
FROM products p
CROSS JOIN workplaces w
WHERE w.isActive = 1
AND NOT EXISTS (
    SELECT 1 FROM product_workplace_stocks pws 
    WHERE pws.productId = p.id AND pws.workplaceId = w.id
);

-- 3. 更新商品库存管理类型
UPDATE products 
SET stockManagementType = 'workplace',
    stockSyncedAt = NOW()
WHERE id IN (SELECT DISTINCT productId FROM product_workplace_stocks);
```

### 5.3 数据验证脚本
```sql
-- 验证迁移数据完整性
SELECT 
    p.id,
    p.name,
    p.stock as original_stock,
    p.totalStock as calculated_total,
    COALESCE(SUM(pws.stock), 0) as workplace_stock_sum,
    CASE 
        WHEN p.stock = COALESCE(SUM(pws.stock), 0) THEN 'OK'
        ELSE 'ERROR'
    END as validation_status
FROM products p
LEFT JOIN product_workplace_stocks pws ON p.id = pws.productId
GROUP BY p.id, p.name, p.stock, p.totalStock
HAVING validation_status = 'ERROR';
```

## 6. 业务逻辑流程图

### 6.1 库存更新流程
```mermaid
graph TD
    A[管理员修改职场库存] --> B{验证库存数量}
    B -->|有效| C[开启数据库事务]
    B -->|无效| D[返回错误信息]
    C --> E[更新职场库存表]
    E --> F[记录库存操作日志]
    F --> G[触发库存告警检查]
    G --> H[更新商品总库存]
    H --> I[提交事务]
    I --> J[返回成功结果]
    
    C --> K{事务执行失败}
    K -->|是| L[回滚事务]
    L --> M[返回错误信息]
```

### 6.2 兑换订单处理流程
```mermaid
graph TD
    A[用户提交兑换申请] --> B[验证商品总库存]
    B --> C{总库存是否充足}
    C -->|不足| D[返回库存不足错误]
    C -->|充足| E[创建兑换订单]
    E --> F[检查目标职场库存]
    F --> G{目标职场库存是否充足}
    G -->|充足| H[设置订单状态为已确认]
    G -->|不足| I[设置订单状态为待处理]
    H --> J[扣减目标职场库存]
    I --> K[标记需要库存调配]
    J --> L[记录库存操作日志]
    K --> M[通知管理员调配库存]
    L --> N[返回兑换成功]
    M --> N

    O[管理员手动调配库存] --> P[更新订单状态为已确认]
    P --> Q[扣减调配后的职场库存]
    Q --> R[记录调配日志]
```

## 7. 技术实现细节

### 7.1 Sequelize模型定义

#### 7.1.1 ProductWorkplaceStock模型
```javascript
// server/models/productWorkplaceStock.js
const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ProductWorkplaceStock = sequelize.define('ProductWorkplaceStock', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  productId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  workplaceId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'workplaces',
      key: 'id'
    }
  },
  stock: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  reservedStock: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0,
      isNotGreaterThanStock(value) {
        if (value > this.stock) {
          throw new Error('预留库存不能大于总库存');
        }
      }
    }
  },
  availableStock: {
    type: DataTypes.VIRTUAL,
    get() {
      return this.getDataValue('stock') - this.getDataValue('reservedStock');
    }
  },
  minStockAlert: {
    type: DataTypes.INTEGER,
    defaultValue: 10,
    validate: {
      min: 0
    }
  },
  maxStockLimit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1
    }
  },
  lastStockUpdate: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  isLowStock: {
    type: DataTypes.VIRTUAL,
    get() {
      const available = this.getDataValue('stock') - this.getDataValue('reservedStock');
      return available <= this.getDataValue('minStockAlert');
    }
  }
}, {
  tableName: 'product_workplace_stocks',
  timestamps: true,
  indexes: [
    {
      unique: true,
      fields: ['productId', 'workplaceId'],
      name: 'uk_product_workplace'
    },
    {
      fields: ['productId'],
      name: 'idx_product_id'
    },
    {
      fields: ['workplaceId'],
      name: 'idx_workplace_id'
    },
    {
      fields: ['stock'],
      name: 'idx_stock'
    }
  ],
  hooks: {
    beforeUpdate: async (instance, options) => {
      instance.lastStockUpdate = new Date();
    },
    afterUpdate: async (instance, options) => {
      // 更新后触发库存告警检查
      await checkStockAlert(instance);
    }
  }
});

module.exports = ProductWorkplaceStock;
```

### 7.2 数据校验规则

#### 7.2.1 库存更新校验
```javascript
// server/validators/stockValidator.js
const { body, param } = require('express-validator');
const { ProductWorkplaceStock, Workplace } = require('../models');

const validateWorkplaceStockUpdate = [
  param('id').isInt().withMessage('商品ID必须是整数'),
  body('workplaceStocks').isArray().withMessage('职场库存数据必须是数组'),
  body('workplaceStocks.*.workplaceId').isInt().withMessage('职场ID必须是整数'),
  body('workplaceStocks.*.stock').isInt({ min: 0 }).withMessage('库存数量必须是非负整数'),
  body('workplaceStocks.*.minStockAlert').optional().isInt({ min: 0 }).withMessage('库存告警阈值必须是非负整数'),
  body('reason').optional().isLength({ max: 200 }).withMessage('操作原因不能超过200字符'),
  
  // 自定义验证：检查职场是否存在且活跃
  body('workplaceStocks.*.workplaceId').custom(async (workplaceId) => {
    const workplace = await Workplace.findByPk(workplaceId);
    if (!workplace) {
      throw new Error(`职场ID ${workplaceId} 不存在`);
    }
    if (!workplace.isActive) {
      throw new Error(`职场 ${workplace.name} 已停用，无法更新库存`);
    }
    return true;
  })
];

module.exports = {
  validateWorkplaceStockUpdate
};
```

### 7.3 库存操作服务类
```javascript
// server/services/stockService.js
const { sequelize } = require('../config/database');
const { Product, ProductWorkplaceStock, StockOperationLog, Workplace } = require('../models');

class StockService {
  /**
   * 更新职场库存
   */
  static async updateWorkplaceStocks(productId, workplaceStocks, operatorInfo, reason = null, batchId = null) {
    const transaction = await sequelize.transaction();
    
    try {
      const product = await Product.findByPk(productId, { transaction });
      if (!product) {
        throw new Error('商品不存在');
      }
      
      const updateResults = [];
      
      for (const stockData of workplaceStocks) {
        const { workplaceId, stock, minStockAlert } = stockData;
        
        // 查找或创建职场库存记录
        const [workplaceStock, created] = await ProductWorkplaceStock.findOrCreate({
          where: { productId, workplaceId },
          defaults: { stock, minStockAlert },
          transaction
        });
        
        if (!created) {
          const oldStock = workplaceStock.stock;
          await workplaceStock.update({
            stock,
            minStockAlert: minStockAlert || workplaceStock.minStockAlert,
            lastStockUpdate: new Date()
          }, { transaction });
          
          // 记录库存操作日志
          await StockOperationLog.create({
            productId,
            workplaceId,
            operationType: 'set',
            beforeStock: oldStock,
            afterStock: stock,
            changeAmount: stock - oldStock,
            operatorId: operatorInfo.id,
            operatorName: operatorInfo.username,
            reason,
            batchId,
            ipAddress: operatorInfo.ipAddress,
            userAgent: operatorInfo.userAgent
          }, { transaction });
        }
        
        updateResults.push({
          workplaceId,
          oldStock: created ? 0 : workplaceStock.stock,
          newStock: stock,
          created
        });
      }
      
      // 更新商品的库存同步时间
      await product.update({
        stockSyncedAt: new Date()
      }, { transaction });
      
      await transaction.commit();
      return updateResults;
      
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
  
  /**
   * 职场间库存转移
   */
  static async transferStock(productId, fromWorkplaceId, toWorkplaceId, quantity, operatorInfo, reason) {
    const transaction = await sequelize.transaction();
    
    try {
      // 验证源职场库存
      const fromStock = await ProductWorkplaceStock.findOne({
        where: { productId, workplaceId: fromWorkplaceId },
        transaction
      });
      
      if (!fromStock || fromStock.availableStock < quantity) {
        throw new Error('源职场库存不足');
      }
      
      // 查找或创建目标职场库存
      const [toStock] = await ProductWorkplaceStock.findOrCreate({
        where: { productId, workplaceId: toWorkplaceId },
        defaults: { stock: 0 },
        transaction
      });
      
      // 执行转移
      await fromStock.update({
        stock: fromStock.stock - quantity,
        lastStockUpdate: new Date()
      }, { transaction });
      
      await toStock.update({
        stock: toStock.stock + quantity,
        lastStockUpdate: new Date()
      }, { transaction });
      
      // 记录转移日志
      const batchId = `TRANSFER_${Date.now()}`;
      
      await StockOperationLog.create({
        productId,
        workplaceId: fromWorkplaceId,
        operationType: 'transfer',
        beforeStock: fromStock.stock + quantity,
        afterStock: fromStock.stock,
        changeAmount: -quantity,
        operatorId: operatorInfo.id,
        operatorName: operatorInfo.username,
        reason: `转移到职场${toWorkplaceId}: ${reason}`,
        batchId,
        ipAddress: operatorInfo.ipAddress,
        userAgent: operatorInfo.userAgent
      }, { transaction });
      
      await StockOperationLog.create({
        productId,
        workplaceId: toWorkplaceId,
        operationType: 'transfer',
        beforeStock: toStock.stock - quantity,
        afterStock: toStock.stock,
        changeAmount: quantity,
        operatorId: operatorInfo.id,
        operatorName: operatorInfo.username,
        reason: `从职场${fromWorkplaceId}转入: ${reason}`,
        batchId,
        ipAddress: operatorInfo.ipAddress,
        userAgent: operatorInfo.userAgent
      }, { transaction });
      
      await transaction.commit();
      
      return {
        fromWorkplace: {
          id: fromWorkplaceId,
          oldStock: fromStock.stock + quantity,
          newStock: fromStock.stock
        },
        toWorkplace: {
          id: toWorkplaceId,
          oldStock: toStock.stock - quantity,
          newStock: toStock.stock
        },
        transferQuantity: quantity
      };
      
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
  
  /**
   * 创建兑换订单（支持职场库存检查）
   */
  static async createExchangeOrder(orderData, userInfo) {
    const transaction = await sequelize.transaction();

    try {
      const { productId, quantity, targetWorkplaceId, ...otherData } = orderData;

      // 验证商品总库存
      const product = await Product.findByPk(productId, { transaction });
      if (!product) {
        throw new Error('商品不存在');
      }

      const totalStock = product.stockManagementType === 'workplace'
        ? await this.calculateTotalStock(productId, transaction)
        : product.stock;

      if (totalStock < quantity) {
        throw new Error(`商品总库存不足，当前总库存：${totalStock}，需要：${quantity}`);
      }

      // 检查目标职场库存（如果是职场分配模式）
      let needsStockAllocation = false;
      let stockWorkplaceId = null;

      if (product.stockManagementType === 'workplace' && targetWorkplaceId) {
        const workplaceStock = await ProductWorkplaceStock.findOne({
          where: { productId, workplaceId: targetWorkplaceId },
          transaction
        });

        if (workplaceStock && workplaceStock.availableStock >= quantity) {
          // 目标职场库存充足，直接扣减
          await workplaceStock.update({
            stock: workplaceStock.stock - quantity,
            lastStockUpdate: new Date()
          }, { transaction });

          stockWorkplaceId = targetWorkplaceId;

          // 记录库存扣减日志
          await StockOperationLog.create({
            productId,
            workplaceId: targetWorkplaceId,
            operationType: 'exchange_deduct',
            beforeStock: workplaceStock.stock + quantity,
            afterStock: workplaceStock.stock,
            changeAmount: -quantity,
            operatorId: userInfo.id,
            operatorName: userInfo.username,
            reason: '兑换订单扣减库存',
            ipAddress: userInfo.ipAddress,
            userAgent: userInfo.userAgent
          }, { transaction });
        } else {
          // 目标职场库存不足，需要管理员调配
          needsStockAllocation = true;
        }
      } else if (product.stockManagementType === 'single') {
        // 单一库存模式，直接扣减
        await product.update({
          stock: product.stock - quantity
        }, { transaction });
      }

      // 创建兑换订单
      const exchange = await Exchange.create({
        ...otherData,
        productId,
        quantity,
        targetWorkplaceId,
        stockWorkplaceId,
        needsStockAllocation,
        stockAllocationStatus: needsStockAllocation ? 'pending' : null,
        status: needsStockAllocation ? 'pending' : 'approved',
        userId: userInfo.id
      }, { transaction });

      await transaction.commit();

      return {
        exchange,
        needsStockAllocation,
        message: needsStockAllocation
          ? '您选择的职场库存不足，订单已进入待处理状态，管理员将安排库存调配'
          : '兑换申请提交成功'
      };

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 管理员手动调配库存
   */
  static async allocateStockForExchange(exchangeId, allocationData, adminInfo) {
    const transaction = await sequelize.transaction();

    try {
      const { stockWorkplaceId, allocationNotes } = allocationData;

      // 获取兑换订单
      const exchange = await Exchange.findByPk(exchangeId, { transaction });
      if (!exchange) {
        throw new Error('兑换订单不存在');
      }

      if (!exchange.needsStockAllocation) {
        throw new Error('该订单不需要库存调配');
      }

      // 检查指定职场库存
      const workplaceStock = await ProductWorkplaceStock.findOne({
        where: {
          productId: exchange.productId,
          workplaceId: stockWorkplaceId
        },
        lock: transaction.LOCK.UPDATE,
        transaction
      });

      if (!workplaceStock || workplaceStock.availableStock < exchange.quantity) {
        throw new Error('指定职场库存不足，无法完成调配');
      }

      // 扣减库存
      await workplaceStock.update({
        stock: workplaceStock.stock - exchange.quantity,
        lastStockUpdate: new Date()
      }, { transaction });

      // 更新兑换订单
      await exchange.update({
        stockWorkplaceId,
        stockAllocationStatus: 'allocated',
        stockAllocatedBy: adminInfo.id,
        stockAllocatedAt: new Date(),
        allocationNotes,
        status: 'approved'
      }, { transaction });

      // 记录库存操作日志
      await StockOperationLog.create({
        productId: exchange.productId,
        workplaceId: stockWorkplaceId,
        operationType: 'exchange_deduct',
        beforeStock: workplaceStock.stock + exchange.quantity,
        afterStock: workplaceStock.stock,
        changeAmount: -exchange.quantity,
        relatedOrderId: exchangeId,
        operatorId: adminInfo.id,
        operatorName: adminInfo.username,
        reason: `管理员调配库存完成兑换 (${allocationNotes})`,
        ipAddress: adminInfo.ipAddress,
        userAgent: adminInfo.userAgent
      }, { transaction });

      await transaction.commit();

      return {
        success: true,
        message: '库存调配完成，订单已确认'
      };

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 计算商品总库存
   */
  static async calculateTotalStock(productId, transaction = null) {
    const workplaceStocks = await ProductWorkplaceStock.findAll({
      where: { productId },
      transaction
    });

    return workplaceStocks.reduce((sum, ws) => sum + ws.stock, 0);
  }
}

module.exports = StockService;
```

**兑换控制器实现**：
```javascript
// server/controllers/exchangeController.js

/**
 * 创建兑换订单
 */
exports.createExchange = async (req, res) => {
  try {
    const {
      productId,
      quantity,
      paymentMethod,
      workplaceId: targetWorkplaceId,
      contactInfo,
      location,
      remarks,
      paymentProofUrl
    } = req.body;

    // 验证必填字段
    if (!productId || !quantity || !paymentMethod || !contactInfo || !location) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段'
      });
    }

    // 获取商品信息计算金额
    const product = await Product.findByPk(productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: '商品不存在'
      });
    }

    const unitPrice = paymentMethod === 'ly' ? product.lyPrice : product.rmbPrice;
    const totalAmount = unitPrice * quantity;

    const orderData = {
      productId,
      quantity,
      paymentMethod,
      targetWorkplaceId,
      contactInfo,
      location,
      remarks,
      paymentProofUrl,
      unitPrice,
      totalAmount
    };

    const userInfo = {
      id: req.user.id,
      username: req.user.username,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    };

    // 创建兑换订单
    const result = await StockService.createExchangeOrder(orderData, userInfo);

    res.status(201).json({
      success: true,
      message: result.message,
      data: {
        exchangeId: result.exchange.id,
        status: result.exchange.status,
        needsStockAllocation: result.needsStockAllocation
      }
    });

  } catch (error) {
    console.error('创建兑换订单失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '创建兑换订单失败'
    });
  }
};

/**
 * 获取需要库存调配的订单列表（管理员）
 */
exports.getPendingAllocationOrders = async (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 20,
      targetWorkplaceId,
      startDate,
      endDate
    } = req.query;

    const offset = (page - 1) * pageSize;
    const limit = parseInt(pageSize);

    // 构建查询条件
    const whereCondition = {
      needsStockAllocation: true,
      stockAllocationStatus: ['pending', null]
    };

    if (targetWorkplaceId) {
      whereCondition.targetWorkplaceId = targetWorkplaceId;
    }

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    }

    // 查询订单
    const { count, rows: orders } = await Exchange.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: Product,
          attributes: ['id', 'name']
        },
        {
          model: Workplace,
          as: 'TargetWorkplace',
          attributes: ['id', 'name']
        },
        {
          model: User,
          attributes: ['id', 'username', 'email']
        }
      ],
      offset,
      limit,
      order: [['createdAt', 'DESC']]
    });

    // 处理订单数据，添加库存信息
    const processedOrders = await Promise.all(orders.map(async (order) => {
      const orderData = order.get({ plain: true });

      // 获取目标职场当前库存
      const targetWorkplaceStock = await ProductWorkplaceStock.findOne({
        where: {
          productId: order.productId,
          workplaceId: order.targetWorkplaceId
        }
      });

      const currentStock = targetWorkplaceStock ? targetWorkplaceStock.availableStock : 0;
      const shortfall = Math.max(0, order.quantity - currentStock);

      // 获取其他职场的可用库存
      const availableWorkplaces = await ProductWorkplaceStock.findAll({
        where: {
          productId: order.productId,
          workplaceId: { [Op.ne]: order.targetWorkplaceId },
          stock: { [Op.gt]: 0 }
        },
        include: [{
          model: Workplace,
          attributes: ['id', 'name']
        }]
      });

      return {
        ...orderData,
        productName: orderData.Product.name,
        targetWorkplaceName: orderData.TargetWorkplace.name,
        currentStock,
        requiredStock: order.quantity,
        shortfall,
        userInfo: {
          username: orderData.User.username,
          contactInfo: orderData.contactInfo
        },
        availableWorkplaces: availableWorkplaces.map(ws => ({
          workplaceId: ws.workplaceId,
          workplaceName: ws.Workplace.name,
          availableStock: ws.availableStock
        }))
      };
    }));

    res.json({
      success: true,
      data: {
        orders: processedOrders,
        pagination: {
          currentPage: parseInt(page),
          pageSize: limit,
          totalItems: count,
          totalPages: Math.ceil(count / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取待处理订单失败:', error);
    res.status(500).json({
      success: false,
      message: '获取待处理订单失败',
      error: error.message
    });
  }
};

/**
 * 管理员调配库存
 */
exports.allocateStock = async (req, res) => {
  try {
    const { id } = req.params;
    const { stockWorkplaceId, allocationNotes } = req.body;

    if (!stockWorkplaceId) {
      return res.status(400).json({
        success: false,
        message: '请指定库存扣减的职场'
      });
    }

    const adminInfo = {
      id: req.user.id,
      username: req.user.username,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    };

    const result = await StockService.allocateStockForExchange(
      id,
      { stockWorkplaceId, allocationNotes },
      adminInfo
    );

    res.json({
      success: true,
      message: result.message
    });

  } catch (error) {
    console.error('库存调配失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '库存调配失败'
    });
  }
};

/**
 * 更新兑换订单状态
 */
exports.updateExchangeStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, adminNotes } = req.body;

    const exchange = await Exchange.findByPk(id);
    if (!exchange) {
      return res.status(404).json({
        success: false,
        message: '兑换订单不存在'
      });
    }

    await exchange.update({
      status,
      adminNotes,
      updatedBy: req.user.id,
      updatedAt: new Date()
    });

    res.json({
      success: true,
      message: '订单状态更新成功'
    });

  } catch (error) {
    console.error('更新订单状态失败:', error);
    res.status(500).json({
      success: false,
      message: '更新订单状态失败',
      error: error.message
    });
  }
};
```

## 8. 测试方案

### 8.1 单元测试用例

#### 8.1.1 库存服务测试
```javascript
// tests/services/stockService.test.js
const { StockService } = require('../../server/services/stockService');
const { Product, ProductWorkplaceStock, Workplace } = require('../../server/models');

describe('StockService', () => {
  beforeEach(async () => {
    // 清理测试数据
    await ProductWorkplaceStock.destroy({ where: {}, force: true });
    await Product.destroy({ where: {}, force: true });
    await Workplace.destroy({ where: {}, force: true });
    
    // 创建测试数据
    await Workplace.bulkCreate([
      { id: 1, name: '北京', code: 'BJ', isActive: true },
      { id: 2, name: '武汉', code: 'WH', isActive: true }
    ]);
    
    await Product.create({
      id: 1,
      name: '测试商品',
      categoryId: 1,
      lyPrice: 100,
      rmbPrice: 10.00,
      stock: 100,
      stockManagementType: 'workplace'
    });
  });
  
  describe('updateWorkplaceStocks', () => {
    test('应该成功更新职场库存', async () => {
      const workplaceStocks = [
        { workplaceId: 1, stock: 50, minStockAlert: 10 },
        { workplaceId: 2, stock: 30, minStockAlert: 5 }
      ];
      
      const operatorInfo = {
        id: 1,
        username: 'admin',
        ipAddress: '127.0.0.1',
        userAgent: 'test'
      };
      
      const result = await StockService.updateWorkplaceStocks(
        1, workplaceStocks, operatorInfo, '测试更新'
      );
      
      expect(result).toHaveLength(2);
      expect(result[0].newStock).toBe(50);
      expect(result[1].newStock).toBe(30);
      
      // 验证数据库中的数据
      const stocks = await ProductWorkplaceStock.findAll({
        where: { productId: 1 }
      });
      expect(stocks).toHaveLength(2);
    });
    
    test('应该在库存不足时抛出错误', async () => {
      const workplaceStocks = [
        { workplaceId: 1, stock: -10 }  // 负数库存
      ];
      
      const operatorInfo = {
        id: 1,
        username: 'admin',
        ipAddress: '127.0.0.1',
        userAgent: 'test'
      };
      
      await expect(
        StockService.updateWorkplaceStocks(1, workplaceStocks, operatorInfo)
      ).rejects.toThrow();
    });
  });
  
  describe('transferStock', () => {
    beforeEach(async () => {
      // 创建初始库存
      await ProductWorkplaceStock.bulkCreate([
        { productId: 1, workplaceId: 1, stock: 50 },
        { productId: 1, workplaceId: 2, stock: 30 }
      ]);
    });
    
    test('应该成功转移库存', async () => {
      const operatorInfo = {
        id: 1,
        username: 'admin',
        ipAddress: '127.0.0.1',
        userAgent: 'test'
      };
      
      const result = await StockService.transferStock(
        1, 1, 2, 10, operatorInfo, '库存调配'
      );
      
      expect(result.fromWorkplace.newStock).toBe(40);
      expect(result.toWorkplace.newStock).toBe(40);
      expect(result.transferQuantity).toBe(10);
    });
    
    test('应该在源库存不足时抛出错误', async () => {
      const operatorInfo = {
        id: 1,
        username: 'admin',
        ipAddress: '127.0.0.1',
        userAgent: 'test'
      };
      
      await expect(
        StockService.transferStock(1, 1, 2, 100, operatorInfo, '库存调配')
      ).rejects.toThrow('源职场库存不足');
    });
  });
});
```

### 8.2 集成测试用例

#### 8.2.1 API接口测试
```javascript
// tests/integration/workplaceStock.test.js
const request = require('supertest');
const app = require('../../server/app');
const { generateAuthToken } = require('../../server/utils/auth');

describe('Workplace Stock API', () => {
  let authToken;
  let productId;
  
  beforeAll(async () => {
    // 创建测试用户和认证token
    authToken = generateAuthToken({ id: 1, username: 'admin', role: 'admin' });
    
    // 创建测试商品
    const productResponse = await request(app)
      .post('/api/products')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        name: '测试商品',
        categoryId: 1,
        lyPrice: 100,
        rmbPrice: 10.00,
        stock: 100
      });
    
    productId = productResponse.body.data.id;
  });
  
  describe('GET /api/products/:id/workplace-stocks', () => {
    test('应该返回商品的职场库存信息', async () => {
      const response = await request(app)
        .get(`/api/products/${productId}/workplace-stocks`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.data.productId).toBe(productId);
      expect(response.body.data.workplaceStocks).toBeInstanceOf(Array);
    });
  });
  
  describe('PUT /api/products/:id/workplace-stocks', () => {
    test('应该成功更新职场库存', async () => {
      const updateData = {
        workplaceStocks: [
          { workplaceId: 1, stock: 50, minStockAlert: 10 },
          { workplaceId: 2, stock: 30, minStockAlert: 5 }
        ],
        reason: '测试更新'
      };
      
      const response = await request(app)
        .put(`/api/products/${productId}/workplace-stocks`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.data.updatedCount).toBe(2);
    });
    
    test('应该在无效数据时返回400错误', async () => {
      const invalidData = {
        workplaceStocks: [
          { workplaceId: 1, stock: -10 }  // 负数库存
        ]
      };
      
      await request(app)
        .put(`/api/products/${productId}/workplace-stocks`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);
    });
  });
});
```

### 8.3 性能测试

#### 8.3.1 并发库存更新测试
```javascript
// tests/performance/concurrentStock.test.js
const { StockService } = require('../../server/services/stockService');

describe('Concurrent Stock Operations', () => {
  test('应该正确处理并发库存更新', async () => {
    const productId = 1;
    const workplaceId = 1;
    const initialStock = 100;
    
    // 创建初始库存
    await ProductWorkplaceStock.create({
      productId,
      workplaceId,
      stock: initialStock
    });
    
    // 模拟10个并发的库存扣减操作
    const concurrentOperations = Array.from({ length: 10 }, (_, i) => 
      StockService.deductStockForExchange(
        productId, workplaceId, 5, i + 1, 
        { id: 1, username: 'user', ipAddress: '127.0.0.1', userAgent: 'test' }
      )
    );
    
    const results = await Promise.allSettled(concurrentOperations);
    
    // 验证结果
    const successCount = results.filter(r => r.status === 'fulfilled').length;
    const finalStock = await ProductWorkplaceStock.findOne({
      where: { productId, workplaceId }
    });
    
    expect(finalStock.stock).toBe(initialStock - (successCount * 5));
  });
});
```

## 9. 风险评估与回滚方案

### 9.1 风险评估

#### 9.1.1 技术风险
| 风险项 | 风险等级 | 影响范围 | 缓解措施 |
|--------|----------|----------|----------|
| 数据迁移失败 | 高 | 全系统 | 完整备份、分阶段迁移、回滚脚本 |
| 并发库存冲突 | 中 | 库存管理 | 数据库锁、事务隔离、重试机制 |
| 性能下降 | 中 | 查询响应 | 索引优化、缓存策略、分页查询 |
| 数据一致性问题 | 高 | 库存准确性 | 事务保护、数据校验、定时同步 |

#### 9.1.2 业务风险
| 风险项 | 风险等级 | 影响范围 | 缓解措施 |
|--------|----------|----------|----------|
| 用户操作复杂度增加 | 低 | 用户体验 | 界面优化、操作指引、培训文档 |
| 库存分配不当 | 中 | 业务运营 | 智能分配算法、管理员审核、历史数据分析 |
| 系统切换期间服务中断 | 中 | 业务连续性 | 灰度发布、快速回滚、监控告警 |

### 9.2 回滚方案

#### 9.2.1 数据回滚脚本
```sql
-- 紧急回滚脚本（在迁移出现问题时使用）

-- 1. 停止应用服务
-- systemctl stop feishu-mall-api

-- 2. 恢复原始数据
DROP TABLE IF EXISTS product_workplace_stocks;
DROP TABLE IF EXISTS stock_operation_logs;

-- 恢复products表结构
ALTER TABLE products 
DROP COLUMN IF EXISTS totalStock,
DROP COLUMN IF EXISTS stockManagementType,
DROP COLUMN IF EXISTS autoStockSync,
DROP COLUMN IF EXISTS stockSyncedAt;

-- 恢复exchanges表结构
ALTER TABLE exchanges 
DROP COLUMN IF EXISTS stockWorkplaceId,
DROP COLUMN IF EXISTS stockDeductedAt,
DROP COLUMN IF EXISTS stockRestoreReason;

-- 从备份恢复数据
INSERT INTO products SELECT * FROM products_backup_20240115;
INSERT INTO exchanges SELECT * FROM exchanges_backup_20240115;

-- 3. 重启应用服务
-- systemctl start feishu-mall-api
```

#### 9.2.2 应用代码回滚
```bash
#!/bin/bash
# rollback.sh - 应用代码回滚脚本

echo "开始回滚到职场库存功能之前的版本..."

# 1. 切换到回滚分支
git checkout main
git pull origin main

# 2. 恢复数据库结构
mysql -u root -p feishu_mall < rollback_schema.sql

# 3. 重启应用
pm2 restart feishu-mall-api

# 4. 验证回滚结果
echo "回滚完成，请验证系统功能是否正常"
```

### 9.3 监控和告警

#### 9.3.1 关键指标监控
- 库存数据一致性检查
- API响应时间监控
- 数据库连接池状态
- 错误日志监控
- 用户操作成功率

#### 9.3.2 告警规则
```javascript
// 库存一致性检查告警
const checkStockConsistency = async () => {
  const inconsistentProducts = await sequelize.query(`
    SELECT p.id, p.name, p.stock, p.totalStock,
           COALESCE(SUM(pws.stock), 0) as workplace_sum
    FROM products p
    LEFT JOIN product_workplace_stocks pws ON p.id = pws.productId
    WHERE p.stockManagementType = 'workplace'
    GROUP BY p.id
    HAVING p.totalStock != workplace_sum
  `);
  
  if (inconsistentProducts[0].length > 0) {
    // 发送告警通知
    await sendAlert('库存数据不一致', {
      count: inconsistentProducts[0].length,
      products: inconsistentProducts[0]
    });
  }
};

// 每5分钟执行一次检查
setInterval(checkStockConsistency, 5 * 60 * 1000);
```

## 10. 系统其他模块影响分析与设计

### 10.1 商品管理相关页面调整

#### 10.1.1 添加商品页面改造

**设计策略**：
- 提供两种库存初始化模式：单一库存模式和职场分配模式
- 默认采用单一库存模式，管理员可选择切换到职场分配模式
- 支持库存智能分配算法和手动分配

**界面设计**：
```vue
<!-- AddProductForm.vue 库存设置部分 -->
<template>
  <div class="stock-management-section">
    <!-- 库存管理模式选择 -->
    <el-form-item label="库存管理模式" prop="stockManagementType">
      <el-radio-group v-model="form.stockManagementType" @change="handleStockModeChange">
        <el-radio label="single">单一库存</el-radio>
        <el-radio label="workplace">职场分配</el-radio>
      </el-radio-group>
      <div class="mode-description">
        <span v-if="form.stockManagementType === 'single'">
          所有库存统一管理，不区分职场
        </span>
        <span v-else>
          库存按职场独立管理，支持职场间转移
        </span>
      </div>
    </el-form-item>

    <!-- 单一库存模式 -->
    <el-form-item
      v-if="form.stockManagementType === 'single'"
      label="初始库存"
      prop="stock"
    >
      <el-input-number
        v-model="form.stock"
        :min="0"
        :max="99999"
        placeholder="请输入初始库存数量"
      />
    </el-form-item>

    <!-- 职场分配模式 -->
    <div v-if="form.stockManagementType === 'workplace'" class="workplace-stock-allocation">
      <div class="allocation-header">
        <span class="section-title">职场库存分配</span>
        <div class="allocation-tools">
          <el-input-number
            v-model="totalStockInput"
            :min="0"
            :max="99999"
            placeholder="总库存"
            @change="handleTotalStockChange"
            style="width: 120px; margin-right: 10px;"
          />
          <el-button size="small" @click="autoAllocateStock">智能分配</el-button>
          <el-button size="small" @click="clearAllStock">清空</el-button>
        </div>
      </div>

      <!-- 职场库存分配表格 -->
      <el-table :data="workplaceStockAllocations" border size="small">
        <el-table-column prop="workplaceName" label="职场" width="120" />
        <el-table-column label="分配库存" width="150">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.stock"
              :min="0"
              :max="totalStockInput"
              @change="handleWorkplaceStockChange"
              size="small"
              style="width: 100%;"
            />
          </template>
        </el-table-column>
        <el-table-column label="库存占比" width="100">
          <template #default="scope">
            <span class="stock-percentage">
              {{ getStockPercentage(scope.row.stock) }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column label="告警阈值" width="120">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.minStockAlert"
              :min="0"
              :max="scope.row.stock"
              size="small"
              style="width: 100%;"
            />
          </template>
        </el-table-column>
      </el-table>

      <!-- 分配汇总信息 -->
      <div class="allocation-summary">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="总分配库存" :value="allocatedStock" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="剩余未分配" :value="remainingStock" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="分配职场数" :value="allocatedWorkplaces" />
          </el-col>
          <el-col :span="6">
            <div class="allocation-status">
              <el-tag :type="allocationStatusType">
                {{ allocationStatusText }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { getWorkplaces } from '../../api/system';

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:modelValue']);

const form = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 职场数据
const workplaces = ref([]);
const workplaceStockAllocations = ref([]);
const totalStockInput = ref(0);

// 计算属性
const allocatedStock = computed(() =>
  workplaceStockAllocations.value.reduce((sum, item) => sum + (item.stock || 0), 0)
);

const remainingStock = computed(() =>
  Math.max(0, totalStockInput.value - allocatedStock.value)
);

const allocatedWorkplaces = computed(() =>
  workplaceStockAllocations.value.filter(item => item.stock > 0).length
);

const allocationStatusType = computed(() => {
  if (remainingStock.value === 0 && allocatedStock.value > 0) return 'success';
  if (remainingStock.value > 0) return 'warning';
  return 'info';
});

const allocationStatusText = computed(() => {
  if (remainingStock.value === 0 && allocatedStock.value > 0) return '分配完成';
  if (remainingStock.value > 0) return '未完全分配';
  return '未开始分配';
});

// 获取库存占比
const getStockPercentage = (stock) => {
  if (totalStockInput.value === 0) return 0;
  return Math.round((stock / totalStockInput.value) * 100);
};

// 库存管理模式切换
const handleStockModeChange = (mode) => {
  if (mode === 'workplace') {
    initWorkplaceAllocations();
  } else {
    // 切换到单一库存模式时，将职场库存汇总到总库存
    form.value.stock = allocatedStock.value;
  }
};

// 初始化职场分配
const initWorkplaceAllocations = () => {
  workplaceStockAllocations.value = workplaces.value
    .filter(w => w.isActive)
    .map(workplace => ({
      workplaceId: workplace.id,
      workplaceName: workplace.name,
      workplaceCode: workplace.code,
      stock: 0,
      minStockAlert: 10
    }));
};

// 总库存变更处理
const handleTotalStockChange = (value) => {
  if (value < allocatedStock.value) {
    // 如果新的总库存小于已分配库存，按比例缩减
    const ratio = value / allocatedStock.value;
    workplaceStockAllocations.value.forEach(item => {
      item.stock = Math.floor(item.stock * ratio);
    });
  }
};

// 职场库存变更处理
const handleWorkplaceStockChange = () => {
  // 确保总分配不超过总库存
  if (allocatedStock.value > totalStockInput.value) {
    totalStockInput.value = allocatedStock.value;
  }
};

// 智能分配库存
const autoAllocateStock = () => {
  if (totalStockInput.value === 0) {
    ElMessage.warning('请先设置总库存数量');
    return;
  }

  const activeWorkplaces = workplaceStockAllocations.value.filter(w => w.workplaceId);
  const averageStock = Math.floor(totalStockInput.value / activeWorkplaces.length);
  const remainder = totalStockInput.value % activeWorkplaces.length;

  activeWorkplaces.forEach((item, index) => {
    item.stock = averageStock + (index < remainder ? 1 : 0);
  });

  ElMessage.success('库存智能分配完成');
};

// 清空所有库存
const clearAllStock = () => {
  workplaceStockAllocations.value.forEach(item => {
    item.stock = 0;
  });
  totalStockInput.value = 0;
};

// 获取职场数据
const fetchWorkplaces = async () => {
  try {
    const response = await getWorkplaces();
    workplaces.value = response.data;
    if (form.value.stockManagementType === 'workplace') {
      initWorkplaceAllocations();
    }
  } catch (error) {
    console.error('获取职场数据失败:', error);
  }
};

onMounted(() => {
  fetchWorkplaces();
});

// 监听表单数据变化，同步到父组件
watch(workplaceStockAllocations, (newValue) => {
  if (form.value.stockManagementType === 'workplace') {
    form.value.workplaceStocks = newValue.filter(item => item.stock > 0);
    form.value.stock = allocatedStock.value; // 同步总库存
  }
}, { deep: true });
</script>

<style scoped>
.stock-management-section {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 20px;
  margin: 16px 0;
}

.mode-description {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.workplace-stock-allocation {
  margin-top: 16px;
}

.allocation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-weight: 500;
  font-size: 14px;
}

.allocation-tools {
  display: flex;
  align-items: center;
}

.stock-percentage {
  color: #409eff;
  font-weight: 500;
}

.allocation-summary {
  margin-top: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.allocation-status {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style>
```

**后端API支持**：
```javascript
// server/controllers/productController.js

/**
 * 创建商品（支持职场库存分配）
 */
exports.createProduct = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      name, categoryId, lyPrice, rmbPrice, description,
      stockManagementType = 'single',
      stock = 0,
      workplaceStocks = [],
      isHot, isNew, status
    } = req.body;

    // 验证分类存在
    const category = await Category.findByPk(categoryId);
    if (!category) {
      await transaction.rollback();
      return res.status(404).json({ message: '分类不存在' });
    }

    // 创建商品基础信息
    const product = await Product.create({
      name,
      categoryId,
      lyPrice,
      rmbPrice,
      description,
      stock: stockManagementType === 'single' ? stock : 0,
      stockManagementType,
      isHot: isHot || false,
      isNew: isNew || false,
      status: status || 'active'
    }, { transaction });

    // 如果是职场库存模式，创建职场库存记录
    if (stockManagementType === 'workplace' && workplaceStocks.length > 0) {
      const workplaceStockData = workplaceStocks.map(ws => ({
        productId: product.id,
        workplaceId: ws.workplaceId,
        stock: ws.stock || 0,
        minStockAlert: ws.minStockAlert || 10
      }));

      await ProductWorkplaceStock.bulkCreate(workplaceStockData, { transaction });

      // 更新商品总库存
      const totalStock = workplaceStocks.reduce((sum, ws) => sum + (ws.stock || 0), 0);
      await product.update({
        stock: totalStock,
        stockSyncedAt: new Date()
      }, { transaction });
    }

    // 记录商品创建日志
    await logProductAction({
      actionType: 'create',
      productId: product.id,
      productName: product.name,
      userId: req.user?.id,
      username: req.user?.username,
      oldValues: null,
      newValues: JSON.stringify({
        ...product.get(),
        workplaceStocks: stockManagementType === 'workplace' ? workplaceStocks : null
      })
    }, req, transaction);

    await transaction.commit();

    // 返回完整的商品信息
    const createdProduct = await Product.findByPk(product.id, {
      include: [
        { model: Category, attributes: ['id', 'name'] },
        {
          model: ProductWorkplaceStock,
          include: [{ model: Workplace, attributes: ['id', 'name', 'code'] }]
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: '商品创建成功',
      data: createdProduct
    });

  } catch (error) {
    await transaction.rollback();
    console.error('创建商品失败:', error);
    res.status(500).json({
      success: false,
      message: '创建商品失败',
      error: error.message
    });
  }
};
```

#### 10.1.2 编辑商品页面改造

**设计策略**：
- 在商品基本信息编辑页面集成职场库存管理
- 提供库存管理模式切换功能（单一库存 ↔ 职场分配）
- 支持库存数据的平滑迁移

**界面设计**：
```vue
<!-- EditProductForm.vue -->
<template>
  <div class="edit-product-form">
    <!-- 基本信息编辑 -->
    <el-card class="basic-info-card" header="基本信息">
      <!-- 商品名称、分类、价格等基本字段 -->
      <!-- ... 基本信息表单 ... -->
    </el-card>

    <!-- 库存管理卡片 -->
    <el-card class="stock-management-card" header="库存管理">
      <div class="stock-mode-switch">
        <el-alert
          v-if="stockModeChanged"
          title="库存管理模式变更提醒"
          type="warning"
          :description="stockModeChangeWarning"
          show-icon
          :closable="false"
          style="margin-bottom: 16px;"
        />

        <el-form-item label="库存管理模式">
          <el-radio-group
            v-model="form.stockManagementType"
            @change="handleStockModeChange"
          >
            <el-radio label="single">单一库存</el-radio>
            <el-radio label="workplace">职场分配</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>

      <!-- 单一库存模式 -->
      <div v-if="form.stockManagementType === 'single'" class="single-stock-mode">
        <el-form-item label="当前库存">
          <el-input-number
            v-model="form.stock"
            :min="0"
            :max="99999"
          />
          <span class="stock-info">
            （原职场库存总计：{{ originalWorkplaceStockTotal }}）
          </span>
        </el-form-item>
      </div>

      <!-- 职场分配模式 -->
      <div v-if="form.stockManagementType === 'workplace'" class="workplace-stock-mode">
        <!-- 快速操作工具栏 -->
        <div class="stock-toolbar">
          <el-button size="small" @click="showStockManager">
            <el-icon><Setting /></el-icon>
            高级库存管理
          </el-button>
          <el-button size="small" @click="showStockHistory">
            <el-icon><Clock /></el-icon>
            库存变更历史
          </el-button>
          <el-button size="small" @click="exportStockData">
            <el-icon><Download /></el-icon>
            导出库存数据
          </el-button>
        </div>

        <!-- 职场库存概览 -->
        <div class="stock-overview">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-statistic title="总库存" :value="totalWorkplaceStock" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="可用库存" :value="totalAvailableStock" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="预留库存" :value="totalReservedStock" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="低库存职场" :value="lowStockWorkplaces" />
            </el-col>
          </el-row>
        </div>

        <!-- 职场库存快速编辑表格 -->
        <el-table :data="workplaceStocks" border size="small" class="workplace-stock-table">
          <el-table-column prop="workplaceName" label="职场" width="120" />
          <el-table-column label="当前库存" width="120">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.stock"
                :min="scope.row.reservedStock || 0"
                :max="9999"
                size="small"
                @change="handleQuickStockChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="reservedStock" label="预留库存" width="100" />
          <el-table-column label="可用库存" width="100">
            <template #default="scope">
              <span :class="getStockStatusClass(scope.row)">
                {{ scope.row.availableStock }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="库存状态" width="100">
            <template #default="scope">
              <el-tag :type="getStockTagType(scope.row)" size="small">
                {{ getStockStatusText(scope.row) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="最后更新" width="150">
            <template #default="scope">
              <span class="update-time">
                {{ formatDateTime(scope.row.lastStockUpdate) }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 保存按钮 -->
    <div class="form-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        type="primary"
        @click="handleSave"
        :loading="saving"
        :disabled="!isFormValid"
      >
        保存更改
      </el-button>
    </div>

    <!-- 高级库存管理弹窗 -->
    <WorkplaceStockManager
      v-model:visible="stockManagerVisible"
      :product="form"
      @updated="handleStockUpdated"
    />

    <!-- 库存变更历史弹窗 -->
    <StockHistoryDialog
      v-model:visible="stockHistoryVisible"
      :product-id="form.id"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Setting, Clock, Download } from '@element-plus/icons-vue';
import { updateProduct, getProductWorkplaceStocks } from '../../api/products';
import WorkplaceStockManager from './WorkplaceStockManager.vue';
import StockHistoryDialog from './StockHistoryDialog.vue';

const props = defineProps({
  productId: {
    type: [String, Number],
    required: true
  }
});

const emit = defineEmits(['saved', 'cancelled']);

// 表单数据
const form = ref({
  id: null,
  name: '',
  categoryId: '',
  lyPrice: 0,
  rmbPrice: 0,
  description: '',
  stock: 0,
  stockManagementType: 'single',
  // ... 其他字段
});

// 职场库存数据
const workplaceStocks = ref([]);
const originalStockManagementType = ref('single');
const stockManagerVisible = ref(false);
const stockHistoryVisible = ref(false);
const saving = ref(false);

// 计算属性
const stockModeChanged = computed(() =>
  form.value.stockManagementType !== originalStockManagementType.value
);

const stockModeChangeWarning = computed(() => {
  if (!stockModeChanged.value) return '';

  if (form.value.stockManagementType === 'workplace') {
    return '切换到职场分配模式后，当前库存将分配到默认职场，您可以手动调整各职场库存分配。';
  } else {
    return '切换到单一库存模式后，所有职场库存将合并为总库存，职场库存分配信息将被清除。';
  }
});

const totalWorkplaceStock = computed(() =>
  workplaceStocks.value.reduce((sum, ws) => sum + (ws.stock || 0), 0)
);

const totalAvailableStock = computed(() =>
  workplaceStocks.value.reduce((sum, ws) => sum + (ws.availableStock || 0), 0)
);

const totalReservedStock = computed(() =>
  workplaceStocks.value.reduce((sum, ws) => sum + (ws.reservedStock || 0), 0)
);

const lowStockWorkplaces = computed(() =>
  workplaceStocks.value.filter(ws => ws.isLowStock).length
);

const originalWorkplaceStockTotal = computed(() => {
  if (originalStockManagementType.value === 'workplace') {
    return totalWorkplaceStock.value;
  }
  return form.value.stock;
});

const isFormValid = computed(() => {
  // 基本验证逻辑
  return form.value.name && form.value.categoryId && form.value.lyPrice >= 0 && form.value.rmbPrice >= 0;
});

// 库存管理模式切换
const handleStockModeChange = async (newMode) => {
  if (newMode === originalStockManagementType.value) return;

  const confirmMessage = newMode === 'workplace'
    ? '切换到职场分配模式将创建职场库存记录，是否继续？'
    : '切换到单一库存模式将清除职场库存分配，是否继续？';

  try {
    await ElMessageBox.confirm(confirmMessage, '确认切换库存管理模式', {
      type: 'warning'
    });

    if (newMode === 'workplace') {
      // 切换到职场分配模式
      await initWorkplaceStocks();
    } else {
      // 切换到单一库存模式
      form.value.stock = totalWorkplaceStock.value;
      workplaceStocks.value = [];
    }
  } catch {
    // 用户取消，恢复原始模式
    form.value.stockManagementType = originalStockManagementType.value;
  }
};

// 初始化职场库存
const initWorkplaceStocks = async () => {
  try {
    const response = await getProductWorkplaceStocks(form.value.id);
    workplaceStocks.value = response.data.workplaceStocks;

    // 如果没有职场库存记录，创建默认分配
    if (workplaceStocks.value.length === 0) {
      // 获取活跃职场并创建默认库存分配
      const workplaces = await getActiveWorkplaces();
      workplaceStocks.value = workplaces.map(wp => ({
        workplaceId: wp.id,
        workplaceName: wp.name,
        workplaceCode: wp.code,
        stock: wp.isDefault ? form.value.stock : 0, // 默认职场分配全部库存
        reservedStock: 0,
        availableStock: wp.isDefault ? form.value.stock : 0,
        minStockAlert: 10,
        isLowStock: false,
        lastStockUpdate: null
      }));
    }
  } catch (error) {
    console.error('初始化职场库存失败:', error);
    ElMessage.error('初始化职场库存失败');
  }
};

// 快速库存变更
const handleQuickStockChange = (workplaceStock) => {
  workplaceStock.availableStock = workplaceStock.stock - (workplaceStock.reservedStock || 0);
  workplaceStock.isLowStock = workplaceStock.availableStock <= workplaceStock.minStockAlert;

  // 更新总库存
  form.value.stock = totalWorkplaceStock.value;
};

// 显示高级库存管理
const showStockManager = () => {
  stockManagerVisible.value = true;
};

// 显示库存历史
const showStockHistory = () => {
  stockHistoryVisible.value = true;
};

// 导出库存数据
const exportStockData = () => {
  // 实现库存数据导出逻辑
  ElMessage.info('库存数据导出功能开发中...');
};

// 库存更新回调
const handleStockUpdated = (updatedStocks) => {
  workplaceStocks.value = updatedStocks;
  form.value.stock = totalWorkplaceStock.value;
  ElMessage.success('职场库存更新成功');
};

// 保存商品
const handleSave = async () => {
  try {
    saving.value = true;

    const updateData = {
      ...form.value,
      workplaceStocks: form.value.stockManagementType === 'workplace'
        ? workplaceStocks.value.filter(ws => ws.stock > 0)
        : []
    };

    await updateProduct(form.value.id, updateData);
    ElMessage.success('商品更新成功');
    emit('saved', updateData);
  } catch (error) {
    console.error('保存商品失败:', error);
    ElMessage.error('保存商品失败');
  } finally {
    saving.value = false;
  }
};

// 取消编辑
const handleCancel = () => {
  emit('cancelled');
};

// 获取库存状态样式
const getStockStatusClass = (workplaceStock) => {
  if (workplaceStock.availableStock === 0) return 'stock-empty';
  if (workplaceStock.isLowStock) return 'stock-low';
  return 'stock-normal';
};

const getStockTagType = (workplaceStock) => {
  if (workplaceStock.availableStock === 0) return 'danger';
  if (workplaceStock.isLowStock) return 'warning';
  return 'success';
};

const getStockStatusText = (workplaceStock) => {
  if (workplaceStock.availableStock === 0) return '缺货';
  if (workplaceStock.isLowStock) return '偏低';
  return '正常';
};

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '未更新';
  return new Date(dateTime).toLocaleString('zh-CN');
};

// 初始化数据
onMounted(async () => {
  // 加载商品数据和职场库存信息
  await loadProductData();
});
</script>

<style scoped>
.edit-product-form {
  max-width: 1200px;
  margin: 0 auto;
}

.basic-info-card,
.stock-management-card {
  margin-bottom: 20px;
}

.stock-mode-switch {
  margin-bottom: 20px;
}

.stock-toolbar {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.stock-overview {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f0f9ff;
  border-radius: 6px;
  border: 1px solid #e1f5fe;
}

.workplace-stock-table {
  margin-bottom: 20px;
}

.stock-info {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

.update-time {
  font-size: 12px;
  color: #909399;
}

.stock-empty {
  color: #f56c6c;
  font-weight: 500;
}

.stock-low {
  color: #e6a23c;
  font-weight: 500;
}

.stock-normal {
  color: #67c23a;
  font-weight: 500;
}

.form-actions {
  text-align: right;
  padding: 20px 0;
  border-top: 1px solid #e4e7ed;
}
</style>
```

#### 10.1.3 商品详情页面改造

**设计策略**：
- 提供职场库存分布的可视化展示
- 集成库存变更历史记录查看
- 支持快速库存操作入口

**界面设计**：
```vue
<!-- ProductDetailView.vue -->
<template>
  <div class="product-detail-view">
    <!-- 商品基本信息 -->
    <el-card class="product-info-card">
      <!-- 基本信息展示 -->
    </el-card>

    <!-- 库存信息卡片 -->
    <el-card class="stock-info-card" header="库存信息">
      <div class="stock-summary">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic
              title="总库存"
              :value="product.totalStock || 0"
              :value-style="{ color: getTotalStockColor() }"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="可用库存"
              :value="totalAvailableStock"
              :value-style="{ color: getAvailableStockColor() }"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="预留库存"
              :value="totalReservedStock"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="库存管理模式"
              :value="stockModeText"
            />
          </el-col>
        </el-row>
      </div>

      <!-- 职场库存分布 -->
      <div v-if="product.stockManagementType === 'workplace'" class="workplace-stock-distribution">
        <div class="section-header">
          <h4>职场库存分布</h4>
          <div class="action-buttons">
            <el-button size="small" @click="refreshStockData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button size="small" @click="showStockManager">
              <el-icon><Edit /></el-icon>
              管理库存
            </el-button>
          </div>
        </div>

        <!-- 库存分布图表 -->
        <div class="stock-chart-container">
          <div class="chart-wrapper">
            <div ref="stockChartRef" class="stock-chart"></div>
          </div>
          <div class="chart-legend">
            <div class="legend-item" v-for="workplace in workplaceStocks" :key="workplace.workplaceId">
              <span class="legend-color" :style="{ backgroundColor: getWorkplaceColor(workplace.workplaceId) }"></span>
              <span class="legend-text">{{ workplace.workplaceName }}</span>
              <span class="legend-value">{{ workplace.stock }}</span>
            </div>
          </div>
        </div>

        <!-- 职场库存详细表格 -->
        <el-table :data="workplaceStocks" border size="small" class="workplace-detail-table">
          <el-table-column prop="workplaceName" label="职场" width="120" />
          <el-table-column label="库存数量" width="100" align="center">
            <template #default="scope">
              <span :class="getStockStatusClass(scope.row)">
                {{ scope.row.stock }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="可用库存" width="100" align="center">
            <template #default="scope">
              <span :class="getStockStatusClass(scope.row)">
                {{ scope.row.availableStock }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="预留库存" width="100" align="center">
            <template #default="scope">
              {{ scope.row.reservedStock || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="库存状态" width="100" align="center">
            <template #default="scope">
              <el-tag :type="getStockTagType(scope.row)" size="small">
                {{ getStockStatusText(scope.row) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="告警阈值" width="100" align="center">
            <template #default="scope">
              {{ scope.row.minStockAlert || 10 }}
            </template>
          </el-table-column>
          <el-table-column label="最后更新" width="150">
            <template #default="scope">
              {{ formatDateTime(scope.row.lastStockUpdate) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template #default="scope">
              <el-button size="small" type="text" @click="quickAdjustStock(scope.row)">
                调整
              </el-button>
              <el-button size="small" type="text" @click="viewStockLog(scope.row)">
                日志
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 单一库存模式显示 -->
      <div v-else class="single-stock-display">
        <el-alert
          title="当前商品采用单一库存管理模式"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>库存数量：<strong>{{ product.stock || 0 }}</strong></p>
            <p>如需按职场分配库存，请切换到职场分配模式。</p>
            <el-button size="small" type="primary" @click="switchToWorkplaceMode">
              切换到职场分配模式
            </el-button>
          </template>
        </el-alert>
      </div>
    </el-card>

    <!-- 库存变更历史 -->
    <el-card class="stock-history-card" header="库存变更历史">
      <div class="history-toolbar">
        <el-date-picker
          v-model="historyDateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="loadStockHistory"
          size="small"
        />
        <el-select v-model="historyWorkplaceFilter" placeholder="选择职场" clearable size="small">
          <el-option label="全部职场" value="" />
          <el-option
            v-for="workplace in workplaceStocks"
            :key="workplace.workplaceId"
            :label="workplace.workplaceName"
            :value="workplace.workplaceId"
          />
        </el-select>
        <el-select v-model="historyOperationFilter" placeholder="操作类型" clearable size="small">
          <el-option label="全部操作" value="" />
          <el-option label="库存增加" value="add" />
          <el-option label="库存减少" value="subtract" />
          <el-option label="库存设置" value="set" />
          <el-option label="库存转移" value="transfer" />
          <el-option label="兑换扣减" value="exchange_deduct" />
          <el-option label="兑换恢复" value="exchange_restore" />
        </el-select>
        <el-button size="small" @click="loadStockHistory">
          <el-icon><Search /></el-icon>
          查询
        </el-button>
      </div>

      <el-table :data="stockHistory" border size="small" v-loading="historyLoading">
        <el-table-column label="操作时间" width="150">
          <template #default="scope">
            {{ formatDateTime(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="workplaceName" label="职场" width="100" />
        <el-table-column label="操作类型" width="100">
          <template #default="scope">
            <el-tag :type="getOperationTagType(scope.row.operationType)" size="small">
              {{ getOperationText(scope.row.operationType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="库存变化" width="150" align="center">
          <template #default="scope">
            <span class="stock-change">
              {{ scope.row.beforeStock }} → {{ scope.row.afterStock }}
              <span :class="getChangeAmountClass(scope.row.changeAmount)">
                ({{ scope.row.changeAmount > 0 ? '+' : '' }}{{ scope.row.changeAmount }})
              </span>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="operatorName" label="操作人" width="100" />
        <el-table-column prop="reason" label="操作原因" min-width="200" />
        <el-table-column label="关联订单" width="120">
          <template #default="scope">
            <el-button
              v-if="scope.row.relatedOrderId"
              size="small"
              type="text"
              @click="viewRelatedOrder(scope.row.relatedOrderId)"
            >
              #{{ scope.row.relatedOrderId }}
            </el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 历史记录分页 -->
      <el-pagination
        v-model:current-page="historyCurrentPage"
        v-model:page-size="historyPageSize"
        :total="historyTotal"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadStockHistory"
        @current-change="loadStockHistory"
        style="margin-top: 16px; text-align: right;"
      />
    </el-card>

    <!-- 库存管理弹窗 -->
    <WorkplaceStockManager
      v-model:visible="stockManagerVisible"
      :product="product"
      @updated="handleStockUpdated"
    />

    <!-- 快速库存调整弹窗 -->
    <QuickStockAdjustDialog
      v-model:visible="quickAdjustVisible"
      :workplace-stock="selectedWorkplaceStock"
      @updated="handleStockUpdated"
    />

    <!-- 库存操作日志弹窗 -->
    <StockLogDialog
      v-model:visible="stockLogVisible"
      :product-id="product.id"
      :workplace-id="selectedWorkplaceStock?.workplaceId"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Refresh, Edit, Search } from '@element-plus/icons-vue';
import * as echarts from 'echarts';
import {
  getProductWorkplaceStocks,
  getStockOperationHistory,
  updateProductStockMode
} from '../../api/products';
import WorkplaceStockManager from './WorkplaceStockManager.vue';
import QuickStockAdjustDialog from './QuickStockAdjustDialog.vue';
import StockLogDialog from './StockLogDialog.vue';

const props = defineProps({
  product: {
    type: Object,
    required: true
  }
});

// 响应式数据
const workplaceStocks = ref([]);
const stockHistory = ref([]);
const stockManagerVisible = ref(false);
const quickAdjustVisible = ref(false);
const stockLogVisible = ref(false);
const selectedWorkplaceStock = ref(null);
const stockChartRef = ref(null);
const historyLoading = ref(false);

// 历史记录筛选
const historyDateRange = ref([]);
const historyWorkplaceFilter = ref('');
const historyOperationFilter = ref('');
const historyCurrentPage = ref(1);
const historyPageSize = ref(20);
const historyTotal = ref(0);

// 计算属性
const totalAvailableStock = computed(() =>
  workplaceStocks.value.reduce((sum, ws) => sum + (ws.availableStock || 0), 0)
);

const totalReservedStock = computed(() =>
  workplaceStocks.value.reduce((sum, ws) => sum + (ws.reservedStock || 0), 0)
);

const stockModeText = computed(() =>
  props.product.stockManagementType === 'workplace' ? '职场分配' : '单一库存'
);

// 颜色获取函数
const getTotalStockColor = () => {
  const total = props.product.totalStock || 0;
  if (total === 0) return '#f56c6c';
  if (total <= 20) return '#e6a23c';
  return '#67c23a';
};

const getAvailableStockColor = () => {
  const available = totalAvailableStock.value;
  if (available === 0) return '#f56c6c';
  if (available <= 10) return '#e6a23c';
  return '#67c23a';
};

const getWorkplaceColor = (workplaceId) => {
  const colors = ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#909399', '#c71585'];
  return colors[workplaceId % colors.length];
};

// 库存状态样式
const getStockStatusClass = (workplaceStock) => {
  if (workplaceStock.availableStock === 0) return 'stock-empty';
  if (workplaceStock.isLowStock) return 'stock-low';
  return 'stock-normal';
};

const getStockTagType = (workplaceStock) => {
  if (workplaceStock.availableStock === 0) return 'danger';
  if (workplaceStock.isLowStock) return 'warning';
  return 'success';
};

const getStockStatusText = (workplaceStock) => {
  if (workplaceStock.availableStock === 0) return '缺货';
  if (workplaceStock.isLowStock) return '偏低';
  return '正常';
};

// 操作类型样式
const getOperationTagType = (operationType) => {
  const typeMap = {
    'add': 'success',
    'subtract': 'warning',
    'set': 'info',
    'transfer': 'primary',
    'exchange_deduct': 'danger',
    'exchange_restore': 'success'
  };
  return typeMap[operationType] || 'info';
};

const getOperationText = (operationType) => {
  const textMap = {
    'add': '增加',
    'subtract': '减少',
    'set': '设置',
    'transfer': '转移',
    'exchange_deduct': '兑换扣减',
    'exchange_restore': '兑换恢复'
  };
  return textMap[operationType] || operationType;
};

const getChangeAmountClass = (changeAmount) => {
  if (changeAmount > 0) return 'change-positive';
  if (changeAmount < 0) return 'change-negative';
  return 'change-neutral';
};

// 初始化库存图表
const initStockChart = () => {
  if (!stockChartRef.value || workplaceStocks.value.length === 0) return;

  const chart = echarts.init(stockChartRef.value);

  const chartData = workplaceStocks.value.map(ws => ({
    name: ws.workplaceName,
    value: ws.stock,
    itemStyle: {
      color: getWorkplaceColor(ws.workplaceId)
    }
  }));

  const option = {
    title: {
      text: '职场库存分布',
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '库存分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '60%'],
        data: chartData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: true,
          formatter: '{b}\n{c}'
        }
      }
    ]
  };

  chart.setOption(option);

  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize();
  });
};

// 刷新库存数据
const refreshStockData = async () => {
  try {
    const response = await getProductWorkplaceStocks(props.product.id);
    workplaceStocks.value = response.data.workplaceStocks;

    // 重新初始化图表
    await nextTick();
    initStockChart();

    ElMessage.success('库存数据已刷新');
  } catch (error) {
    console.error('刷新库存数据失败:', error);
    ElMessage.error('刷新库存数据失败');
  }
};

// 显示库存管理器
const showStockManager = () => {
  stockManagerVisible.value = true;
};

// 快速调整库存
const quickAdjustStock = (workplaceStock) => {
  selectedWorkplaceStock.value = workplaceStock;
  quickAdjustVisible.value = true;
};

// 查看库存日志
const viewStockLog = (workplaceStock) => {
  selectedWorkplaceStock.value = workplaceStock;
  stockLogVisible.value = true;
};

// 切换到职场分配模式
const switchToWorkplaceMode = async () => {
  try {
    await ElMessageBox.confirm(
      '切换到职场分配模式后，当前库存将分配到默认职场，您可以后续调整分配。是否继续？',
      '确认切换库存管理模式',
      { type: 'warning' }
    );

    await updateProductStockMode(props.product.id, 'workplace');

    // 重新加载数据
    await refreshStockData();

    ElMessage.success('已切换到职场分配模式');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换库存模式失败:', error);
      ElMessage.error('切换库存模式失败');
    }
  }
};

// 加载库存历史
const loadStockHistory = async () => {
  try {
    historyLoading.value = true;

    const params = {
      productId: props.product.id,
      page: historyCurrentPage.value,
      pageSize: historyPageSize.value,
      workplaceId: historyWorkplaceFilter.value || undefined,
      operationType: historyOperationFilter.value || undefined,
      startDate: historyDateRange.value?.[0]?.toISOString().split('T')[0],
      endDate: historyDateRange.value?.[1]?.toISOString().split('T')[0]
    };

    const response = await getStockOperationHistory(params);
    stockHistory.value = response.data.logs;
    historyTotal.value = response.data.pagination.totalItems;
  } catch (error) {
    console.error('加载库存历史失败:', error);
    ElMessage.error('加载库存历史失败');
  } finally {
    historyLoading.value = false;
  }
};

// 查看关联订单
const viewRelatedOrder = (orderId) => {
  // 跳转到订单详情页面
  window.open(`/admin/exchanges/${orderId}`, '_blank');
};

// 库存更新回调
const handleStockUpdated = () => {
  refreshStockData();
  loadStockHistory();
};

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

// 初始化
onMounted(async () => {
  if (props.product.stockManagementType === 'workplace') {
    await refreshStockData();
    await nextTick();
    initStockChart();
  }
  await loadStockHistory();
});
</script>

<style scoped>
.product-detail-view {
  max-width: 1200px;
  margin: 0 auto;
}

.product-info-card,
.stock-info-card,
.stock-history-card {
  margin-bottom: 20px;
}

.stock-summary {
  margin-bottom: 24px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.workplace-stock-distribution {
  margin-top: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.stock-chart-container {
  display: flex;
  margin-bottom: 24px;
}

.chart-wrapper {
  flex: 1;
  height: 300px;
}

.stock-chart {
  width: 100%;
  height: 100%;
}

.chart-legend {
  width: 200px;
  padding-left: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 8px;
}

.legend-text {
  flex: 1;
}

.legend-value {
  font-weight: 500;
  color: #409eff;
}

.workplace-detail-table {
  margin-bottom: 20px;
}

.single-stock-display {
  padding: 20px;
}

.history-toolbar {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  align-items: center;
}

.stock-change {
  font-family: 'Monaco', 'Menlo', monospace;
}

.change-positive {
  color: #67c23a;
  font-weight: 500;
}

.change-negative {
  color: #f56c6c;
  font-weight: 500;
}

.change-neutral {
  color: #909399;
}

.stock-empty {
  color: #f56c6c;
  font-weight: 500;
}

.stock-low {
  color: #e6a23c;
  font-weight: 500;
}

.stock-normal {
  color: #67c23a;
  font-weight: 500;
}
</style>
```

#### 10.1.4 商品导入功能改造

**设计策略**：
- 支持导入时指定库存管理模式
- 提供职场库存分配的批量导入模板
- 智能库存分配算法

**导入模板设计**：
```csv
商品名称,分类ID,光年币价格,人民币价格,商品描述,库存管理模式,总库存,北京库存,武汉库存,长沙库存,库存告警阈值
测试商品1,1,100,10.00,商品描述,workplace,150,50,60,40,10
测试商品2,2,200,20.00,商品描述,single,100,,,,,15
```

**后端导入逻辑**：
```javascript
// server/controllers/productController.js

/**
 * 批量导入商品（支持职场库存）
 */
exports.importProducts = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    if (!req.file) {
      return res.status(400).json({ message: '请上传导入文件' });
    }

    // 解析Excel/CSV文件
    const workbook = XLSX.readFile(req.file.path);
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const jsonData = XLSX.utils.sheet_to_json(worksheet);

    if (jsonData.length === 0) {
      return res.status(400).json({ message: '导入文件为空' });
    }

    // 获取所有活跃职场
    const activeWorkplaces = await Workplace.findAll({
      where: { isActive: true },
      order: [['name', 'ASC']]
    });

    const workplaceMap = new Map();
    activeWorkplaces.forEach(wp => {
      workplaceMap.set(wp.name, wp.id);
      workplaceMap.set(wp.code, wp.id);
    });

    const importResults = {
      successCount: 0,
      failCount: 0,
      errors: [],
      warnings: []
    };

    // 批量处理商品数据
    for (let i = 0; i < jsonData.length; i++) {
      const row = jsonData[i];
      const rowIndex = i + 2; // Excel行号（从2开始）

      try {
        // 验证必填字段
        if (!row['商品名称'] || !row['分类ID'] || !row['光年币价格'] || !row['人民币价格']) {
          throw new Error('缺少必填字段');
        }

        // 验证分类存在
        const category = await Category.findByPk(row['分类ID']);
        if (!category) {
          throw new Error(`分类ID ${row['分类ID']} 不存在`);
        }

        // 确定库存管理模式
        const stockManagementType = row['库存管理模式'] === 'workplace' ? 'workplace' : 'single';

        // 创建商品基础信息
        const productData = {
          name: row['商品名称'],
          categoryId: parseInt(row['分类ID']),
          lyPrice: parseInt(row['光年币价格']),
          rmbPrice: parseFloat(row['人民币价格']),
          description: row['商品描述'] || '',
          stockManagementType,
          stock: 0, // 初始为0，后续根据模式设置
          status: 'active'
        };

        const product = await Product.create(productData, { transaction });

        // 处理库存分配
        if (stockManagementType === 'workplace') {
          const workplaceStocks = [];
          let totalStock = 0;

          // 遍历职场列，查找对应的库存数据
          for (const workplace of activeWorkplaces) {
            const stockColumnName = `${workplace.name}库存`;
            const stockValue = parseInt(row[stockColumnName]) || 0;

            if (stockValue > 0) {
              workplaceStocks.push({
                productId: product.id,
                workplaceId: workplace.id,
                stock: stockValue,
                minStockAlert: parseInt(row['库存告警阈值']) || 10
              });
              totalStock += stockValue;
            }
          }

          // 如果没有指定职场库存，但有总库存，则分配到第一个职场
          if (workplaceStocks.length === 0 && row['总库存']) {
            const totalStockValue = parseInt(row['总库存']) || 0;
            if (totalStockValue > 0 && activeWorkplaces.length > 0) {
              workplaceStocks.push({
                productId: product.id,
                workplaceId: activeWorkplaces[0].id,
                stock: totalStockValue,
                minStockAlert: parseInt(row['库存告警阈值']) || 10
              });
              totalStock = totalStockValue;
            }
          }

          // 创建职场库存记录
          if (workplaceStocks.length > 0) {
            await ProductWorkplaceStock.bulkCreate(workplaceStocks, { transaction });
          }

          // 更新商品总库存
          await product.update({
            stock: totalStock,
            stockSyncedAt: new Date()
          }, { transaction });

        } else {
          // 单一库存模式
          const stockValue = parseInt(row['总库存']) || 0;
          await product.update({ stock: stockValue }, { transaction });
        }

        importResults.successCount++;

      } catch (error) {
        importResults.failCount++;
        importResults.errors.push({
          row: rowIndex,
          productName: row['商品名称'] || '未知',
          error: error.message
        });
        console.error(`导入第${rowIndex}行失败:`, error);
      }
    }

    // 记录导入日志
    await createLog({
      action: 'product_bulk_import',
      entityType: 'product',
      entityId: null,
      oldValue: null,
      newValue: JSON.stringify({
        totalRows: jsonData.length,
        successCount: importResults.successCount,
        failCount: importResults.failCount
      }),
      userId: req.user.id,
      username: req.user.username,
      description: `批量导入商品: 成功${importResults.successCount}个，失败${importResults.failCount}个`
    }, req, transaction);

    await transaction.commit();

    // 清理上传的临时文件
    fs.unlinkSync(req.file.path);

    res.json({
      success: true,
      message: '商品导入完成',
      data: importResults
    });

  } catch (error) {
    await transaction.rollback();
    console.error('批量导入商品失败:', error);

    // 清理上传的临时文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      success: false,
      message: '批量导入商品失败',
      error: error.message
    });
  }
};

/**
 * 下载导入模板
 */
exports.downloadImportTemplate = async (req, res) => {
  try {
    // 获取活跃职场列表
    const activeWorkplaces = await Workplace.findAll({
      where: { isActive: true },
      order: [['name', 'ASC']]
    });

    // 构建模板表头
    const headers = [
      '商品名称', '分类ID', '光年币价格', '人民币价格', '商品描述',
      '库存管理模式', '总库存'
    ];

    // 添加职场库存列
    activeWorkplaces.forEach(workplace => {
      headers.push(`${workplace.name}库存`);
    });

    headers.push('库存告警阈值');

    // 创建示例数据
    const exampleData = [
      {
        '商品名称': '示例商品1',
        '分类ID': 1,
        '光年币价格': 100,
        '人民币价格': 10.00,
        '商品描述': '这是一个示例商品',
        '库存管理模式': 'workplace',
        '总库存': 150,
        '库存告警阈值': 10
      },
      {
        '商品名称': '示例商品2',
        '分类ID': 2,
        '光年币价格': 200,
        '人民币价格': 20.00,
        '商品描述': '这是另一个示例商品',
        '库存管理模式': 'single',
        '总库存': 100,
        '库存告警阈值': 15
      }
    ];

    // 为示例数据添加职场库存
    exampleData[0][`${activeWorkplaces[0]?.name || '北京'}库存`] = 50;
    exampleData[0][`${activeWorkplaces[1]?.name || '武汉'}库存`] = 60;
    exampleData[0][`${activeWorkplaces[2]?.name || '长沙'}库存`] = 40;

    // 创建工作簿
    const workbook = XLSX.utils.book_new();

    // 创建数据工作表
    const worksheet = XLSX.utils.json_to_sheet(exampleData);
    XLSX.utils.book_append_sheet(workbook, worksheet, '商品数据');

    // 创建说明工作表
    const instructionData = [
      { '字段名': '商品名称', '说明': '商品的名称，必填', '示例': '苹果手机' },
      { '字段名': '分类ID', '说明': '商品分类的ID，必填', '示例': '1' },
      { '字段名': '光年币价格', '说明': '光年币价格，必填，整数', '示例': '100' },
      { '字段名': '人民币价格', '说明': '人民币价格，必填，可带小数', '示例': '10.50' },
      { '字段名': '商品描述', '说明': '商品的详细描述，可选', '示例': '高品质商品' },
      { '字段名': '库存管理模式', '说明': 'single=单一库存，workplace=职场分配', '示例': 'workplace' },
      { '字段名': '总库存', '说明': '商品总库存数量', '示例': '150' },
      { '字段名': '职场库存', '说明': '各职场的库存分配，仅职场模式有效', '示例': '50' },
      { '字段名': '库存告警阈值', '说明': '库存低于此值时告警，可选', '示例': '10' }
    ];

    const instructionSheet = XLSX.utils.json_to_sheet(instructionData);
    XLSX.utils.book_append_sheet(workbook, instructionSheet, '导入说明');

    // 生成文件
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=product_import_template.xlsx');
    res.send(buffer);

  } catch (error) {
    console.error('下载导入模板失败:', error);
    res.status(500).json({
      success: false,
      message: '下载导入模板失败',
      error: error.message
    });
  }
};
```

#### 10.1.5 商品导出功能改造

**设计策略**：
- 导出数据包含各职场库存明细
- 支持按职场筛选导出
- 提供多种导出格式（Excel、CSV）

**后端导出逻辑**：
```javascript
/**
 * 导出商品数据（包含职场库存）
 */
exports.exportProducts = async (req, res) => {
  try {
    const {
      format = 'xlsx',
      includeWorkplaceStocks = true,
      workplaceIds = [],
      ...filters
    } = req.query;

    // 构建查询条件
    const whereCondition = buildProductWhereCondition(filters);

    // 查询商品数据
    const products = await Product.findAll({
      where: whereCondition,
      include: [
        {
          model: Category,
          attributes: ['id', 'name']
        },
        {
          model: ProductWorkplaceStock,
          include: [{
            model: Workplace,
            attributes: ['id', 'name', 'code'],
            where: workplaceIds.length > 0 ? { id: workplaceIds } : { isActive: true },
            required: false
          }],
          required: false
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    // 获取所有相关职场
    const allWorkplaces = await Workplace.findAll({
      where: workplaceIds.length > 0 ? { id: workplaceIds } : { isActive: true },
      order: [['name', 'ASC']]
    });

    // 构建导出数据
    const exportData = products.map(product => {
      const productData = product.get({ plain: true });

      const exportRow = {
        '商品ID': productData.id,
        '商品名称': productData.name,
        '分类': productData.Category?.name || '',
        '光年币价格': productData.lyPrice,
        '人民币价格': productData.rmbPrice,
        '商品描述': productData.description || '',
        '库存管理模式': productData.stockManagementType === 'workplace' ? '职场分配' : '单一库存',
        '总库存': productData.stock || 0,
        '兑换次数': productData.exchangeCount || 0,
        '商品状态': productData.status === 'active' ? '上线' : '下线',
        '创建时间': new Date(productData.createdAt).toLocaleString('zh-CN'),
        '更新时间': new Date(productData.updatedAt).toLocaleString('zh-CN')
      };

      // 添加职场库存列
      if (includeWorkplaceStocks === 'true') {
        const workplaceStockMap = new Map();

        // 构建职场库存映射
        if (productData.ProductWorkplaceStocks) {
          productData.ProductWorkplaceStocks.forEach(pws => {
            if (pws.Workplace) {
              workplaceStockMap.set(pws.Workplace.name, {
                stock: pws.stock,
                availableStock: pws.stock - (pws.reservedStock || 0),
                reservedStock: pws.reservedStock || 0,
                minStockAlert: pws.minStockAlert || 10,
                lastUpdate: pws.lastStockUpdate ? new Date(pws.lastStockUpdate).toLocaleString('zh-CN') : ''
              });
            }
          });
        }

        // 为每个职场添加库存列
        allWorkplaces.forEach(workplace => {
          const stockInfo = workplaceStockMap.get(workplace.name) || {
            stock: 0,
            availableStock: 0,
            reservedStock: 0,
            minStockAlert: 10,
            lastUpdate: ''
          };

          exportRow[`${workplace.name}库存`] = stockInfo.stock;
          exportRow[`${workplace.name}可用库存`] = stockInfo.availableStock;
          exportRow[`${workplace.name}预留库存`] = stockInfo.reservedStock;
          exportRow[`${workplace.name}告警阈值`] = stockInfo.minStockAlert;
          exportRow[`${workplace.name}最后更新`] = stockInfo.lastUpdate;
        });
      }

      return exportRow;
    });

    // 生成文件
    let buffer;
    let contentType;
    let filename;

    if (format === 'csv') {
      // CSV格式
      const csv = Papa.unparse(exportData, {
        header: true,
        encoding: 'utf8'
      });
      buffer = Buffer.from('\ufeff' + csv, 'utf8'); // 添加BOM以支持中文
      contentType = 'text/csv; charset=utf-8';
      filename = `products_export_${new Date().toISOString().split('T')[0]}.csv`;
    } else {
      // Excel格式
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(exportData);

      // 设置列宽
      const colWidths = Object.keys(exportData[0] || {}).map(key => ({
        wch: Math.max(key.length, 15)
      }));
      worksheet['!cols'] = colWidths;

      XLSX.utils.book_append_sheet(workbook, worksheet, '商品数据');

      // 添加汇总信息工作表
      const summaryData = [
        { '统计项': '商品总数', '数值': products.length },
        { '统计项': '上线商品', '数值': products.filter(p => p.status === 'active').length },
        { '统计项': '下线商品', '数值': products.filter(p => p.status === 'inactive').length },
        { '统计项': '职场分配商品', '数值': products.filter(p => p.stockManagementType === 'workplace').length },
        { '统计项': '单一库存商品', '数值': products.filter(p => p.stockManagementType === 'single').length },
        { '统计项': '总库存数量', '数值': products.reduce((sum, p) => sum + (p.stock || 0), 0) },
        { '统计项': '导出时间', '数值': new Date().toLocaleString('zh-CN') }
      ];

      const summarySheet = XLSX.utils.json_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(workbook, summarySheet, '导出汇总');

      buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      filename = `products_export_${new Date().toISOString().split('T')[0]}.xlsx`;
    }

    // 记录导出日志
    await createLog({
      action: 'product_export',
      entityType: 'product',
      entityId: null,
      oldValue: null,
      newValue: JSON.stringify({
        exportCount: products.length,
        format,
        includeWorkplaceStocks,
        workplaceIds: workplaceIds.length > 0 ? workplaceIds : 'all'
      }),
      userId: req.user.id,
      username: req.user.username,
      description: `导出商品数据: ${products.length}个商品，格式: ${format}`
    }, req);

    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename=${encodeURIComponent(filename)}`);
    res.send(buffer);

  } catch (error) {
    console.error('导出商品数据失败:', error);
    res.status(500).json({
      success: false,
      message: '导出商品数据失败',
      error: error.message
    });
  }
};
```

### 10.2 用户端功能影响

#### 10.2.1 商品展示页面改造

**设计策略**：
- 根据用户所在职场显示对应库存状态
- 提供职场切换功能（如果用户有权限）
- 智能库存状态提示

**前端实现**：
```vue
<!-- ProductCard.vue 用户端商品卡片 -->
<template>
  <div class="user-product-card">
    <!-- 商品图片 -->
    <div class="product-img" @click="handleImageClick">
      <el-image :src="productImage" fit="cover" :alt="product.name" />

      <!-- 库存状态标签 -->
      <div class="stock-status-badge">
        <el-tag :type="stockStatusType" size="small">
          {{ stockStatusText }}
        </el-tag>
      </div>
    </div>

    <!-- 商品信息 -->
    <div class="product-info" @click="handleInfoClick">
      <h3 class="product-name">{{ product.name }}</h3>

      <!-- 价格显示 -->
      <div class="price-container">
        <div class="price-row light-bg">
          <span class="currency-label">光年币:</span>
          <span class="currency-value">{{ product.lyPrice }}</span>
        </div>
        <div class="price-row dark-bg">
          <span class="currency-label">人民币:</span>
          <span class="currency-value">{{ product.rmbPrice }}元</span>
        </div>
      </div>

      <!-- 库存和兑换信息 -->
      <div class="stock-exchange-info">
        <div class="stock-info">
          <span class="label">库存状态:</span>
          <span :class="stockStatusClass">{{ stockDisplayText }}</span>
        </div>
        <div class="exchange-count">
          已兑换: {{ product.exchangeCount || 0 }}+
        </div>
      </div>

      <!-- 职场选择（如果商品支持多职场且用户有权限） -->
      <div v-if="showWorkplaceSelector" class="workplace-selector">
        <el-select
          v-model="selectedWorkplaceId"
          placeholder="选择职场"
          size="small"
          @change="handleWorkplaceChange"
        >
          <el-option
            v-for="workplace in availableWorkplaces"
            :key="workplace.id"
            :label="`${workplace.name} (库存: ${workplace.availableStock})`"
            :value="workplace.id"
            :disabled="workplace.availableStock === 0"
          />
        </el-select>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useAuthStore } from '../stores/auth';

const props = defineProps({
  product: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['click', 'image-click', 'workplace-change']);

const authStore = useAuthStore();
const selectedWorkplaceId = ref(null);

// 计算属性
const userWorkplace = computed(() => authStore.user?.workplace);

const availableWorkplaces = computed(() => {
  if (!props.product.workplaceStocks) return [];

  return props.product.workplaceStocks.map(ws => ({
    id: ws.workplaceId,
    name: ws.workplaceName,
    availableStock: ws.availableStock || 0,
    isUserWorkplace: ws.workplaceId === userWorkplace.value?.id
  })).sort((a, b) => {
    // 用户所在职场排在前面
    if (a.isUserWorkplace && !b.isUserWorkplace) return -1;
    if (!a.isUserWorkplace && b.isUserWorkplace) return 1;
    return a.name.localeCompare(b.name);
  });
});

const showWorkplaceSelector = computed(() => {
  return props.product.stockManagementType === 'workplace' &&
         availableWorkplaces.value.length > 1 &&
         authStore.user?.permissions?.includes('select_workplace');
});

const currentWorkplaceStock = computed(() => {
  if (!props.product.workplaceStocks) return null;

  const workplaceId = selectedWorkplaceId.value || userWorkplace.value?.id;
  return props.product.workplaceStocks.find(ws => ws.workplaceId === workplaceId);
});

const stockStatusType = computed(() => {
  if (props.product.stockManagementType === 'single') {
    const stock = props.product.stock || 0;
    if (stock === 0) return 'danger';
    if (stock <= 10) return 'warning';
    return 'success';
  } else {
    const workplaceStock = currentWorkplaceStock.value;
    if (!workplaceStock || workplaceStock.availableStock === 0) return 'danger';
    if (workplaceStock.isLowStock) return 'warning';
    return 'success';
  }
});

const stockStatusText = computed(() => {
  if (props.product.stockManagementType === 'single') {
    const stock = props.product.stock || 0;
    if (stock === 0) return '缺货';
    if (stock <= 10) return '库存偏低';
    return '有货';
  } else {
    const workplaceStock = currentWorkplaceStock.value;
    if (!workplaceStock || workplaceStock.availableStock === 0) return '缺货';
    if (workplaceStock.isLowStock) return '库存偏低';
    return '有货';
  }
});

const stockStatusClass = computed(() => {
  const type = stockStatusType.value;
  return {
    'stock-normal': type === 'success',
    'stock-low': type === 'warning',
    'stock-empty': type === 'danger'
  };
});

const stockDisplayText = computed(() => {
  if (props.product.stockManagementType === 'single') {
    return `${props.product.stock || 0} 件`;
  } else {
    const workplaceStock = currentWorkplaceStock.value;
    if (!workplaceStock) return '0 件';

    if (showWorkplaceSelector.value) {
      return `${workplaceStock.availableStock} 件`;
    } else {
      // 只显示用户所在职场的库存
      return `${workplaceStock.availableStock} 件`;
    }
  }
});

// 事件处理
const handleInfoClick = () => {
  emit('click', props.product);
};

const handleImageClick = (event) => {
  event.stopPropagation();
  emit('image-click', props.product);
};

const handleWorkplaceChange = (workplaceId) => {
  emit('workplace-change', {
    product: props.product,
    workplaceId
  });
};

// 初始化选择用户所在职场
watch(() => userWorkplace.value, (newWorkplace) => {
  if (newWorkplace && !selectedWorkplaceId.value) {
    selectedWorkplaceId.value = newWorkplace.id;
  }
}, { immediate: true });
</script>

<style scoped>
.user-product-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.user-product-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.product-img {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.stock-status-badge {
  position: absolute;
  top: 8px;
  right: 8px;
}

.product-info {
  padding: 16px;
}

.product-name {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 12px 0;
  color: #303133;
  line-height: 1.4;
}

.price-container {
  margin-bottom: 12px;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  border-radius: 4px;
  margin-bottom: 4px;
}

.light-bg {
  background-color: #f0f9ff;
}

.dark-bg {
  background-color: #e1f5fe;
}

.currency-label {
  font-size: 12px;
  color: #606266;
}

.currency-value {
  font-size: 14px;
  font-weight: 500;
  color: #409eff;
}

.stock-exchange-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 12px;
}

.stock-info .label {
  color: #909399;
}

.stock-normal {
  color: #67c23a;
  font-weight: 500;
}

.stock-low {
  color: #e6a23c;
  font-weight: 500;
}

.stock-empty {
  color: #f56c6c;
  font-weight: 500;
}

.exchange-count {
  color: #909399;
}

.workplace-selector {
  margin-top: 8px;
}
</style>
```

#### 10.2.2 兑换流程改造

**设计策略**：
- 保持原有兑换流程和界面设计不变
- 只显示商品总库存，不显示各职场具体库存
- 保留职场选择功能，但移除智能推荐和自动调货
- 总库存充足即可提交申请，后端根据职场库存情况设置订单状态

**兑换表单组件**：
```vue
<!-- ExchangeForm.vue 用户端兑换表单 -->
<template>
  <el-dialog
    v-model="visible"
    title="商品兑换"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <!-- 商品信息展示 -->
      <div class="product-summary">
        <div class="product-image">
          <el-image :src="product.imageUrl" fit="cover" />
        </div>
        <div class="product-details">
          <h3>{{ product.name }}</h3>
          <div class="price-info">
            <span v-if="form.paymentMethod === 'ly'">
              光年币: {{ product.lyPrice }} × {{ form.quantity }} = {{ totalAmount }}
            </span>
            <span v-else>
              人民币: ¥{{ product.rmbPrice }} × {{ form.quantity }} = ¥{{ totalAmount }}
            </span>
          </div>
          <!-- 只显示总库存 -->
          <div class="stock-info">
            <span class="stock-label">当前库存：</span>
            <span :class="totalStockClass">{{ product.totalStock || product.stock || 0 }} 件</span>
          </div>
        </div>
      </div>

      <!-- 兑换数量 -->
      <el-form-item label="兑换数量" prop="quantity">
        <el-input-number
          v-model="form.quantity"
          :min="1"
          :max="maxQuantity"
        />
        <span class="quantity-hint">
          最多可兑换 {{ maxQuantity }} 件
        </span>
      </el-form-item>

      <!-- 支付方式 -->
      <el-form-item label="支付方式" prop="paymentMethod">
        <el-radio-group v-model="form.paymentMethod" @change="handlePaymentMethodChange">
          <el-radio label="ly">光年币支付</el-radio>
          <el-radio label="rmb">人民币支付</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 职场选择 -->
      <el-form-item
        v-if="showWorkplaceSelection"
        label="选择职场"
        prop="workplaceId"
      >
        <el-select
          v-model="form.workplaceId"
          placeholder="请选择职场"
        >
          <el-option
            v-for="workplace in availableWorkplaces"
            :key="workplace.id"
            :value="workplace.id"
            :label="workplace.name"
          />
        </el-select>
        <div class="workplace-selection-hint">
          <el-alert
            title="职场选择说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>请选择您希望从哪个职场兑换商品。</p>
              <p>如果选择的职场库存不足，订单将进入待处理状态，管理员会安排库存调配。</p>
            </template>
          </el-alert>
        </div>
      </el-form-item>

      <!-- 联系方式 -->
      <el-form-item label="联系方式" prop="contactInfo">
        <el-input
          v-model="form.contactInfo"
          placeholder="请输入您的联系方式"
        />
      </el-form-item>

      <!-- 配送地址 -->
      <el-form-item label="配送地址" prop="location">
        <el-input
          v-model="form.location"
          type="textarea"
          :rows="2"
          placeholder="请输入配送地址"
        />
      </el-form-item>

      <!-- 备注 -->
      <el-form-item label="备注">
        <el-input
          v-model="form.remarks"
          type="textarea"
          :rows="2"
          placeholder="其他需要说明的信息（可选）"
        />
      </el-form-item>

      <!-- 支付凭证上传（人民币支付时） -->
      <el-form-item
        v-if="form.paymentMethod === 'rmb'"
        label="支付凭证"
        prop="paymentProofUrl"
      >
        <el-upload
          class="payment-proof-upload"
          :action="uploadUrl"
          :headers="uploadHeaders"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :before-upload="beforeUpload"
          :limit="1"
          accept="image/*"
        >
          <el-button type="primary">上传支付凭证</el-button>
          <template #tip>
            <div class="upload-tip">
              请上传支付截图，支持jpg、png格式，大小不超过5MB
            </div>
          </template>
        </el-upload>
        <div v-if="form.paymentProofUrl" class="uploaded-proof">
          <el-image
            :src="form.paymentProofUrl"
            fit="cover"
            style="width: 100px; height: 100px;"
            :preview-src-list="[form.paymentProofUrl]"
          />
        </div>
      </el-form-item>
    </el-form>

    <!-- 兑换确认信息 -->
    <div class="exchange-summary">
      <el-card>
        <template #header>
          <span>兑换确认</span>
        </template>
        <div class="summary-item">
          <span>商品名称:</span>
          <span>{{ product.name }}</span>
        </div>
        <div class="summary-item">
          <span>兑换数量:</span>
          <span>{{ form.quantity }} 件</span>
        </div>
        <div class="summary-item">
          <span>支付方式:</span>
          <span>{{ form.paymentMethod === 'ly' ? '光年币' : '人民币' }}</span>
        </div>
        <div v-if="showWorkplaceSelection" class="summary-item">
          <span>兑换职场:</span>
          <span>{{ selectedWorkplaceName || '未选择' }}</span>
        </div>
        <div class="summary-item total">
          <span>支付金额:</span>
          <span class="amount">
            {{ form.paymentMethod === 'ly' ? totalAmount : `¥${totalAmount}` }}
          </span>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          :disabled="!isFormValid"
        >
          确认兑换
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useAuthStore } from '../stores/auth';
import { createExchange } from '../api/exchanges';
import { getProductWorkplaceStocks } from '../api/products';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  product: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:visible', 'success']);

const authStore = useAuthStore();
const formRef = ref(null);
const submitting = ref(false);
const availableWorkplaces = ref([]);

// 表单数据
const form = ref({
  quantity: 1,
  paymentMethod: 'ly',
  workplaceId: null,
  contactInfo: '',
  location: '',
  remarks: '',
  paymentProofUrl: ''
});

// 表单验证规则
const rules = {
  quantity: [
    { required: true, message: '请输入兑换数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '数量必须大于0', trigger: 'blur' }
  ],
  paymentMethod: [
    { required: true, message: '请选择支付方式', trigger: 'change' }
  ],
  workplaceId: [
    { required: true, message: '请选择职场', trigger: 'change' }
  ],
  contactInfo: [
    { required: true, message: '请输入联系方式', trigger: 'blur' }
  ],
  location: [
    { required: true, message: '请输入配送地址', trigger: 'blur' }
  ],
  paymentProofUrl: [
    { required: true, message: '请上传支付凭证', trigger: 'change' }
  ]
};

// 计算属性
const showWorkplaceSelection = computed(() =>
  props.product.stockManagementType === 'workplace'
);

const totalStock = computed(() =>
  props.product.totalStock || props.product.stock || 0
);

const totalStockClass = computed(() => {
  const stock = totalStock.value;
  if (stock === 0) return 'stock-empty';
  if (stock <= 10) return 'stock-low';
  return 'stock-normal';
});

const maxQuantity = computed(() => {
  // 基于总库存限制兑换数量
  return Math.min(totalStock.value, 100);
});

const totalAmount = computed(() => {
  const price = form.value.paymentMethod === 'ly'
    ? props.product.lyPrice
    : props.product.rmbPrice;
  return (price * form.value.quantity).toFixed(2);
});

const selectedWorkplaceName = computed(() => {
  const workplace = availableWorkplaces.value.find(wp => wp.id === form.value.workplaceId);
  return workplace ? workplace.name : '';
});

const isFormValid = computed(() => {
  const basicValid = form.value.quantity > 0 &&
                    form.value.paymentMethod &&
                    form.value.contactInfo &&
                    form.value.location &&
                    form.value.quantity <= totalStock.value; // 基于总库存验证

  const workplaceValid = !showWorkplaceSelection.value || form.value.workplaceId;

  const paymentProofValid = form.value.paymentMethod !== 'rmb' || form.value.paymentProofUrl;

  return basicValid && workplaceValid && paymentProofValid;
});

// 方法
const handlePaymentMethodChange = () => {
  form.value.paymentProofUrl = '';
};

const loadAvailableWorkplaces = async () => {
  if (props.product.stockManagementType !== 'workplace') return;

  try {
    // 获取所有活跃职场，不考虑库存情况
    const response = await getWorkplaces({ isActive: true });
    availableWorkplaces.value = response.data;

    // 默认选择用户所在职场（如果有）
    const userWorkplace = authStore.user?.workplace;
    if (userWorkplace && !form.value.workplaceId) {
      const userWorkplaceOption = availableWorkplaces.value.find(wp => wp.id === userWorkplace.id);
      if (userWorkplaceOption) {
        form.value.workplaceId = userWorkplace.id;
      }
    }
  } catch (error) {
    console.error('加载职场列表失败:', error);
    ElMessage.error('加载职场列表失败');
  }
};

const handleSubmit = async () => {
  try {
    await formRef.value.validate();

    submitting.value = true;

    const exchangeData = {
      productId: props.product.id,
      quantity: form.value.quantity,
      paymentMethod: form.value.paymentMethod,
      contactInfo: form.value.contactInfo,
      location: form.value.location,
      remarks: form.value.remarks,
      paymentProofUrl: form.value.paymentProofUrl
    };

    if (showWorkplaceSelection.value) {
      exchangeData.workplaceId = form.value.workplaceId;
    }

    await createExchange(exchangeData);

    ElMessage.success('兑换申请提交成功！');
    emit('success');
    handleClose();

  } catch (error) {
    console.error('提交兑换申请失败:', error);
    ElMessage.error(error.message || '提交兑换申请失败');
  } finally {
    submitting.value = false;
  }
};

const handleClose = () => {
  emit('update:visible', false);
  // 重置表单
  form.value = {
    quantity: 1,
    paymentMethod: 'ly',
    workplaceId: null,
    contactInfo: '',
    location: '',
    remarks: '',
    paymentProofUrl: ''
  };
};

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    loadAvailableWorkplaces();
  }
});
</script>

<style scoped>
.product-summary {
  display: flex;
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.product-image {
  width: 80px;
  height: 80px;
  margin-right: 16px;
  border-radius: 4px;
  overflow: hidden;
}

.product-details h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
}

.price-info {
  color: #409eff;
  font-weight: 500;
}

.quantity-hint {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

.stock-info {
  margin-top: 8px;
  font-size: 14px;
}

.stock-label {
  color: #606266;
}

.stock-normal {
  color: #67c23a;
  font-weight: 500;
}

.stock-low {
  color: #e6a23c;
  font-weight: 500;
}

.stock-empty {
  color: #f56c6c;
  font-weight: 500;
}

.workplace-selection-hint {
  margin-top: 8px;
}

.exchange-summary {
  margin: 20px 0;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.summary-item.total {
  font-weight: 500;
  font-size: 16px;
  border-top: 1px solid #e4e7ed;
  padding-top: 8px;
  margin-top: 12px;
}

.amount {
  color: #409eff;
  font-weight: 600;
}

.payment-proof-upload {
  width: 100%;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.uploaded-proof {
  margin-top: 8px;
}
</style>
```

#### 10.2.3 管理员库存调配界面

**设计策略**：
- 提供待处理兑换订单的管理界面
- 支持职场间库存转移的手动操作
- 完整的库存调配流程管理

**待处理订单管理组件**：
```vue
<!-- PendingAllocationOrders.vue 管理员待处理订单界面 -->
<template>
  <div class="pending-allocation-orders">
    <el-card header="待库存调配订单">
      <!-- 筛选工具栏 -->
      <div class="filter-toolbar">
        <el-select v-model="filters.targetWorkplaceId" placeholder="目标职场" clearable>
          <el-option label="全部职场" value="" />
          <el-option
            v-for="workplace in workplaces"
            :key="workplace.id"
            :label="workplace.name"
            :value="workplace.id"
          />
        </el-select>
        <el-date-picker
          v-model="filters.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
        <el-button type="primary" @click="loadPendingOrders">
          <el-icon><Search /></el-icon>
          查询
        </el-button>
        <el-button @click="resetFilters">重置</el-button>
      </div>

      <!-- 订单列表 -->
      <el-table :data="pendingOrders" border v-loading="loading">
        <el-table-column prop="id" label="订单ID" width="80" />
        <el-table-column label="商品信息" width="200">
          <template #default="scope">
            <div class="product-info">
              <div class="product-name">{{ scope.row.productName }}</div>
              <div class="product-quantity">数量: {{ scope.row.quantity }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="目标职场" width="120">
          <template #default="scope">
            <el-tag type="primary">{{ scope.row.targetWorkplaceName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="库存状况" width="150">
          <template #default="scope">
            <div class="stock-status">
              <div class="current-stock">
                当前: <span class="stock-number">{{ scope.row.currentStock }}</span>
              </div>
              <div class="required-stock">
                需要: <span class="stock-number required">{{ scope.row.requiredStock }}</span>
              </div>
              <div class="shortfall">
                缺口: <span class="stock-number shortage">{{ scope.row.shortfall }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="可调配职场" width="200">
          <template #default="scope">
            <div class="available-workplaces">
              <el-tag
                v-for="workplace in scope.row.availableWorkplaces"
                :key="workplace.workplaceId"
                size="small"
                style="margin: 2px;"
              >
                {{ workplace.workplaceName }} ({{ workplace.availableStock }})
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="用户信息" width="150">
          <template #default="scope">
            <div class="user-info">
              <div>{{ scope.row.userInfo.username }}</div>
              <div class="contact">{{ scope.row.userInfo.contactInfo }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="申请时间" width="150">
          <template #default="scope">
            {{ formatDateTime(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="showStockTransferDialog(scope.row)"
            >
              调配库存
            </el-button>
            <el-button
              size="small"
              type="success"
              @click="approveOrder(scope.row)"
              :disabled="scope.row.shortfall > 0"
            >
              确认订单
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :total="pagination.totalItems"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadPendingOrders"
        @current-change="loadPendingOrders"
        style="margin-top: 16px; text-align: right;"
      />
    </el-card>

    <!-- 库存调配弹窗 -->
    <StockTransferDialog
      v-model:visible="transferDialogVisible"
      :order="selectedOrder"
      @success="handleTransferSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import {
  getPendingAllocationOrders,
  approveExchangeOrder
} from '../../api/admin/exchanges';
import { getWorkplaces } from '../../api/system';
import StockTransferDialog from './StockTransferDialog.vue';

// 响应式数据
const loading = ref(false);
const pendingOrders = ref([]);
const workplaces = ref([]);
const transferDialogVisible = ref(false);
const selectedOrder = ref(null);

// 筛选条件
const filters = ref({
  targetWorkplaceId: '',
  dateRange: []
});

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 20,
  totalItems: 0
});

// 加载待处理订单
const loadPendingOrders = async () => {
  try {
    loading.value = true;

    const params = {
      page: pagination.value.currentPage,
      pageSize: pagination.value.pageSize,
      targetWorkplaceId: filters.value.targetWorkplaceId || undefined,
      startDate: filters.value.dateRange?.[0]?.toISOString().split('T')[0],
      endDate: filters.value.dateRange?.[1]?.toISOString().split('T')[0]
    };

    const response = await getPendingAllocationOrders(params);
    pendingOrders.value = response.data.orders;
    pagination.value.totalItems = response.data.pagination.totalItems;
  } catch (error) {
    console.error('加载待处理订单失败:', error);
    ElMessage.error('加载待处理订单失败');
  } finally {
    loading.value = false;
  }
};

// 显示库存调配弹窗
const showStockTransferDialog = (order) => {
  selectedOrder.value = order;
  transferDialogVisible.value = true;
};

// 确认订单
const approveOrder = async (order) => {
  if (order.shortfall > 0) {
    ElMessage.warning('库存不足，请先调配库存');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确认批准订单 #${order.id}？`,
      '确认操作',
      { type: 'warning' }
    );

    await approveExchangeOrder(order.id, {
      status: 'approved',
      adminNotes: '库存充足，订单已确认'
    });

    ElMessage.success('订单已确认');
    await loadPendingOrders();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认订单失败:', error);
      ElMessage.error('确认订单失败');
    }
  }
};

// 库存调配成功回调
const handleTransferSuccess = () => {
  transferDialogVisible.value = false;
  loadPendingOrders();
  ElMessage.success('库存调配成功');
};

// 重置筛选条件
const resetFilters = () => {
  filters.value = {
    targetWorkplaceId: '',
    dateRange: []
  };
  pagination.value.currentPage = 1;
  loadPendingOrders();
};

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return new Date(dateTime).toLocaleString('zh-CN');
};

// 初始化
onMounted(async () => {
  await loadWorkplaces();
  await loadPendingOrders();
});
</script>

<style scoped>
.pending-allocation-orders {
  padding: 20px;
}

.filter-toolbar {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  align-items: center;
}

.product-info {
  line-height: 1.4;
}

.product-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.product-quantity {
  font-size: 12px;
  color: #909399;
}

.stock-status {
  font-size: 12px;
  line-height: 1.4;
}

.stock-number {
  font-weight: 500;
}

.stock-number.required {
  color: #409eff;
}

.stock-number.shortage {
  color: #f56c6c;
}

.available-workplaces {
  max-width: 180px;
}

.user-info {
  line-height: 1.4;
}

.contact {
  font-size: 12px;
  color: #909399;
}
</style>
```

### 10.3 系统管理功能

#### 10.3.1 职场管理页面改造

**设计策略**：
- 新增、编辑、删除职场时自动处理相关商品库存
- 提供库存迁移和分配策略选择
- 完善的数据一致性检查

**职场删除处理逻辑**：
```javascript
// server/controllers/systemController.js

/**
 * 删除职场（处理库存迁移）
 */
exports.deleteWorkplace = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { migrationStrategy = 'merge', targetWorkplaceId = null } = req.body;

    // 检查职场是否存在
    const workplace = await Workplace.findByPk(id, { transaction });
    if (!workplace) {
      await transaction.rollback();
      return res.status(404).json({ message: '职场不存在' });
    }

    // 检查是否有关联的库存数据
    const workplaceStocks = await ProductWorkplaceStock.findAll({
      where: { workplaceId: id },
      include: [{ model: Product, attributes: ['id', 'name'] }],
      transaction
    });

    if (workplaceStocks.length > 0) {
      // 根据迁移策略处理库存
      switch (migrationStrategy) {
        case 'merge':
          // 合并到指定职场
          if (!targetWorkplaceId) {
            await transaction.rollback();
            return res.status(400).json({
              message: '选择合并策略时必须指定目标职场'
            });
          }
          await mergeWorkplaceStocks(id, targetWorkplaceId, transaction);
          break;

        case 'distribute':
          // 平均分配到其他职场
          await distributeWorkplaceStocks(id, transaction);
          break;

        case 'clear':
          // 清空库存（将库存设为0）
          await clearWorkplaceStocks(id, transaction);
          break;

        default:
          await transaction.rollback();
          return res.status(400).json({
            message: '无效的库存迁移策略'
          });
      }
    }

    // 检查是否有关联的用户
    const userCount = await User.count({
      where: { workplaceId: id },
      transaction
    });

    if (userCount > 0) {
      // 将用户的职场设置为null或默认职场
      await User.update(
        { workplaceId: null },
        { where: { workplaceId: id }, transaction }
      );
    }

    // 检查是否有关联的兑换记录
    const exchangeCount = await Exchange.count({
      where: { stockWorkplaceId: id },
      transaction
    });

    // 删除职场
    await workplace.destroy({ transaction });

    // 记录删除日志
    await createLog({
      action: ACTIONS.WORKPLACE_DELETE,
      entityType: ENTITY_TYPES.WORKPLACE,
      entityId: id,
      oldValue: JSON.stringify(workplace.get()),
      newValue: null,
      userId: req.user.id,
      username: req.user.username,
      description: `删除职场 "${workplace.name}"，库存迁移策略：${migrationStrategy}，影响商品：${workplaceStocks.length}个，影响用户：${userCount}个，影响订单：${exchangeCount}个`
    }, req, transaction);

    await transaction.commit();

    res.json({
      success: true,
      message: '职场删除成功',
      data: {
        deletedWorkplace: workplace.name,
        affectedProducts: workplaceStocks.length,
        affectedUsers: userCount,
        affectedExchanges: exchangeCount,
        migrationStrategy
      }
    });

  } catch (error) {
    await transaction.rollback();
    console.error('删除职场失败:', error);
    res.status(500).json({
      success: false,
      message: '删除职场失败',
      error: error.message
    });
  }
};

/**
 * 合并职场库存到目标职场
 */
async function mergeWorkplaceStocks(sourceWorkplaceId, targetWorkplaceId, transaction) {
  const sourceStocks = await ProductWorkplaceStock.findAll({
    where: { workplaceId: sourceWorkplaceId },
    transaction
  });

  for (const sourceStock of sourceStocks) {
    // 查找目标职场是否已有该商品的库存记录
    const targetStock = await ProductWorkplaceStock.findOne({
      where: {
        productId: sourceStock.productId,
        workplaceId: targetWorkplaceId
      },
      transaction
    });

    if (targetStock) {
      // 合并库存
      await targetStock.update({
        stock: targetStock.stock + sourceStock.stock,
        reservedStock: targetStock.reservedStock + (sourceStock.reservedStock || 0),
        lastStockUpdate: new Date()
      }, { transaction });
    } else {
      // 创建新的库存记录
      await ProductWorkplaceStock.create({
        productId: sourceStock.productId,
        workplaceId: targetWorkplaceId,
        stock: sourceStock.stock,
        reservedStock: sourceStock.reservedStock || 0,
        minStockAlert: sourceStock.minStockAlert || 10
      }, { transaction });
    }

    // 记录库存操作日志
    await StockOperationLog.create({
      productId: sourceStock.productId,
      workplaceId: targetWorkplaceId,
      operationType: 'transfer',
      beforeStock: targetStock ? targetStock.stock : 0,
      afterStock: targetStock ? targetStock.stock + sourceStock.stock : sourceStock.stock,
      changeAmount: sourceStock.stock,
      reason: `职场合并：从职场${sourceWorkplaceId}转移库存`,
      operatorId: null,
      operatorName: 'SYSTEM'
    }, { transaction });

    // 删除源库存记录
    await sourceStock.destroy({ transaction });
  }
}

/**
 * 分配职场库存到其他职场
 */
async function distributeWorkplaceStocks(sourceWorkplaceId, transaction) {
  // 获取其他活跃职场
  const activeWorkplaces = await Workplace.findAll({
    where: {
      isActive: true,
      id: { [Op.ne]: sourceWorkplaceId }
    },
    transaction
  });

  if (activeWorkplaces.length === 0) {
    throw new Error('没有其他活跃职场可以分配库存');
  }

  const sourceStocks = await ProductWorkplaceStock.findAll({
    where: { workplaceId: sourceWorkplaceId },
    transaction
  });

  for (const sourceStock of sourceStocks) {
    const stockPerWorkplace = Math.floor(sourceStock.stock / activeWorkplaces.length);
    const remainder = sourceStock.stock % activeWorkplaces.length;

    for (let i = 0; i < activeWorkplaces.length; i++) {
      const workplace = activeWorkplaces[i];
      const allocatedStock = stockPerWorkplace + (i < remainder ? 1 : 0);

      if (allocatedStock > 0) {
        // 查找或创建目标职场库存记录
        const [targetStock, created] = await ProductWorkplaceStock.findOrCreate({
          where: {
            productId: sourceStock.productId,
            workplaceId: workplace.id
          },
          defaults: {
            stock: allocatedStock,
            minStockAlert: sourceStock.minStockAlert || 10
          },
          transaction
        });

        if (!created) {
          await targetStock.update({
            stock: targetStock.stock + allocatedStock,
            lastStockUpdate: new Date()
          }, { transaction });
        }

        // 记录库存操作日志
        await StockOperationLog.create({
          productId: sourceStock.productId,
          workplaceId: workplace.id,
          operationType: 'transfer',
          beforeStock: created ? 0 : targetStock.stock - allocatedStock,
          afterStock: created ? allocatedStock : targetStock.stock,
          changeAmount: allocatedStock,
          reason: `职场删除分配：从职场${sourceWorkplaceId}分配库存`,
          operatorId: null,
          operatorName: 'SYSTEM'
        }, { transaction });
      }
    }

    // 删除源库存记录
    await sourceStock.destroy({ transaction });
  }
}

/**
 * 清空职场库存
 */
async function clearWorkplaceStocks(sourceWorkplaceId, transaction) {
  const sourceStocks = await ProductWorkplaceStock.findAll({
    where: { workplaceId: sourceWorkplaceId },
    transaction
  });

  for (const sourceStock of sourceStocks) {
    // 记录库存清空日志
    await StockOperationLog.create({
      productId: sourceStock.productId,
      workplaceId: sourceWorkplaceId,
      operationType: 'subtract',
      beforeStock: sourceStock.stock,
      afterStock: 0,
      changeAmount: -sourceStock.stock,
      reason: `职场删除：清空库存`,
      operatorId: null,
      operatorName: 'SYSTEM'
    }, { transaction });

    // 删除库存记录
    await sourceStock.destroy({ transaction });
  }
}
```

#### 10.3.2 数据统计报表改造

**设计策略**：
- 所有统计报表支持按职场维度展示
- 提供职场对比分析功能
- 增加库存分布统计

**统计报表API**：
```javascript
/**
 * 获取职场维度的销售统计
 */
exports.getWorkplaceSalesStats = async (req, res) => {
  try {
    const {
      startDate,
      endDate,
      workplaceIds = [],
      groupBy = 'workplace' // workplace, month, week, day
    } = req.query;

    const whereCondition = {
      status: ['completed', 'shipped']
    };

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    }

    if (workplaceIds.length > 0) {
      whereCondition.stockWorkplaceId = workplaceIds;
    }

    let groupByFields = [];
    let selectFields = [
      [sequelize.fn('COUNT', sequelize.col('Exchange.id')), 'orderCount'],
      [sequelize.fn('SUM', sequelize.col('Exchange.quantity')), 'totalQuantity'],
      [sequelize.fn('SUM', sequelize.col('Exchange.totalAmount')), 'totalAmount']
    ];

    switch (groupBy) {
      case 'workplace':
        groupByFields = ['stockWorkplaceId', 'Workplace.name'];
        selectFields.push(
          [sequelize.col('stockWorkplaceId'), 'workplaceId'],
          [sequelize.col('Workplace.name'), 'workplaceName']
        );
        break;
      case 'month':
        groupByFields = [
          sequelize.fn('DATE_FORMAT', sequelize.col('Exchange.createdAt'), '%Y-%m'),
          'stockWorkplaceId',
          'Workplace.name'
        ];
        selectFields.push(
          [sequelize.fn('DATE_FORMAT', sequelize.col('Exchange.createdAt'), '%Y-%m'), 'month'],
          [sequelize.col('stockWorkplaceId'), 'workplaceId'],
          [sequelize.col('Workplace.name'), 'workplaceName']
        );
        break;
      // 其他时间维度...
    }

    const stats = await Exchange.findAll({
      attributes: selectFields,
      include: [
        {
          model: Workplace,
          attributes: [],
          required: true
        }
      ],
      where: whereCondition,
      group: groupByFields,
      order: [[sequelize.col('totalAmount'), 'DESC']]
    });

    // 计算汇总数据
    const summary = {
      totalOrders: stats.reduce((sum, item) => sum + parseInt(item.get('orderCount')), 0),
      totalQuantity: stats.reduce((sum, item) => sum + parseInt(item.get('totalQuantity')), 0),
      totalAmount: stats.reduce((sum, item) => sum + parseFloat(item.get('totalAmount')), 0),
      workplaceCount: new Set(stats.map(item => item.get('workplaceId'))).size
    };

    res.json({
      success: true,
      data: {
        stats: stats.map(item => item.get({ plain: true })),
        summary,
        period: { startDate, endDate },
        groupBy
      }
    });

  } catch (error) {
    console.error('获取职场销售统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取职场销售统计失败',
      error: error.message
    });
  }
};

/**
 * 获取库存分布统计
 */
exports.getStockDistributionStats = async (req, res) => {
  try {
    const { categoryIds = [], workplaceIds = [] } = req.query;

    const whereCondition = {};
    if (categoryIds.length > 0) {
      whereCondition.categoryId = categoryIds;
    }

    const workplaceCondition = { isActive: true };
    if (workplaceIds.length > 0) {
      workplaceCondition.id = workplaceIds;
    }

    // 获取库存分布数据
    const stockDistribution = await ProductWorkplaceStock.findAll({
      attributes: [
        [sequelize.col('Workplace.id'), 'workplaceId'],
        [sequelize.col('Workplace.name'), 'workplaceName'],
        [sequelize.fn('COUNT', sequelize.col('ProductWorkplaceStock.id')), 'productCount'],
        [sequelize.fn('SUM', sequelize.col('ProductWorkplaceStock.stock')), 'totalStock'],
        [sequelize.fn('SUM', sequelize.col('ProductWorkplaceStock.reservedStock')), 'totalReservedStock'],
        [sequelize.fn('AVG', sequelize.col('ProductWorkplaceStock.stock')), 'avgStock'],
        [sequelize.fn('COUNT', sequelize.literal('CASE WHEN ProductWorkplaceStock.stock = 0 THEN 1 END')), 'outOfStockCount'],
        [sequelize.fn('COUNT', sequelize.literal('CASE WHEN ProductWorkplaceStock.stock <= ProductWorkplaceStock.minStockAlert THEN 1 END')), 'lowStockCount']
      ],
      include: [
        {
          model: Workplace,
          attributes: [],
          where: workplaceCondition,
          required: true
        },
        {
          model: Product,
          attributes: [],
          where: whereCondition,
          required: true
        }
      ],
      group: ['Workplace.id', 'Workplace.name'],
      order: [[sequelize.fn('SUM', sequelize.col('ProductWorkplaceStock.stock')), 'DESC']]
    });

    // 获取分类维度的库存分布
    const categoryDistribution = await ProductWorkplaceStock.findAll({
      attributes: [
        [sequelize.col('Product.Category.id'), 'categoryId'],
        [sequelize.col('Product.Category.name'), 'categoryName'],
        [sequelize.fn('COUNT', sequelize.col('ProductWorkplaceStock.id')), 'productCount'],
        [sequelize.fn('SUM', sequelize.col('ProductWorkplaceStock.stock')), 'totalStock']
      ],
      include: [
        {
          model: Product,
          attributes: [],
          include: [{
            model: Category,
            attributes: [],
            required: true
          }],
          where: whereCondition,
          required: true
        },
        {
          model: Workplace,
          attributes: [],
          where: workplaceCondition,
          required: true
        }
      ],
      group: ['Product.Category.id', 'Product.Category.name'],
      order: [[sequelize.fn('SUM', sequelize.col('ProductWorkplaceStock.stock')), 'DESC']]
    });

    // 计算总体统计
    const overallStats = await ProductWorkplaceStock.findOne({
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('ProductWorkplaceStock.id')), 'totalRecords'],
        [sequelize.fn('SUM', sequelize.col('ProductWorkplaceStock.stock')), 'grandTotalStock'],
        [sequelize.fn('SUM', sequelize.col('ProductWorkplaceStock.reservedStock')), 'grandTotalReservedStock'],
        [sequelize.fn('COUNT', sequelize.literal('CASE WHEN ProductWorkplaceStock.stock = 0 THEN 1 END')), 'grandOutOfStockCount'],
        [sequelize.fn('COUNT', sequelize.literal('CASE WHEN ProductWorkplaceStock.stock <= ProductWorkplaceStock.minStockAlert THEN 1 END')), 'grandLowStockCount']
      ],
      include: [
        {
          model: Workplace,
          attributes: [],
          where: workplaceCondition,
          required: true
        },
        {
          model: Product,
          attributes: [],
          where: whereCondition,
          required: true
        }
      ]
    });

    res.json({
      success: true,
      data: {
        workplaceDistribution: stockDistribution.map(item => item.get({ plain: true })),
        categoryDistribution: categoryDistribution.map(item => item.get({ plain: true })),
        overallStats: overallStats.get({ plain: true })
      }
    });

  } catch (error) {
    console.error('获取库存分布统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取库存分布统计失败',
      error: error.message
    });
  }
};
```

#### 10.3.3 库存告警功能改造

**设计策略**：
- 支持按职场设置不同的告警规则
- 提供多种告警方式（邮件、短信、系统通知）
- 智能告警频率控制

**告警配置表设计**：
```sql
-- 库存告警配置表
CREATE TABLE stock_alert_configs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    workplaceId INT NULL COMMENT '职场ID，NULL表示全局配置',
    categoryId INT NULL COMMENT '分类ID，NULL表示所有分类',
    productId INT NULL COMMENT '商品ID，NULL表示所有商品',
    alertType ENUM('low_stock', 'out_of_stock', 'high_demand') NOT NULL COMMENT '告警类型',
    threshold INT NOT NULL COMMENT '告警阈值',
    isEnabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    alertMethods JSON NOT NULL COMMENT '告警方式：["email", "sms", "notification"]',
    recipients JSON NOT NULL COMMENT '告警接收人列表',
    alertFrequency ENUM('immediate', 'hourly', 'daily') NOT NULL DEFAULT 'immediate' COMMENT '告警频率',
    lastAlertTime DATETIME NULL COMMENT '最后告警时间',
    createdBy INT NOT NULL COMMENT '创建人ID',
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_workplace_id (workplaceId),
    INDEX idx_category_id (categoryId),
    INDEX idx_product_id (productId),
    INDEX idx_alert_type (alertType),
    INDEX idx_is_enabled (isEnabled),

    CONSTRAINT fk_sac_workplace FOREIGN KEY (workplaceId) REFERENCES workplaces(id) ON DELETE CASCADE,
    CONSTRAINT fk_sac_category FOREIGN KEY (categoryId) REFERENCES categories(id) ON DELETE CASCADE,
    CONSTRAINT fk_sac_product FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE,
    CONSTRAINT fk_sac_creator FOREIGN KEY (createdBy) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='库存告警配置表';
```

**告警服务实现**：
```javascript
// server/services/stockAlertService.js
class StockAlertService {
  /**
   * 检查库存告警
   */
  static async checkStockAlerts() {
    try {
      console.log('开始检查库存告警...');

      // 获取所有启用的告警配置
      const alertConfigs = await StockAlertConfig.findAll({
        where: { isEnabled: true },
        include: [
          { model: Workplace, required: false },
          { model: Category, required: false },
          { model: Product, required: false }
        ]
      });

      const alertResults = [];

      for (const config of alertConfigs) {
        const alerts = await this.checkConfigAlerts(config);
        alertResults.push(...alerts);
      }

      // 发送告警通知
      for (const alert of alertResults) {
        await this.sendAlert(alert);
      }

      console.log(`库存告警检查完成，共发送 ${alertResults.length} 条告警`);
      return alertResults;

    } catch (error) {
      console.error('检查库存告警失败:', error);
      throw error;
    }
  }

  /**
   * 检查单个配置的告警
   */
  static async checkConfigAlerts(config) {
    const alerts = [];

    // 构建查询条件
    const whereCondition = {};
    if (config.workplaceId) whereCondition.workplaceId = config.workplaceId;
    if (config.productId) whereCondition.productId = config.productId;

    const productWhereCondition = {};
    if (config.categoryId) productWhereCondition.categoryId = config.categoryId;

    // 查询符合条件的库存记录
    const workplaceStocks = await ProductWorkplaceStock.findAll({
      where: whereCondition,
      include: [
        {
          model: Product,
          where: productWhereCondition,
          include: [{ model: Category }]
        },
        { model: Workplace }
      ]
    });

    for (const stock of workplaceStocks) {
      const shouldAlert = this.shouldTriggerAlert(stock, config);

      if (shouldAlert) {
        // 检查告警频率限制
        if (await this.checkAlertFrequency(config, stock)) {
          alerts.push({
            config,
            stock,
            alertType: config.alertType,
            message: this.generateAlertMessage(stock, config)
          });

          // 更新最后告警时间
          await config.update({ lastAlertTime: new Date() });
        }
      }
    }

    return alerts;
  }

  /**
   * 判断是否应该触发告警
   */
  static shouldTriggerAlert(stock, config) {
    switch (config.alertType) {
      case 'low_stock':
        return stock.stock <= config.threshold;
      case 'out_of_stock':
        return stock.stock === 0;
      case 'high_demand':
        // 基于最近的兑换频率判断
        return this.checkHighDemand(stock, config.threshold);
      default:
        return false;
    }
  }

  /**
   * 检查告警频率限制
   */
  static async checkAlertFrequency(config, stock) {
    if (!config.lastAlertTime) return true;

    const now = new Date();
    const lastAlert = new Date(config.lastAlertTime);
    const timeDiff = now - lastAlert;

    switch (config.alertFrequency) {
      case 'immediate':
        return true;
      case 'hourly':
        return timeDiff >= 60 * 60 * 1000; // 1小时
      case 'daily':
        return timeDiff >= 24 * 60 * 60 * 1000; // 24小时
      default:
        return true;
    }
  }

  /**
   * 生成告警消息
   */
  static generateAlertMessage(stock, config) {
    const product = stock.Product;
    const workplace = stock.Workplace;

    switch (config.alertType) {
      case 'low_stock':
        return `【库存告警】${workplace.name} - ${product.name} 库存偏低，当前库存：${stock.stock}，告警阈值：${config.threshold}`;
      case 'out_of_stock':
        return `【缺货告警】${workplace.name} - ${product.name} 已缺货，请及时补充库存`;
      case 'high_demand':
        return `【高需求告警】${workplace.name} - ${product.name} 需求量激增，建议增加库存`;
      default:
        return `【库存告警】${workplace.name} - ${product.name} 触发告警`;
    }
  }

  /**
   * 发送告警通知
   */
  static async sendAlert(alert) {
    const { config, message } = alert;
    const methods = JSON.parse(config.alertMethods);
    const recipients = JSON.parse(config.recipients);

    for (const method of methods) {
      switch (method) {
        case 'email':
          await this.sendEmailAlert(recipients, message, alert);
          break;
        case 'sms':
          await this.sendSmsAlert(recipients, message, alert);
          break;
        case 'notification':
          await this.sendSystemNotification(recipients, message, alert);
          break;
      }
    }

    // 记录告警日志
    await StockAlertLog.create({
      configId: config.id,
      productId: alert.stock.productId,
      workplaceId: alert.stock.workplaceId,
      alertType: config.alertType,
      message,
      recipients: config.recipients,
      alertMethods: config.alertMethods,
      stockLevel: alert.stock.stock,
      threshold: config.threshold
    });
  }

  /**
   * 发送邮件告警
   */
  static async sendEmailAlert(recipients, message, alert) {
    // 实现邮件发送逻辑
    console.log('发送邮件告警:', message, '收件人:', recipients);
  }

  /**
   * 发送短信告警
   */
  static async sendSmsAlert(recipients, message, alert) {
    // 实现短信发送逻辑
    console.log('发送短信告警:', message, '收件人:', recipients);
  }

  /**
   * 发送系统通知
   */
  static async sendSystemNotification(recipients, message, alert) {
    for (const userId of recipients) {
      await Notification.create({
        userId,
        title: '库存告警',
        content: message,
        type: 'stock_alert',
        relatedType: 'product',
        relatedId: alert.stock.productId,
        priority: 'high'
      });
    }
  }

  /**
   * 检查高需求情况
   */
  static async checkHighDemand(stock, threshold) {
    // 查询最近7天的兑换记录
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const recentExchanges = await Exchange.count({
      where: {
        productId: stock.productId,
        stockWorkplaceId: stock.workplaceId,
        createdAt: { [Op.gte]: sevenDaysAgo },
        status: ['pending', 'approved', 'shipped', 'completed']
      }
    });

    // 如果最近7天的兑换次数超过阈值，认为是高需求
    return recentExchanges >= threshold;
  }
}

module.exports = StockAlertService;
```

### 10.4 数据一致性和业务规则

#### 10.4.1 库存同步机制

**设计策略**：
- 提供单一库存模式和职场库存模式的平滑切换
- 自动数据同步和一致性检查
- 支持手动触发同步操作

**同步服务实现**：
```javascript
// server/services/stockSyncService.js
class StockSyncService {
  /**
   * 切换库存管理模式
   */
  static async switchStockMode(productId, newMode, options = {}) {
    const transaction = await sequelize.transaction();

    try {
      const product = await Product.findByPk(productId, { transaction });
      if (!product) {
        throw new Error('商品不存在');
      }

      const oldMode = product.stockManagementType;
      if (oldMode === newMode) {
        await transaction.rollback();
        return { success: true, message: '库存管理模式未发生变化' };
      }

      if (newMode === 'workplace') {
        // 切换到职场分配模式
        await this.switchToWorkplaceMode(product, options, transaction);
      } else {
        // 切换到单一库存模式
        await this.switchToSingleMode(product, options, transaction);
      }

      // 更新商品的库存管理模式
      await product.update({
        stockManagementType: newMode,
        stockSyncedAt: new Date()
      }, { transaction });

      // 记录模式切换日志
      await StockOperationLog.create({
        productId,
        workplaceId: null,
        operationType: 'sync',
        beforeStock: 0,
        afterStock: 0,
        changeAmount: 0,
        reason: `库存管理模式切换：${oldMode} → ${newMode}`,
        operatorId: options.operatorId,
        operatorName: options.operatorName || 'SYSTEM'
      }, { transaction });

      await transaction.commit();

      return {
        success: true,
        message: `库存管理模式已切换为${newMode === 'workplace' ? '职场分配' : '单一库存'}`,
        oldMode,
        newMode
      };

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 切换到职场分配模式
   */
  static async switchToWorkplaceMode(product, options, transaction) {
    const {
      defaultWorkplaceId = null,
      distributionStrategy = 'default' // default, average, custom
    } = options;

    // 获取活跃职场
    const activeWorkplaces = await Workplace.findAll({
      where: { isActive: true },
      order: [['name', 'ASC']],
      transaction
    });

    if (activeWorkplaces.length === 0) {
      throw new Error('没有活跃的职场，无法切换到职场分配模式');
    }

    const currentStock = product.stock || 0;

    // 检查是否已有职场库存记录
    const existingStocks = await ProductWorkplaceStock.findAll({
      where: { productId: product.id },
      transaction
    });

    if (existingStocks.length > 0) {
      // 已有职场库存记录，只需更新模式
      return;
    }

    // 根据分配策略创建职场库存记录
    switch (distributionStrategy) {
      case 'default':
        // 分配到默认职场或第一个职场
        const targetWorkplace = defaultWorkplaceId
          ? activeWorkplaces.find(wp => wp.id === defaultWorkplaceId)
          : activeWorkplaces[0];

        if (targetWorkplace) {
          await ProductWorkplaceStock.create({
            productId: product.id,
            workplaceId: targetWorkplace.id,
            stock: currentStock,
            minStockAlert: 10
          }, { transaction });
        }
        break;

      case 'average':
        // 平均分配到所有职场
        const stockPerWorkplace = Math.floor(currentStock / activeWorkplaces.length);
        const remainder = currentStock % activeWorkplaces.length;

        for (let i = 0; i < activeWorkplaces.length; i++) {
          const workplace = activeWorkplaces[i];
          const allocatedStock = stockPerWorkplace + (i < remainder ? 1 : 0);

          await ProductWorkplaceStock.create({
            productId: product.id,
            workplaceId: workplace.id,
            stock: allocatedStock,
            minStockAlert: 10
          }, { transaction });
        }
        break;

      case 'custom':
        // 自定义分配（需要在options中提供分配方案）
        const customDistribution = options.customDistribution || [];
        for (const allocation of customDistribution) {
          await ProductWorkplaceStock.create({
            productId: product.id,
            workplaceId: allocation.workplaceId,
            stock: allocation.stock,
            minStockAlert: allocation.minStockAlert || 10
          }, { transaction });
        }
        break;
    }
  }

  /**
   * 切换到单一库存模式
   */
  static async switchToSingleMode(product, options, transaction) {
    // 获取所有职场库存
    const workplaceStocks = await ProductWorkplaceStock.findAll({
      where: { productId: product.id },
      transaction
    });

    // 计算总库存
    const totalStock = workplaceStocks.reduce((sum, ws) => sum + ws.stock, 0);

    // 更新商品总库存
    await product.update({ stock: totalStock }, { transaction });

    // 删除职场库存记录（可选择保留历史记录）
    if (options.clearWorkplaceStocks !== false) {
      for (const workplaceStock of workplaceStocks) {
        await StockOperationLog.create({
          productId: product.id,
          workplaceId: workplaceStock.workplaceId,
          operationType: 'sync',
          beforeStock: workplaceStock.stock,
          afterStock: 0,
          changeAmount: -workplaceStock.stock,
          reason: '切换到单一库存模式，清除职场库存记录',
          operatorId: options.operatorId,
          operatorName: options.operatorName || 'SYSTEM'
        }, { transaction });

        await workplaceStock.destroy({ transaction });
      }
    }
  }

  /**
   * 数据一致性检查
   */
  static async checkDataConsistency(productIds = []) {
    const whereCondition = productIds.length > 0
      ? { id: productIds, stockManagementType: 'workplace' }
      : { stockManagementType: 'workplace' };

    const products = await Product.findAll({
      where: whereCondition,
      include: [{
        model: ProductWorkplaceStock,
        required: false
      }]
    });

    const inconsistencies = [];

    for (const product of products) {
      const workplaceStockTotal = product.ProductWorkplaceStocks
        ? product.ProductWorkplaceStocks.reduce((sum, ws) => sum + ws.stock, 0)
        : 0;

      if (product.stock !== workplaceStockTotal) {
        inconsistencies.push({
          productId: product.id,
          productName: product.name,
          productStock: product.stock,
          workplaceStockTotal,
          difference: product.stock - workplaceStockTotal
        });
      }
    }

    return inconsistencies;
  }

  /**
   * 修复数据一致性问题
   */
  static async fixDataConsistency(inconsistencies, strategy = 'sync_to_workplace') {
    const transaction = await sequelize.transaction();

    try {
      const fixResults = [];

      for (const inconsistency of inconsistencies) {
        const product = await Product.findByPk(inconsistency.productId, { transaction });

        switch (strategy) {
          case 'sync_to_workplace':
            // 以职场库存为准，更新商品总库存
            await product.update({
              stock: inconsistency.workplaceStockTotal,
              stockSyncedAt: new Date()
            }, { transaction });

            fixResults.push({
              productId: inconsistency.productId,
              action: 'updated_product_stock',
              oldValue: inconsistency.productStock,
              newValue: inconsistency.workplaceStockTotal
            });
            break;

          case 'sync_to_product':
            // 以商品总库存为准，重新分配职场库存
            const workplaceStocks = await ProductWorkplaceStock.findAll({
              where: { productId: inconsistency.productId },
              transaction
            });

            if (workplaceStocks.length > 0) {
              // 按比例重新分配
              const totalWorkplaceStock = inconsistency.workplaceStockTotal;
              const targetStock = inconsistency.productStock;

              for (const ws of workplaceStocks) {
                const ratio = totalWorkplaceStock > 0 ? ws.stock / totalWorkplaceStock : 1 / workplaceStocks.length;
                const newStock = Math.round(targetStock * ratio);

                await ws.update({
                  stock: newStock,
                  lastStockUpdate: new Date()
                }, { transaction });
              }
            }

            fixResults.push({
              productId: inconsistency.productId,
              action: 'redistributed_workplace_stocks',
              targetStock: inconsistency.productStock
            });
            break;
        }

        // 记录修复日志
        await StockOperationLog.create({
          productId: inconsistency.productId,
          workplaceId: null,
          operationType: 'sync',
          beforeStock: inconsistency.productStock,
          afterStock: strategy === 'sync_to_workplace' ? inconsistency.workplaceStockTotal : inconsistency.productStock,
          changeAmount: 0,
          reason: `数据一致性修复：${strategy}`,
          operatorId: null,
          operatorName: 'SYSTEM'
        }, { transaction });
      }

      await transaction.commit();
      return fixResults;

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 定时同步任务
   */
  static async scheduledSync() {
    try {
      console.log('开始执行库存数据同步检查...');

      // 检查数据一致性
      const inconsistencies = await this.checkDataConsistency();

      if (inconsistencies.length > 0) {
        console.log(`发现 ${inconsistencies.length} 个数据不一致问题`);

        // 自动修复（以职场库存为准）
        const fixResults = await this.fixDataConsistency(inconsistencies, 'sync_to_workplace');

        console.log('数据一致性问题已自动修复:', fixResults);

        // 发送告警通知
        await this.sendSyncAlert(inconsistencies, fixResults);
      } else {
        console.log('库存数据一致性检查通过');
      }

    } catch (error) {
      console.error('库存数据同步检查失败:', error);
    }
  }

  /**
   * 发送同步告警
   */
  static async sendSyncAlert(inconsistencies, fixResults) {
    const message = `库存数据同步告警：发现 ${inconsistencies.length} 个不一致问题，已自动修复 ${fixResults.length} 个`;

    // 发送系统通知给管理员
    const adminUsers = await User.findAll({
      where: { role: 'admin', isActive: true }
    });

    for (const admin of adminUsers) {
      await Notification.create({
        userId: admin.id,
        title: '库存数据同步告警',
        content: message,
        type: 'system_alert',
        priority: 'high',
        data: JSON.stringify({ inconsistencies, fixResults })
      });
    }
  }
}

module.exports = StockSyncService;
```

## 11. 总结

本设计文档提供了商品库存管理系统多职场库存分配功能的完整优化方案，包括：

### 🎯 **核心功能特点**

1. **完整的数据库设计**：新增职场库存表、操作日志表，优化现有表结构
2. **全面的API接口**：支持职场库存查询、更新、转移等操作
3. **用户友好的前端界面**：职场库存管理弹窗、兑换职场选择等
4. **安全的数据迁移**：渐进式迁移策略，完整的回滚方案
5. **详细的技术实现**：Sequelize模型、服务类、校验规则等
6. **全面的测试方案**：单元测试、集成测试、性能测试
7. **完善的风险控制**：风险评估、监控告警、应急预案
8. **系统模块影响分析**：商品管理、用户端功能、系统管理等全面考虑
9. **数据一致性保障**：同步机制、告警系统、自动修复功能
10. **权限控制设计**：角色权限、操作审计、安全防护

### 🔧 **兑换流程设计特点**

#### **用户端体验**
- **保持原有流程**：兑换界面和操作流程保持不变，降低用户学习成本
- **简化库存显示**：只显示商品总库存，避免用户困惑
- **职场选择自由**：用户可选择任意职场，不受当前库存限制
- **智能状态提示**：清晰说明订单处理状态和后续流程

#### **管理员控制**
- **手动库存调配**：移除所有自动调货机制，确保管理员完全控制
- **待处理订单管理**：专门的管理界面处理需要调配的订单
- **灵活调配策略**：支持职场间库存转移，满足不同业务需求
- **完整操作记录**：所有调配操作都有详细的日志记录

#### **业务逻辑优化**
- **总库存验证**：基于总库存判断是否允许兑换申请
- **智能订单状态**：根据职场库存情况自动设置订单状态
- **分离库存管理**：兑换申请和库存调配分离，提高系统灵活性
- **数据一致性**：确保库存数据在各个环节的准确性

### 📊 **技术架构亮点**

#### **数据库设计**
- **职场库存表**：支持细粒度的库存管理
- **订单状态扩展**：新增库存调配相关状态字段
- **操作日志完整**：记录所有库存变更的详细信息

#### **后端服务**
- **StockService**：核心库存管理服务，支持多种操作模式
- **ExchangeController**：兑换控制器，处理订单创建和状态管理
- **事务保护**：所有关键操作都在数据库事务中执行

#### **前端组件**
- **动态表格**：根据职场数量自动调整显示列
- **管理员界面**：专门的库存调配和订单管理界面
- **用户体验优化**：简洁明了的兑换流程

### 🛡️ **系统优势**

1. **业务灵活性**：支持复杂的多职场库存管理需求
2. **操作可控性**：管理员完全控制库存调配过程
3. **数据准确性**：完整的数据验证和一致性检查
4. **扩展性强**：支持未来更多职场和功能扩展
5. **维护性好**：清晰的代码结构和完整的文档

该方案确保了系统的稳定性、可扩展性和用户体验，为多职场库存管理提供了坚实的技术基础，同时满足了业务对手动控制和灵活管理的需求。
