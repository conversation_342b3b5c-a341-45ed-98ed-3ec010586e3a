<template>
  <div class="dashboard-container">
    <!-- 页面标题和描述 -->
    <div class="dashboard-header">
      <div class="header-left">
        <h1 class="page-title">
          数据分析仪表盘
        </h1>
        <p class="page-description">
          查看系统关键数据和统计图表
        </p>
      </div>
      <div class="header-actions">
        <el-dropdown @command="exportData">
          <el-button type="primary">
            导出数据
            <el-icon class="el-icon--right">
              <component :is="ElementPlusIcons.ArrowDown" />
            </el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="excel">
                导出Excel
              </el-dropdown-item>
              <el-dropdown-item command="pdf">
                导出PDF
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button :loading="isRefreshing" @click="refreshDashboard">
          <el-icon><component :is="ElementPlusIcons.Refresh" /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 指标卡片区域 -->
    <el-row :gutter="20" class="stat-cards">
      <el-col
        v-for="(stat, index) in statCards"
        :key="index"
        :xs="12"
        :sm="12"
        :md="6"
        :lg="6"
      >
        <el-card :body-style="{ padding: '24px' }" class="stat-card" :class="stat.type">
          <div class="stat-card-content">
            <div class="stat-icon">
              <el-icon :size="32">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">
                {{ stat.value }}
                <span v-if="stat.unit" class="value-unit">{{ stat.unit }}</span>
              </div>
              <div class="stat-label">
                {{ stat.label }}
              </div>
              <div class="stat-trend" :class="stat.trendDirection">
                <el-icon :size="14">
                  <component :is="getTrendIcon(stat.trendDirection)" />
                </el-icon>
                <span>{{ Math.abs(stat.trendValue) }}%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="chart-section">
      <!-- 销售趋势图 -->
      <el-col :xs="24" :lg="16">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <h3>销售趋势分析</h3>
              <div class="chart-actions">
                <el-radio-group v-model="salesTrendPeriod" size="small">
                  <el-radio-button label="week">
                    周
                  </el-radio-button>
                  <el-radio-button label="month">
                    月
                  </el-radio-button>
                  <el-radio-button label="year">
                    年
                  </el-radio-button>
                </el-radio-group>
                <el-tooltip content="刷新数据" placement="top">
                  <el-button
                    size="small"
                    circle
                    style="margin-left: 10px;"
                    @click="fetchSalesTrend(salesTrendPeriod)"
                  >
                    <el-icon><component :is="ElementPlusIcons.Refresh" /></el-icon>
                  </el-button>
                </el-tooltip>
              </div>
            </div>
          </template>
          <div v-loading="isLoadingSalesChart" class="chart-container">
            <VChart
              v-if="!chartError.sales"
              class="chart"
              :option="salesChartOption"
              autoresize
            />
            <div v-if="chartError.sales" class="chart-error">
              <el-empty description="图表加载失败" :image-size="80">
                <el-button type="primary" @click="fetchSalesTrend(salesTrendPeriod)">
                  重新加载
                </el-button>
              </el-empty>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 分类占比图 -->
      <el-col :xs="24" :lg="8">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <h3>商品分类占比</h3>
              <div class="chart-actions">
                <el-tooltip content="刷新数据" placement="top">
                  <el-button size="small" circle @click="refreshCategoryData">
                    <el-icon><component :is="ElementPlusIcons.Refresh" /></el-icon>
                  </el-button>
                </el-tooltip>
              </div>
            </div>
          </template>
          <div v-loading="isLoadingCategoryData" class="chart-container">
            <VChart
              v-if="!chartError.category"
              class="chart"
              :option="categoryChartOption"
              autoresize
            />
            <div v-if="chartError.category" class="chart-error">
              <el-empty description="图表加载失败" :image-size="80">
                <el-button type="primary" @click="refreshCategoryData">
                  重新加载
                </el-button>
              </el-empty>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 库存趋势图表区域 -->
    <el-row :gutter="20" class="chart-section">
      <!-- 库存变化趋势图 -->
      <el-col :xs="24" :lg="24">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <h3>库存变化趋势分析</h3>
              <div class="chart-actions">
                <el-select
                  v-model="stockTrendCategory"
                  placeholder="选择分类"
                  size="small"
                  style="margin-right: 10px; width: 120px;"
                >
                  <el-option label="全部分类" value="all" />
                  <el-option
                    v-for="category in categoryData"
                    :key="category.id"
                    :label="category.name"
                    :value="category.id"
                  />
                </el-select>
                <el-radio-group v-model="stockTrendPeriod" size="small">
                  <el-radio-button label="week">
                    周
                  </el-radio-button>
                  <el-radio-button label="month">
                    月
                  </el-radio-button>
                  <el-radio-button label="year">
                    年
                  </el-radio-button>
                </el-radio-group>
                <el-tooltip content="刷新数据" placement="top">
                  <el-button
                    size="small"
                    circle
                    style="margin-left: 10px;"
                    @click="fetchStockTrend(stockTrendPeriod, stockTrendCategory)"
                  >
                    <el-icon><component :is="ElementPlusIcons.Refresh" /></el-icon>
                  </el-button>
                </el-tooltip>
              </div>
            </div>
          </template>
          <div class="chart-container">
            <div v-if="stockTrendData.currentTotalStock > 0" class="stock-summary">
              <el-tag type="info" size="large">
                当前总库存：{{ stockTrendData.currentTotalStock.toLocaleString() }} 件
              </el-tag>
            </div>
            <VChart
              v-loading="isLoadingStockTrend"
              class="chart"
              :option="stockChartOption"
              autoresize
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 中间区域：用户活动和销售地区 -->
    <el-row :gutter="20" class="chart-section">
      <!-- 用户活跃度分析 - 占据整行宽度 -->
      <el-col :xs="24" :lg="24">
        <el-card class="chart-card user-activity-card">
          <template #header>
            <div class="chart-header">
              <h3>用户活跃度分析</h3>
              <div class="chart-actions">
                <el-select v-model="userActivityPeriod" placeholder="选择时段" size="small">
                  <el-option label="最近7天" value="7days" />
                  <el-option label="最近30天" value="30days" />
                  <el-option label="最近90天" value="90days" />
                </el-select>
                <el-tooltip content="刷新数据" placement="top">
                  <el-button
                    size="small"
                    circle
                    style="margin-left: 10px;"
                    @click="fetchUserActivity(userActivityPeriod)"
                  >
                    <el-icon><component :is="ElementPlusIcons.Refresh" /></el-icon>
                  </el-button>
                </el-tooltip>
              </div>
            </div>
          </template>
          <div class="chart-container expanded-chart">
            <VChart class="chart" :option="userActivityOption" autoresize />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 销售职场分布区域 -->
    <el-row :gutter="20" class="chart-section">
      <!-- 销售职场分布（光年币） -->
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <h3>销售职场分布（光年币）</h3>
              <div class="chart-actions">
                <el-button size="small" circle @click="refreshWorkplaceStats">
                  <el-icon><component :is="ElementPlusIcons.Refresh" /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
          <div class="chart-container">
            <VChart class="chart" :option="lyWorkplaceOption" autoresize />
          </div>
        </el-card>
      </el-col>

      <!-- 销售职场分布（人民币） -->
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <h3>销售职场分布（人民币）</h3>
              <div class="chart-actions">
                <el-button size="small" circle @click="refreshWorkplaceStats">
                  <el-icon><component :is="ElementPlusIcons.Refresh" /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
          <div class="chart-container">
            <VChart class="chart" :option="rmbWorkplaceOption" autoresize />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 支付偏好分析 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <PaymentPreferenceCard />
      </el-col>
    </el-row>

    <!-- 最近订单和热门商品表格 -->
    <el-row :gutter="20" class="data-tables">
      <!-- 最近订单 -->
      <el-col :xs="24" :lg="14">
        <el-card class="table-card">
          <template #header>
            <div class="table-header">
              <h3>最近订单</h3>
              <div>
                <el-button size="small" circle @click="refreshOrders">
                  <el-icon><component :is="ElementPlusIcons.Refresh" /></el-icon>
                </el-button>
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click="viewAllOrders"
                >
                  查看全部
                  <el-icon><component :is="ElementPlusIcons.ArrowRight" /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
          <div class="table-container">
            <el-table
              v-loading="isLoadingOrders"
              :data="recentOrders"
              style="width: 100%"
              size="default"
              :max-height="400"
              stripe
              :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
            >
              <el-table-column
                prop="id"
                label="订单号"
                width="80"
                show-overflow-tooltip
              />
              <el-table-column
                prop="customer"
                label="姓名"
                width="90"
                show-overflow-tooltip
              />
              <el-table-column
                prop="productName"
                label="商品"
                min-width="120"
                show-overflow-tooltip
              />
              <el-table-column
                prop="quantity"
                label="数量"
                width="70"
                align="center"
              />
              <el-table-column prop="date" label="日期" width="90" />
              <el-table-column label="支付方式" width="90" align="center">
                <template #default="scope">
                  <el-tag
                    v-if="scope.row.paymentMethod === 'ly'"
                    type="primary"
                    effect="light"
                    size="small"
                  >
                    光年币
                  </el-tag>
                  <el-tag
                    v-else
                    type="success"
                    effect="light"
                    size="small"
                  >
                    人民币
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="总收入" min-width="110" align="center">
                <template #default="scope">
                  <span v-if="scope.row.paymentMethod === 'ly'" class="order-amount ly">
                    {{ scope.row.amount.toLocaleString() }}
                  </span>
                  <span v-else class="order-amount rmb">
                    ¥{{ scope.row.amount.toFixed(2) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="状态" width="80" align="center">
                <template #default="scope">
                  <el-tag :type="getStatusTag(scope.row.status)" size="small">
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-if="recentOrders.length === 0 && !isLoadingOrders" class="empty-data">
            暂无订单数据
          </div>

          <!-- 显示数据统计信息 -->
          <div v-if="recentOrders.length > 0" class="table-footer">
            <span class="data-count">显示最近 {{ recentOrders.length }} 条订单</span>
            <el-button
              size="small"
              type="primary"
              text
              @click="viewAllOrders"
            >
              查看全部订单
            </el-button>
          </div>
        </el-card>
      </el-col>

      <!-- 热门商品 -->
      <el-col :xs="24" :lg="10">
        <el-card class="table-card">
          <template #header>
            <div class="table-header">
              <h3>热门商品</h3>
              <div>
                <el-button size="small" circle @click="refreshProducts">
                  <el-icon><component :is="ElementPlusIcons.Refresh" /></el-icon>
                </el-button>
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click="viewAllProducts"
                >
                  查看全部
                  <el-icon><component :is="ElementPlusIcons.ArrowRight" /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
          <div class="table-container">
            <el-table
              v-loading="isLoadingProducts"
              :data="popularProducts"
              style="width: 100%"
              size="default"
              :max-height="400"
              stripe
              :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
            >
              <el-table-column width="60" label="图片">
                <template #default="scope">
                  <el-avatar
                    shape="square"
                    :size="36"
                    :src="scope.row.image"
                    fit="cover"
                  >
                    <el-icon><component :is="ElementPlusIcons.Goods" /></el-icon>
                  </el-avatar>
                </template>
              </el-table-column>
              <el-table-column
                prop="name"
                label="商品名称"
                min-width="120"
                show-overflow-tooltip
              />
              <el-table-column
                prop="category"
                label="分类"
                width="100"
                show-overflow-tooltip
              />
              <el-table-column
                prop="sales"
                label="兑换量"
                width="80"
                align="center"
              />
            </el-table>
          </div>
          <div v-if="popularProducts.length === 0 && !isLoadingProducts" class="empty-data">
            暂无热门商品数据
          </div>

          <!-- 显示数据统计信息 -->
          <div v-if="popularProducts.length > 0" class="table-footer">
            <span class="data-count">显示前 {{ popularProducts.length }} 个热门商品</span>
            <el-button
              size="small"
              type="primary"
              text
              @click="viewAllProducts"
            >
              查看全部商品
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
// 简化ECharts导入
import * as echarts from 'echarts';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
// 简化图标导入
import * as ElementPlusIcons from '@element-plus/icons-vue';

// 导入API
import { getProducts, getPopularProducts } from '../../api/products';
import { getCategoriesWithProductCount } from '../../api/categories';
import { getAllFeedbacks } from '../../api/feedback';
import { getRecentExchanges, getWorkplaceExchangeStats } from '../../api/exchanges';
import { getDashboardStats, getSalesTrend, getStockTrend, getUserActivity, exportDashboardData } from '../../api/dashboard';

// 导入 vue-echarts
import VChart from 'vue-echarts';

// 导入支付偏好分析组件
import PaymentPreferenceCard from '../../components/PaymentPreferenceCard.vue';

const router = useRouter();

// 状态卡片数据
const statCards = ref([
  {
    icon: ElementPlusIcons.ShoppingCart,
    label: '总订单数',
    value: '加载中...',
    trendDirection: 'up',
    trendValue: 0,
    type: 'primary'
  },
  {
    icon: ElementPlusIcons.Money,
    label: '人民币收入',
    value: '加载中...',
    trendDirection: 'up',
    trendValue: 0,
    type: 'danger'
  },
  {
    icon: ElementPlusIcons.Star,
    label: '光年币收入',
    value: '加载中...',
    unit: '个光年币',
    trendDirection: 'up',
    trendValue: 0,
    type: 'warning'
  },
  {
    icon: ElementPlusIcons.User,
    label: '活跃用户',
    value: '加载中...',
    trendDirection: 'up',
    trendValue: 0,
    type: 'info'
  },
  {
    icon: ElementPlusIcons.Goods,
    label: '商品总数',
    value: '加载中...',
    trendDirection: 'up',
    trendValue: 0,
    type: 'success'
  },
  {
    icon: ElementPlusIcons.ChatDotRound,
    label: '反馈数量',
    value: '加载中...',
    trendDirection: 'up',
    trendValue: 0,
    type: 'warning'
  },
  {
    icon: ElementPlusIcons.ShoppingBag,
    label: '总兑换数量',
    value: '加载中...',
    trendDirection: 'up',
    trendValue: 0,
    type: 'primary'
  },
  {
    icon: ElementPlusIcons.Box,
    label: '总库存数量',
    value: '加载中...',
    trendDirection: 'up',
    trendValue: 0,
    type: 'success'
  }
]);

// 商品分类数据
const categoryData = ref([]);
const isLoadingCategoryData = ref(false);

// 是否正在加载统计数据
const isLoadingStats = ref(false);

// 获取仪表盘综合统计数据
const fetchDashboardStats = async (retryCount = 0) => {
  try {
    console.log('[Dashboard-DEBUG] 开始获取仪表盘统计数据');
    isLoadingStats.value = true;

    const stats = await getDashboardStats();
    console.log('[Dashboard-DEBUG] 仪表盘统计数据原始数据:', JSON.stringify(stats));

    if (!stats) {
      console.error('[Dashboard-DEBUG] 仪表盘统计数据为空');
      throw new Error('仪表盘统计数据为空');
    }

    // 更新总订单数卡片
    if (stats.totalSales && typeof stats.totalSales.value !== 'undefined') {
      console.log('[Dashboard-DEBUG] 更新总订单数卡片:', stats.totalSales.value);
      statCards.value[0].value = `${parseInt(stats.totalSales.value).toLocaleString()}`;
      statCards.value[0].trendDirection = stats.totalSales.trend || 'up';
      statCards.value[0].trendValue = stats.totalSales.growth || 0;
    } else {
      console.log('[Dashboard-DEBUG] 总订单数数据缺失，使用默认值');
      statCards.value[0].value = '0';
    }

    // 更新人民币收入卡片
    if (stats.rmbSales && typeof stats.rmbSales.value !== 'undefined') {
      const rmbValue = parseFloat(stats.rmbSales.value);
      console.log('[Dashboard-DEBUG] 更新人民币收入卡片:', rmbValue);
      statCards.value[1].value = `¥${rmbValue.toFixed(2)}`;
      statCards.value[1].trendDirection = stats.rmbSales.trend || 'up';
      statCards.value[1].trendValue = stats.rmbSales.growth || 0;
    } else {
      console.log('[Dashboard-DEBUG] 人民币收入数据缺失，使用默认值');
      statCards.value[1].value = '¥0.00';
    }

    // 更新光年币收入卡片
    if (stats.lySales && typeof stats.lySales.value !== 'undefined') {
      const lyValue = parseInt(stats.lySales.value);
      console.log('[Dashboard-DEBUG] 更新光年币收入卡片:', lyValue);
      statCards.value[2].value = `${lyValue.toLocaleString()}`;
      statCards.value[2].unit = '个光年币';
      statCards.value[2].trendDirection = stats.lySales.trend || 'up';
      statCards.value[2].trendValue = stats.lySales.growth || 0;
    } else {
      console.log('[Dashboard-DEBUG] 光年币收入数据缺失，使用默认值');
      statCards.value[2].value = '0';
    }

    // 更新活跃用户卡片
    if (stats.activeUsers) {
      statCards.value[3].value = stats.activeUsers.value.toLocaleString();
      statCards.value[3].trendDirection = stats.activeUsers.trend || 'up';
      statCards.value[3].trendValue = stats.activeUsers.growth || 0;
    } else {
      statCards.value[3].value = '0';
    }

    // 更新商品总数卡片
    if (stats.totalProducts) {
      statCards.value[4].value = stats.totalProducts.value.toLocaleString();
      statCards.value[4].trendDirection = stats.totalProducts.trend || 'up';
      statCards.value[4].trendValue = stats.totalProducts.growth || 0;
    } else {
      statCards.value[4].value = '0';
    }

    // 更新反馈数量卡片
    if (stats.totalFeedbacks) {
      statCards.value[5].value = stats.totalFeedbacks.value.toLocaleString();
      statCards.value[5].trendDirection = stats.totalFeedbacks.trend || 'up';
      statCards.value[5].trendValue = stats.totalFeedbacks.growth || 0;
    } else {
      statCards.value[5].value = '0';
    }

    // 更新总兑换数量卡片
    if (stats.totalExchangeQuantity) {
      statCards.value[6].value = stats.totalExchangeQuantity.value.toLocaleString();
      statCards.value[6].trendDirection = stats.totalExchangeQuantity.trend || 'up';
      statCards.value[6].trendValue = stats.totalExchangeQuantity.growth || 0;
    } else {
      statCards.value[6].value = '0';
    }

    // 更新总库存数量卡片
    if (stats.totalStock) {
      statCards.value[7].value = stats.totalStock.value.toLocaleString();
      statCards.value[7].trendDirection = stats.totalStock.trend || 'up';
      statCards.value[7].trendValue = stats.totalStock.growth || 0;
    } else {
      statCards.value[7].value = '0';
    }

    console.log('[Dashboard] 仪表盘统计数据加载成功');
  } catch (error) {
    console.error('[Dashboard] 获取仪表盘统计数据失败:', error);

    // 重试逻辑 - 最多重试3次
    if (retryCount < 3) {
      console.log(`[Dashboard] 尝试重新获取仪表盘数据，第 ${retryCount + 1} 次重试...`);
      setTimeout(() => {
        fetchDashboardStats(retryCount + 1);
      }, 1000 * (retryCount + 1)); // 逐渐增加延迟时间
      return;
    }

    // 超过重试次数，显示错误
    ElMessage.error('获取统计数据失败，请刷新页面重试或重新登录');

    // 将所有卡片显示为默认值
    statCards.value.forEach((card, index) => {
      if (index === 2) { // 光年币收入
        card.value = '0';
      } else if (index === 0 || index === 1) { // 总销售额和人民币收入
        card.value = index === 0 ? '0' : '¥0.00';
      } else { // 其他卡片
        card.value = '0';
      }
      card.trendDirection = 'down';
      card.trendValue = 0;
    });
  } finally {
    isLoadingStats.value = false;
  }
};

// 获取商品分类占比数据
const fetchCategoryData = async () => {
  isLoadingCategoryData.value = true;
  chartError.value.category = false;
  try {
    const response = await getCategoriesWithProductCount();

    if (response && Array.isArray(response)) {
      // 转换数据格式以适应图表
      categoryData.value = response
        .filter(category => category.productCount > 0) // 只显示有商品的分类
        .map(category => ({
          name: category.name,
          value: category.productCount
        }));
    }
  } catch (error) {
    console.error('获取分类数据失败:', error);
    chartError.value.category = true;
    ElMessage.error('获取分类数据失败');
  } finally {
    isLoadingCategoryData.value = false;
  }
};

// 获取趋势图标
const getTrendIcon = (trend) => trend === 'up' ? ElementPlusIcons.CaretTop : ElementPlusIcons.CaretBottom;

// 销售趋势图选择的周期
const salesTrendPeriod = ref('month');

// 销售趋势数据
const salesChartData = ref({
  labels: [],
  rmbSales: [],
  lySales: [],
  orders: []
});

// 销售趋势数据加载状态
const isLoadingSalesTrend = ref(false);
const isLoadingSalesChart = ref(false);

// 图表错误状态
const chartError = ref({
  sales: false,
  category: false,
  stock: false
});

// 刷新状态
const isRefreshing = ref(false);

// 防抖函数
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 获取销售趋势数据
const fetchSalesTrend = async (period = 'month') => {
  try {
    console.log('获取销售趋势数据，周期:', period);
    isLoadingSalesTrend.value = true;
    isLoadingSalesChart.value = true;
    chartError.value.sales = false;

    const data = await getSalesTrend({ period });

    // 更新图表数据
    salesChartData.value = {
      labels: data.timeLabels,
      rmbSales: data.rmbSalesData,
      lySales: data.lySalesData,
      orders: data.orderData
    };

    console.log('销售趋势数据加载成功');
  } catch (error) {
    console.error('获取销售趋势数据失败:', error);
    chartError.value.sales = true;
    ElMessage.error('获取销售趋势数据失败');
  } finally {
    isLoadingSalesTrend.value = false;
    isLoadingSalesChart.value = false;
  }
};

// 监听销售趋势周期变化
watch(salesTrendPeriod, (newValue) => {
  fetchSalesTrend(newValue);
});

// 库存趋势数据
const stockTrendData = ref({
  labels: [],
  totalStock: [],
  stockIncrease: [],
  stockDecrease: [],
  netChange: [],
  currentTotalStock: 0
});

// 库存趋势数据加载状态
const isLoadingStockTrend = ref(false);

// 库存趋势选择的周期
const stockTrendPeriod = ref('month');

// 库存趋势选择的分类
const stockTrendCategory = ref('all');

// 获取库存趋势数据
const fetchStockTrend = async (period = 'month', category = 'all') => {
  try {
    console.log('获取库存趋势数据，周期:', period, '分类:', category);
    isLoadingStockTrend.value = true;

    const data = await getStockTrend({ period, category });

    // 更新图表数据
    stockTrendData.value = {
      labels: data.timeLabels,
      totalStock: data.totalStockData,
      stockIncrease: data.stockIncreaseData,
      stockDecrease: data.stockDecreaseData,
      netChange: data.netChangeData,
      currentTotalStock: data.currentTotalStock
    };

    console.log('库存趋势数据加载成功');
  } catch (error) {
    console.error('获取库存趋势数据失败:', error);
    ElMessage.error('获取库存趋势数据失败');
  } finally {
    isLoadingStockTrend.value = false;
  }
};

// 监听库存趋势周期变化
watch(stockTrendPeriod, (newValue) => {
  fetchStockTrend(newValue, stockTrendCategory.value);
});

// 监听库存趋势分类变化
watch(stockTrendCategory, (newValue) => {
  fetchStockTrend(stockTrendPeriod.value, newValue);
});

// 销售趋势图配置
const salesChartOption = computed(() => {
  if (isLoadingSalesTrend.value) {
    // 加载状态下显示加载提示
    return {
      title: {
        text: '数据加载中...',
        left: 'center',
        top: 'middle',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      }
    };
  }

  if (salesChartData.value.labels.length === 0) {
    // 无数据时显示提示
    return {
      title: {
        text: '暂无销售趋势数据',
        left: 'center',
        top: 'middle',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      }
    };
  }

  return {
    color: ['#F56C6C', '#E6A23C', '#409EFF'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params) {
        let tooltip = `${params[0].axisValue}<br/>`;

        // 遍历所有数据系列
        params.forEach(param => {
          const marker = param.marker;
          const seriesName = param.seriesName;
          let valueText = '';

          // 根据系列名称添加单位
          if (seriesName === '人民币收入') {
            valueText = `${param.value} 元`;
          } else if (seriesName === '光年币收入') {
            valueText = `${param.value} 个光年币`;
          } else {
            valueText = `${param.value} 单`;
          }

          tooltip += `${marker} ${seriesName}: ${valueText}<br/>`;
        });

        return tooltip;
      }
    },
    legend: {
      data: ['人民币收入', '光年币收入', '订单量']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: salesChartData.value.labels,
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '金额',
        position: 'left',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#F56C6C'
          }
        },
        axisLabel: {
          formatter: '{value} 元'
        }
      },
      {
        type: 'value',
        name: '光年币',
        position: 'right',
        offset: 60,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#E6A23C'
          }
        },
        axisLabel: {
          formatter: '{value} 个光年币'
        }
      },
      {
        type: 'value',
        name: '订单量',
        position: 'right',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#409EFF'
          }
        },
        axisLabel: {
          formatter: '{value} 单'
        }
      }
    ],
    series: [
      {
        name: '人民币收入',
        type: 'bar',
        barWidth: '30%',
        data: salesChartData.value.rmbSales,
        yAxisIndex: 0
      },
      {
        name: '光年币收入',
        type: 'bar',
        barWidth: '30%',
        data: salesChartData.value.lySales,
        yAxisIndex: 1
      },
      {
        name: '订单量',
        type: 'line',
        smooth: true,
        data: salesChartData.value.orders,
        yAxisIndex: 2
      }
    ]
  };
});

// 库存趋势图配置
const stockChartOption = computed(() => {
  if (isLoadingStockTrend.value) {
    // 加载状态下显示加载提示
    return {
      title: {
        text: '数据加载中...',
        left: 'center',
        top: 'middle',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      }
    };
  }

  if (stockTrendData.value.labels.length === 0) {
    // 无数据时显示提示
    return {
      title: {
        text: '暂无库存趋势数据',
        left: 'center',
        top: 'middle',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      }
    };
  }

  return {
    color: ['#67C23A', '#E6A23C', '#F56C6C', '#409EFF'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: function (params) {
        let tooltip = `${params[0].axisValue}<br/>`;

        // 遍历所有数据系列
        params.forEach(param => {
          const marker = param.marker;
          const seriesName = param.seriesName;
          let valueText = '';

          // 根据系列名称添加单位
          if (seriesName === '总库存') {
            valueText = `${param.value} 件`;
          } else if (seriesName === '库存增加') {
            valueText = `+${param.value} 件`;
          } else if (seriesName === '库存减少') {
            valueText = `-${param.value} 件`;
          } else if (seriesName === '净变化') {
            const sign = param.value >= 0 ? '+' : '';
            valueText = `${sign}${param.value} 件`;
          }

          tooltip += `${marker} ${seriesName}: ${valueText}<br/>`;
        });

        return tooltip;
      }
    },
    legend: {
      data: ['总库存', '库存增加', '库存减少', '净变化']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: stockTrendData.value.labels,
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '库存数量',
        position: 'left',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#67C23A'
          }
        },
        axisLabel: {
          formatter: '{value} 件'
        }
      },
      {
        type: 'value',
        name: '变化量',
        position: 'right',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#409EFF'
          }
        },
        axisLabel: {
          formatter: '{value} 件'
        }
      }
    ],
    series: [
      {
        name: '总库存',
        type: 'line',
        smooth: true,
        data: stockTrendData.value.totalStock,
        yAxisIndex: 0,
        lineStyle: {
          width: 3
        },
        emphasis: {
          focus: 'series'
        }
      },
      {
        name: '库存增加',
        type: 'bar',
        stack: 'change',
        data: stockTrendData.value.stockIncrease,
        yAxisIndex: 1,
        itemStyle: {
          color: '#E6A23C'
        }
      },
      {
        name: '库存减少',
        type: 'bar',
        stack: 'change',
        data: stockTrendData.value.stockDecrease,
        yAxisIndex: 1,
        itemStyle: {
          color: '#F56C6C'
        }
      },
      {
        name: '净变化',
        type: 'line',
        smooth: true,
        data: stockTrendData.value.netChange,
        yAxisIndex: 1,
        lineStyle: {
          type: 'dashed',
          width: 2
        },
        emphasis: {
          focus: 'series'
        }
      }
    ]
  };
});

// 分类占比图配置
const categoryChartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b} : {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    data: categoryData.value.map(item => item.name)
  },
  series: [
    {
      name: '分类占比',
      type: 'pie',
      radius: '55%',
      center: ['50%', '60%'],
      data: categoryData.value.length > 0 ? categoryData.value : [
        { value: 0, name: '暂无数据' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}));

// 用户活跃度选择的周期
const userActivityPeriod = ref('30days');

// 用户活跃度数据
const userActivityData = ref({
  labels: [],
  activeUsers: [],
  newUsers: []
});

// 获取用户活跃度数据
const fetchUserActivity = async (period = '30days') => {
  try {
    console.log('获取用户活跃度数据，周期:', period);
    isLoadingUserActivity.value = true;

    const data = await getUserActivity({ period });

    // 更新图表数据
    userActivityData.value = {
      labels: data.timeLabels,
      activeUsers: data.activeUsers,
      newUsers: data.newUsers
    };

    console.log('用户活跃度数据加载成功');
  } catch (error) {
    console.error('获取用户活跃度数据失败:', error);
    ElMessage.error('获取用户活跃度数据失败');
  } finally {
    isLoadingUserActivity.value = false;
  }
};

// 监听用户活跃度周期变化
watch(userActivityPeriod, (newValue) => {
  fetchUserActivity(newValue);
});

// 是否正在加载用户活跃度数据
const isLoadingUserActivity = ref(false);

// 用户活跃度图表配置
const userActivityOption = computed(() => {
  if (isLoadingUserActivity.value) {
    // 加载状态下显示加载提示
    return {
      title: {
        text: '数据加载中...',
        left: 'center',
        top: 'middle',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      }
    };
  }

  if (userActivityData.value.labels.length === 0) {
    // 无数据时显示提示
    return {
      title: {
        text: '暂无用户活跃度数据',
        left: 'center',
        top: 'middle',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      }
    };
  }

  return {
    title: {
      text: '用户活跃度趋势',
      subtext: `${userActivityPeriod.value === '7days' ? '最近7天' : userActivityPeriod.value === '30days' ? '最近30天' : '最近90天'}`,
      left: 'center',
      top: 0,
      textStyle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#303133'
      },
      subtextStyle: {
        fontSize: 14,
        fontWeight: '500',
        color: '#606266',
        lineHeight: 24
      },
      itemGap: 8
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: function (params) {
        let result = `<div style="font-weight:bold;margin-bottom:5px;">${params[0].axisValue}</div>`;
        params.forEach(item => {
          const color = item.color;
          const marker = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${color};"></span>`;
          result += `${marker}${item.seriesName}: <strong>${item.value}</strong> 人<br/>`;
        });
        return result;
      }
    },
    legend: {
      data: ['活跃用户', '新增用户'],
      top: 50,
      textStyle: {
        fontSize: 14,
        color: '#606266'
      },
      itemWidth: 15,
      itemHeight: 10,
      itemGap: 25
    },
    grid: {
      left: '3%',
      right: '4%',
      top: 95,
      bottom: '5%',
      containLabel: true
    },
    toolbox: {
      feature: {
        saveAsImage: { title: '保存为图片' },
        dataView: { title: '数据视图', lang: ['数据视图', '关闭', '刷新'] }
      },
      right: 20,
      top: 50,
      iconStyle: {
        borderColor: '#999'
      },
      emphasis: {
        iconStyle: {
          borderColor: '#409EFF'
        }
      }
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 100
      }
    ],
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: userActivityData.value.labels,
        axisLine: {
          lineStyle: {
            color: '#999'
          }
        },
        axisLabel: {
          fontSize: 12,
          margin: 12,
          color: '#666'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '用户数',
        nameTextStyle: {
          color: '#666',
          fontSize: 13,
          padding: [0, 0, 0, 30]
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#eee'
          }
        },
        axisLabel: {
          formatter: '{value} 人',
          color: '#666'
        }
      }
    ],
    series: [
      {
        name: '活跃用户',
        type: 'line',
        stack: 'Total',
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.7)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        },
        emphasis: {
          focus: 'series'
        },
        data: userActivityData.value.activeUsers,
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#409EFF'
        },
        itemStyle: {
          color: '#409EFF',
          borderWidth: 2
        },
        symbolSize: 7
      },
      {
        name: '新增用户',
        type: 'line',
        stack: 'Total',
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(103, 194, 58, 0.7)' },
              { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }
            ]
          }
        },
        emphasis: {
          focus: 'series'
        },
        data: userActivityData.value.newUsers,
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#67C23A'
        },
        itemStyle: {
          color: '#67C23A',
          borderWidth: 2
        },
        symbolSize: 7
      }
    ]
  };
});

// 销售职场分布数据
const workplaceData = ref([]);
const isLoadingWorkplaceData = ref(false);

// 获取职场分布数据
const fetchWorkplaceData = async () => {
  try {
    console.log('[Dashboard] 开始获取职场分布数据');
    isLoadingWorkplaceData.value = true;

    const data = await getWorkplaceExchangeStats();
    console.log('[Dashboard] 职场分布数据返回:', data);

    if (!data || !Array.isArray(data)) {
      console.error('[Dashboard] 职场数据格式错误，期望数组，实际返回:', typeof data);
      ElMessage.error('职场分布数据格式错误');
      workplaceData.value = [];
      return;
    }

    if (data.length === 0) {
      console.log('[Dashboard] 职场分布数据为空');
      workplaceData.value = [];
      return;
    }

    // 检查返回数据的格式是否正确
    if (!data[0].hasOwnProperty('rmbAmount') || !data[0].hasOwnProperty('lyAmount')) {
      console.error('[Dashboard] 职场数据缺少必要的字段 rmbAmount 或 lyAmount');
      // 添加缺失字段
      workplaceData.value = data.map(item => ({
        ...item,
        rmbAmount: item.rmbAmount || 0,
        lyAmount: item.lyAmount || 0,
        count: item.count || 0
      }));
    } else {
      workplaceData.value = data;
    }

    console.log('[Dashboard] 职场分布数据加载成功，共', workplaceData.value.length, '条记录');
  } catch (error) {
    console.error('[Dashboard] 获取职场分布数据失败:', error);
    if (error.response) {
      console.error('[Dashboard] 错误状态码:', error.response.status);
      console.error('[Dashboard] 错误响应:', error.response.data);
    }
    ElMessage.error('获取职场分布数据失败，请稍后重试');
    workplaceData.value = [];
  } finally {
    isLoadingWorkplaceData.value = false;
  }
};

// 刷新职场分布数据
const refreshWorkplaceStats = async () => {
  await fetchWorkplaceData();
  ElMessage.success('职场分布数据已刷新');
};

// 人民币职场分布图表配置
const rmbWorkplaceOption = computed(() => {
  if (workplaceData.value.length === 0 || isLoadingWorkplaceData.value) {
    return {
      title: {
        text: isLoadingWorkplaceData.value ? '数据加载中...' : '暂无职场分布数据',
        left: 'center',
        top: 'middle',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      }
    };
  }

  // 按人民币收入排序并获取前10个职场
  const sortedData = [...workplaceData.value]
    .sort((a, b) => b.rmbAmount - a.rmbAmount)
    .filter(item => item.rmbAmount > 0)
    .slice(0, 10);

  if (sortedData.length === 0) {
    return {
      title: {
        text: '暂无人民币收入数据',
        left: 'center',
        top: 'middle',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      }
    };
  }

  // 提取数据
  const dataNames = sortedData.map(item => item.name);
  const dataValues = sortedData.map(item => item.rmbAmount);

  return {
    title: {
      text: '人民币收入分布'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params) {
        const item = params[0];
        const dataIndex = item.dataIndex;
        const workplace = sortedData[dataIndex];

        return `<div>
          <b>${workplace.name}</b><br/>
          人民币收入: ${workplace.rmbAmount.toLocaleString()} 元<br/>
          订单数量: ${workplace.rmbCount || 0} 单
        </div>`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dataNames,
      axisLabel: {
        interval: 0,
        rotate: 30,
        formatter: function (value) {
          if (value.length > 4) {
            return value.substring(0, 4) + '...';
          }
          return value;
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '金额',
      axisLabel: {
        formatter: '{value} 元'
      }
    },
    series: [
      {
        name: '人民币收入',
        type: 'bar',
        itemStyle: {
          color: '#F56C6C'
        },
        data: dataValues
      }
    ]
  };
});

// 光年币职场分布图表配置
const lyWorkplaceOption = computed(() => {
  if (workplaceData.value.length === 0 || isLoadingWorkplaceData.value) {
    return {
      title: {
        text: isLoadingWorkplaceData.value ? '数据加载中...' : '暂无职场分布数据',
        left: 'center',
        top: 'middle',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      }
    };
  }

  // 按光年币收入排序并获取前10个职场
  const sortedData = [...workplaceData.value]
    .sort((a, b) => b.lyAmount - a.lyAmount)
    .filter(item => item.lyAmount > 0)
    .slice(0, 10);

  if (sortedData.length === 0) {
    return {
      title: {
        text: '暂无光年币收入数据',
        left: 'center',
        top: 'middle',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      }
    };
  }

  // 提取数据
  const dataNames = sortedData.map(item => item.name);
  const dataValues = sortedData.map(item => item.lyAmount);

  return {
    title: {
      text: '光年币收入分布'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params) {
        const item = params[0];
        const dataIndex = item.dataIndex;
        const workplace = sortedData[dataIndex];

        return `<div>
          <b>${workplace.name}</b><br/>
          光年币收入: ${workplace.lyAmount.toLocaleString()} 个光年币<br/>
          订单数量: ${workplace.lyCount || 0} 单
        </div>`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dataNames,
      axisLabel: {
        interval: 0,
        rotate: 30,
        formatter: function (value) {
          if (value.length > 4) {
            return value.substring(0, 4) + '...';
          }
          return value;
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '数量',
      axisLabel: {
        formatter: '{value} 个光年币'
      }
    },
    series: [
      {
        name: '光年币收入',
        type: 'bar',
        itemStyle: {
          color: '#E6A23C'
        },
        data: dataValues
      }
    ]
  };
});

// 最近订单数据
const recentOrders = ref([]);
const isLoadingOrders = ref(false);

// 热门商品数据
const popularProducts = ref([]);
const isLoadingProducts = ref(false);

// 获取最近订单数据
const fetchRecentOrders = async () => {
  isLoadingOrders.value = true;
  try {
    console.log('开始获取最近订单数据...');
    const response = await getRecentExchanges({ limit: 7 });
    console.log('获取最近订单数据成功:', response);

    // 添加调试信息
    if (Array.isArray(response)) {
      console.log(`接收到 ${response.length} 条订单数据`);

      // 统计支付方式
      const paymentMethodStats = {
        ly: 0,
        rmb: 0,
        unknown: 0
      };

      // 处理数据，确保支付方式正确标记
      const processedData = response.map(order => {
        // 调试输出
        console.log(`处理订单: ID=${order.id}, 原支付方式=${order.paymentMethod}, 类型=${typeof order.paymentMethod}, 金额=${order.amount}`);

        // 深度调试paymentMethod字段
        if (order.paymentMethod !== 'ly' && order.paymentMethod !== 'rmb') {
          console.warn(`异常支付方式值: "${order.paymentMethod}", 类型: ${typeof order.paymentMethod}, JSON.stringify结果: ${JSON.stringify(order.paymentMethod)}`);
        }

        let processedPaymentMethod = order.paymentMethod;

        // 尝试修复可能的异常值
        if (order.paymentMethod && (
          order.paymentMethod === 'ly' ||
            order.paymentMethod.toString() === 'ly' ||
            order.paymentMethod.toLowerCase() === 'ly'
        )) {
          processedPaymentMethod = 'ly';
          paymentMethodStats.ly++;
        } else {
          processedPaymentMethod = 'rmb';
          paymentMethodStats.rmb++;

          if (order.paymentMethod !== 'rmb') {
            console.log(`订单${order.id}支付方式异常: ${order.paymentMethod}，已修正为rmb`);
            paymentMethodStats.unknown++;
          }
        }

        const processedOrder = {
          ...order,
          // 确保支付方式字段的值是准确的
          paymentMethod: processedPaymentMethod
        };

        console.log(`处理后订单: ID=${processedOrder.id}, 支付方式=${processedOrder.paymentMethod}, 金额=${processedOrder.amount}`);

        return processedOrder;
      });

      console.log('订单支付方式统计:', paymentMethodStats);
      recentOrders.value = processedData;
    } else {
      console.error('订单数据格式错误:', response);
      ElMessage.error('订单数据格式错误');
    }
  } catch (error) {
    console.error('获取最近订单失败:', error);

    // 显示更详细的错误信息
    let errorMessage = '获取最近订单失败';
    if (error.response) {
      errorMessage += `: ${error.response.status} ${error.response.statusText}`;
      console.error('错误响应:', error.response.data);
    } else if (error.message) {
      errorMessage += `: ${error.message}`;
    }

    ElMessage.error(errorMessage);
  } finally {
    isLoadingOrders.value = false;
  }
};

// 获取热门商品数据
const fetchPopularProducts = async () => {
  isLoadingProducts.value = true;
  try {
    console.log('开始获取热门商品数据...');
    const response = await getPopularProducts({ limit: 5 });
    console.log('获取热门商品数据成功:', response);
    if (Array.isArray(response)) {
      popularProducts.value = response;
    } else {
      console.error('商品数据格式错误:', response);
      ElMessage.error('商品数据格式错误');
    }
  } catch (error) {
    console.error('获取热门商品失败:', error);

    // 显示更详细的错误信息
    let errorMessage = '获取热门商品失败';
    if (error.response) {
      errorMessage += `: ${error.response.status} ${error.response.statusText}`;
      console.error('错误响应:', error.response.data);
    } else if (error.message) {
      errorMessage += `: ${error.message}`;
    }

    ElMessage.error(errorMessage);
  } finally {
    isLoadingProducts.value = false;
  }
};

// 获取订单状态对应的Tag类型
const getStatusTag = (status) => {
  const statusMap = {
    '已批准': 'success',
    '待处理': 'info',
    '处理中': 'info',
    '已发货': 'warning',
    '已完成': 'success',
    '已拒绝': 'danger',
    '已取消': 'info'
  };
  return statusMap[status] || 'info';
};

// 查看所有订单
const viewAllOrders = () => {
  try {
    router.push('/admin/exchanges');
    ElMessage.success('正在跳转到兑换管理页面...');
  } catch (error) {
    console.error('导航失败:', error);
    ElMessage.error('页面跳转失败');
  }
};

// 查看所有商品
const viewAllProducts = () => {
  try {
    router.push('/admin/products');
    ElMessage.success('正在跳转到商品管理页面...');
  } catch (error) {
    console.error('导航失败:', error);
    ElMessage.error('页面跳转失败');
  }
};

// 刷新分类数据
const refreshCategoryData = async () => {
  await fetchCategoryData();
  ElMessage.success('分类数据已刷新');
};

// 刷新订单数据
const refreshOrders = async () => {
  await fetchRecentOrders();
  ElMessage.success('订单数据已刷新');
};

// 刷新热门商品数据
const refreshProducts = async () => {
  await fetchPopularProducts();
  ElMessage.success('热门商品数据已刷新');
};

// 刷新仪表盘
const refreshDashboard = async () => {
  if (isRefreshing.value) return;

  try {
    isRefreshing.value = true;
    ElMessage.info('正在刷新仪表盘数据...');

    // 使用Promise.all同时发起请求
    await Promise.all([
      fetchDashboardStats().catch(error => {
        console.error('[Dashboard] 加载统计数据失败:', error);
        setTimeout(() => {
          fetchDashboardStats().catch(err => console.error('[Dashboard] 重试加载统计数据失败:', err));
        }, 2000);
      }),
      fetchSalesTrend(salesTrendPeriod.value).catch(error => {
        console.error('[Dashboard] 加载销售趋势数据失败:', error);
      }),
      fetchStockTrend(stockTrendPeriod.value, stockTrendCategory.value).catch(error => {
        console.error('[Dashboard] 加载库存趋势数据失败:', error);
      }),
      fetchUserActivity(userActivityPeriod.value).catch(error => {
        console.error('[Dashboard] 加载用户活跃度数据失败:', error);
      }),
      fetchWorkplaceData().catch(error => {
        console.error('[Dashboard] 加载职场分布数据失败:', error);
      }),
      fetchCategoryData().catch(error => {
        console.error('[Dashboard] 加载分类数据失败:', error);
      }),
      fetchRecentOrders().catch(error => {
        console.error('[Dashboard] 加载最近订单失败:', error);
      }),
      fetchPopularProducts().catch(error => {
        console.error('[Dashboard] 加载热门商品失败:', error);
      })
    ]);

    ElMessage.success('仪表盘数据已刷新');
  } catch (error) {
    console.error('[Dashboard] 刷新仪表盘数据失败:', error);
    ElMessage.error('刷新数据失败，请稍后重试');
  } finally {
    isRefreshing.value = false;
  }
};

// 导出仪表盘数据
const exportData = async (format = 'excel') => {
  try {
    console.log('[Dashboard] 导出仪表盘数据，格式:', format, '周期:', salesTrendPeriod.value);
    ElMessage.info('正在生成导出文件，请稍候...');

    // 显示一个较长时间的加载指示器
    const loadingInstance = ElLoading.service({
      lock: true,
      text: `正在生成${format === 'excel' ? 'Excel' : 'PDF'}文件，请耐心等待...`,
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 确认当前登录状态
    const token = sessionStorage.getItem('token');
    if (!token) {
      loadingInstance.close();
      ElMessage.error('需要登录才能导出数据');
      return;
    }

    console.log('[Dashboard] 开始调用导出API');

    try {
      const response = await exportDashboardData({
        format,
        period: salesTrendPeriod.value
      });

      console.log('[Dashboard] 导出API返回成功:', response.status, response.headers);

      // 检查响应
      if (!response || !response.data) {
        loadingInstance.close();
        throw new Error('服务器返回的响应无效');
      }

      // 检查响应数据大小
      if (response.data.size < 100) {
        console.error('[Dashboard] 导出文件大小异常小:', response.data.size, '字节');
        loadingInstance.close();
        throw new Error('导出的文件数据异常，可能是服务器错误');
      }

      // 检查内容类型
      const contentType = response.headers['content-type'];
      console.log('[Dashboard] 响应内容类型:', contentType);

      // 确定正确的MIME类型
      let mimeType = '';
      if (format === 'excel') {
        mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      } else {
        mimeType = 'application/pdf';
      }

      // 创建Blob对象
      const blob = new Blob([response.data], { type: contentType || mimeType });
      console.log('[Dashboard] 创建Blob对象，大小:', blob.size, '字节');

      // 创建下载链接
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);

      // 从Content-Disposition获取文件名或使用默认文件名
      let filename = `仪表盘数据_${new Date().toLocaleDateString()}.${format === 'excel' ? 'xlsx' : 'pdf'}`;
      const contentDisposition = response.headers['content-disposition'];
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '');
        }
      }

      console.log('[Dashboard] 下载文件名:', filename);

      // 设置下载文件名
      link.download = filename;

      // 触发下载
      document.body.appendChild(link);
      link.click();

      // 清理DOM
      document.body.removeChild(link);

      // 释放URL对象
      setTimeout(() => {
        URL.revokeObjectURL(link.href);
        loadingInstance.close();
        ElMessage.success('导出成功');
      }, 100);
    } catch (apiError) {
      console.error('[Dashboard] API调用错误:', apiError);
      loadingInstance.close();
      throw apiError;
    }
  } catch (error) {
    console.error('[Dashboard] 导出数据失败:', error);

    // 获取详细的错误信息
    let errorMessage = '导出数据失败';
    if (error.response) {
      console.error('[Dashboard] 错误响应:', error.response.status, error.response.data);

      if (error.response.status === 401) {
        errorMessage = '您需要重新登录才能导出数据';
      } else if (error.response.status === 500) {
        errorMessage = `服务器内部错误: ${error.response.data?.message || '导出失败'}`;
      } else {
        errorMessage = `导出失败 (HTTP ${error.response.status})`;
      }
    } else if (error.message) {
      errorMessage = error.message;
    }

    // 尝试提取更详细的错误信息
    if (error.response && error.response.data instanceof Blob) {
      try {
        const reader = new FileReader();
        reader.onload = function () {
          try {
            const errorData = JSON.parse(reader.result);
            const detailMessage = errorData.message || errorData.error || JSON.stringify(errorData);
            ElMessageBox.alert(`${errorMessage}\n\n详细信息: ${detailMessage}`, '导出失败', {
              type: 'error',
              confirmButtonText: '确定'
            });
          } catch (e) {
            // 无法解析JSON，显示原始错误
            ElMessageBox.alert(errorMessage, '导出失败', {
              type: 'error',
              confirmButtonText: '确定'
            });
          }
        };
        reader.readAsText(error.response.data);
      } catch (e) {
        // 回退到显示基本错误
        ElMessageBox.alert(errorMessage, '导出失败', {
          type: 'error',
          confirmButtonText: '确定'
        });
      }
    } else {
      // 显示基本错误
      ElMessageBox.alert(errorMessage, '导出失败', {
        type: 'error',
        confirmButtonText: '确定'
      });
    }
  } finally {
    // 确保加载指示器被关闭
    try {
      ElLoading.service().close();
    } catch (e) {
      console.error('[Dashboard] 关闭加载指示器失败', e);
    }
  }
};

// 组件挂载时加载所有数据
onMounted(async () => {
  try {
    console.log('[Dashboard] 仪表盘组件挂载，开始加载数据');

    // 设置初始状态
    statCards.value.forEach((card, index) => {
      if (index === 2) { // 光年币
        card.value = '0';
      } else if (index === 0 || index === 1) { // 总销售额和人民币收入
        card.value = '¥0';
      } else { // 其他卡片
        card.value = '0';
      }
    });

    // 使用Promise.all同时发起请求，但不等待全部完成
    Promise.all([
      fetchDashboardStats().catch(error => {
        console.error('[Dashboard] 加载统计数据失败:', error);
        setTimeout(() => {
          fetchDashboardStats().catch(err => console.error('[Dashboard] 重试加载统计数据失败:', err));
        }, 2000);
      }),
      fetchSalesTrend(salesTrendPeriod.value).catch(error => {
        console.error('[Dashboard] 加载销售趋势数据失败:', error);
      }),
      fetchStockTrend(stockTrendPeriod.value, stockTrendCategory.value).catch(error => {
        console.error('[Dashboard] 加载库存趋势数据失败:', error);
      }),
      fetchUserActivity(userActivityPeriod.value).catch(error => {
        console.error('[Dashboard] 加载用户活跃度数据失败:', error);
      }),
      fetchWorkplaceData().catch(error => {
        console.error('[Dashboard] 加载职场分布数据失败:', error);
      }),
      fetchCategoryData().catch(error => {
        console.error('[Dashboard] 加载分类数据失败:', error);
      }),
      fetchRecentOrders().catch(error => {
        console.error('[Dashboard] 加载最近订单失败:', error);
      }),
      fetchPopularProducts().catch(error => {
        console.error('[Dashboard] 加载热门商品失败:', error);
      })
    ]).then(() => {
      console.log('[Dashboard] 所有数据加载完成');
    }).catch(error => {
      console.error('[Dashboard] 数据加载过程中发生错误:', error);
    });

  } catch (error) {
    console.error('[Dashboard] 仪表盘初始化错误:', error);
    ElMessage.error('加载仪表盘数据时出错，请刷新页面重试');
  }
});
</script>

<style scoped>
.dashboard-container {
  padding: 24px;
  background-color: #f8fafc;
  min-height: calc(100vh - 60px);
}

.dashboard-header {
  margin-bottom: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
}

.header-left {
  flex: 1;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-actions {
  margin-left: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.stat-cards {
  margin-bottom: 24px;
}

.stat-card {
  height: 140px;
  transition: all 0.3s ease;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px -6px rgba(0, 0, 0, 0.15);
  border-color: transparent;
}

.stat-card-content {
  display: flex;
  align-items: center;
  height: 100%;
  position: relative;
}

.stat-card.primary .stat-icon {
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(37, 99, 235, 0.05) 100%);
  color: #2563eb;
}

.stat-card.info .stat-icon {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(99, 102, 241, 0.05) 100%);
  color: #6366f1;
}

.stat-card.success .stat-icon {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%);
  color: #10b981;
}

.stat-card.warning .stat-icon {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%);
  color: #f59e0b;
}

.stat-card.danger .stat-icon {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.05) 100%);
  color: #ef4444;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  border-radius: 16px;
  margin-right: 20px;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 80px;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 6px;
  line-height: 1.2;
  display: flex;
  align-items: baseline;
}

.value-unit {
  font-size: 14px;
  font-weight: 500;
  margin-left: 6px;
  color: #6b7280;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 10px;
  font-weight: 500;
}

.stat-trend {
  display: flex;
  align-items: center;
  font-size: 13px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  width: fit-content;
}

.stat-trend.up {
  color: #10b981;
  background-color: rgba(16, 185, 129, 0.1);
}

.stat-trend.down {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
}

.stat-trend .el-icon {
  margin-right: 4px;
}

.chart-section {
  margin-bottom: 24px;
}

.chart-card {
  min-height: 400px;
  height: auto;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.chart-card:hover {
  box-shadow: 0 8px 16px -4px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

.chart-header,
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid #f3f4f6;
}

.chart-header h3,
.table-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.chart-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-container {
  height: 380px;
  position: relative;
  padding: 16px 0;
}

/* 扩展的用户活跃度图表容器 */
.expanded-chart {
  height: 420px;
  padding: 20px 0;
}

.chart {
  height: 100%;
  width: 100%;
}

/* 用户活跃度卡片特殊样式 */
.user-activity-card {
  min-height: 520px;
}

.user-activity-card .chart-header {
  padding-bottom: 16px;
  border-bottom: 2px solid #f0f2f5;
}

.user-activity-card .chart-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.user-activity-card .chart-actions {
  gap: 12px;
}

.chart-error {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-card {
  min-height: 400px;
  height: auto;
}

.table-container {
  min-height: 300px;
  max-height: 450px;
  overflow: auto;
}



.data-tables {
  margin-bottom: 24px;
}

.empty-data {
  text-align: center;
  padding: 30px 0;
  color: #909399;
  font-size: 14px;
}

/* 表格样式优化 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-header .el-button {
  margin-left: 8px;
}

/* 订单金额样式优化 */
.order-amount {
  font-weight: 600;
  font-size: 13px;
}

.order-amount.rmb {
  color: #2563eb;
}

.order-amount.ly {
  color: #f59e0b;
}

/* 表格滚动条样式 */
.table-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 表格底部样式 */
.table-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0 8px 0;
  border-top: 1px solid #ebeef5;
  margin-top: 8px;
}

.data-count {
  font-size: 12px;
  color: #909399;
}

/* 库存趋势相关样式 */
.stock-summary {
  margin-bottom: 16px;
  text-align: center;
}

.stock-summary .el-tag {
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 6px;
}

.chart-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-actions .el-select {
  min-width: 120px;
}

@media (max-width: 768px) {
  .stat-cards > div {
    margin-bottom: 16px;
  }

  .stat-card {
    height: 120px;
  }

  .stat-icon {
    width: 56px;
    height: 56px;
    margin-right: 16px;
  }

  .stat-value {
    font-size: 24px;
  }

  .stat-label {
    font-size: 13px;
  }

  .stat-trend {
    font-size: 12px;
    padding: 3px 6px;
  }

  .chart-section > div {
    margin-bottom: 16px;
  }

  .data-tables > div {
    margin-bottom: 16px;
  }

  .chart-container {
    height: 320px;
    padding: 12px 0;
  }

  /* 移动端扩展图表容器 */
  .expanded-chart {
    height: 360px;
    padding: 16px 0;
  }

  .chart-card {
    min-height: 360px;
  }

  .user-activity-card {
    min-height: 460px;
  }

  .user-activity-card .chart-header h3 {
    font-size: 16px;
  }

  .user-activity-card .chart-actions {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .user-activity-card .chart-actions .el-select {
    width: 100%;
  }

  .chart-header h3 {
    font-size: 15px;
  }

  .chart-actions .el-radio-group {
    transform: scale(0.9);
  }

  .table-card {
    min-height: 350px;
  }

  .table-container {
    max-height: 350px;
  }

  /* 移动端表格优化 */
  .el-table .el-table__cell {
    padding: 8px 4px;
  }

  .order-amount {
    font-size: 12px;
  }
}
</style>
