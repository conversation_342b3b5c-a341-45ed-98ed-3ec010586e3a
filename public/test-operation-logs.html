<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试操作日志</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #666;
        }
        button {
            background: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #66b1ff;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #f0f9ff;
            border: 1px solid #b3d8ff;
            color: #0066cc;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffb3b3;
            color: #cc0000;
        }
        .info {
            background: #f9f9f9;
            border: 1px solid #ddd;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 操作日志功能测试</h1>
        
        <div class="test-section">
            <h3>1. API接口测试</h3>
            <button onclick="testGetAllLogs()">获取所有日志</button>
            <button onclick="testGetTransferLogs()">获取转移日志</button>
            <button onclick="testGetProductLogs()">获取商品日志</button>
            <button onclick="testGetWorkplaceLogs()">获取职场日志</button>
            <div id="apiResult" class="result info">点击按钮开始测试...</div>
        </div>
        
        <div class="test-section">
            <h3>2. 前端组件测试</h3>
            <p>请在库存管理页面点击"操作日志"按钮，检查对话框是否能正常显示数据。</p>
            <button onclick="openStockManagement()">打开库存管理页面</button>
            <div class="result info">
操作步骤：
1. 点击上方按钮打开库存管理页面
2. 在页面中找到"操作日志"按钮并点击
3. 检查操作日志对话框是否显示数据
4. 尝试使用筛选功能
5. 检查分页功能是否正常
            </div>
        </div>
        
        <div class="test-section">
            <h3>3. 数据验证</h3>
            <button onclick="validateData()">验证数据完整性</button>
            <div id="validationResult" class="result info">点击按钮开始验证...</div>
        </div>
    </div>

    <script>
        // 获取token
        async function getToken() {
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: '超管',
                        email: '<EMAIL>',
                        password: '654321',
                        userType: 'admin'
                    })
                });
                
                const data = await response.json();
                return data.token;
            } catch (error) {
                console.error('获取token失败:', error);
                return null;
            }
        }

        // 测试获取所有日志
        async function testGetAllLogs() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.textContent = '正在获取所有日志...';
            resultDiv.className = 'result info';
            
            try {
                const token = await getToken();
                if (!token) {
                    throw new Error('无法获取认证token');
                }
                
                const response = await fetch('/api/stocks/operation-logs?page=1&limit=10', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 成功获取日志数据
总记录数: ${data.data.pagination.total}
当前页记录数: ${data.data.logs.length}
第一条记录: ${JSON.stringify(data.data.logs[0], null, 2)}`;
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 获取日志失败: ${error.message}`;
            }
        }

        // 测试获取转移日志
        async function testGetTransferLogs() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.textContent = '正在获取转移日志...';
            resultDiv.className = 'result info';
            
            try {
                const token = await getToken();
                if (!token) {
                    throw new Error('无法获取认证token');
                }
                
                const response = await fetch('/api/stocks/operation-logs?operationType=transfer&page=1&limit=5', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 成功获取转移日志
转移操作记录数: ${data.data.logs.length}
示例记录: ${JSON.stringify(data.data.logs[0], null, 2)}`;
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 获取转移日志失败: ${error.message}`;
            }
        }

        // 测试获取商品日志
        async function testGetProductLogs() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.textContent = '正在获取哪吒捏捏乐日志...';
            resultDiv.className = 'result info';
            
            try {
                const token = await getToken();
                if (!token) {
                    throw new Error('无法获取认证token');
                }
                
                const response = await fetch('/api/stocks/operation-logs?productId=15&page=1&limit=5', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 成功获取哪吒捏捏乐日志
记录数: ${data.data.logs.length}
商品名称: ${data.data.logs[0]?.product?.name || '未知'}
示例记录: ${JSON.stringify(data.data.logs[0], null, 2)}`;
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 获取商品日志失败: ${error.message}`;
            }
        }

        // 测试获取职场日志
        async function testGetWorkplaceLogs() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.textContent = '正在获取北京职场日志...';
            resultDiv.className = 'result info';
            
            try {
                const token = await getToken();
                if (!token) {
                    throw new Error('无法获取认证token');
                }
                
                const response = await fetch('/api/stocks/operation-logs?workplaceId=1&page=1&limit=5', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 成功获取北京职场日志
记录数: ${data.data.logs.length}
职场名称: ${data.data.logs[0]?.workplace?.name || '未知'}
示例记录: ${JSON.stringify(data.data.logs[0], null, 2)}`;
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 获取职场日志失败: ${error.message}`;
            }
        }

        // 打开库存管理页面
        function openStockManagement() {
            window.open('/admin/stock-management', '_blank');
        }

        // 验证数据完整性
        async function validateData() {
            const resultDiv = document.getElementById('validationResult');
            resultDiv.textContent = '正在验证数据完整性...';
            resultDiv.className = 'result info';
            
            try {
                const token = await getToken();
                if (!token) {
                    throw new Error('无法获取认证token');
                }
                
                // 获取所有日志
                const response = await fetch('/api/stocks/operation-logs?page=1&limit=100', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const logs = data.data.logs;
                    const stats = {
                        total: logs.length,
                        withProduct: logs.filter(log => log.product).length,
                        withWorkplace: logs.filter(log => log.workplace).length,
                        withOperator: logs.filter(log => log.operator).length,
                        operationTypes: {}
                    };
                    
                    logs.forEach(log => {
                        stats.operationTypes[log.operationType] = (stats.operationTypes[log.operationType] || 0) + 1;
                    });
                    
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 数据验证完成
总记录数: ${stats.total}
包含商品信息: ${stats.withProduct}/${stats.total}
包含职场信息: ${stats.withWorkplace}/${stats.total}
包含操作员信息: ${stats.withOperator}/${stats.total}
操作类型分布: ${JSON.stringify(stats.operationTypes, null, 2)}`;
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 数据验证失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>
