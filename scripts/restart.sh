#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

echo -e "${YELLOW}===== 项目重启脚本 =====${NC}"

# 项目路径
PROJECT_DIR="/Users/<USER>/Desktop/chattywork/workyy"
SERVER_DIR="$PROJECT_DIR/server"

# 数据库配置
DB_HOST="localhost"
DB_PORT=3306
DB_USER="root"
DB_PASSWORD="password"
DB_NAME="feishu_mall"

# 强制设置为开发环境
export NODE_ENV=development
export SERVER_URL=http://localhost:3000
export FEISHU_REDIRECT_URI=http://localhost:3000/api/feishu/callback

# 输出当前设置的环境变量
echo -e "${BLUE}===== 当前环境变量设置 =====${NC}"
echo -e "NODE_ENV: $NODE_ENV"
echo -e "SERVER_URL: $SERVER_URL"
echo -e "FEISHU_REDIRECT_URI: $FEISHU_REDIRECT_URI"
echo -e "${BLUE}=========================${NC}"

# 检查是否有生产环境的配置文件
if [ -f "$SERVER_DIR/.env.production" ]; then
  echo -e "${YELLOW}警告: 检测到生产环境配置文件，在本地开发时将被忽略${NC}"
fi

# 检查MySQL服务状态
echo -e "${BLUE}正在检查MySQL服务状态...${NC}"
if ! command -v mysql &> /dev/null; then
  echo -e "${YELLOW}警告: MySQL命令行工具未安装，跳过数据库检查${NC}"
else
  # 尝试连接到MySQL
  if mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD -e "SELECT 1" &>/dev/null; then
    echo -e "${GREEN}MySQL服务运行正常${NC}"

    # 检查数据库是否存在
    if mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD -e "USE $DB_NAME" &>/dev/null; then
      echo -e "${GREEN}数据库 $DB_NAME 已就绪${NC}"
    else
      echo -e "${YELLOW}数据库 $DB_NAME 不存在，尝试创建...${NC}"
      if mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD -e "CREATE DATABASE $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" &>/dev/null; then
        echo -e "${GREEN}成功创建数据库 $DB_NAME${NC}"
      else
        echo -e "${RED}无法创建数据库 $DB_NAME，请手动创建${NC}"
      fi
    fi
  else
    echo -e "${RED}警告: 无法连接到MySQL服务器，请确保MySQL服务已启动${NC}"
    echo -e "${YELLOW}尝试继续启动应用...${NC}"
  fi
fi

# 停止所有进程
echo -e "${BLUE}正在停止所有相关进程...${NC}"

# 查找并终止前端和后端进程
kill_port() {
  local port=$1
  local description=$2

  local port_pid=$(lsof -ti:$port)
  if [ ! -z "$port_pid" ]; then
    echo -e "停止${description}进程 (PID: $port_pid, 端口: $port)"
    kill -9 $port_pid 2>/dev/null
    sleep 1

    # 再次检查是否已终止
    if lsof -ti:$port > /dev/null; then
      echo -e "${RED}警告: 端口 $port 仍被占用，尝试强制终止...${NC}"
      lsof -ti:$port | xargs -r kill -9
      sleep 1
    fi
  fi
}

# 停止占用后端端口的进程
kill_port 3000 "后端服务器"

# 停止前端各种可能的端口
for port in 5173 5174 5175 5176 5177; do
  kill_port $port "前端服务器"
done

# 查找并终止所有vite进程
VITE_PIDS=$(ps -ef | grep vite | grep -v grep | awk '{print $2}')
if [ ! -z "$VITE_PIDS" ]; then
  echo -e "停止所有vite进程 (PIDs: $VITE_PIDS)"
  echo $VITE_PIDS | xargs -r kill -9 2>/dev/null
fi

# 查找并终止所有node进程（可选，默认注释）
# NODE_PIDS=$(ps -ef | grep node | grep server.js | grep -v grep | awk '{print $2}')
# if [ ! -z "$NODE_PIDS" ]; then
#   echo -e "停止所有node服务器进程 (PIDs: $NODE_PIDS)"
#   echo $NODE_PIDS | xargs -r kill -9 2>/dev/null
# fi

# 等待进程完全终止
echo -e "等待进程终止..."
sleep 2

# 再次检查3000端口是否已释放
if lsof -ti:3000 > /dev/null; then
  echo -e "${RED}警告: 端口3000仍被占用，尝试强制终止...${NC}"
  lsof -ti:3000 | xargs -r kill -9
  sleep 1

  # 如果还是被占用，则退出
  if lsof -ti:3000 > /dev/null; then
    echo -e "${RED}错误: 无法释放端口3000，请手动检查占用进程后重试${NC}"
    exit 1
  fi
fi

# 清除可能存在的环境缓存
echo -e "${BLUE}清除环境缓存...${NC}"
if [ -f "$SERVER_DIR/.env.cache" ]; then
  rm -f "$SERVER_DIR/.env.cache"
  echo -e "${GREEN}已删除环境缓存文件${NC}"
fi

# 确保开发环境变量文件存在并包含正确的配置
echo -e "${BLUE}更新开发环境配置...${NC}"
cat > "$SERVER_DIR/.env" << EOF
# 数据库配置
DB_HOST=$DB_HOST
DB_PORT=$DB_PORT
DB_USER=$DB_USER
DB_PASSWORD=$DB_PASSWORD
DB_NAME=$DB_NAME

# 应用配置
NODE_ENV=development
PORT=3000
SERVER_URL=http://localhost:3000
FEISHU_REDIRECT_URI=http://localhost:3000/api/feishu/callback

# 日志配置
LOG_LEVEL=info
EOF
echo -e "${GREEN}已更新环境变量文件${NC}"

# 启动后端服务器
echo -e "${BLUE}正在启动后端服务器...${NC}"
cd $SERVER_DIR

# 添加重试机制，最多尝试3次
MAX_RETRIES=3
retry_count=0
server_started=false

while [ $retry_count -lt $MAX_RETRIES ] && [ "$server_started" = false ]; do
  # 使用明确的环境变量启动服务器
  NODE_ENV=development \
  SERVER_URL=http://localhost:3000 \
  FEISHU_REDIRECT_URI=http://localhost:3000/api/feishu/callback \
  node server.js > server.log 2>&1 &
  
  SERVER_PID=$!
  echo -e "已启动后端服务器 (PID: $SERVER_PID)"

  # 等待服务器启动，最多等待10秒
  echo -e "等待后端服务器启动..."
  for i in {1..10}; do
    sleep 1
    if curl -s http://localhost:3000/api/health > /dev/null; then
      server_started=true
      break
    fi

    # 检查进程是否还在运行
    if ! ps -p $SERVER_PID > /dev/null; then
      echo -e "${RED}后端服务器启动失败，进程已退出${NC}"
      # 输出日志以便调试
      echo -e "${YELLOW}服务器日志:${NC}"
      tail -n 20 server.log
      break
    fi
  done

  if [ "$server_started" = false ]; then
    echo -e "${RED}尝试 $((retry_count+1))/$MAX_RETRIES 启动后端服务器失败，正在重试...${NC}"
    # 终止可能仍在运行的服务器进程
    kill -9 $SERVER_PID 2>/dev/null
    sleep 2
    retry_count=$((retry_count+1))
  fi
done

if [ "$server_started" = false ]; then
  echo -e "${RED}错误: 无法启动后端服务器，请检查日志文件: $SERVER_DIR/server.log${NC}"
  exit 1
else
  echo -e "${GREEN}后端服务器启动成功! (PID: $SERVER_PID)${NC}"

  # 验证环境设置
  echo -e "${BLUE}验证服务器环境设置...${NC}"
  ENV_CHECK=$(curl -s http://localhost:3000/api/env-check 2>/dev/null || echo "无法访问环境检查接口")
  if [[ "$ENV_CHECK" == *"development"* ]]; then
    echo -e "${GREEN}服务器确认运行在开发环境模式${NC}"
  else
    echo -e "${YELLOW}警告: 无法确认服务器环境模式，请手动检查${NC}"
  fi

  # 测试日志系统
  echo -e "${BLUE}正在测试日志系统...${NC}"
  if curl -s http://localhost:3000/api/logs/stats > /dev/null; then
    echo -e "${GREEN}日志系统运行正常${NC}"
  else
    echo -e "${YELLOW}警告: 无法访问日志统计接口，日志系统可能需要配置${NC}"
  fi
fi

# 启动前端开发服务器
echo -e "${BLUE}正在启动前端开发服务器...${NC}"
cd $PROJECT_DIR
npm run dev > frontend.log 2>&1 &
FRONTEND_PID=$!

# 等待前端服务器启动
echo -e "等待前端开发服务器启动..."
sleep 5

# 检查前端服务器是否还在运行
if ! ps -p $FRONTEND_PID > /dev/null; then
  echo -e "${RED}前端服务器启动失败，查看日志: $PROJECT_DIR/frontend.log${NC}"
  tail -n 20 frontend.log
  exit 1
fi

# 检测前端服务器实际使用的端口
FRONTEND_PORT=""
for port in 5173 5174 5175 5176 5177; do
  if lsof -i :$port | grep -q node; then
    FRONTEND_PORT=$port
    break
  fi
done

if [ -z "$FRONTEND_PORT" ]; then
  echo -e "${YELLOW}警告: 无法检测到前端服务器端口，但进程(PID: $FRONTEND_PID)仍在运行${NC}"
  FRONTEND_PORT="(自动分配)"
else
  echo -e "${GREEN}前端服务器运行在端口: $FRONTEND_PORT${NC}"
fi

# 输出成功信息
echo -e "\n${GREEN}===== 重启完成 =====${NC}"
echo -e "${BLUE}后端API: ${NC}http://localhost:3000/api"
echo -e "${BLUE}日志管理: ${NC}http://localhost:3000/api/logs"

if [ -z "$FRONTEND_PORT" ] || [ "$FRONTEND_PORT" = "(自动分配)" ]; then
  echo -e "${BLUE}前端应用: ${NC}http://localhost:517x (查看上方Vite输出获取确切端口)"
else
  echo -e "${BLUE}前端应用: ${NC}http://localhost:$FRONTEND_PORT"
fi

echo -e "${YELLOW}如果页面显示'服务器未连接'，请尝试刷新浏览器 (Ctrl+F5)${NC}"
echo -e "${YELLOW}查看日志: 后端 - $SERVER_DIR/server.log  前端 - $PROJECT_DIR/frontend.log${NC}"

# 环境提示
echo -e "\n${GREEN}===== 环境信息 =====${NC}"
echo -e "${BLUE}当前模式: ${NC}本地开发环境"
echo -e "${BLUE}后端环境: ${NC}development"
echo -e "${BLUE}API基础URL: ${NC}http://localhost:3000"
echo -e "${YELLOW}如果支付宝收款码不显示，请尝试访问 http://localhost:$FRONTEND_PORT/test-payment.html 进行测试${NC}"