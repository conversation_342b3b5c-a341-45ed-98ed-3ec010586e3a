USE feishu_mall;

CREATE TABLE IF NOT EXISTS hot_product_configs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  timeRange VARCHAR(20) NOT NULL,
  enabled BOOLEAN DEFAULT TRUE,
  maxCount INT DEFAULT 10,
  minExchangeCount INT DEFAULT 1,
  exchangeWeight DECIMAL(3,2) DEFAULT 1.0,
  stockWeight DECIMAL(3,2) DEFAULT 0.1,
  autoUpdateEnabled BOOLEAN DEFAULT TRUE,
  updateFrequency VARCHAR(50) DEFAULT '0 */1 * * *',
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_time_range (timeRange)
);

CREATE TABLE IF NOT EXISTS hot_product_history (
  id INT AUTO_INCREMENT PRIMARY KEY,
  productId INT NOT NULL,
  timeRange VARCHAR(20) NOT NULL,
  hotScore DECIMAL(10,2) NOT NULL,
  `rank` INT NOT NULL,
  exchangeCount INT NOT NULL,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE,
  INDEX idx_product_time (productId, timeRange),
  INDEX idx_time_rank (timeRange, `rank`)
);

INSERT IGNORE INTO hot_product_configs (timeRange, enabled, maxCount, minExchangeCount, exchangeWeight, stockWeight, autoUpdateEnabled, updateFrequency) VALUES
('all', TRUE, 15, 1, 1.0, 0.1, TRUE, '0 */2 * * *'),
('30d', TRUE, 10, 1, 1.0, 0.1, TRUE, '0 */1 * * *'),
('7d', TRUE, 8, 1, 1.0, 0.1, TRUE, '0 */1 * * *'),
('1d', TRUE, 5, 1, 1.0, 0.1, TRUE, '0 */1 * * *');
