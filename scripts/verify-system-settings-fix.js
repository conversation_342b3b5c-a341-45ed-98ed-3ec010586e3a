#!/usr/bin/env node

/**
 * 系统设置模块修复验证脚本
 * 用于验证管理后台系统设置模块的前端渲染问题是否已修复
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 开始验证系统设置模块修复...\n');

// 检查项目列表
const checks = [
  {
    name: '检查IntelligentSchedule.vue组件生命周期修复',
    check: () => {
      const filePath = path.join(__dirname, '../src/views/admin/system/IntelligentSchedule.vue');
      const content = fs.readFileSync(filePath, 'utf8');

      // 检查是否正确导入了onUnmounted
      const hasCorrectImport = content.includes('import { ref, reactive, onMounted, onUnmounted } from \'vue\'');

      // 检查是否正确使用了onUnmounted
      const hasCorrectUsage = content.includes('onUnmounted(() => {') &&
                             content.includes('clearInterval(metricsInterval)');

      // 检查是否没有嵌套的onUnmounted
      const noNestedUnmounted = !content.includes('onMounted(() => {') ||
                               !content.match(/onMounted\([^)]*onUnmounted/);

      return hasCorrectImport && hasCorrectUsage && noNestedUnmounted;
    }
  },
  {
    name: '检查DiagnosticTools.vue组件错误处理',
    check: () => {
      const filePath = path.join(__dirname, '../src/views/admin/system/DiagnosticTools.vue');
      const content = fs.readFileSync(filePath, 'utf8');

      // 检查是否添加了错误处理
      const hasErrorHandling = content.includes('diagnosticList.value = [];') &&
                              content.includes('total.value = 0;');

      // 检查是否添加了ErrorBoundary
      const hasErrorBoundary = content.includes('ErrorBoundary') &&
                              content.includes('import ErrorBoundary');

      return hasErrorHandling && hasErrorBoundary;
    }
  },
  {
    name: '检查路由错误处理',
    check: () => {
      const filePath = path.join(__dirname, '../src/router/index.js');
      const content = fs.readFileSync(filePath, 'utf8');

      // 检查是否添加了路由错误处理
      const hasAfterEach = content.includes('router.afterEach');
      const hasOnError = content.includes('router.onError');

      return hasAfterEach && hasOnError;
    }
  },
  {
    name: '检查ErrorBoundary组件存在',
    check: () => {
      const filePath = path.join(__dirname, '../src/components/ErrorBoundary.vue');
      return fs.existsSync(filePath);
    }
  },
  {
    name: '检查组件导入正确性',
    check: () => {
      const intelligentSchedulePath = path.join(__dirname, '../src/views/admin/system/IntelligentSchedule.vue');
      const diagnosticToolsPath = path.join(__dirname, '../src/views/admin/system/DiagnosticTools.vue');

      const intelligentContent = fs.readFileSync(intelligentSchedulePath, 'utf8');
      const diagnosticContent = fs.readFileSync(diagnosticToolsPath, 'utf8');

      // 检查是否正确导入了ErrorBoundary
      const intelligentHasImport = intelligentContent.includes('import ErrorBoundary from \'@/components/ErrorBoundary.vue\'');
      const diagnosticHasImport = diagnosticContent.includes('import ErrorBoundary from \'@/components/ErrorBoundary.vue\'');

      return intelligentHasImport && diagnosticHasImport;
    }
  }
];

// 执行检查
let passedChecks = 0;
let totalChecks = checks.length;

checks.forEach((check, index) => {
  try {
    const result = check.check();
    if (result) {
      console.log(`✅ ${check.name}`);
      passedChecks++;
    } else {
      console.log(`❌ ${check.name}`);
    }
  } catch (error) {
    console.log(`❌ ${check.name} - 检查失败: ${error.message}`);
  }
});

console.log(`\n📊 检查结果: ${passedChecks}/${totalChecks} 项通过`);

if (passedChecks === totalChecks) {
  console.log('🎉 所有修复项目验证通过！');
  console.log('\n📋 修复总结:');
  console.log('1. ✅ 修复了IntelligentSchedule.vue组件的生命周期钩子错误');
  console.log('2. ✅ 添加了错误处理和容错机制');
  console.log('3. ✅ 为高级功能组件添加了ErrorBoundary包装');
  console.log('4. ✅ 增强了路由错误处理');
  console.log('5. ✅ 优化了API调用的错误处理');

  console.log('\n🔧 建议测试步骤:');
  console.log('1. 启动项目: npm run dev');
  console.log('2. 登录管理后台');
  console.log('3. 进入系统设置页面');
  console.log('4. 点击"高级功能模块"');
  console.log('5. 点击"智能实际调度和高级诊断工具"');
  console.log('6. 再次点击"系统设置"验证页面不会变空白');

  process.exit(0);
} else {
  console.log('⚠️  部分修复项目未通过验证，请检查相关文件');
  process.exit(1);
}
