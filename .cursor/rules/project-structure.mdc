---
description:
globs:
alwaysApply: false
---
# 光年小卖部项目结构

## 项目概述
光年小卖部是一个电商平台，支持商品管理、用户管理、订单处理和飞书登录集成。该平台使用Vue 3和Express构建，支持光年币(虚拟货币)和人民币两种支付方式。

## 目录结构

### 前端 (根目录)
- 基于Vue 3的前端应用
- 主要入口：[package.json](mdc:package.json)
- 配置文件：[.eslintrc.js](mdc:client/.eslintrc.js)
- ESLint配置：[eslint.config.js](mdc:eslint.config.js)

### 后端 (server目录)
- 基于Express的后端API服务
- 主要入口：[server/server.js](mdc:server/server.js)
- API路由：[server/routes](mdc:server/routes)
- 数据模型：[server/models](mdc:server/models)
- 控制器：[server/controllers](mdc:server/controllers)
- 中间件：[server/middlewares](mdc:server/middlewares)
- 配置文件：[server/package.json](mdc:server/package.json)
- ESLint配置：[server/eslint.config.js](mdc:server/eslint.config.js)

### 文档 (docs目录)
- 项目文档：[docs/README.md](mdc:docs/README.md)
- 代码规范：[docs/code-standard.md](mdc:docs/code-standard.md)

## 主要功能模块

### 商品管理
- 路由：[server/routes/products.js](mdc:server/routes/products.js)
- API文档：通过Swagger生成，见[server/config/swagger.js](mdc:server/config/swagger.js)

### 用户管理
- 认证：[server/routes/auth.js](mdc:server/routes/auth.js)
- 用户路由：[server/routes/users.js](mdc:server/routes/users.js)

### 订单处理
- 订单路由：[server/routes/exchanges.js](mdc:server/routes/exchanges.js)

### 飞书集成
- 飞书登录：[server/routes/feishu.js](mdc:server/routes/feishu.js)

## 工具和配置
- VSCode配置：[.vscode/settings.json](mdc:.vscode/settings.json)
- Swagger API文档：[server/config/swagger.js](mdc:server/config/swagger.js)
- API首页：[server/public/index.html](mdc:server/public/index.html)
