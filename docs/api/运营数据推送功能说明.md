# 运营数据推送功能说明

## 功能概述

运营数据推送功能为光年小卖部提供了全面的飞书群通知系统，包括五大核心通知类型：

1. **新用户欢迎通知** - 飞书用户注册时的欢迎消息
2. **销售里程碑庆祝** - 达到特定销售目标时的庆祝通知
3. **异常订单预警** - 大额订单或异常行为的实时预警
4. **用户反馈汇总** - 新反馈或重要建议的及时提醒
5. **新品上架通知** - 管理员添加新商品时的推广通知

## 核心服务

### 1. FeishuBotService (飞书机器人服务)

**文件位置**: `server/services/feishuBotService.js`

**新增方法**:
- `sendNewUserWelcome(user)` - 发送新用户欢迎通知
- `sendSalesMilestone(milestoneData)` - 发送销售里程碑庆祝通知
- `sendOrderAlert(alertData)` - 发送异常订单预警通知
- `sendFeedbackAlert(feedbackData)` - 发送用户反馈汇总通知
- `sendNewProductNotification(productData)` - 发送新品上架通知

### 2. OperationalNotificationService (运营数据推送服务)

**文件位置**: `server/services/operationalNotificationService.js`

**核心功能**:
- 统一管理各种运营通知的触发逻辑
- 配置里程碑阈值和异常订单预警规则
- 智能判断反馈重要程度
- 获取总体统计数据

## 功能详情

### 1. 新用户欢迎通知

**触发时机**: 飞书用户首次注册时

**通知内容**:
- 用户基本信息（姓名、部门路径）
- 注册时间和认证方式
- 联系方式（手机、邮箱）
- 欢迎词和使用指引

**集成位置**: `server/services/feishuService.js` 的 `createOrUpdateFeishuUser` 方法

### 2. 销售里程碑庆祝

**触发时机**: 每次创建兑换申请后检查

**里程碑配置**:
- 订单数量: [10, 50, 100, 200, 500, 1000, 2000, 5000]
- 光年币收入: [1000, 5000, 10000, 20000, 50000, 100000]
- 人民币收入: [500, 1000, 2000, 5000, 10000, 20000]
- 活跃用户: [10, 25, 50, 100, 200, 500]

**通知内容**:
- 里程碑类型和达成值
- 当前统计数据
- 下一个目标
- 最热门商品信息

**集成位置**: `server/controllers/exchangeController.js` 的 `createExchange` 方法

### 3. 异常订单预警

**触发时机**: 每次创建兑换申请后检查

**预警规则**:
- 大额订单: 光年币 ≥ 5000 或 人民币 ≥ 1000
- 大数量订单: 单次兑换 ≥ 20 个商品
- 频繁下单: 用户单日订单 > 10 单
- 快速下单: 5分钟内下单 > 3 次

**风险级别**:
- 高风险 (red): 超过阈值2倍或快速下单
- 中等风险 (orange): 超过阈值但未达2倍
- 低风险 (yellow): 轻微异常

**通知内容**:
- 风险级别和预警原因
- 订单详细信息
- 用户信息和联系方式
- 处理建议

**集成位置**: `server/controllers/exchangeController.js` 的 `createExchange` 方法

### 4. 用户反馈汇总

**触发时机**: 用户提交反馈时

**重要反馈判断**:
- Bug反馈或投诉类型
- 内容包含紧急关键词: ['紧急', '重要', '严重', '无法使用', '故障', '错误', '异常']

**通知内容**:
- 反馈人信息和部门
- 反馈类型和优先级
- 反馈内容预览
- 统计信息（本月反馈总数、用户反馈次数）

**集成位置**: `server/controllers/feedbackController.js` 的 `createFeedback` 方法

### 5. 新品上架通知

**触发时机**: 管理员添加新商品时

**通知内容**:
- 商品基本信息（名称、分类、上架时间）
- 价格信息（光年币价格、人民币价格）
- 库存状态（根据库存数量显示不同状态）
- 商品描述（限制150字符，超出自动截取）
- 上架人员信息
- 购买链接和管理链接

**库存状态分级**:
- 充足库存: ≥ 100 件（绿色主题）
- 正常库存: 20-99 件（绿色主题）
- 有限库存: 1-19 件（橙色主题，显示⚠️提醒）
- 暂无库存: 0 件（灰色主题，显示🚫标识）

**集成位置**: `server/controllers/productController.js` 的 `createProduct` 方法

## 测试接口

系统提供了手动触发各种通知的测试接口（仅管理员可用）:

- `POST /api/system/trigger-new-user-welcome` - 测试新用户欢迎通知
- `POST /api/system/trigger-sales-milestone` - 测试销售里程碑通知
- `POST /api/system/trigger-order-alert` - 测试异常订单预警
- `POST /api/system/trigger-feedback-alert` - 测试用户反馈通知
- `POST /api/system/trigger-new-product-notification` - 测试新品上架通知

## 配置说明

### 环境变量

- `FEISHU_BOT_WEBHOOK_URL` - 飞书群机器人Webhook地址
- `FRONTEND_URL` - 前端系统URL（用于生成跳转链接）

### 里程碑配置

可在 `OperationalNotificationService` 构造函数中修改里程碑阈值:

```javascript
this.milestones = {
  orders: [10, 50, 100, 200, 500, 1000, 2000, 5000],
  revenue_ly: [1000, 5000, 10000, 20000, 50000, 100000],
  revenue_rmb: [500, 1000, 2000, 5000, 10000, 20000],
  users: [10, 25, 50, 100, 200, 500]
};
```

### 异常订单预警配置

可在 `OperationalNotificationService` 构造函数中修改预警规则:

```javascript
this.alertConfig = {
  highQuantityThreshold: 20,
  highValueThreshold: {
    ly: 5000,
    rmb: 1000
  },
  userDailyOrderLimit: 10,
  timeWindowMinutes: 5
};
```

## 错误处理

所有运营通知都采用非阻塞设计:
- 通知发送失败不会影响主业务流程
- 错误信息会记录到控制台日志
- 提供详细的错误信息用于排查问题

## 测试验证

运行测试脚本验证功能:

```bash
cd server
node test/testOperationalNotifications.js
```

测试脚本会模拟各种场景并发送测试通知到飞书群，验证所有功能是否正常工作。

## 注意事项

1. **仅飞书用户**: 新用户欢迎通知仅针对通过飞书登录的用户
2. **权限要求**: 测试接口需要管理员权限
3. **网络依赖**: 需要确保服务器能访问飞书API
4. **群配置**: 需要正确配置飞书群机器人Webhook地址

## 功能扩展

系统设计支持轻松扩展新的通知类型:

1. 在 `FeishuBotService` 中添加新的发送方法
2. 在 `OperationalNotificationService` 中添加触发逻辑
3. 在相应的控制器中集成调用
4. 添加测试接口和测试用例

通过这套完整的运营数据推送系统，管理员可以实时了解平台运营状况，及时响应用户需求和异常情况。

## 扩展功能 (新增)

### 6. 月度报告

**触发时机**: 每月第1个工作日上午10:00

**报告内容**:
- 更深度的数据分析和洞察
- 月度核心指标（订单总数、日均订单、转化率、环比增长等）
- 收入分析和支付方式占比
- 热门商品和活跃部门TOP5排行
- 活跃用户TOP5和峰值日期分析
- 智能数据洞察（基于数据表现的分析建议）

**数据洞察示例**:
- 订单量环比增长分析
- 用户转化率评估
- 支付方式偏好分析
- 业务活跃度建议

**集成位置**: `ScheduledReportService` 中的月度报告定时任务

### 7. 智能节假日识别

**功能概述**: 基于中国法定节假日和调休日历，智能识别工作日状态

**核心功能**:
- 法定节假日识别（元旦、春节、清明、劳动节、端午、中秋、国庆）
- 调休工作日识别（节假日调休需要上班的周末）
- 工作日数量计算（月度、日期范围）
- 下一个工作日查找

**智能跳过机制**:
- 每日报告在节假日自动跳过
- 每周报告延迟到下一个工作日
- 月度报告推迟到当月第一个工作日

**配置范围**: 支持2024-2025年中国法定节假日，可扩展配置

### 8. 系统维护通知

**通知类型**:
- `scheduled` - 计划维护（蓝色主题）
- `emergency` - 紧急维护（红色主题）
- `update` - 版本更新（绿色主题）
- `hotfix` - 热修复（红色主题）

**通知内容**:
- 维护主题和时间安排
- 维护原因和预计耗时
- 影响范围和准备事项
- 技术支持联系方式

**使用场景**:
- 定期系统维护提醒
- 紧急故障修复通知
- 版本升级公告
- 安全更新提醒

### 9. 错误告警推送

**严重程度分级**:
- `critical` - 严重（红色，1次即发送）
- `high` - 高（红色，10分钟内3次发送）
- `medium` - 中等（橙色，15分钟内5次发送）
- `low` - 低（黄色，30分钟内10次发送）

**智能频率控制**:
- 基于错误类型和严重程度的发送频率限制
- 防止告警风暴和重复通知
- 错误历史记录和统计分析

**预定义错误类型**:
- 数据库连接错误
- API接口错误
- 飞书服务错误
- 文件上传错误

**通知内容**:
- 错误类型和严重程度
- 详细错误信息和堆栈跟踪
- 受影响组件和用户范围
- 推荐解决方案和监控链接

## 新增测试接口

- `POST /api/system/trigger-monthly-report` - 测试月度报告
- `POST /api/system/trigger-maintenance-notification` - 测试系统维护通知
- `POST /api/system/trigger-error-alert` - 测试错误告警推送
- `GET /api/system/holiday-status?date=YYYY-MM-DD` - 查询日期状态
- `GET /api/system/working-days?year=YYYY&month=MM` - 查询工作日统计

## 新增服务组件

### HolidayService (节假日识别服务)
**文件位置**: `server/services/holidayService.js`

**核心方法**:
- `isHoliday(date)` - 判断是否是节假日
- `isWorkingDay(date)` - 判断是否是工作日
- `getDateStatus(date)` - 获取日期完整状态
- `getWorkingDaysInMonth(year, month)` - 获取月度工作日数量
- `getWorkingDaysInRange(startDate, endDate)` - 获取范围工作日数量

### SystemEventService (系统事件通知服务)
**文件位置**: `server/services/systemEventService.js`

**核心方法**:
- `sendMaintenanceNotification(maintenanceInfo)` - 发送维护通知
- `sendErrorAlert(errorInfo)` - 发送错误告警
- `sendDatabaseError(error)` - 数据库错误告警
- `sendAPIError(error, endpoint)` - API错误告警
- `getErrorStatistics()` - 获取错误统计
- `getMaintenanceSchedule()` - 获取维护计划

## 扩展配置说明

### 节假日配置
可在 `HolidayService` 构造函数中添加新年份的节假日配置：

```javascript
this.holidays2026 = {
  '2026-01-01': '元旦',
  // ... 其他节假日
};
```

### 错误告警配置
可在 `SystemEventService` 构造函数中调整告警频率：

```javascript
this.alertConfig.errorThresholds = {
  critical: { count: 1, timeWindow: 300 },
  // ... 其他配置
};
```

## 运行测试

使用扩展功能测试脚本验证所有新功能：

```bash
cd server
node test/testExtendedFeatures.js
```

测试脚本将验证：
- 节假日识别的准确性
- 月度报告的数据分析
- 系统维护通知的发送
- 错误告警的分级推送
- 服务状态和统计功能

通过这些扩展功能，系统提供了更加完善的运营监控和通知能力，支持智能化的报告推送和全面的系统事件管理。 