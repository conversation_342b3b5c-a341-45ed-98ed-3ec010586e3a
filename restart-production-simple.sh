#!/bin/bash

# 生产环境快速重启脚本 (服务器本地执行版本)
# 在服务器 ************** 的 /www/wwwroot/workyy 目录下执行

# 设置颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# 项目配置
PROJECT_DIR="/www/wwwroot/workyy"
PM2_APP_NAME="workyy"
BACKEND_PORT=3000
FRONTEND_PORT=80
SERVER_IP="**************"

echo -e "${YELLOW}===== 生产环境快速重启 =====${NC}"
echo -e "${BLUE}项目目录: $PROJECT_DIR${NC}"
echo -e "${BLUE}PM2应用: $PM2_APP_NAME${NC}"

# 检查是否在正确的目录
if [ ! -d "$PROJECT_DIR" ]; then
    echo -e "${RED}错误: 项目目录 $PROJECT_DIR 不存在${NC}"
    exit 1
fi

cd $PROJECT_DIR

echo -e "${BLUE}1. 检查当前状态...${NC}"
pm2 list

echo -e "${BLUE}2. 重启后端服务...${NC}"
pm2 restart $PM2_APP_NAME
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 后端服务重启成功${NC}"
else
    echo -e "${RED}✗ 后端服务重启失败${NC}"
    exit 1
fi

echo -e "${BLUE}3. 重新加载Nginx...${NC}"
nginx -t
if [ $? -eq 0 ]; then
    systemctl reload nginx
    echo -e "${GREEN}✓ Nginx配置检查通过并重新加载${NC}"
else
    echo -e "${RED}✗ Nginx配置检查失败${NC}"
    exit 1
fi

echo -e "${BLUE}4. 等待服务启动...${NC}"
sleep 5

echo -e "${BLUE}5. 验证服务状态...${NC}"
echo -e "${YELLOW}PM2应用状态:${NC}"
pm2 list | grep $PM2_APP_NAME

echo -e "${YELLOW}Nginx服务状态:${NC}"
systemctl status nginx --no-pager -l | head -10

echo -e "${BLUE}6. 测试服务访问...${NC}"
echo -e "${YELLOW}测试后端API健康检查:${NC}"
curl -s http://localhost:$BACKEND_PORT/api/health || echo "后端API测试失败"

echo -e "${YELLOW}测试前端页面访问:${NC}"
HTTP_CODE=$(curl -s -o /dev/null -w '%{http_code}' http://localhost:$FRONTEND_PORT)
echo "前端页面HTTP状态码: $HTTP_CODE"

echo -e "\n${GREEN}===== 重启完成 =====${NC}"
echo -e "${BLUE}前端访问: ${NC}http://$SERVER_IP"
echo -e "${BLUE}后端API: ${NC}http://$SERVER_IP:$BACKEND_PORT/api"
echo -e "${BLUE}管理后台: ${NC}http://$SERVER_IP/admin"

echo -e "\n${YELLOW}常用监控命令:${NC}"
echo -e "${BLUE}查看PM2状态: ${NC}pm2 monit"
echo -e "${BLUE}查看应用日志: ${NC}pm2 logs $PM2_APP_NAME --lines 50"
echo -e "${BLUE}查看Nginx日志: ${NC}tail -f /var/log/nginx/access.log"
