-- 库存数据一致性验证脚本
-- 检查商品表和职场库存表之间的数据一致性

-- 1. 检查所有商品的库存一致性
SELECT 
    '库存一致性检查' as check_type,
    p.id,
    p.name,
    p.stock as product_stock,
    p.stockManagementType,
    COALESCE(SUM(pws.stock), 0) as workplace_stock_sum,
    CASE 
        WHEN p.stockManagementType = 'single' THEN 'OK (单一库存模式)'
        WHEN p.stockManagementType = 'workplace' AND p.stock = COALESCE(SUM(pws.stock), 0) THEN 'OK'
        WHEN p.stockManagementType = 'workplace' AND p.stock != COALESCE(SUM(pws.stock), 0) THEN 'ERROR (库存不一致)'
        ELSE 'UNKNOWN'
    END as validation_status
FROM products p
LEFT JOIN product_workplace_stocks pws ON p.id = pws.productId
GROUP BY p.id, p.name, p.stock, p.stockManagementType
ORDER BY p.id;

-- 2. 统计各种库存管理模式的商品数量
SELECT 
    '库存管理模式统计' as check_type,
    stockManagementType,
    COUNT(*) as product_count,
    SUM(stock) as total_stock
FROM products 
GROUP BY stockManagementType;

-- 3. 检查是否有孤立的职场库存记录（商品不存在）
SELECT 
    '孤立职场库存检查' as check_type,
    pws.id as workplace_stock_id,
    pws.productId,
    pws.workplaceId,
    pws.stock,
    'ERROR (商品不存在)' as status
FROM product_workplace_stocks pws
LEFT JOIN products p ON pws.productId = p.id
WHERE p.id IS NULL;

-- 4. 检查职场分配模式商品是否都有职场库存记录
SELECT 
    '职场库存记录检查' as check_type,
    p.id,
    p.name,
    p.stockManagementType,
    COUNT(pws.id) as workplace_stock_records,
    CASE 
        WHEN p.stockManagementType = 'workplace' AND COUNT(pws.id) = 0 THEN 'ERROR (缺少职场库存记录)'
        WHEN p.stockManagementType = 'workplace' AND COUNT(pws.id) > 0 THEN 'OK'
        WHEN p.stockManagementType = 'single' THEN 'OK (单一库存模式)'
        ELSE 'UNKNOWN'
    END as status
FROM products p
LEFT JOIN product_workplace_stocks pws ON p.id = pws.productId
WHERE p.stockManagementType = 'workplace'
GROUP BY p.id, p.name, p.stockManagementType
HAVING COUNT(pws.id) = 0;

-- 5. 显示当前所有商品的详细库存信息
SELECT 
    '详细库存信息' as info_type,
    p.id,
    p.name,
    p.stock as total_stock,
    p.stockManagementType,
    p.stockSyncedAt,
    GROUP_CONCAT(
        CONCAT(w.name, ':', pws.stock) 
        ORDER BY w.name 
        SEPARATOR ', '
    ) as workplace_stocks
FROM products p
LEFT JOIN product_workplace_stocks pws ON p.id = pws.productId
LEFT JOIN workplaces w ON pws.workplaceId = w.id
GROUP BY p.id, p.name, p.stock, p.stockManagementType, p.stockSyncedAt
ORDER BY p.id;
