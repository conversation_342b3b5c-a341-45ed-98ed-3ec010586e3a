<template>
  <div class="user-management">
    <div class="page-header">
      <h1 class="page-title">用户管理</h1>

      <!-- 用户统计卡片 -->
      <div class="stats-cards">
        <el-card class="stats-card total-users">
          <div class="stats-icon"><el-icon><User /></el-icon></div>
          <div class="stats-info">
            <div class="stats-title">总用户数</div>
            <div class="stats-value">{{ userStats.totalUsers }}</div>
          </div>
        </el-card>

        <el-card class="stats-card admin-users">
          <div class="stats-icon"><el-icon><UserFilled /></el-icon></div>
          <div class="stats-info">
            <div class="stats-title">管理员</div>
            <div class="stats-value">{{ userStats.adminUsers }}</div>
          </div>
        </el-card>

        <el-card class="stats-card regular-users">
          <div class="stats-icon"><el-icon><Avatar /></el-icon></div>
          <div class="stats-info">
            <div class="stats-title">普通用户</div>
            <div class="stats-value">{{ userStats.regularUsers }}</div>
          </div>
        </el-card>

        <el-card class="stats-card disabled-users">
          <div class="stats-icon"><el-icon><CircleCloseFilled /></el-icon></div>
          <div class="stats-info">
            <div class="stats-title">禁用用户</div>
            <div class="stats-value">{{ userStats.disabledUsers }}</div>
          </div>
        </el-card>
      </div>

      <div class="header-actions">
      <div class="search-container">
        <el-input
          v-model="searchQuery"
          placeholder="搜索姓名或邮箱"
          clearable
          @clear="fetchUsers"
          @keyup.enter="fetchUsers"
          class="search-input"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="fetchUsers">
          <el-icon><Search /></el-icon>
          查询
        </el-button>
        <el-button type="info" @click="toggleAdvancedSearch">
          <el-icon><SetUp /></el-icon>
          {{ showAdvancedSearch ? '收起' : '高级搜索' }}
        </el-button>
        </div>

        <!-- 高级搜索表单 -->
        <div class="advanced-search-form" v-if="showAdvancedSearch">
          <el-form :model="searchForm" inline>
            <el-form-item label="关键词">
              <el-input v-model="searchForm.search" placeholder="搜索姓名或邮箱" clearable />
            </el-form-item>

            <el-form-item label="角色">
              <el-select v-model="searchForm.role" placeholder="选择角色" clearable>
                <el-option label="管理员" value="admin" />
                <el-option label="普通用户" value="user" />
              </el-select>
            </el-form-item>

            <el-form-item label="部门">
              <el-input v-model="searchForm.department" placeholder="输入部门" clearable />
            </el-form-item>

            <el-form-item label="职场">
              <el-select v-model="searchForm.workplace" placeholder="选择职场" clearable>
                <el-option v-for="item in workplaces" :key="item.name" :label="item.name" :value="item.name" />
              </el-select>
            </el-form-item>

            <el-form-item label="状态">
              <el-select v-model="searchForm.isActive" placeholder="选择状态" clearable>
                <el-option label="正常" :value="true" />
                <el-option label="已禁用" :value="false" />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="fetchUsers">搜索</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="action-buttons">
          <el-button type="primary" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            新建用户
          </el-button>
          <el-button type="success" @click="showImportDialog">
            <el-icon><Upload /></el-icon>
            批量导入
          </el-button>
          <el-button type="danger" @click="batchDeleteUsers" :disabled="selectedUsers.length === 0">
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
          <el-dropdown @command="exportUserList">
            <el-button>
              <el-icon><Download /></el-icon>
              导出
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="csv">导出为CSV</el-dropdown-item>
                <el-dropdown-item command="xlsx">导出为Excel</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="users"
      border
      stripe
      style="width: 100%"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column
        prop="username"
        label="姓名"
        min-width="150"
        sortable="custom"
      />
      <el-table-column prop="email" label="邮箱" min-width="200">
        <template #default="{ row }">
          <span>{{ row.email || '未设置' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="mobile" label="手机号码" min-width="120">
        <template #default="{ row }">
          <span>{{ row.mobile ? formatMobileNumber(row.mobile) : '未设置' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="department"
        label="部门"
        width="120"
        :filters="departmentFilters"
        :filter-method="filterHandler"
        column-key="department"
      >
        <template #default="{ row }">
          <span>{{ row.department || '未设置' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="departmentPath" label="部门路径" width="250">
        <template #default="{ row }">
          <el-tooltip v-if="row.departmentPath && row.departmentPath.length > 20"
                      :content="row.departmentPath"
                      placement="top"
                      :show-after="500">
            <span class="text-truncate-path">{{ row.departmentPath }}</span>
          </el-tooltip>
          <span v-else-if="row.departmentPath">{{ row.departmentPath }}</span>
          <span v-else-if="row.department">{{ row.department }}</span>
          <span v-else>未设置</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="workplace"
        label="职场"
        width="120"
        :filters="workplaceFilters"
        :filter-method="filterHandler"
        column-key="workplace"
      >
        <template #default="{ row }">
          <span>{{ row.workplace || '未设置' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="role"
        label="角色"
        width="120"
        sortable="custom"
        :filters="roleFilters"
        :filter-method="filterHandler"
        column-key="role"
      >
        <template #default="{ row }">
          <el-tag :type="row.role === 'admin' ? 'danger' : 'info'">
            {{ row.role === 'admin' ? '管理员' : '普通用户' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="isActive"
        label="状态"
        width="100"
        sortable="custom"
        :filters="statusFilters"
        :filter-method="filterHandler"
        column-key="isActive"
      >
        <template #default="{ row }">
          <el-tag :type="row.isActive === false ? 'danger' : 'success'" size="small">
            {{ row.isActive === false ? '已禁用' : '正常' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="createdAt"
        label="注册时间"
        width="180"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ formatDateTime(row.createdAt) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="360" fixed="right">
        <template #default="{ row }">
          <el-button size="small" type="success" @click="showUserDetail(row)">
            <el-icon><View /></el-icon>
            详情
          </el-button>

          <el-dropdown size="small" split-button type="primary" style="margin-left: 8px;" @command="handleBasicInfoCommand($event, row)">
            基本信息
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="department">部门</el-dropdown-item>
                <el-dropdown-item command="workplace">职场</el-dropdown-item>
                <el-dropdown-item command="email">邮箱</el-dropdown-item>
                <el-dropdown-item command="mobile">手机号码</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <el-dropdown size="small" split-button type="warning" style="margin-left: 8px;" @command="handleSecurityCommand($event, row)">
            安全设置
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="role" :disabled="isSelfUser(row.id)">角色</el-dropdown-item>
                <el-dropdown-item command="resetPwd">重置密码</el-dropdown-item>
                <el-dropdown-item
                  command="status"
                  :disabled="isSelfUser(row.id)"
                  divided
                >
                  {{ row.isActive === false ? '启用账号' : '禁用账号' }}
                </el-dropdown-item>
                <el-dropdown-item command="delete" :disabled="isSelfUser(row.id)" divided>删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="fetchUsers"
        @current-change="fetchUsers"
      />
    </div>

    <!-- 批量操作浮动工具栏 -->
    <transition name="fade">
      <div v-if="selectedUsers.length > 0" class="batch-operations-toolbar">
        <div class="toolbar-info">
          <el-icon><Select /></el-icon>
          <span>已选择 {{ selectedUsers.length }} 个用户</span>
        </div>
        <div class="toolbar-actions">
          <el-button-group>
            <el-button type="success" size="small" @click="batchEnableUsers">
              <el-icon><Check /></el-icon>
              批量启用
            </el-button>
            <el-button type="warning" size="small" @click="batchDisableUsers">
              <el-icon><Close /></el-icon>
              批量禁用
            </el-button>
          </el-button-group>

          <el-dropdown @command="handleBatchDepartment" trigger="click">
            <el-button type="primary" size="small">
              批量修改部门
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="edit">选择部门</el-dropdown-item>
                <el-dropdown-item command="clear" divided>清除部门</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <el-dropdown @command="handleBatchWorkplace" trigger="click">
            <el-button type="primary" size="small">
              批量修改职场
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-for="item in workplaces" :key="item.id" :command="item.name">
                  {{ item.name }}
                </el-dropdown-item>
                <el-dropdown-item command="clear" divided>清除职场</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <el-button type="danger" size="small" @click="batchDeleteUsers">
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>

          <el-button type="info" size="small" @click="clearSelection">
            <el-icon><Close /></el-icon>
            取消选择
          </el-button>
        </div>
      </div>
    </transition>

    <!-- 创建用户对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      title="创建新用户"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-position="top"
      >
        <el-form-item label="姓名" prop="username">
          <el-input v-model="createForm.username" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="createForm.password" type="password" show-password placeholder="请输入密码" />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="createForm.confirmPassword" type="password" show-password placeholder="请再次输入密码" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="createForm.email" placeholder="请输入邮箱" />
          <div class="form-tip">邮箱必须唯一，不可与其他用户重复</div>
        </el-form-item>
        <el-form-item label="手机号码" prop="mobile">
          <el-input v-model="createForm.mobile" placeholder="请输入手机号码（可选）" />
        </el-form-item>
        <el-form-item label="部门" prop="department">
          <el-input v-model="createForm.department" placeholder="请输入部门（可选）" />
        </el-form-item>
        <el-form-item label="职场" prop="workplace">
          <el-select v-model="createForm.workplace" placeholder="请选择职场（可选）" clearable class="w-full">
            <el-option label="北京" value="北京" />
            <el-option label="武汉" value="武汉" />
            <el-option label="长沙" value="长沙" />
            <el-option label="西安" value="西安" />
            <el-option label="深圳" value="深圳" />
          </el-select>
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="createForm.role" class="w-full">
            <el-option label="普通用户" value="user" />
            <el-option label="管理员" value="admin" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="createDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="createUser" :loading="submitting">
            创建
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入用户对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="批量导入用户"
      width="500px"
      destroy-on-close
    >
      <div class="import-tips">
        <el-alert
          title="请上传符合格式的文件，文件大小不超过2MB"
          type="info"
          :closable="false"
          show-icon
          class="mb-4"
        />
        <p>文件格式要求:</p>
        <ul>
          <li>支持的格式: CSV, Excel(.xlsx/.xls)</li>
          <li>第一行为表头：username,password,email,role,department,workplace,mobile,registrationDate</li>
          <li>姓名(username)、密码(password)和邮箱(email)为必填项</li>
          <li>角色(role)必须为admin或user，默认为user</li>
          <li>邮箱(email)必须以@guanghe.tv结尾</li>
          <li>部门(department)和职场(workplace)为可选项</li>
          <li>职场(workplace)可选值：北京、武汉、长沙、西安、深圳</li>
          <li>注册时间(registrationDate)为可选项，支持常见日期格式，如：2023-01-15、2023/1/15、2024/5/1</li>
        </ul>
        <div class="download-template">
          <el-button-group>
            <el-button size="small" @click="downloadTemplate('csv')">下载CSV模板</el-button>
            <el-button size="small" @click="downloadTemplate('xlsx')">下载Excel模板</el-button>
          </el-button-group>
        </div>
      </div>

      <el-upload
        class="upload-container"
        drag
        :action="null"
        :auto-upload="false"
        :on-change="handleFileChange"
        :on-remove="() => { importFile.value = null; importFileList.value = []; }"
        :before-remove="() => { return true; }"
        :file-list="importFileList"
        :limit="1"
        accept=".csv,.xlsx,.xls"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">拖拽文件到此处或 <em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip">
            支持CSV和Excel文件(.xlsx,.xls)
          </div>
        </template>
      </el-upload>

      <div class="import-troubleshooting">
        <el-collapse>
          <el-collapse-item title="常见导入问题解决方法" name="1">
            <ol class="troubleshooting-list">
              <li>确保文件格式正确（CSV或Excel）且文件大小不超过2MB</li>
              <li>确保所有必填字段（姓名、密码、邮箱）都已填写</li>
              <li>确保邮箱地址以@guanghe.tv结尾</li>
              <li>确保角色值只能是"admin"或"user"</li>
              <li>确保密码长度至少为6个字符</li>
              <li>避免使用已存在的邮箱</li>
              <li>如果使用Excel，请确保没有特殊格式和公式</li>
              <li>尝试使用系统提供的模板，避免手动创建表格</li>
            </ol>
          </el-collapse-item>
        </el-collapse>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="importUsers" :loading="importing" :disabled="!importFile">
            开始导入
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 部门对话框 -->
    <el-dialog
      v-model="departmentDialogVisible"
      title="更新用户部门"
      width="400px"
      destroy-on-close
    >
      <template v-if="selectedUser">
        <p class="dialog-user-info">
          用户：{{ selectedUser.username }}
          <el-tag size="small" class="ml-2">当前部门: {{ selectedUser.department || '未设置' }}</el-tag>
        </p>
        <el-form
          ref="departmentFormRef"
          :model="departmentForm"
          :rules="departmentRules"
          label-position="top"
        >
          <el-form-item label="部门名称" prop="department">
            <el-input
              v-model="departmentForm.department"
              placeholder="请输入部门名称"
              clearable
              class="w-full"
            />
          </el-form-item>
        </el-form>
      </template>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="departmentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateDepartment" :loading="submitting">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 角色对话框 -->
    <el-dialog
      v-model="roleDialogVisible"
      title="修改用户角色"
      width="400px"
      destroy-on-close
    >
      <template v-if="selectedUser">
        <p class="dialog-user-info">
          用户：{{ selectedUser.username }}
          <el-tag size="small" class="ml-2" :type="selectedUser.role === 'admin' ? 'danger' : 'info'">
            当前角色: {{ selectedUser.role === 'admin' ? '管理员' : '普通用户' }}
          </el-tag>
        </p>
        <el-form
          ref="roleFormRef"
          :model="roleForm"
          :rules="roleRules"
          label-position="top"
        >
          <el-form-item label="选择角色" prop="role">
            <el-select v-model="roleForm.role" class="w-full">
              <el-option label="普通用户" value="user" />
              <el-option label="管理员" value="admin" />
            </el-select>
          </el-form-item>
        </el-form>
      </template>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="roleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateRole" :loading="submitting">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 职场对话框 -->
    <el-dialog
      v-model="workplaceDialogVisible"
      title="更新用户职场"
      width="400px"
      destroy-on-close
    >
      <template v-if="selectedUser">
        <p class="dialog-user-info">
          用户：{{ selectedUser.username }}
          <el-tag size="small" class="ml-2">当前职场: {{ selectedUser.workplace || '未设置' }}</el-tag>
        </p>
        <el-form
          ref="workplaceFormRef"
          :model="workplaceForm"
          :rules="workplaceRules"
          label-position="top"
        >
          <el-form-item label="选择职场" prop="workplace">
            <el-select
              v-model="workplaceForm.workplace"
              placeholder="请选择职场"
              clearable
              class="w-full"
              :loading="loadingWorkplaces"
            >
              <el-option
                v-for="item in workplaces"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </template>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="workplaceDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateWorkplace" :loading="submitting">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 邮箱对话框 -->
    <el-dialog
      v-model="emailDialogVisible"
      title="更新用户邮箱"
      width="400px"
      destroy-on-close
    >
      <template v-if="selectedUser">
        <p class="dialog-user-info">
          用户：{{ selectedUser.username }}
          <el-tag size="small" class="ml-2">当前邮箱: {{ selectedUser.email || '未设置' }}</el-tag>
        </p>
        <el-form
          ref="emailFormRef"
          :model="emailForm"
          :rules="emailRules"
          label-position="top"
        >
          <el-form-item label="邮箱地址" prop="email">
            <el-input
              v-model="emailForm.email"
              placeholder="请输入邮箱地址"
              clearable
              class="w-full"
            />
          </el-form-item>
        </el-form>
      </template>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="emailDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateEmail" :loading="submitting">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 手机号码对话框 -->
    <el-dialog
      v-model="mobileDialogVisible"
      title="更新用户手机号码"
      width="400px"
      destroy-on-close
    >
      <template v-if="selectedUser">
        <p class="dialog-user-info">
          用户：{{ selectedUser.username }}
          <el-tag size="small" class="ml-2">当前手机号码: {{ selectedUser.mobile ? formatMobileNumber(selectedUser.mobile) : '未设置' }}</el-tag>
        </p>
        <el-form
          ref="mobileFormRef"
          :model="mobileForm"
          :rules="mobileRules"
          label-position="top"
        >
          <el-form-item label="手机号码" prop="mobile">
            <el-input
              v-model="mobileForm.mobile"
              placeholder="请输入手机号码"
              clearable
              class="w-full"
            />
          </el-form-item>
        </el-form>
      </template>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="mobileDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateMobile" :loading="submitting">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog
      v-model="resetDialogVisible"
      title="重置用户密码"
      width="400px"
      destroy-on-close
    >
      <template v-if="selectedUser">
        <p class="dialog-user-info">
          用户：{{ selectedUser.username }}
        </p>
        <el-alert
          title="请谨慎操作，密码重置后将立即生效"
          type="warning"
          :closable="false"
          show-icon
          class="mb-4"
        />
        <el-form
          ref="passwordFormRef"
          :model="passwordForm"
          :rules="passwordRules"
          label-position="top"
        >
          <el-form-item label="新密码" prop="password">
            <el-input
              v-model="passwordForm.password"
              type="password"
              show-password
              placeholder="请输入新密码"
            />
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="passwordForm.confirmPassword"
              type="password"
              show-password
              placeholder="请再次输入新密码"
            />
          </el-form-item>
        </el-form>
      </template>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="resetPassword" :loading="submitting">
            确认重置
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="userDetailDialogVisible"
      title="用户详情"
      width="700px"
      destroy-on-close
      top="5vh"
    >
      <template v-if="selectedUser">
        <div class="user-detail-container">
          <!-- 基本信息 -->
          <div class="detail-section">
            <h3 class="section-title">基本信息</h3>
            <div class="detail-grid">
              <div class="detail-item">
                <label>用户ID:</label>
                <span>{{ selectedUser.id }}</span>
              </div>
              <div class="detail-item">
                <label>姓名:</label>
                <span>{{ selectedUser.username || '未设置' }}</span>
              </div>
              <div class="detail-item">
                <label>邮箱:</label>
                <span>{{ selectedUser.email || '未设置' }}</span>
              </div>
              <div class="detail-item">
                <label>手机号码:</label>
                <span>{{ selectedUser.mobile ? formatMobileNumber(selectedUser.mobile) : '未设置' }}</span>
              </div>
              <div class="detail-item">
                <label>角色:</label>
                <el-tag :type="selectedUser.role === 'admin' ? 'danger' : 'info'" size="small">
                  {{ selectedUser.role === 'admin' ? '管理员' : '普通用户' }}
                </el-tag>
              </div>
              <div class="detail-item">
                <label>光年币:</label>
                <span class="points-value">{{ selectedUser.points || 0 }}</span>
              </div>
            </div>
          </div>

          <!-- 部门信息 -->
          <div class="detail-section">
            <h3 class="section-title">部门信息</h3>
            <div class="detail-grid">
              <div class="detail-item">
                <label>当前部门:</label>
                <span>{{ selectedUser.department || '未设置' }}</span>
              </div>
              <div class="detail-item">
                <label>职场:</label>
                <span>{{ selectedUser.workplace || '未设置' }}</span>
              </div>
              <div class="detail-item full-width">
                <label>完整部门路径:</label>
                <div class="department-path">
                  {{ selectedUser.departmentPath || selectedUser.department || '未设置' }}
                </div>
              </div>
            </div>
          </div>

          <!-- 系统信息 -->
          <div class="detail-section">
            <h3 class="section-title">系统信息</h3>
            <div class="detail-grid">
              <div class="detail-item">
                <label>注册时间:</label>
                <span>{{ formatDateTime(selectedUser.createdAt) }}</span>
              </div>
              <div class="detail-item">
                <label>最后更新:</label>
                <span>{{ formatDateTime(selectedUser.updatedAt) }}</span>
              </div>
              <div class="detail-item">
                <label>最后登录:</label>
                <span>{{ selectedUser.lastLoginAt ? formatDateTime(selectedUser.lastLoginAt) : '未记录' }}</span>
              </div>
              <div class="detail-item">
                <label>登录IP:</label>
                <span>{{ selectedUser.lastLoginIp || '未记录' }}</span>
              </div>
              <div class="detail-item">
                <label>账户状态:</label>
                <el-tag :type="selectedUser.isActive === false ? 'danger' : 'success'" size="small">
                  {{ selectedUser.isActive === false ? '已禁用' : '正常' }}
                </el-tag>
              </div>
              <div class="detail-item">
                <label>数据来源:</label>
                <span>{{ selectedUser.email && selectedUser.email.includes('@guanghe.tv') ? '飞书同步' : '手动创建' }}</span>
              </div>
            </div>
          </div>
        </div>
      </template>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="userDetailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量修改部门对话框 -->
    <el-dialog
      v-model="batchDepartmentDialogVisible"
      title="批量修改部门"
      width="400px"
      destroy-on-close
    >
      <p class="dialog-user-info">
        已选择: <el-tag type="info">{{ selectedUsers.length }}</el-tag> 个用户
      </p>
      <el-form
        ref="batchDepartmentFormRef"
        :model="batchDepartmentForm"
        :rules="departmentRules"
        label-position="top"
      >
        <el-form-item label="部门名称" prop="department">
          <el-input
            v-model="batchDepartmentForm.department"
            placeholder="请输入部门名称"
            clearable
            class="w-full"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchDepartmentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateBatchDepartment" :loading="submitting">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Plus, Upload, Download, UploadFilled, ArrowDown, Delete, View, User, UserFilled, Avatar, CircleCloseFilled, SetUp, Check, Close, Select, OfficeBuilding } from '@element-plus/icons-vue';
import { useAuthStore } from '../../stores/auth';
import { formatMobileNumber } from '../../utils/format';
import {
  getUsers,
  createUser as apiCreateUser,
  deleteUser,
  updateUserRole,
  updateUserDepartment,
  updateUserEmail,
  updateUserMobile,
  updateUserWorkplace,
  resetUserPassword as apiResetUserPassword,
  importUsers as apiImportUsers,
  exportUsers as apiExportUsers,
  downloadUserTemplate,
  batchDeleteUsers as apiBatchDeleteUsers,
  toggleUserStatus as apiToggleUserStatus,
  batchUpdateUserStatus,
  batchUpdateUserDepartment,
  batchUpdateUserWorkplace
} from '@/api/users';
import { getAllActiveWorkplaces } from '../../api/system';

const authStore = useAuthStore();

// 用户数据
const users = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const searchQuery = ref('');
const submitting = ref(false);

// 高级搜索相关
const showAdvancedSearch = ref(false);
const searchForm = reactive({
  search: '',
  role: '',
  department: '',
  workplace: '',
  isActive: ''
});

// 排序参数
const sortParams = reactive({
  sortField: 'createdAt',
  sortOrder: 'DESC'
});

// 筛选器配置
const roleFilters = computed(() => [
  { text: '管理员', value: 'admin' },
  { text: '普通用户', value: 'user' }
]);

const statusFilters = computed(() => [
  { text: '正常', value: true },
  { text: '已禁用', value: false }
]);

const departmentFilters = computed(() => {
  const departments = [...new Set(users.value.map(user => user.department).filter(Boolean))];
  return departments.map(dept => ({ text: dept, value: dept }));
});

const workplaceFilters = computed(() => {
  const workplaceSet = [...new Set(users.value.map(user => user.workplace).filter(Boolean))];
  return workplaceSet.map(wp => ({ text: wp, value: wp }));
});

// 当前选中的用户
const selectedUser = ref(null);

// 对话框状态
const departmentDialogVisible = ref(false);
const workplaceDialogVisible = ref(false);
const emailDialogVisible = ref(false);
const mobileDialogVisible = ref(false);
const roleDialogVisible = ref(false);
const createDialogVisible = ref(false);
const resetDialogVisible = ref(false);
const importDialogVisible = ref(false);
const importing = ref(false);
const userDetailDialogVisible = ref(false);

// 批量操作对话框状态
const batchDepartmentDialogVisible = ref(false);
const batchDepartmentFormRef = ref(null);
const batchDepartmentForm = reactive({
  department: ''
});

// 导入文件
const importFile = ref(null);
const importFileList = ref([]);

// 表单引用
const departmentFormRef = ref(null);
const roleFormRef = ref(null);
const workplaceFormRef = ref(null);
const emailFormRef = ref(null);
const mobileFormRef = ref(null);
const passwordFormRef = ref(null);
const createFormRef = ref(null);

// 表单数据
const departmentForm = reactive({
  department: ''
});

const workplaceForm = reactive({
  workplace: ''
});

const emailForm = ref({ email: '' });
const emailRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ]
};

const mobileForm = ref({ mobile: '' });
const mobileRules = {
  mobile: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur' }
  ]
};

const roleForm = reactive({
  role: ''
});

const passwordForm = reactive({
  password: '',
  confirmPassword: ''
});

const createForm = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  email: '',
  role: 'user',
  department: '',
  workplace: '',
  mobile: ''
});

// 表单验证规则
const departmentRules = {
  department: [
    { max: 50, message: '部门名称不能超过50个字符', trigger: 'blur' }
  ]
};

const workplaceRules = {
  workplace: [
    { max: 50, message: '职场名称不能超过50个字符', trigger: 'blur' }
  ]
};

const roleRules = {
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
};

const passwordRules = {
  password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少为6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.password) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
};

const createRules = {
  username: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 30, message: '姓名长度在2到30个字符之间', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少为6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== createForm.password) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value || value === '') {
          callback(new Error('邮箱不能为空'));
          return;
        }
        if (!value.endsWith('@guanghe.tv')) {
          callback(new Error('邮箱必须以@guanghe.tv结尾'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
};

// 判断是否是当前用户
const isSelfUser = (userId) => {
  return userId === authStore.user?.id;
};

// 获取用户列表
const fetchUsers = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      search: searchForm.search || searchQuery.value,
      role: searchForm.role,
      department: searchForm.department,
      workplace: searchForm.workplace,
      isActive: searchForm.isActive === '' ? undefined : searchForm.isActive,
      sortField: sortParams.sortField,
      sortOrder: sortParams.sortOrder
    };

    const response = await getUsers(params);
    users.value = response.data || [];
    total.value = response.total || 0;
  } catch (error) {
    console.error('获取用户列表失败:', error);
    ElMessage.error('获取用户列表失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 处理表格排序变化
const handleSortChange = ({ prop, order }) => {
  if (prop && order) {
    sortParams.sortField = prop;
    sortParams.sortOrder = order === 'ascending' ? 'ASC' : 'DESC';
  } else {
    // 重置为默认排序
    sortParams.sortField = 'createdAt';
    sortParams.sortOrder = 'DESC';
  }
  fetchUsers();
};

// 表格筛选处理
const filterHandler = (value, row, column) => {
  const property = column.property;
  if (property === 'isActive') {
    return row[property] === value;
  }
  return row[property] === value;
};

// 切换高级搜索显示
const toggleAdvancedSearch = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value;
  if (showAdvancedSearch.value) {
    searchForm.search = searchQuery.value;
  }
};

// 重置搜索条件
const resetSearch = () => {
  searchForm.search = '';
  searchForm.role = '';
  searchForm.department = '';
  searchForm.workplace = '';
  searchForm.isActive = '';
  fetchUsers();
};

// 显示创建用户对话框
const showCreateDialog = () => {
  createForm.username = '';
  createForm.password = '';
  createForm.confirmPassword = '';
  createForm.email = '';
  createForm.role = 'user';
  createForm.department = '';
  createForm.workplace = '';
  createForm.mobile = '';
  createDialogVisible.value = true;
};

// 创建用户
const createUser = async () => {
  if (!createFormRef.value) return;

  await createFormRef.value.validate(async (valid) => {
    if (!valid) return;

    submitting.value = true;

    try {
      const userData = {
        username: createForm.username,
        password: createForm.password,
        email: createForm.email,
        role: createForm.role,
        department: createForm.department || undefined,
        workplace: createForm.workplace || undefined,
        mobile: createForm.mobile || undefined
      };

      await apiCreateUser(userData);

      ElMessage.success('用户创建成功');
      createDialogVisible.value = false;
      fetchUsers();
    } catch (error) {
      console.error('创建用户失败:', error);
      if (error.response?.status === 409) {
        // 处理冲突错误
        if (error.response.data.code === 'EMAIL_ALREADY_EXIST' ||
            error.response.data.message.includes('邮箱已被注册')) {
          ElMessage.error(`邮箱"${createForm.email}"已被其他用户使用，请更换邮箱地址`);
        } else if (error.response.data.code === 'USERNAME_ALREADY_EXIST' ||
                  error.response.data.message.includes('用户名已存在')) {
          // 这个分支应该不会被触发，因为我们已经移除了用户名唯一约束
          ElMessage.error(`用户名"${createForm.username}"已被使用，请更换用户名`);
        } else {
          ElMessage.error(error.response.data.message || '创建用户失败，用户信息冲突');
        }
      } else if (error.response?.status === 500) {
        // 显示服务器错误的更多信息（如果有的话）
        const errorMessage = error.response.data.error
          ? `服务器错误: ${error.response.data.error}`
          : error.response.data.message || '服务器内部错误，请联系管理员';
        ElMessage.error(errorMessage);
      } else {
        ElMessage.error(error.response?.data?.message || '创建用户失败，请稍后重试');
      }
    } finally {
      submitting.value = false;
    }
  });
};

// 确认删除用户
const confirmDeleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？此操作不可逆！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    await deleteUser(user.id);
    ElMessage.success('用户删除成功');
    fetchUsers();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error);
      ElMessage.error(error.response?.data?.message || '删除用户失败，请稍后重试');
    }
  }
};

// 处理基本信息命令
const handleBasicInfoCommand = (command, row) => {
  console.log('处理命令:', command, '用户:', row);
  selectedUser.value = row;

  switch (command) {
    case 'department':
      // 显示部门对话框
      departmentForm.department = row.department || '';
      departmentDialogVisible.value = true;
      break;
    case 'workplace':
      // 显示职场对话框
      workplaceForm.workplace = row.workplace || '';
      fetchWorkplaces();
      workplaceDialogVisible.value = true;
      break;
    case 'email':
      // 显示邮箱对话框
      emailForm.value.email = row.email || '';
      emailDialogVisible.value = true;
      break;
    case 'mobile':
      // 显示手机号码对话框
      mobileForm.value.mobile = row.mobile || '';
      mobileDialogVisible.value = true;
      break;
  }
};

// 处理安全设置菜单命令
const handleSecurityCommand = (command, row) => {
  selectedUser.value = row;

  switch (command) {
    case 'role':
      showRoleDialog(row);
      break;
    case 'resetPwd':
      showResetDialog(row);
      break;
    case 'status':
      toggleUserStatus(row);
      break;
    case 'delete':
      confirmDeleteUser(row);
      break;
  }
};

// 显示部门对话框
const showDepartmentDialog = (user) => {
  selectedUser.value = user;
  departmentForm.department = user.department || '';
  departmentDialogVisible.value = true;
};

// 更新用户部门
const updateDepartment = async () => {
  if (!departmentFormRef.value) return;

  await departmentFormRef.value.validate(async (valid) => {
    if (!valid) return;

    submitting.value = true;

    try {
      await updateUserDepartment(selectedUser.value.id, departmentForm.department);

      ElMessage.success('用户部门更新成功');
      departmentDialogVisible.value = false;
      fetchUsers();
    } catch (error) {
      console.error('更新用户部门失败:', error);
      ElMessage.error('更新用户部门失败，请稍后重试');
    } finally {
      submitting.value = false;
    }
  });
};

// 显示职场对话框
const showWorkplaceDialog = (user) => {
  selectedUser.value = user;
  workplaceForm.workplace = user.workplace || '';
  workplaceDialogVisible.value = true;
};

// 更新用户职场
const updateWorkplace = async () => {
  if (!workplaceFormRef.value) return;

  await workplaceFormRef.value.validate(async (valid) => {
    if (!valid) return;

    submitting.value = true;

    try {
      // 获取workplaceId
      let workplaceId = null;
      if (workplaceForm.workplace) {
        const selectedWorkplace = workplaces.value.find(w => w.name === workplaceForm.workplace);
        if (selectedWorkplace) {
          workplaceId = selectedWorkplace.id;
        }
      }

      await updateUserWorkplace(selectedUser.value.id, {
        workplace: workplaceForm.workplace,
        workplaceId
      });

      ElMessage.success('职场更新成功');
      workplaceDialogVisible.value = false;
      fetchUsers();
    } catch (error) {
      ElMessage.error('职场更新失败: ' + (error.message || '未知错误'));
    } finally {
      submitting.value = false;
    }
  });
};

// 显示邮箱对话框
const showEmailDialog = (user) => {
  selectedUser.value = user;
  emailForm.email = user.email || '';
  emailDialogVisible.value = true;
};

// 更新用户邮箱
const updateEmail = async () => {
  if (!emailFormRef.value) return;

  await emailFormRef.value.validate(async (valid) => {
    if (!valid) return;

    submitting.value = true;

    try {
      await updateUserEmail(selectedUser.value.id, emailForm.email);

      ElMessage.success('用户邮箱更新成功');
      emailDialogVisible.value = false;
      fetchUsers();
    } catch (error) {
      console.error('更新用户邮箱失败:', error);
      if (error.response?.data?.message) {
        // 检查是否是邮箱已存在的错误
        if (error.response.data.code === 'EMAIL_ALREADY_EXIST' ||
            error.response.data.message.includes('邮箱已被使用') ||
            error.response.data.message.includes('already exist') ||
            error.response.data.message.includes('duplicate')) {
          ElMessage.error(`邮箱"${emailForm.email}"已被其他用户使用，请更换邮箱地址`);
        } else {
          ElMessage.error(error.response.data.message);
        }
      } else {
        ElMessage.error('更新用户邮箱失败，请稍后重试');
      }
    } finally {
      submitting.value = false;
    }
  });
};

// 显示角色对话框
const showRoleDialog = (user) => {
  selectedUser.value = user;
  roleForm.role = user.role || 'user';
  roleDialogVisible.value = true;
};

// 更新用户角色
const updateRole = async () => {
  if (!roleFormRef.value) return;

  await roleFormRef.value.validate(async (valid) => {
    if (!valid) return;

    // 确认提示
    if (selectedUser.value.role !== roleForm.role) {
      try {
        await ElMessageBox.confirm(
          `确定要将用户 "${selectedUser.value.username}" 的角色从 "${selectedUser.value.role === 'admin' ? '管理员' : '普通用户'}" 改为 "${roleForm.role === 'admin' ? '管理员' : '普通用户'}" 吗？`,
          '确认修改',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );
      } catch {
        return; // 用户取消操作
      }
    }

    submitting.value = true;

    try {
      await updateUserRole(selectedUser.value.id, roleForm.role);

      ElMessage.success('用户角色更新成功');
      roleDialogVisible.value = false;
      fetchUsers();
    } catch (error) {
      console.error('更新用户角色失败:', error);
      ElMessage.error('更新用户角色失败，请稍后重试');
    } finally {
      submitting.value = false;
    }
  });
};

// 显示重置密码对话框
const showResetDialog = (user) => {
  selectedUser.value = user;
  passwordForm.password = '';
  passwordForm.confirmPassword = '';
  resetDialogVisible.value = true;
};

// 重置用户密码
const resetPassword = async () => {
  if (!passwordFormRef.value) return;

  await passwordFormRef.value.validate(async (valid) => {
    if (!valid) return;

    // 确认提示
    try {
      await ElMessageBox.confirm(
        `确定要重置用户 "${selectedUser.value.username}" 的密码吗？`,
        '确认重置',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );
    } catch {
      return; // 用户取消操作
    }

    submitting.value = true;

    try {
      await apiResetUserPassword(selectedUser.value.id, passwordForm.password);

      ElMessage.success('用户密码重置成功');
      resetDialogVisible.value = false;
    } catch (error) {
      console.error('重置用户密码失败:', error);
      ElMessage.error('重置用户密码失败，请稍后重试');
    } finally {
      submitting.value = false;
    }
  });
};

// 显示导入对话框
const showImportDialog = () => {
  importFileList.value = [];
  importFile.value = null;
  importDialogVisible.value = true;
};

// 处理文件上传
const handleFileChange = (file) => {
  console.log('文件上传事件触发:', file);
  console.log('文件信息:', {
    名称: file.name,
    类型: file.type,
    大小: file.size,
    原始对象: file.raw ? '存在' : '不存在'
  });

  const isCSV = file.name.endsWith('.csv');
  const isExcel = file.name.endsWith('.xlsx') || file.name.endsWith('.xls');
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isCSV && !isExcel) {
    ElMessage.error('只能上传CSV或Excel文件!');
    importFileList.value = [];
    importFile.value = null;
    return false;
  }

  if (!isLt2M) {
    ElMessage.error('文件大小不能超过2MB!');
    importFileList.value = [];
    importFile.value = null;
    return false;
  }

  if (!file.raw) {
    ElMessage.error('文件对象无效，请重新选择文件');
    importFileList.value = [];
    importFile.value = null;
    return false;
  }

  // 文件验证通过
  importFileList.value = [file];
  importFile.value = file.raw;
  console.log('文件已准备好上传:', importFile.value);
  ElMessage.success(`文件 "${file.name}" 准备就绪，点击"开始导入"按钮开始导入`);
  return true;
};

// 下载导入模板
const downloadTemplate = async (format) => {
  if (format === 'csv') {
    const header = 'username,password,email,role,department,workplace,mobile,registrationDate\n';
    const exampleData = 'user1,password123,<EMAIL>,user,销售部,北京,13800000001,2023-01-15\nuser2,password456,<EMAIL>,admin,技术部,武汉,13900000002,2022-05-20\n';
    const csvContent = header + exampleData;

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', '用户导入模板.csv');
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } else if (format === 'xlsx') {
    try {
      // 使用API函数下载Excel模板
      const response = await downloadUserTemplate('xlsx');

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', '用户导入模板.xlsx');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('下载Excel模板失败:', error);
      ElMessage.error('下载Excel模板失败，请稍后重试');
    }
  }
};

// 导入用户
const importUsers = async () => {
  if (!importFile.value) {
    ElMessage.warning('请先选择文件');
    return;
  }

  // 再次验证文件
  if (!(importFile.value instanceof File)) {
    console.error('无效的文件对象:', importFile.value);
    ElMessage.error('文件对象无效，请重新选择文件');
    importFile.value = null;
    importFileList.value = [];
    return;
  }

  console.log('开始导入文件:', {
    文件名: importFile.value.name,
    类型: importFile.value.type,
    大小: `${(importFile.value.size / 1024).toFixed(2)}KB`
  });

  importing.value = true;

  try {
    // 显示正在处理的提示
    ElMessage({
      type: 'info',
      message: '正在处理文件，请稍候...',
      duration: 2000
    });

    // 使用API函数而不是直接使用axios
    console.log('调用API导入用户...');
    const response = await apiImportUsers(importFile.value);

    ElMessage.success(response.message || '用户导入成功');
    importDialogVisible.value = false;
    fetchUsers();

    if (response.errors && response.errors.length > 0) {
      // 构建详细的错误信息
      let errorDetails = '<div style="max-height: 300px; overflow-y: auto;">';
      errorDetails += '<table style="width: 100%; border-collapse: collapse;">';
      errorDetails += '<tr style="background-color: #f2f2f2;"><th style="padding: 8px; text-align: left; border: 1px solid #ddd;">行号</th><th style="padding: 8px; text-align: left; border: 1px solid #ddd;">错误原因</th></tr>';

      response.errors.forEach(error => {
        errorDetails += `<tr><td style="padding: 8px; text-align: left; border: 1px solid #ddd;">${error.line || '未知'}</td><td style="padding: 8px; text-align: left; border: 1px solid #ddd;">${error.message || '未知错误'}</td></tr>`;
      });

      errorDetails += '</table></div>';

      ElMessageBox.alert(
        `<div>成功导入: <strong>${response.success}</strong> 条记录<br>导入失败: <strong>${response.errors.length}</strong> 条记录</div><br>${errorDetails}`,
        '导入结果',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          type: 'warning',
          customClass: 'import-result-dialog'
        }
      );
    }
  } catch (error) {
    console.error('导入用户失败:', error);
    let errorMessage = '导入用户失败';

    // 尝试从错误响应中提取更详细的信息
    if (error.response) {
      if (error.response.status === 413) {
        errorMessage = '文件过大，请确保文件小于2MB';
      } else if (error.response.status === 400) {
        errorMessage = error.response.data?.message || '文件格式不正确或缺少必要字段';
      } else if (error.response.status === 500) {
        errorMessage = '服务器处理文件时出错，请确保文件格式正确且不包含特殊字符';

        // 如果服务器返回了具体错误，显示它
        if (error.response.data?.error) {
          console.error('服务器错误详情:', error.response.data.error);
          errorMessage += `\n错误详情: ${error.response.data.error}`;
        }
      } else {
        errorMessage = error.response.data?.message || '导入用户失败，请稍后重试';
      }
    } else if (error.code === 'ECONNABORTED') {
      errorMessage = '请求超时，文件可能过大或服务器处理时间较长';
    }

    ElMessage.error(errorMessage);
  } finally {
    importing.value = false;
  }
};

// 导出用户列表
const exportUserList = async (format = 'csv') => {
  try {
    const params = {
      search: searchForm.search || searchQuery.value,
      role: searchForm.role,
      department: searchForm.department,
      workplace: searchForm.workplace,
      isActive: searchForm.isActive === '' ? undefined : searchForm.isActive,
      format: format
    };
    const response = await apiExportUsers(params);

    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response]));
    const link = document.createElement('a');
    link.href = url;
    const extension = format === 'xlsx' ? 'xlsx' : 'csv';
    link.setAttribute('download', `用户列表_${new Date().getTime()}.${extension}`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    ElMessage.success('用户导出成功');
  } catch (error) {
    console.error('导出用户失败:', error);
    ElMessage.error('导出用户失败，请稍后重试');
  }
};

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN');
};

// 已选择的用户
const selectedUsers = ref([]);

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  selectedUsers.value = selection;
};

// 批量删除用户
const batchDeleteUsers = async () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请至少选择一个用户');
    return;
  }

  // 检查是否包含当前用户
  const hasSelf = selectedUsers.value.some(user => isSelfUser(user.id));
  if (hasSelf) {
    ElMessage.warning('不能删除自己的账户');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedUsers.value.length} 个用户吗？此操作不可逆！`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const userIds = selectedUsers.value.map(user => user.id);
    await apiBatchDeleteUsers(userIds);
    ElMessage.success('用户批量删除成功');
    fetchUsers();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除用户失败:', error);
      ElMessage.error(error.response?.data?.message || '批量删除用户失败，请稍后重试');
    }
  }
};

// 职场数据
const workplaces = ref([]);
const loadingWorkplaces = ref(false);

// 获取职场数据
const fetchWorkplaces = async () => {
  loadingWorkplaces.value = true;
  try {
    const response = await getAllActiveWorkplaces();
    workplaces.value = response;
  } catch (error) {
    console.error('获取职场列表失败:', error);
    ElMessage.error('获取职场列表失败，请刷新页面重试');
  } finally {
    loadingWorkplaces.value = false;
  }
};

// 更新用户手机号码
const updateMobile = async () => {
  if (!selectedUser.value) return;

  try {
    await mobileFormRef.value.validate();

    submitting.value = true;
    const response = await updateUserMobile(selectedUser.value.id, mobileForm.value.mobile);

    // 更新本地数据
    const index = users.value.findIndex(u => u.id === selectedUser.value.id);
    if (index !== -1) {
      users.value[index].mobile = mobileForm.value.mobile;
    }

    ElMessage.success('手机号码更新成功');
    mobileDialogVisible.value = false;
  } catch (error) {
    console.error('更新手机号码失败:', error);
    ElMessage.error(error.response?.data?.message || '更新手机号码失败');
  } finally {
    submitting.value = false;
  }
};

// 用户统计数据
const userStats = computed(() => {
  return {
    // 总用户数（使用API返回的total值）
    totalUsers: total.value,
    // 管理员数量
    adminUsers: users.value.filter(user => user.role === 'admin').length,
    // 普通用户数量
    regularUsers: users.value.filter(user => user.role === 'user' || !user.role).length,
    // 禁用用户数量
    disabledUsers: users.value.filter(user => user.isActive === false).length
  };
});

// 显示用户详情对话框
const showUserDetail = (user) => {
  selectedUser.value = user;
  userDetailDialogVisible.value = true;
};

// 切换用户状态（启用/禁用）
const toggleUserStatus = async (user) => {
  try {
    // 确认操作
    await ElMessageBox.confirm(
      `确定要${user.isActive ? '禁用' : '启用'}用户 "${user.username}" 吗？${user.isActive ? '禁用后该用户将无法登录系统。' : ''}`,
      `${user.isActive ? '禁用' : '启用'}用户`,
      {
        confirmButtonText: user.isActive ? '禁用' : '启用',
        cancelButtonText: '取消',
        type: user.isActive ? 'warning' : 'info'
      }
    );

    // 调用API切换状态
    const newStatus = !user.isActive;
    const response = await apiToggleUserStatus(user.id, newStatus);

    // 更新本地数据
    const index = users.value.findIndex(u => u.id === user.id);
    if (index !== -1) {
      users.value[index].isActive = newStatus;
    }

    // 提示成功
    ElMessage.success(response.message || `用户${newStatus ? '启用' : '禁用'}成功`);
  } catch (error) {
    if (error === 'cancel') return;
    console.error('切换用户状态失败:', error);
    ElMessage.error(error.message || `操作失败，请稍后重试`);
  }
};

// 清除表格选择
const clearSelection = () => {
  const table = document.querySelector('.el-table');
  if (table && table.__vue__) {
    table.__vue__.clearSelection();
  } else {
    selectedUsers.value = [];
  }
};

// 批量启用用户
const batchEnableUsers = async () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请至少选择一个用户');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要启用选中的 ${selectedUsers.value.length} 个用户吗？`,
      '确认批量启用',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    );

    const userIds = selectedUsers.value.map(user => user.id);
    const response = await batchUpdateUserStatus(userIds, true);
    ElMessage.success(response.message || `成功启用 ${response.updatedCount || selectedUsers.value.length} 个用户`);
    fetchUsers();
    clearSelection();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量启用用户失败:', error);
      ElMessage.error(error.response?.data?.message || '批量启用用户失败，请稍后重试');
    }
  }
};

// 批量禁用用户
const batchDisableUsers = async () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请至少选择一个用户');
    return;
  }

  // 检查是否包含当前用户
  const hasSelf = selectedUsers.value.some(user => isSelfUser(user.id));
  if (hasSelf) {
    ElMessage.warning('不能禁用自己的账户');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要禁用选中的 ${selectedUsers.value.length} 个用户吗？禁用后用户将无法登录系统。`,
      '确认批量禁用',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const userIds = selectedUsers.value.map(user => user.id);
    const response = await batchUpdateUserStatus(userIds, false);
    ElMessage.success(response.message || `成功禁用 ${response.updatedCount || selectedUsers.value.length} 个用户`);
    fetchUsers();
    clearSelection();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量禁用用户失败:', error);
      ElMessage.error(error.response?.data?.message || '批量禁用用户失败，请稍后重试');
    }
  }
};

// 处理批量部门操作
const handleBatchDepartment = (command) => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请至少选择一个用户');
    return;
  }

  if (command === 'edit') {
    batchDepartmentForm.department = '';
    batchDepartmentDialogVisible.value = true;
  } else if (command === 'clear') {
    clearBatchDepartment();
  }
};

// 清除批量部门
const clearBatchDepartment = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要清除选中的 ${selectedUsers.value.length} 个用户的部门信息吗？`,
      '确认清除部门',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const userIds = selectedUsers.value.map(user => user.id);
    const response = await batchUpdateUserDepartment(userIds, '');
    ElMessage.success(response.message || `成功清除 ${response.updatedCount || selectedUsers.value.length} 个用户的部门信息`);
    fetchUsers();
    clearSelection();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清除用户部门失败:', error);
      ElMessage.error(error.response?.data?.message || '清除用户部门失败，请稍后重试');
    }
  }
};

// 更新批量部门
const updateBatchDepartment = async () => {
  if (!batchDepartmentFormRef.value) return;

  await batchDepartmentFormRef.value.validate(async (valid) => {
    if (!valid) return;

    submitting.value = true;

    try {
      const userIds = selectedUsers.value.map(user => user.id);
      const response = await batchUpdateUserDepartment(userIds, batchDepartmentForm.department);

      ElMessage.success(response.message || `成功更新 ${response.updatedCount || selectedUsers.value.length} 个用户的部门信息`);
      batchDepartmentDialogVisible.value = false;
      fetchUsers();
      clearSelection();
    } catch (error) {
      console.error('批量更新用户部门失败:', error);
      ElMessage.error(error.response?.data?.message || '批量更新用户部门失败，请稍后重试');
    } finally {
      submitting.value = false;
    }
  });
};

// 处理批量职场操作
const handleBatchWorkplace = async (command) => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请至少选择一个用户');
    return;
  }

  if (command === 'clear') {
    // 清除职场
    try {
      await ElMessageBox.confirm(
        `确定要清除选中的 ${selectedUsers.value.length} 个用户的职场信息吗？`,
        '确认清除职场',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );

      const userIds = selectedUsers.value.map(user => user.id);
      const response = await batchUpdateUserWorkplace(userIds, '', null);
      ElMessage.success(response.message || `成功清除 ${response.updatedCount || selectedUsers.value.length} 个用户的职场信息`);
      fetchUsers();
      clearSelection();
    } catch (error) {
      if (error !== 'cancel') {
        console.error('清除用户职场失败:', error);
        ElMessage.error(error.response?.data?.message || '清除用户职场失败，请稍后重试');
      }
    }
  } else {
    // 设置职场
    try {
      // 获取workplaceId
      const selectedWorkplace = workplaces.value.find(w => w.name === command);
      if (!selectedWorkplace) {
        ElMessage.error('职场信息获取失败');
        return;
      }

      await ElMessageBox.confirm(
        `确定要将选中的 ${selectedUsers.value.length} 个用户的职场修改为"${command}"吗？`,
        '确认修改职场',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );

      const userIds = selectedUsers.value.map(user => user.id);
      const response = await batchUpdateUserWorkplace(userIds, command, selectedWorkplace.id);
      ElMessage.success(response.message || `成功更新 ${response.updatedCount || selectedUsers.value.length} 个用户的职场信息`);
      fetchUsers();
      clearSelection();
    } catch (error) {
      if (error !== 'cancel') {
        console.error('批量更新用户职场失败:', error);
        ElMessage.error(error.response?.data?.message || '批量更新用户职场失败，请稍后重试');
      }
    }
  }
};

// 初始化
onMounted(() => {
  fetchUsers();
  fetchWorkplaces();
});
</script>

<style scoped>
.user-management {
  padding: 20px;
}

/* 统计卡片样式 */
.stats-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 16px;
}

.stats-card {
  flex: 1;
  min-width: 160px;
  display: flex;
  padding: 12px;
  border-radius: 6px;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.stats-icon {
  font-size: 20px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  color: white;
}

.stats-info {
  display: flex;
  flex-direction: column;
}

.stats-title {
  font-size: 12px;
  color: #909399;
  margin-bottom: 3px;
}

.stats-value {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

/* 各卡片主题色 */
.total-users .stats-icon {
  background-color: #409eff;
}

.admin-users .stats-icon {
  background-color: #f56c6c;
}

.regular-users .stats-icon {
  background-color: #67c23a;
}

.disabled-users .stats-icon {
  background-color: #909399;
}

.page-header {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 15px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.page-title {
  font-size: 24px;
  margin: 0;
  color: #303133;
}

.search-container {
  display: flex;
  gap: 10px;
}

.search-input {
  width: 220px;
}

/* 高级搜索表单样式 */
.advanced-search-form {
  width: 100%;
  margin: 15px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.advanced-search-form .el-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: flex-start;
}

.advanced-search-form .el-form-item {
  margin-bottom: 10px;
  margin-right: 15px;
}

.advanced-search-form .el-input,
.advanced-search-form .el-select {
  width: 180px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.dialog-user-info {
  margin-bottom: 20px;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.import-tips {
  margin-bottom: 20px;
}

.import-tips ul {
  padding-left: 20px;
  margin: 10px 0;
}

.download-template {
  margin-top: 10px;
}

.upload-container {
  width: 100%;
}

.w-full {
  width: 100%;
}

.mb-4 {
  margin-bottom: 16px;
}

.ml-2 {
  margin-left: 8px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  padding-top: 4px;
}

/* 导入结果对话框样式 */
:deep(.import-result-dialog) {
  max-width: 90%;
  min-width: 600px;
}

.import-troubleshooting {
  margin-top: 20px;
  margin-bottom: 20px;
}

.troubleshooting-list {
  padding-left: 24px;
  line-height: 1.8;
}

.troubleshooting-list li {
  margin-bottom: 8px;
  color: #606266;
}

@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
    align-items: flex-start;
  }

  .stats-cards {
    flex-direction: column;
  }

  .stats-card {
    width: 100%;
  }

  .advanced-search-form .el-form {
    flex-direction: column;
  }

  .advanced-search-form .el-input,
  .advanced-search-form .el-select {
    width: 100%;
  }
}

/* 用户管理样式 */
.user-management {
  padding: 20px;
}

/* 批量操作浮动工具栏样式 */
.batch-operations-toolbar {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2000;
  background-color: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  padding: 10px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 80%;
  max-width: 1000px;
}

.toolbar-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
  font-weight: 500;
}

.toolbar-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateX(-50%) translateY(20px);
}

.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
  display: inline-block;
}

.text-truncate-path {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 230px;
  display: inline-block;
  cursor: help;
}

.user-detail-container {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.detail-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #303133;
  display: flex;
  align-items: center;
}

.section-title:before {
  content: '';
  width: 4px;
  height: 16px;
  background-color: #409eff;
  margin-right: 8px;
  border-radius: 2px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  padding: 8px 0;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-item label {
  font-weight: 600;
  color: #606266;
  min-width: 100px;
  flex-shrink: 0;
}

.detail-item span {
  color: #303133;
  word-break: break-all;
  line-height: 1.5;
}

.department-path {
  font-family: 'Courier New', monospace;
  background-color: #f5f7fa;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  font-size: 13px;
  line-height: 1.6;
  margin-top: 4px;
  word-break: break-all;
}

.points-value {
  color: #e6a23c;
  font-weight: 600;
  font-size: 14px;
}

.points-value:after {
  content: ' 光年币';
  color: #909399;
  font-weight: 400;
  font-size: 12px;
}

@media (max-width: 768px) {
  .detail-grid {
    grid-template-columns: 1fr;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .detail-item label {
    margin-bottom: 4px;
  }

  .user-detail-container {
    padding: 10px;
  }

  .batch-operations-toolbar {
    flex-direction: column;
    width: 90%;
    padding: 12px;
  }

  .toolbar-info {
    margin-bottom: 10px;
  }

  .toolbar-actions {
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style>
