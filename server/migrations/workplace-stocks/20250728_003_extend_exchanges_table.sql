-- =====================================================
-- 多职场库存分配功能 - 第一阶段迁移脚本
-- 扩展兑换表 (exchanges) 添加职场选择字段
-- 创建时间: 2025-07-28
-- 版本: v1.0.0
-- =====================================================

-- 检查 workplaceId 字段是否已存在
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'exchanges' 
    AND column_name = 'workplaceId'
);

-- 只有在字段不存在时才添加
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE exchanges 
     ADD COLUMN workplaceId INT NULL COMMENT ''兑换职场ID'' AFTER productId,
     ADD INDEX idx_workplace_id (workplaceId),
     ADD CONSTRAINT fk_exchange_workplace FOREIGN KEY (workplaceId) REFERENCES workplaces(id) ON DELETE SET NULL ON UPDATE CASCADE;',
    'SELECT ''Column workplaceId already exists in exchanges table, skipping addition'' as message;'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证字段添加结果
SELECT 
    CASE 
        WHEN @column_exists = 0 THEN '✅ 字段 workplaceId 添加到 exchanges 表成功'
        ELSE '⚠️  字段 workplaceId 已存在于 exchanges 表，跳过添加'
    END as result;

-- 检查 stockSnapshot 字段是否已存在（用于记录兑换时的库存快照）
SET @snapshot_column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'exchanges' 
    AND column_name = 'stockSnapshot'
);

-- 添加库存快照字段
SET @sql2 = IF(@snapshot_column_exists = 0, 
    'ALTER TABLE exchanges 
     ADD COLUMN stockSnapshot JSON NULL COMMENT ''兑换时库存快照（记录各职场库存状态）'' AFTER workplaceId;',
    'SELECT ''Column stockSnapshot already exists in exchanges table, skipping addition'' as message;'
);

PREPARE stmt2 FROM @sql2;
EXECUTE stmt2;
DEALLOCATE PREPARE stmt2;

-- 验证库存快照字段添加结果
SELECT 
    CASE 
        WHEN @snapshot_column_exists = 0 THEN '✅ 字段 stockSnapshot 添加到 exchanges 表成功'
        ELSE '⚠️  字段 stockSnapshot 已存在于 exchanges 表，跳过添加'
    END as result;

-- 显示更新后的表结构
DESCRIBE exchanges;
