# 🚀 生产环境部署检查清单

## ✅ 部署前准备工作

### 1. 服务器环境检查
- [ ] Node.js (版本 14.x 或更高) 已安装
- [ ] MySQL (版本 5.7 或更高) 已安装并运行
- [ ] PM2 已全局安装: `npm install -g pm2`
- [ ] Nginx 已安装并运行
- [ ] 防火墙已开放 80 和 3000 端口

### 2. 数据库配置
- [ ] 执行数据库初始化脚本: `mysql -u root -p < create_production_db.sql`
- [ ] 验证数据库 `feishu_mall` 已创建
- [ ] 验证数据表已正确创建（users, products, orders）

### 3. 飞书应用配置检查
- [ ] 飞书开放平台中添加生产环境回调地址: `http://47.122.122.245/api/feishu/callback`
- [ ] 确认手机号权限 `contact:user.phone:readonly` 已开通
- [ ] 验证应用ID: `cli_a66b3b2dcab8d013`

## 🔧 配置文件检查

### 4. 前端配置
- [ ] `.env.production` 配置正确
  - API地址: `http://47.122.122.245:3000/api`
- [ ] 前端构建成功: `npm run build`
- [ ] `dist` 目录生成且包含文件

### 5. 后端配置
- [ ] `server/.env.production` 配置正确
  - 数据库连接信息
  - 飞书应用配置
  - JWT密钥
- [ ] 后端依赖安装: `cd server && npm install --omit=dev`

### 6. Nginx配置
- [ ] `nginx_fixed.conf` 已更新，仅负责静态文件服务
- [ ] Nginx配置文件已复制到正确位置
- [ ] Nginx配置语法检查: `nginx -t`
- [ ] Nginx已重启: `systemctl restart nginx`

## 🚀 部署执行

### 7. 自动部署
- [ ] 运行部署脚本: `chmod +x production-deploy.sh && ./production-deploy.sh`
- [ ] 检查脚本执行日志，无错误

### 8. 服务启动验证
- [ ] PM2服务状态正常: `pm2 list`
- [ ] 后端健康检查: `curl http://47.122.122.245:3000/api/health`
- [ ] 前端访问正常: `http://47.122.122.245`
- [ ] API直接访问正常: `http://47.122.122.245:3000/api/health`

## 🧪 功能测试

### 9. 基础功能测试
- [ ] 前端页面正常加载
- [ ] 用户注册功能正常
- [ ] 本地登录功能正常
- [ ] 商品浏览功能正常

### 10. 飞书登录测试
- [ ] 飞书登录按钮可点击
- [ ] 跳转到飞书授权页面
- [ ] 授权后成功回调
- [ ] 用户信息正确获取（包括手机号）
- [ ] 用户状态正确保存到数据库

### 11. 上传功能测试
- [ ] 图片上传功能正常
- [ ] 上传文件可正常访问: `http://47.122.122.245:3000/uploads/文件名`

## 🔍 监控与日志

### 12. 日志检查
- [ ] PM2日志正常: `pm2 logs exchange-mall-api`
- [ ] Nginx访问日志正常: `/www/wwwlogs/47.122.122.245.log`
- [ ] Nginx错误日志无异常: `/www/wwwlogs/47.122.122.245.error.log`

### 13. 性能监控
- [ ] 服务器资源使用正常
- [ ] 数据库连接正常
- [ ] 响应时间正常

## 🚨 回滚准备

### 14. 备份检查
- [ ] 数据库备份已创建
- [ ] 代码版本已标记
- [ ] 回滚计划已准备

## 📞 关键联系信息

- **飞书应用ID**: `cli_a66b3b2dcab8d013`
- **生产环境地址**: `http://47.122.122.245`
- **API地址**: `http://47.122.122.245:3000/api`
- **数据库**: `feishu_mall`

## ⚠️ 常见问题解决

1. **前端白屏**: 检查API地址配置和3000端口访问
2. **API无法访问**: 检查PM2服务状态和3000端口开放情况
3. **飞书登录失败**: 检查回调地址配置和权限申请
4. **上传功能异常**: 检查uploads目录权限和Nginx配置 