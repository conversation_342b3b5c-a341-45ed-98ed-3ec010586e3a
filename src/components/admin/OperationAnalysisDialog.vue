<template>
  <el-dialog
    v-model="dialogVisible"
    title="操作统计分析"
    width="1200px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="analysis-content">
      <!-- 概览统计 -->
      <div class="overview-section">
        <h3 class="section-title">
          <i class="fas fa-chart-bar"></i>
          概览统计
        </h3>
        <div class="overview-cards">
          <div class="overview-card">
            <div class="card-icon">
              <i class="fas fa-list-alt"></i>
            </div>
            <div class="card-content">
              <div class="card-value">{{ analysisData.totalOperations }}</div>
              <div class="card-label">总操作数</div>
            </div>
          </div>
          <div class="overview-card">
            <div class="card-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="card-content">
              <div class="card-value">{{ Object.keys(analysisData.userStats || {}).length }}</div>
              <div class="card-label">活跃用户</div>
            </div>
          </div>
          <div class="overview-card">
            <div class="card-icon">
              <i class="fas fa-building"></i>
            </div>
            <div class="card-content">
              <div class="card-value">{{ Object.keys(analysisData.workplaceStats || {}).length }}</div>
              <div class="card-label">涉及职场</div>
            </div>
          </div>
          <div class="overview-card">
            <div class="card-icon">
              <i class="fas fa-box"></i>
            </div>
            <div class="card-content">
              <div class="card-value">{{ Object.keys(analysisData.productStats || {}).length }}</div>
              <div class="card-label">涉及商品</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 图表分析 -->
      <div class="charts-section">
        <div class="chart-row">
          <!-- 操作类型分布 -->
          <div class="chart-card">
            <h4 class="chart-title">操作类型分布</h4>
            <div class="chart-container">
              <div ref="operationTypeChart" class="chart"></div>
            </div>
          </div>
          
          <!-- 时间趋势 -->
          <div class="chart-card">
            <h4 class="chart-title">操作时间趋势</h4>
            <div class="chart-container">
              <div ref="timelineChart" class="chart"></div>
            </div>
          </div>
        </div>

        <div class="chart-row">
          <!-- 用户活跃度 -->
          <div class="chart-card">
            <h4 class="chart-title">用户活跃度</h4>
            <div class="chart-container">
              <div ref="userActivityChart" class="chart"></div>
            </div>
          </div>
          
          <!-- 职场操作分布 -->
          <div class="chart-card">
            <h4 class="chart-title">职场操作分布</h4>
            <div class="chart-container">
              <div ref="workplaceChart" class="chart"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 详细统计表格 -->
      <div class="tables-section">
        <el-tabs v-model="activeTab" type="card">
          <el-tab-pane label="操作类型统计" name="operationType">
            <el-table :data="operationTypeTableData" stripe>
              <el-table-column prop="type" label="操作类型" />
              <el-table-column prop="count" label="操作次数" sortable />
              <el-table-column prop="percentage" label="占比" />
            </el-table>
          </el-tab-pane>
          
          <el-tab-pane label="用户统计" name="user">
            <el-table :data="userTableData" stripe>
              <el-table-column prop="user" label="用户" />
              <el-table-column prop="count" label="操作次数" sortable />
              <el-table-column prop="percentage" label="占比" />
              <el-table-column prop="lastOperation" label="最后操作时间" />
            </el-table>
          </el-tab-pane>
          
          <el-tab-pane label="职场统计" name="workplace">
            <el-table :data="workplaceTableData" stripe>
              <el-table-column prop="workplace" label="职场" />
              <el-table-column prop="count" label="操作次数" sortable />
              <el-table-column prop="percentage" label="占比" />
            </el-table>
          </el-tab-pane>
          
          <el-tab-pane label="商品统计" name="product">
            <el-table :data="productTableData" stripe>
              <el-table-column prop="product" label="商品" />
              <el-table-column prop="count" label="操作次数" sortable />
              <el-table-column prop="percentage" label="占比" />
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 异常检测 -->
      <div class="anomaly-section" v-if="anomalies.length > 0">
        <h3 class="section-title">
          <i class="fas fa-exclamation-triangle"></i>
          异常检测
        </h3>
        <div class="anomaly-list">
          <div v-for="anomaly in anomalies" :key="anomaly.id" class="anomaly-item">
            <div class="anomaly-icon">
              <i :class="getAnomalyIcon(anomaly.type)"></i>
            </div>
            <div class="anomaly-content">
              <div class="anomaly-title">{{ anomaly.title }}</div>
              <div class="anomaly-description">{{ anomaly.description }}</div>
            </div>
            <div class="anomaly-severity">
              <el-tag :type="getSeverityColor(anomaly.severity)">
                {{ getSeverityText(anomaly.severity) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="exportAnalysis">导出分析报告</el-button>
        <el-button type="info" @click="refreshAnalysis">刷新分析</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

export default {
  name: 'OperationAnalysisDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    logs: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const dialogVisible = ref(false)
    const activeTab = ref('operationType')
    const operationTypeChart = ref(null)
    const timelineChart = ref(null)
    const userActivityChart = ref(null)
    const workplaceChart = ref(null)

    // 分析数据
    const analysisData = computed(() => {
      const data = {
        totalOperations: props.logs.length,
        operationTypes: {},
        userStats: {},
        workplaceStats: {},
        productStats: {},
        timeStats: {}
      }

      props.logs.forEach(log => {
        // 操作类型统计
        const type = log.type
        data.operationTypes[type] = (data.operationTypes[type] || 0) + 1

        // 用户统计
        const user = log.userName || '未知用户'
        if (!data.userStats[user]) {
          data.userStats[user] = { count: 0, lastOperation: log.timestamp }
        }
        data.userStats[user].count++
        if (new Date(log.timestamp) > new Date(data.userStats[user].lastOperation)) {
          data.userStats[user].lastOperation = log.timestamp
        }

        // 职场统计
        const workplace = log.workplaceName || '全局'
        data.workplaceStats[workplace] = (data.workplaceStats[workplace] || 0) + 1

        // 商品统计
        const product = log.productName || '未知商品'
        data.productStats[product] = (data.productStats[product] || 0) + 1

        // 时间统计
        const date = new Date(log.timestamp).toDateString()
        data.timeStats[date] = (data.timeStats[date] || 0) + 1
      })

      return data
    })

    // 表格数据
    const operationTypeTableData = computed(() => {
      const total = analysisData.value.totalOperations
      return Object.entries(analysisData.value.operationTypes).map(([type, count]) => ({
        type: getOperationTypeText(type),
        count,
        percentage: `${((count / total) * 100).toFixed(1)}%`
      }))
    })

    const userTableData = computed(() => {
      const total = analysisData.value.totalOperations
      return Object.entries(analysisData.value.userStats).map(([user, stats]) => ({
        user,
        count: stats.count,
        percentage: `${((stats.count / total) * 100).toFixed(1)}%`,
        lastOperation: formatDateTime(stats.lastOperation)
      }))
    })

    const workplaceTableData = computed(() => {
      const total = analysisData.value.totalOperations
      return Object.entries(analysisData.value.workplaceStats).map(([workplace, count]) => ({
        workplace,
        count,
        percentage: `${((count / total) * 100).toFixed(1)}%`
      }))
    })

    const productTableData = computed(() => {
      const total = analysisData.value.totalOperations
      return Object.entries(analysisData.value.productStats).map(([product, count]) => ({
        product,
        count,
        percentage: `${((count / total) * 100).toFixed(1)}%`
      }))
    })

    // 异常检测
    const anomalies = computed(() => {
      const anomalies = []
      
      // 检测频繁操作用户
      Object.entries(analysisData.value.userStats).forEach(([user, stats]) => {
        if (stats.count > analysisData.value.totalOperations * 0.5) {
          anomalies.push({
            id: `frequent_user_${user}`,
            type: 'frequent_operation',
            title: '频繁操作用户',
            description: `用户 ${user} 的操作次数异常频繁 (${stats.count} 次)`,
            severity: 'warning'
          })
        }
      })

      // 检测失败操作
      const failedCount = props.logs.filter(log => log.status === 'failed').length
      if (failedCount > analysisData.value.totalOperations * 0.1) {
        anomalies.push({
          id: 'high_failure_rate',
          type: 'failure',
          title: '高失败率',
          description: `操作失败率过高 (${failedCount}/${analysisData.value.totalOperations})`,
          severity: 'danger'
        })
      }

      return anomalies
    })

    // 工具方法
    const getOperationTypeText = (type) => {
      const typeMap = {
        stock_update: '库存更新',
        stock_transfer: '库存转移',
        quick_adjust: '快速调整',
        batch_quick_adjust: '批量调整',
        batch_edit: '批量编辑'
      }
      return typeMap[type] || type
    }

    const formatDateTime = (dateTime) => {
      if (!dateTime) return ''
      try {
        return new Date(dateTime).toLocaleString()
      } catch (error) {
        return dateTime
      }
    }

    const getAnomalyIcon = (type) => {
      const iconMap = {
        frequent_operation: 'fas fa-user-clock',
        failure: 'fas fa-times-circle'
      }
      return iconMap[type] || 'fas fa-exclamation-triangle'
    }

    const getSeverityColor = (severity) => {
      const colorMap = {
        info: 'info',
        warning: 'warning',
        danger: 'danger'
      }
      return colorMap[severity] || 'info'
    }

    const getSeverityText = (severity) => {
      const textMap = {
        info: '信息',
        warning: '警告',
        danger: '危险'
      }
      return textMap[severity] || severity
    }

    // 图表初始化
    const initCharts = async () => {
      await nextTick()
      
      // 操作类型分布饼图
      if (operationTypeChart.value) {
        const chart = echarts.init(operationTypeChart.value)
        const data = Object.entries(analysisData.value.operationTypes).map(([type, count]) => ({
          name: getOperationTypeText(type),
          value: count
        }))
        
        chart.setOption({
          tooltip: { trigger: 'item' },
          series: [{
            type: 'pie',
            radius: '60%',
            data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }]
        })
      }

      // 时间趋势图
      if (timelineChart.value) {
        const chart = echarts.init(timelineChart.value)
        const dates = Object.keys(analysisData.value.timeStats).sort()
        const values = dates.map(date => analysisData.value.timeStats[date])
        
        chart.setOption({
          tooltip: { trigger: 'axis' },
          xAxis: {
            type: 'category',
            data: dates.map(date => new Date(date).toLocaleDateString())
          },
          yAxis: { type: 'value' },
          series: [{
            type: 'line',
            data: values,
            smooth: true
          }]
        })
      }

      // 用户活跃度柱状图
      if (userActivityChart.value) {
        const chart = echarts.init(userActivityChart.value)
        const userData = Object.entries(analysisData.value.userStats)
          .sort((a, b) => b[1].count - a[1].count)
          .slice(0, 10)
        
        chart.setOption({
          tooltip: { trigger: 'axis' },
          xAxis: {
            type: 'category',
            data: userData.map(([user]) => user),
            axisLabel: { rotate: 45 }
          },
          yAxis: { type: 'value' },
          series: [{
            type: 'bar',
            data: userData.map(([, stats]) => stats.count)
          }]
        })
      }

      // 职场操作分布
      if (workplaceChart.value) {
        const chart = echarts.init(workplaceChart.value)
        const workplaceData = Object.entries(analysisData.value.workplaceStats)
        
        chart.setOption({
          tooltip: { trigger: 'axis' },
          xAxis: {
            type: 'category',
            data: workplaceData.map(([workplace]) => workplace)
          },
          yAxis: { type: 'value' },
          series: [{
            type: 'bar',
            data: workplaceData.map(([, count]) => count)
          }]
        })
      }
    }

    // 方法
    const handleClose = () => {
      emit('update:modelValue', false)
    }

    const exportAnalysis = () => {
      try {
        const reportContent = [
          '操作统计分析报告',
          '=' * 50,
          `生成时间: ${new Date().toLocaleString()}`,
          `分析数据范围: ${props.logs.length} 条操作记录`,
          '',
          '概览统计:',
          `- 总操作数: ${analysisData.value.totalOperations}`,
          `- 活跃用户: ${Object.keys(analysisData.value.userStats).length}`,
          `- 涉及职场: ${Object.keys(analysisData.value.workplaceStats).length}`,
          `- 涉及商品: ${Object.keys(analysisData.value.productStats).length}`,
          '',
          '操作类型分布:',
          ...Object.entries(analysisData.value.operationTypes).map(([type, count]) => 
            `- ${getOperationTypeText(type)}: ${count} (${((count / analysisData.value.totalOperations) * 100).toFixed(1)}%)`
          ),
          '',
          '用户活跃度:',
          ...Object.entries(analysisData.value.userStats).map(([user, stats]) => 
            `- ${user}: ${stats.count} 次操作`
          ),
          '',
          '异常检测:',
          ...anomalies.value.map(anomaly => 
            `- ${anomaly.title}: ${anomaly.description}`
          )
        ].join('\n')

        const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8;' })
        const link = document.createElement('a')
        const url = URL.createObjectURL(blob)
        link.setAttribute('href', url)
        link.setAttribute('download', `操作分析报告_${new Date().toISOString().slice(0, 10)}.txt`)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        ElMessage.success('分析报告导出成功')
      } catch (error) {
        console.error('导出分析报告失败:', error)
        ElMessage.error('导出分析报告失败')
      }
    }

    const refreshAnalysis = () => {
      initCharts()
      ElMessage.success('分析数据已刷新')
    }

    // 监听器
    watch(() => props.modelValue, (newVal) => {
      dialogVisible.value = newVal
      if (newVal) {
        nextTick(() => {
          initCharts()
        })
      }
    })

    watch(dialogVisible, (newVal) => {
      if (!newVal) {
        emit('update:modelValue', false)
      }
    })

    return {
      dialogVisible,
      activeTab,
      operationTypeChart,
      timelineChart,
      userActivityChart,
      workplaceChart,
      analysisData,
      operationTypeTableData,
      userTableData,
      workplaceTableData,
      productTableData,
      anomalies,
      getOperationTypeText,
      formatDateTime,
      getAnomalyIcon,
      getSeverityColor,
      getSeverityText,
      handleClose,
      exportAnalysis,
      refreshAnalysis
    }
  }
}
</script>

<style scoped>
.analysis-content {
  max-height: 80vh;
  overflow-y: auto;
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.section-title i {
  color: #409eff;
}

.overview-section {
  margin-bottom: 32px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.overview-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 20px;
  color: white;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.card-value {
  font-size: 28px;
  font-weight: 700;
  line-height: 1;
}

.card-label {
  font-size: 14px;
  opacity: 0.9;
  margin-top: 4px;
}

.charts-section {
  margin-bottom: 32px;
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
}

.chart-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.chart {
  width: 100%;
  height: 300px;
}

.tables-section {
  margin-bottom: 32px;
}

.anomaly-section {
  margin-bottom: 20px;
}

.anomaly-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.anomaly-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 8px;
}

.anomaly-icon {
  width: 40px;
  height: 40px;
  background: #fa8c16;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.anomaly-content {
  flex: 1;
}

.anomaly-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.anomaly-description {
  font-size: 14px;
  color: #606266;
}

@media (max-width: 768px) {
  .chart-row {
    grid-template-columns: 1fr;
  }
  
  .overview-cards {
    grid-template-columns: 1fr;
  }
}
</style>
