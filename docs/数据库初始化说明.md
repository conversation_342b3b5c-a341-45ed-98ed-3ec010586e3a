# 光年小卖部 - 生产环境数据库初始化说明

## 概述

本文档说明如何使用 `create_production_db.sql` 脚本初始化光年小卖部的生产环境数据库。

## 脚本特性

### ✅ 完整性保证
- **22个核心表**：包含系统运行所需的所有数据表
- **完整约束**：主键、外键、唯一键、索引全部配置
- **字段注释**：每个表和字段都有详细的中文注释
- **数据类型**：严格按照业务需求定义数据类型和长度

### ✅ 安全性设计
- **事务保护**：整个初始化过程在事务中执行
- **外键检查**：临时禁用外键检查，完成后恢复
- **重复执行**：支持多次执行，使用 `DROP TABLE IF EXISTS`
- **错误回滚**：任何错误都会自动回滚所有更改

### ✅ 生产就绪
- **字符集**：使用 utf8mb4 支持 emoji 和特殊字符
- **存储引擎**：使用 InnoDB 支持事务和外键
- **索引优化**：为查询频繁的字段创建合适的索引
- **初始数据**：包含系统运行必需的基础数据

## 数据库结构

### 1. 基础数据表
| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `workplaces` | 职场信息 | id, name, code, description |
| `users` | 用户信息 | id, username, email, role, points |
| `categories` | 商品分类 | id, name, description, sortOrder |

### 2. 商品相关表
| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `products` | 商品信息 | id, name, lyPrice, rmbPrice, stock |
| `product_images` | 商品图片 | id, productId, imageUrl |
| `product_price_history` | 价格历史 | id, productId, oldPrice, newPrice |

### 3. 订单交易表
| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `exchanges` | 兑换订单 | id, userId, productId, status, paymentMethod |

### 4. 内容管理表
| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `announcements` | 系统公告 | id, title, content, type, status |
| `feedback` | 用户反馈（简化版） | id, userId, content |
| `feedbacks` | 用户反馈（完整版） | id, title, content, type, status |

### 5. 通知系统表
| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `notifications` | 用户通知 | id, type, title, content, recipientId |
| `message_templates` | 消息模板 | id, template_name, notification_type, template_content |
| `notification_configs` | 通知配置 | id, notification_type, enabled, webhook_url |
| `notification_logs` | 通知日志 | id, notification_type, status, sent_at |
| `notification_schedules` | 通知调度 | id, schedule_type, schedule_config |
| `notification_diagnostics` | 通知诊断 | id, diagnostic_type, status, details |
| `sending_schedules` | 发送调度 | id, schedule_name, cron_expression |

### 6. 热门商品系统表
| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `hot_product_configs` | 热门商品配置 | id, timeRange, enabled, maxCount |
| `hot_product_history` | 热门商品历史 | id, productId, hotScore, rank |

### 7. 系统表
| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `logs` | 系统日志 | id, action, entityType, userId |
| `SequelizeMeta` | 迁移记录 | name |

## 使用方法

### 1. 直接执行（推荐）
```bash
# 在项目根目录执行
mysql -u root -ppassword < create_production_db.sql
```

### 2. 分步执行
```bash
# 1. 登录 MySQL
mysql -u root -ppassword

# 2. 执行脚本
source create_production_db.sql;

# 3. 验证结果
USE feishu_mall;
SHOW TABLES;
```

### 3. 远程服务器执行
```bash
# 上传脚本到服务器后执行
scp create_production_db.sql root@your-server:/tmp/
ssh root@your-server "mysql -u root -ppassword < /tmp/create_production_db.sql"
```

## 初始数据

脚本会自动插入以下初始数据：

### 职场数据
- 北京、武汉、长沙、西安、深圳、上海、广州、成都

### 管理员账户
- **用户名**：超管
- **邮箱**：<EMAIL>  
- **密码**：654321（已加密存储）
- **角色**：admin

### 商品分类
- 解压玩具、盲盒类、定制手工制品类、杯具类
- 日常用品类、家居用品类、多功能用品类
- 办公用品、电子产品及配件、家居生活用品、创意小摆件

### 热门商品配置
- 全部时间、30天、7天、1天四个时间维度的配置

### 通知模板
- 订单状态变更通知
- 库存预警通知  
- 新商品上架通知
- 用户反馈通知

## 验证检查

执行完成后，可以运行以下命令验证：

```sql
-- 检查表数量
SELECT COUNT(*) as table_count 
FROM information_schema.tables 
WHERE table_schema = 'feishu_mall';

-- 检查初始数据
SELECT COUNT(*) as workplace_count FROM workplaces;
SELECT COUNT(*) as category_count FROM categories;
SELECT COUNT(*) as admin_count FROM users WHERE role = 'admin';
SELECT COUNT(*) as template_count FROM message_templates;

-- 检查外键约束
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'feishu_mall' 
  AND REFERENCED_TABLE_NAME IS NOT NULL;
```

## 注意事项

### ⚠️ 重要提醒
1. **备份现有数据**：如果数据库已存在，请先备份
2. **权限检查**：确保 MySQL 用户有创建数据库和表的权限
3. **字符集**：确保 MySQL 服务器支持 utf8mb4 字符集
4. **存储空间**：确保有足够的磁盘空间

### 🔧 故障排除
- **权限不足**：检查 MySQL 用户权限
- **字符集错误**：检查 MySQL 服务器配置
- **外键错误**：检查表创建顺序和字段类型
- **语法错误**：检查 MySQL 版本兼容性

## 后续操作

初始化完成后，建议：

1. **修改默认密码**：更改管理员账户密码
2. **配置应用**：更新应用程序的数据库连接配置
3. **运行测试**：执行应用程序测试确保一切正常
4. **设置备份**：配置定期数据库备份策略

## 版本信息

- **脚本版本**：2.0
- **创建时间**：2025-07-17
- **兼容性**：MySQL 5.7+ / MariaDB 10.2+
- **字符集**：utf8mb4
- **存储引擎**：InnoDB
