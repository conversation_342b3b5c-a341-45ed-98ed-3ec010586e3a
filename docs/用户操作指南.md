# 光年小卖部系统用户操作指南

## 📋 系统概述

### 🎯 系统简介
光年小卖部是洋葱学园专为企业内部员工福利兑换设计的现代化电商平台。系统采用前后端分离架构，深度集成飞书生态系统，为员工提供便捷的商品兑换和购买服务。

### ✨ 主要特性
- **双支付体系**：支持光年币（虚拟货币）和人民币两种支付方式
- **飞书生态集成**：OAuth单点登录、部门信息同步、群机器人通知
- **智能订单系统**：自动生成格式化订单编号（GNB-光年币/RMB-人民币前缀）
- **价格快照机制**：订单创建时保存价格快照，历史订单价格不受商品调价影响
- **移动端友好**：响应式设计，完美适配PC端和移动设备
- **实时通知**：订单状态变更自动推送飞书群通知

### 👥 用户角色说明
- **管理员（admin）**：拥有系统全部功能权限，可管理商品、订单、用户和系统设置
- **普通用户（user）**：可浏览商品、下单购买、查看个人订单和管理个人信息

### 🏗️ 系统架构
- **前端**：Vue 3 + Element Plus + Pinia + Vue Router
- **后端**：Node.js + Express + Sequelize + MySQL
- **数据库**：MySQL (feishu_mall)
- **认证**：JWT + 飞书OAuth集成

---

## 🔐 管理员操作指南

### 1. 登录系统

#### 1.1 飞书登录（推荐）
1. 访问系统首页，点击"登录"按钮
2. 选择"飞书登录"选项
3. 跳转到飞书授权页面，使用公司飞书账号登录
4. 授权成功后自动跳转回系统

#### 1.2 账号密码登录
**管理员账号信息：**
- 用户名：超管
- 邮箱：<EMAIL>
- 密码：654321

**登录步骤：**
1. 点击"账号密码登录"
2. 输入管理员邮箱和密码
3. 点击"登录"按钮

### 2. 商品管理

#### 2.1 商品列表管理
- **访问路径**：管理后台 → 商品管理
- **主要功能**：
  - 查看所有商品信息（名称、分类、价格、库存、状态）
  - 支持按分类、状态、关键词筛选
  - 支持按价格、库存、创建时间排序

#### 2.2 添加新商品
1. 点击"添加商品"按钮
2. 填写商品基本信息：
   - 商品名称（必填）
   - 商品分类（必填）
   - 光年币价格（必填）
   - 人民币价格（必填）
   - 商品描述
   - 初始库存
3. 设置商品标签：
   - 热门商品（isHot）
   - 新品标识（isNew）
4. 选择商品状态（上架/下架）
5. 上传商品图片
6. 点击"保存"完成添加

#### 2.3 编辑商品信息
1. 在商品列表中点击"编辑"按钮
2. 修改商品信息
3. 点击"保存"确认修改
4. 系统会自动记录价格变更历史

#### 2.4 库存管理
- **库存调整**：支持单个商品库存调整和批量库存更新
- **低库存告警**：系统自动监控库存状态，低库存时发送通知
- **库存变更日志**：完整记录所有库存变更操作

### 3. 订单管理

#### 3.1 订单列表查看
- **访问路径**：管理后台 → 兑换管理
- **筛选功能**：
  - 按订单状态筛选（待处理、已批准、已发货、已完成、已拒绝、已取消）
  - 按支付方式筛选（光年币/人民币）
  - 按时间范围筛选
  - 按用户或商品搜索

#### 3.2 订单状态管理
**订单状态流转：**
1. **待处理（pending）**：用户刚提交的订单
2. **已批准（approved）**：管理员审核通过
3. **已发货（shipped）**：商品已发出，可填写物流信息
4. **已完成（completed）**：用户确认收货
5. **已拒绝（rejected）**：管理员拒绝订单
6. **已取消（cancelled）**：用户或管理员取消订单

**状态操作：**
- 点击订单行的"操作"按钮
- 选择相应的状态变更操作
- 填写备注信息（可选）
- 确认操作

#### 3.3 光年币订单处理
1. 查看订单详情，确认商品和数量
2. 检查用户光年币余额是否充足
3. 点击"批准"按钮，系统自动扣减光年币
4. 填写发货信息（物流公司、物流单号）
5. 更新订单状态为"已发货"

#### 3.4 人民币订单处理
1. 查看订单详情和支付凭证
2. 验证支付凭证的真实性
3. 确认收款到账后点击"批准"
4. 填写发货信息
5. 更新订单状态

#### 3.5 批量操作
- **批量审批**：选择多个待处理订单，批量批准或拒绝
- **批量发货**：批量更新已批准订单的物流信息
- **批量导出**：导出订单数据为Excel文件

### 4. 用户管理

#### 4.1 用户列表管理
- **访问路径**：管理后台 → 用户管理
- **查看信息**：用户名、邮箱、部门、职场、角色、光年币余额、注册时间
- **搜索筛选**：支持按姓名、邮箱、部门、角色筛选

#### 4.2 用户权限管理
1. 在用户列表中找到目标用户
2. 点击"编辑"按钮
3. 修改用户角色（admin/user）
4. 更新用户部门和职场信息
5. 设置用户状态（启用/禁用）

#### 4.3 光年币管理
- **查看余额**：在用户列表中查看每个用户的光年币余额
- **余额调整**：管理员可以调整用户的光年币余额
- **消费记录**：查看用户的光年币消费历史

### 5. 数据统计分析

#### 5.1 数据仪表盘
- **访问路径**：管理后台 → 数据分析仪表盘
- **核心指标**：
  - 总订单数和近期趋势
  - 销售额统计（光年币/人民币）
  - 活跃用户数量
  - 热门商品排行

#### 5.2 销售数据分析
- **销售趋势图**：查看不同时间段的销售趋势
- **支付偏好分析**：分析用户支付方式偏好
- **商品分类统计**：各分类商品的销售情况
- **职场分布统计**：不同职场的订单分布

#### 5.3 用户行为分析
- **用户活跃度**：统计用户登录和购买行为
- **部门活跃度排行**：各部门的参与度统计
- **用户增长趋势**：新用户注册趋势分析

#### 5.4 数据导出
- **统计报表导出**：支持Excel格式导出
- **订单数据导出**：批量导出订单详细信息
- **用户数据导出**：导出用户基本信息和消费记录

### 6. 系统设置

#### 6.1 通知管理
- **访问路径**：管理后台 → 系统设置 → 通知管理
- **配置项目**：
  - 飞书群通知开关
  - 通知模板管理
  - 发送时间控制
  - Webhook地址配置

#### 6.2 职场管理
- **职场信息维护**：添加、编辑、删除职场信息
- **配送地址管理**：设置各职场的配送地址
- **职场用户统计**：查看各职场的用户分布

#### 6.3 系统维护
- **数据备份**：定期备份系统数据
- **日志管理**：查看系统操作日志
- **性能监控**：监控系统运行状态
- **缓存管理**：清理系统缓存

---

## 👤 用户操作指南

### 1. 用户注册和登录

#### 1.1 飞书登录（推荐）
1. 访问系统首页
2. 点击"登录"按钮
3. 选择"飞书登录"
4. 使用公司飞书账号授权登录
5. 首次登录会自动创建用户账号

#### 1.2 账号注册
1. 在登录页面点击"立即注册"
2. 填写注册信息：
   - 姓名（必填）
   - 邮箱（必填，建议使用公司邮箱）
   - 手机号码
   - 所属部门（必填）
   - 工作职场（必填）
   - 登录密码（必填）
3. 点击"注册"完成账号创建

#### 1.3 账号密码登录
1. 输入注册时的邮箱和密码
2. 可选择"记住我"保持登录状态
3. 点击"登录"进入系统

### 2. 商品浏览和搜索

#### 2.1 商品展示页面
- **首页展示**：显示所有上架商品
- **商品信息**：商品图片、名称、光年币价格、人民币价格、库存状态
- **商品标签**：热门商品、新品标识

#### 2.2 商品筛选功能
- **分类筛选**：按商品分类筛选
- **价格排序**：按光年币价格或人民币价格排序
- **状态筛选**：查看有库存的商品
- **关键词搜索**：按商品名称搜索

#### 2.3 商品详情查看
1. 点击商品卡片查看详情
2. 查看商品详细描述
3. 浏览商品图片（支持大图预览）
4. 查看当前库存状态
5. 阅读兑换指引

### 3. 下单流程

#### 3.1 光年币兑换流程
1. 选择心仪商品，点击"申请兑换"
2. 确认商品信息和光年币价格
3. 选择配送职场
4. 填写备注信息（可选）
5. 确认光年币余额充足
6. 提交兑换申请
7. 等待管理员审核

#### 3.2 人民币购买流程
1. 选择商品，点击"申请购买"
2. 确认商品信息和人民币价格
3. 选择配送职场
4. 查看支付宝收款码
5. 使用支付宝扫码支付
6. 上传支付凭证截图
7. 提交购买申请
8. 等待管理员确认收款并发货

#### 3.3 订单确认要点
- **库存检查**：确认商品有库存
- **价格确认**：仔细核对商品价格
- **配送地址**：选择正确的职场配送地址
- **支付凭证**：人民币支付务必保留支付截图

### 4. 订单查看和管理

#### 4.1 我的订单页面
- **访问路径**：用户头像下拉菜单 → 我的兑换
- **订单状态标签**：
  - 全部：查看所有订单
  - 待处理：等待管理员审核的订单
  - 已批准：已通过审核的订单
  - 已发货：已发货待收货的订单
  - 已完成：已完成的订单
  - 已拒绝：被拒绝的订单
  - 已取消：已取消的订单

#### 4.2 订单详情查看
1. 在订单列表中点击"详情"按钮
2. 查看订单基本信息：
   - 订单编号
   - 商品信息
   - 支付方式和金额
   - 订单状态
   - 创建时间
3. 查看物流信息（已发货订单）
4. 查看支付凭证（人民币订单）

#### 4.3 订单操作
- **取消订单**：待处理状态的订单可以取消
- **确认收货**：已发货订单可以确认收货
- **查看物流**：跟踪商品配送状态

### 5. 个人信息管理

#### 5.1 个人中心
- **访问路径**：用户头像下拉菜单 → 个人中心
- **基本信息**：姓名、邮箱、部门、职场、注册时间
- **光年币余额**：当前可用光年币数量
- **最近登录**：最后登录时间和IP地址

#### 5.2 信息修改
1. 在个人中心点击"编辑信息"
2. 可修改的信息：
   - 手机号码
   - 工作职场
   - 个人头像（飞书用户自动同步）
3. 点击"保存"确认修改

#### 5.3 密码管理
- **修改密码**：在个人中心修改登录密码
- **忘记密码**：联系管理员重置密码
- **飞书用户**：无需设置密码，直接使用飞书登录

---

## ❓ 常见问题解答

### 支付问题处理

**Q1：光年币余额不足怎么办？**
A：联系管理员或HR部门充值光年币，或选择人民币支付方式。

**Q2：人民币支付后如何确认？**
A：上传支付宝付款截图，管理员确认收款后会批准订单。

**Q3：支付凭证上传失败怎么办？**
A：检查图片格式（支持JPG、PNG），确保图片大小不超过5MB，网络连接正常。

### 订单异常处理

**Q4：订单被拒绝了怎么办？**
A：查看拒绝原因，可能是库存不足、支付凭证有问题等，联系管理员了解详情。

**Q5：订单状态长时间不更新？**
A：联系管理员查看处理进度，可能需要人工审核时间。

**Q6：收到商品与订单不符？**
A：立即联系管理员，提供订单号和商品照片，申请退换货处理。

### 系统使用常见错误

**Q7：页面显示空白或加载失败？**
A：
1. 刷新页面（F5或Ctrl+R）
2. 清除浏览器缓存
3. 检查网络连接
4. 尝试使用Chrome或Firefox最新版本

**Q8：飞书登录失败？**
A：
1. 确认邮箱以@guanghe.tv结尾
2. 检查飞书账号状态
3. 清除浏览器Cookie后重试
4. 确认网络可以访问飞书服务

**Q9：无法上传图片？**
A：
1. 检查图片格式（JPG、PNG、GIF）
2. 确保图片大小不超过5MB
3. 检查网络连接稳定性
4. 尝试压缩图片后重新上传

---

## 🔧 技术说明

### 系统环境要求

**服务器环境：**
- 操作系统：Linux/Windows/macOS
- Node.js：≥ 16.0.0
- MySQL：≥ 8.0
- 内存：≥ 4GB RAM
- 存储：≥ 20GB 可用空间

**网络要求：**
- 稳定的互联网连接
- 能够访问飞书开放平台API
- 支持HTTPS协议

### 浏览器兼容性

**推荐浏览器：**
- Chrome 90+（推荐）
- Firefox 88+
- Safari 14+
- Edge 90+

**移动端支持：**
- iOS Safari 14+
- Android Chrome 90+
- 微信内置浏览器
- 飞书内置浏览器

**不支持的浏览器：**
- Internet Explorer（所有版本）
- Chrome 89及以下版本
- Firefox 87及以下版本

### 移动端使用说明

**响应式设计特性：**
- 自适应屏幕尺寸
- 触摸友好的操作界面
- 优化的移动端导航
- 快速加载和流畅滚动

**移动端操作建议：**
1. 使用竖屏模式获得最佳体验
2. 确保网络连接稳定
3. 定期清理浏览器缓存
4. 使用最新版本的移动浏览器

**移动端限制：**
- 部分复杂图表可能显示简化版本
- 批量操作功能在移动端受限
- 建议重要操作在PC端完成

---

## 📞 技术支持

**联系方式：**
- 技术支持邮箱：<EMAIL>
- 系统管理员：亚媚姐（飞书联系）
- 紧急联系：通过飞书群联系技术团队

**服务时间：**
- 工作日：9:00-18:00
- 紧急问题：24小时响应
- 系统维护：通常安排在非工作时间

**常用链接：**
- 系统首页：http://47.122.122.245
- 管理后台：http://47.122.122.245/admin
- API文档：http://47.122.122.245:3000/api-docs
- 帮助中心：系统内置帮助文档

---

*本文档最后更新时间：2025年7月22日*
*文档版本：v1.0.0*
*适用系统版本：光年小卖部 v2.0+*
