<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`编辑库存 - ${product?.name || ''}`"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="stock-edit-content" v-if="product">
      <!-- 商品信息 -->
      <div class="product-summary">
        <div class="product-info">
          <img
            :src="fixImageUrl(product.imageUrl)"
            :alt="product.name"
            class="product-image"
          />
          <div class="product-details">
            <h3 class="product-name">{{ product.name }}</h3>
            <p class="product-category">{{ product.Category?.name || '未分类' }}</p>
            <div class="current-stock">
              <span class="label">当前总库存:</span>
              <span class="value">{{ product.totalStock || product.stock || 0 }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 职场库存编辑表单 -->
      <div class="stock-form">
        <h4 class="form-title">
          <i class="fas fa-edit"></i>
          职场库存分配
        </h4>
        
        <el-form 
          :model="formData" 
          :rules="formRules"
          ref="formRef"
          label-width="100px"
        >
          <!-- 操作原因 -->
          <el-form-item label="操作原因" prop="reason">
            <el-input
              v-model="formData.reason"
              placeholder="请输入库存调整原因"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>

          <!-- 职场库存列表 -->
          <el-form-item label="职场库存">
            <div class="workplace-stocks-editor">
              <div 
                v-for="(stockUpdate, index) in formData.stockUpdates"
                :key="stockUpdate.workplaceId"
                class="workplace-stock-row"
              >
                <div class="workplace-info">
                  <div class="workplace-name">
                    {{ getWorkplaceName(stockUpdate.workplaceId) }}
                  </div>
                  <div class="workplace-code">
                    {{ getWorkplaceCode(stockUpdate.workplaceId) }}
                  </div>
                </div>

                <div class="stock-inputs">
                  <div class="input-group">
                    <label>库存数量</label>
                    <el-input-number
                      v-model="stockUpdate.stock"
                      :min="0"
                      :max="stockUpdate.maxStockLimit || 9999"
                      :precision="0"
                      size="small"
                      style="width: 120px"
                    />
                  </div>

                  <div class="input-group">
                    <label>告警阈值</label>
                    <el-input-number
                      v-model="stockUpdate.minStockAlert"
                      :min="0"
                      :max="999"
                      :precision="0"
                      size="small"
                      style="width: 100px"
                    />
                  </div>

                  <div class="input-group" v-if="stockUpdate.maxStockLimit !== undefined">
                    <label>最大限制</label>
                    <el-input-number
                      v-model="stockUpdate.maxStockLimit"
                      :min="1"
                      :max="9999"
                      :precision="0"
                      size="small"
                      style="width: 120px"
                      placeholder="无限制"
                      clearable
                    />
                  </div>
                </div>

                <div class="stock-actions">
                  <el-button
                    type="danger"
                    size="small"
                    icon="el-icon-delete"
                    @click="removeWorkplace(index)"
                    v-if="formData.stockUpdates.length > 1"
                  >
                    移除
                  </el-button>
                </div>
              </div>

              <!-- 添加职场按钮 -->
              <div class="add-workplace" v-if="availableWorkplaces.length > 0">
                <el-select
                  v-model="selectedNewWorkplace"
                  placeholder="选择要添加的职场"
                  size="small"
                  style="width: 200px; margin-right: 12px;"
                >
                  <el-option
                    v-for="workplace in availableWorkplaces"
                    :key="workplace.id"
                    :label="`${workplace.name} (${workplace.code})`"
                    :value="workplace.id"
                  />
                </el-select>
                <el-button
                  type="primary"
                  size="small"
                  icon="el-icon-plus"
                  @click="addWorkplace"
                  :disabled="!selectedNewWorkplace"
                >
                  添加职场
                </el-button>
              </div>
            </div>
          </el-form-item>

          <!-- 库存汇总 -->
          <el-form-item label="库存汇总">
            <div class="stock-summary">
              <div class="summary-item">
                <span class="summary-label">新总库存:</span>
                <span class="summary-value" :class="{ 'changed': newTotalStock !== originalTotalStock }">
                  {{ newTotalStock }}
                </span>
              </div>
              <div class="summary-item" v-if="newTotalStock !== originalTotalStock">
                <span class="summary-label">变化量:</span>
                <span class="summary-value" :class="stockChangeClass">
                  {{ stockChangeText }}
                </span>
              </div>
            </div>
          </el-form-item>

          <!-- 快速操作 -->
          <el-form-item label="快速操作">
            <div class="quick-actions">
              <el-button size="small" @click="averageDistribute">
                平均分配
              </el-button>
              <el-button size="small" @click="clearAllStocks">
                清空库存
              </el-button>
              <el-button size="small" @click="resetToOriginal">
                重置原值
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm"
          :loading="loading"
        >
          确定更新
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { fixImageUrl } from '@/utils/imageUtils'

export default {
  name: 'StockEditDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    product: {
      type: Object,
      default: null
    },
    workplaces: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:modelValue', 'confirm'],
  setup(props, { emit }) {
    const dialogVisible = ref(false)
    const loading = ref(false)
    const formRef = ref(null)
    const selectedNewWorkplace = ref(null)

    const formData = ref({
      reason: '',
      stockUpdates: []
    })

    const formRules = {
      reason: [
        { required: true, message: '请输入操作原因', trigger: 'blur' }
      ]
    }

    // 验证库存数量是否超过最大限制
    const validateStockLimit = (stockUpdate) => {
      if (stockUpdate.maxStockLimit && stockUpdate.maxStockLimit > 0 && stockUpdate.stock > stockUpdate.maxStockLimit) {
        const workplaceName = getWorkplaceName(stockUpdate.workplaceId)
        ElMessage.error(`${workplaceName}的库存数量(${stockUpdate.stock})不能超过最大限制(${stockUpdate.maxStockLimit})`)
        return false
      }
      return true
    }

    // 计算属性
    const activeWorkplaces = computed(() => {
      return props.workplaces.filter(w => w.isActive)
    })

    const availableWorkplaces = computed(() => {
      const usedWorkplaceIds = formData.value.stockUpdates.map(s => s.workplaceId)
      return activeWorkplaces.value.filter(w => !usedWorkplaceIds.includes(w.id))
    })

    const originalTotalStock = computed(() => {
      return props.product?.totalStock || props.product?.stock || 0
    })

    const newTotalStock = computed(() => {
      return formData.value.stockUpdates.reduce((sum, update) => sum + (update.stock || 0), 0)
    })

    const stockChangeClass = computed(() => {
      const change = newTotalStock.value - originalTotalStock.value
      if (change > 0) return 'increase'
      if (change < 0) return 'decrease'
      return ''
    })

    const stockChangeText = computed(() => {
      const change = newTotalStock.value - originalTotalStock.value
      if (change > 0) return `+${change}`
      if (change < 0) return `${change}`
      return '0'
    })

    // 方法
    const getWorkplaceName = (workplaceId) => {
      const workplace = props.workplaces.find(w => w.id === workplaceId)
      return workplace?.name || '未知职场'
    }

    const getWorkplaceCode = (workplaceId) => {
      const workplace = props.workplaces.find(w => w.id === workplaceId)
      return workplace?.code || ''
    }

    const initializeForm = () => {
      if (!props.product) return

      const stockUpdates = []
      
      if (props.product.workplaceStocks && props.product.workplaceStocks.length > 0) {
        // 已有职场库存，使用现有数据
        stockUpdates.push(...props.product.workplaceStocks.map(stock => ({
          workplaceId: stock.workplaceId,
          stock: stock.stock,
          minStockAlert: stock.minStockAlert,
          maxStockLimit: stock.maxStockLimit
        })))
      } else {
        // 没有职场库存，为所有活跃职场初始化
        const totalStock = props.product.stock || 0
        const stockPerWorkplace = Math.floor(totalStock / activeWorkplaces.value.length)
        const remainder = totalStock % activeWorkplaces.value.length

        activeWorkplaces.value.forEach((workplace, index) => {
          stockUpdates.push({
            workplaceId: workplace.id,
            stock: stockPerWorkplace + (index < remainder ? 1 : 0),
            minStockAlert: 10,
            maxStockLimit: null
          })
        })
      }

      formData.value = {
        reason: '',
        stockUpdates
      }
    }

    const addWorkplace = () => {
      if (!selectedNewWorkplace.value) return

      formData.value.stockUpdates.push({
        workplaceId: selectedNewWorkplace.value,
        stock: 0,
        minStockAlert: 10,
        maxStockLimit: null
      })

      selectedNewWorkplace.value = null
    }

    const removeWorkplace = (index) => {
      formData.value.stockUpdates.splice(index, 1)
    }

    const averageDistribute = () => {
      const totalStock = originalTotalStock.value
      const workplaceCount = formData.value.stockUpdates.length
      
      if (workplaceCount === 0) return

      const stockPerWorkplace = Math.floor(totalStock / workplaceCount)
      const remainder = totalStock % workplaceCount

      formData.value.stockUpdates.forEach((update, index) => {
        update.stock = stockPerWorkplace + (index < remainder ? 1 : 0)
      })

      ElMessage.success('已平均分配库存')
    }

    const clearAllStocks = () => {
      formData.value.stockUpdates.forEach(update => {
        update.stock = 0
      })
      ElMessage.success('已清空所有库存')
    }

    const resetToOriginal = () => {
      initializeForm()
      ElMessage.success('已重置为原始值')
    }

    const handleClose = () => {
      dialogVisible.value = false
      emit('update:modelValue', false)
    }

    const handleConfirm = async () => {
      if (!formRef.value) return

      try {
        await formRef.value.validate()

        // 验证所有库存更新是否符合最大限制
        for (const stockUpdate of formData.value.stockUpdates) {
          if (!validateStockLimit(stockUpdate)) {
            return
          }
        }

        loading.value = true
        emit('confirm', {
          stockUpdates: formData.value.stockUpdates,
          reason: formData.value.reason
        })
      } catch (error) {
        console.error('表单验证失败:', error)
      } finally {
        loading.value = false
      }
    }

    // 监听器
    watch(() => props.modelValue, (newVal) => {
      dialogVisible.value = newVal
      if (newVal) {
        nextTick(() => {
          initializeForm()
        })
      }
    })

    watch(dialogVisible, (newVal) => {
      if (!newVal) {
        emit('update:modelValue', false)
      }
    })

    return {
      dialogVisible,
      loading,
      formRef,
      selectedNewWorkplace,
      formData,
      formRules,
      activeWorkplaces,
      availableWorkplaces,
      originalTotalStock,
      newTotalStock,
      stockChangeClass,
      stockChangeText,
      getWorkplaceName,
      getWorkplaceCode,
      validateStockLimit,
      addWorkplace,
      removeWorkplace,
      averageDistribute,
      clearAllStocks,
      resetToOriginal,
      handleClose,
      handleConfirm,
      fixImageUrl
    }
  }
}
</script>

<style scoped>
.stock-edit-content {
  max-height: 70vh;
  overflow-y: auto;
}

/* 商品信息 */
.product-summary {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.product-image {
  width: 64px;
  height: 64px;
  border-radius: 6px;
  object-fit: cover;
  border: 1px solid #e4e7ed;
}

.product-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.product-category {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #909399;
}

.current-stock .label {
  color: #606266;
  margin-right: 8px;
}

.current-stock .value {
  font-weight: 600;
  color: #409eff;
}

/* 表单 */
.form-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-title i {
  color: #409eff;
}

/* 职场库存编辑器 */
.workplace-stocks-editor {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.workplace-stock-row {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f2f5;
  background: white;
}

.workplace-stock-row:last-child {
  border-bottom: none;
}

.workplace-info {
  min-width: 120px;
  margin-right: 20px;
}

.workplace-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.workplace-code {
  font-size: 12px;
  color: #909399;
  background: #f0f2f5;
  padding: 2px 6px;
  border-radius: 3px;
  display: inline-block;
}

.stock-inputs {
  flex: 1;
  display: flex;
  gap: 16px;
  align-items: flex-end;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.input-group label {
  font-size: 12px;
  color: #606266;
}

.stock-actions {
  margin-left: 16px;
}

/* 添加职场 */
.add-workplace {
  padding: 16px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
}

/* 库存汇总 */
.stock-summary {
  display: flex;
  gap: 24px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-label {
  color: #606266;
  font-size: 14px;
}

.summary-value {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
}

.summary-value.changed {
  color: #409eff;
}

.summary-value.increase {
  color: #67c23a;
}

.summary-value.decrease {
  color: #f56c6c;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  gap: 8px;
}

/* 响应式 */
@media (max-width: 768px) {
  .workplace-stock-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .workplace-info {
    margin-right: 0;
  }
  
  .stock-inputs {
    flex-direction: column;
    gap: 12px;
  }
  
  .input-group {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
  
  .stock-actions {
    margin-left: 0;
    align-self: flex-end;
  }
  
  .add-workplace {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .stock-summary {
    flex-direction: column;
    gap: 8px;
  }
  
  .quick-actions {
    flex-wrap: wrap;
  }
}
</style>
