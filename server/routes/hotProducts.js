const express = require('express');
const router = express.Router();
const hotProductController = require('../controllers/hotProductController');
const { authenticate, isAdmin } = require('../middlewares/auth');

/**
 * @swagger
 * components:
 *   schemas:
 *     HotProductConfig:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         timeRange:
 *           type: string
 *           enum: [all, 30d, 7d, 1d]
 *         enabled:
 *           type: boolean
 *         maxCount:
 *           type: integer
 *         minExchangeCount:
 *           type: integer
 *         exchangeWeight:
 *           type: number
 *         stockWeight:
 *           type: number
 *         autoUpdateEnabled:
 *           type: boolean
 *         updateFrequency:
 *           type: string
 */

/**
 * @swagger
 * /hot-products/configs:
 *   get:
 *     summary: 获取所有热门商品配置
 *     tags: [Hot Products]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 返回配置列表
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/HotProductConfig'
 */
router.get('/configs', authenticate, isAdmin, hotProductController.getConfigs);

/**
 * @swagger
 * /hot-products/configs:
 *   put:
 *     summary: 批量更新热门商品配置
 *     tags: [Hot Products]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               configs:
 *                 type: array
 *                 items:
 *                   $ref: '#/components/schemas/HotProductConfig'
 *     responses:
 *       200:
 *         description: 配置更新成功
 */
router.put('/configs', authenticate, isAdmin, hotProductController.updateConfigs);

/**
 * @swagger
 * /hot-products/update:
 *   post:
 *     summary: 手动触发热门商品更新
 *     tags: [Hot Products]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               timeRange:
 *                 type: string
 *                 enum: [all, 30d, 7d, 1d]
 *                 description: 指定时间维度，不传则更新所有
 *     responses:
 *       200:
 *         description: 更新成功
 */
router.post('/update', authenticate, isAdmin, hotProductController.triggerUpdate);

/**
 * @swagger
 * /hot-products/history:
 *   get:
 *     summary: 获取热门商品历史记录
 *     tags: [Hot Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeRange
 *         schema:
 *           type: string
 *           enum: [all, 30d, 7d, 1d]
 *       - in: query
 *         name: productId
 *         schema:
 *           type: integer
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: 返回历史记录
 */
router.get('/history', authenticate, isAdmin, hotProductController.getHistory);

/**
 * @swagger
 * /hot-products/stats:
 *   get:
 *     summary: 获取热门商品统计信息
 *     tags: [Hot Products]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 返回统计信息
 */
router.get('/stats', authenticate, isAdmin, hotProductController.getStats);

/**
 * @swagger
 * /hot-products/cleanup:
 *   post:
 *     summary: 清理历史记录
 *     tags: [Hot Products]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               daysToKeep:
 *                 type: integer
 *                 default: 90
 *     responses:
 *       200:
 *         description: 清理成功
 */
router.post('/cleanup', authenticate, isAdmin, hotProductController.cleanupHistory);

// 公开接口 - 不需要管理员权限

/**
 * @swagger
 * /hot-products:
 *   get:
 *     summary: 获取指定时间维度的热门商品
 *     tags: [Hot Products]
 *     parameters:
 *       - in: query
 *         name: timeRange
 *         schema:
 *           type: string
 *           enum: [all, 30d, 7d, 1d]
 *           default: all
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *     responses:
 *       200:
 *         description: 返回热门商品列表
 */
router.get('/', hotProductController.getHotProducts);

/**
 * @swagger
 * /hot-products/all:
 *   get:
 *     summary: 获取所有时间维度的热门商品
 *     tags: [Hot Products]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *     responses:
 *       200:
 *         description: 返回所有时间维度的热门商品
 */
router.get('/all', hotProductController.getAllHotProducts);

module.exports = router;
