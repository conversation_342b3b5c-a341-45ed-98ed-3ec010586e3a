<template>
  <div class="help-doc feishu-integration">
    <h2>飞书机器人集成</h2>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>功能概述</h3>
        </div>
      </template>
      <div class="card-content">
        <p>光年小卖部深度集成飞书生态，提供完整的飞书登录认证和群消息推送功能。通过飞书机器人，系统可以自动推送销售报告、订单通知、里程碑庆祝等重要信息到指定的飞书群。</p>
        
        <div class="info-block">
          <h4>核心功能</h4>
          <ul>
            <li><strong>飞书登录</strong>：用户可以使用飞书账号直接登录系统</li>
            <li><strong>自动报告</strong>：每日、每周、每月自动推送销售数据报告</li>
            <li><strong>实时通知</strong>：新用户注册、重要订单、系统异常等实时推送</li>
            <li><strong>里程碑庆祝</strong>：达成销售目标时自动发送庆祝消息</li>
            <li><strong>智能调度</strong>：可配置推送时间和频率</li>
          </ul>
        </div>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>飞书登录配置</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>配置步骤</h4>
        <ol>
          <li>在飞书开放平台创建应用</li>
          <li>获取App ID和App Secret</li>
          <li>配置回调地址：<code>https://your-domain.com/feishu/callback</code></li>
          <li>在系统设置中配置飞书应用信息</li>
          <li>测试登录功能是否正常</li>
        </ol>
        
        <h4>用户登录流程</h4>
        <p>用户可以通过以下方式登录系统：</p>
        <ul>
          <li>在登录页面点击"飞书登录"按钮</li>
          <li>跳转到飞书授权页面</li>
          <li>用户确认授权后自动返回系统</li>
          <li>系统自动创建或更新用户信息</li>
          <li>登录成功，进入系统主页</li>
        </ul>
        
        <el-alert
          title="注意：首次使用飞书登录的用户会自动创建账号，邮箱必须以@guanghe.tv结尾"
          type="info"
          :closable="false"
          show-icon
          style="margin: 15px 0;"
        />
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>群消息推送</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>推送类型</h4>
        <el-table :data="notificationTypes" style="width: 100%">
          <el-table-column prop="type" label="推送类型" width="150" />
          <el-table-column prop="description" label="说明" />
          <el-table-column prop="frequency" label="推送频率" width="120" />
        </el-table>
        
        <h4>消息模板</h4>
        <p>系统提供多种预设消息模板，您也可以自定义模板：</p>
        <ul>
          <li><strong>销售报告模板</strong>：包含订单数量、销售额、热门商品等信息</li>
          <li><strong>里程碑庆祝模板</strong>：达成特定目标时的庆祝消息</li>
          <li><strong>异常预警模板</strong>：库存不足、大额订单等异常情况提醒</li>
          <li><strong>新用户欢迎模板</strong>：新用户注册时的欢迎消息</li>
        </ul>
        
        <h4>智能调度</h4>
        <p>系统支持智能调度功能，可以：</p>
        <ul>
          <li>设置推送时间（避免非工作时间打扰）</li>
          <li>配置推送频率（防止消息过于频繁）</li>
          <li>设置推送条件（只在满足特定条件时推送）</li>
          <li>支持节假日自动跳过</li>
        </ul>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>配置管理</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>通知配置</h4>
        <p>在系统设置 > 通知配置中，您可以：</p>
        <ul>
          <li>启用或禁用各种类型的通知</li>
          <li>设置Webhook地址</li>
          <li>配置推送时间</li>
          <li>设置重试次数</li>
        </ul>
        
        <h4>消息模板管理</h4>
        <p>在系统设置 > 自定义消息模板中，您可以：</p>
        <ul>
          <li>创建自定义消息模板</li>
          <li>编辑现有模板内容</li>
          <li>设置模板变量</li>
          <li>预览模板效果</li>
        </ul>
        
        <h4>智能调度设置</h4>
        <p>在系统设置 > 智能发送时间控制中，您可以：</p>
        <ul>
          <li>设置工作时间范围</li>
          <li>配置节假日规则</li>
          <li>设置消息发送间隔</li>
          <li>配置紧急消息例外规则</li>
        </ul>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>故障排查</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>常见问题</h4>
        <el-collapse>
          <el-collapse-item title="飞书登录失败怎么办？" name="1">
            <p>检查以下配置：</p>
            <ol>
              <li>确认飞书应用的App ID和App Secret配置正确</li>
              <li>检查回调地址是否与飞书开放平台配置一致</li>
              <li>确认用户邮箱以@guanghe.tv结尾</li>
              <li>查看系统日志获取详细错误信息</li>
            </ol>
          </el-collapse-item>
          
          <el-collapse-item title="群消息推送失败怎么办？" name="2">
            <p>排查步骤：</p>
            <ol>
              <li>检查Webhook地址是否正确</li>
              <li>确认机器人已添加到目标群聊</li>
              <li>查看通知配置是否启用</li>
              <li>检查网络连接是否正常</li>
              <li>查看系统日志中的错误信息</li>
            </ol>
          </el-collapse-item>
          
          <el-collapse-item title="如何测试飞书机器人功能？" name="3">
            <p>测试方法：</p>
            <ol>
              <li>进入系统设置 > 高级诊断工具</li>
              <li>选择要测试的通知类型</li>
              <li>点击"发送测试消息"按钮</li>
              <li>检查飞书群是否收到测试消息</li>
              <li>查看发送结果和日志信息</li>
            </ol>
          </el-collapse-item>
          
          <el-collapse-item title="如何修改推送时间？" name="4">
            <p>修改步骤：</p>
            <ol>
              <li>进入系统设置 > 智能发送时间控制</li>
              <li>选择要修改的通知类型</li>
              <li>设置新的推送时间</li>
              <li>保存配置</li>
              <li>系统会在下次推送时使用新时间</li>
            </ol>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
  </div>
</template>

<script setup>
// 通知类型数据
const notificationTypes = [
  {
    type: '每日报告',
    description: '每日销售数据汇总，包含订单数量、销售额、热门商品等',
    frequency: '每日19:00'
  },
  {
    type: '每周报告',
    description: '每周销售数据分析，包含趋势图表和部门排行',
    frequency: '每周一09:00'
  },
  {
    type: '每月报告',
    description: '每月运营数据总结，包含增长率和用户分析',
    frequency: '每月1日09:00'
  },
  {
    type: '新用户欢迎',
    description: '新用户通过飞书登录注册时发送欢迎消息',
    frequency: '实时推送'
  },
  {
    type: '里程碑庆祝',
    description: '达成销售目标或重要里程碑时发送庆祝消息',
    frequency: '触发推送'
  },
  {
    type: '异常订单预警',
    description: '大额订单或异常行为的实时预警通知',
    frequency: '实时推送'
  },
  {
    type: '用户反馈汇总',
    description: '收到新的用户反馈或重要建议时的提醒',
    frequency: '实时推送'
  },
  {
    type: '新品上架通知',
    description: '管理员添加新商品时的推广通知',
    frequency: '触发推送'
  }
];
</script>

<style scoped>
.feishu-integration h2 {
  margin-bottom: 20px;
  font-weight: bold;
  color: #303133;
}

.help-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.card-content {
  line-height: 1.6;
}

.card-content h4 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-size: 16px;
  color: #303133;
}

.card-content p {
  margin-bottom: 12px;
  color: #606266;
}

.card-content ul, .card-content ol {
  padding-left: 20px;
  margin-bottom: 16px;
}

.card-content li {
  margin-bottom: 6px;
  color: #606266;
}

.info-block {
  background-color: #f0f9ff;
  border-left: 4px solid #409EFF;
  padding: 12px 16px;
  margin: 16px 0;
  border-radius: 4px;
}

code {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 2px 6px;
  font-family: monospace;
  color: #c41d7f;
}
</style>
