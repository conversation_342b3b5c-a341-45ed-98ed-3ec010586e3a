<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公告格式测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .content-display {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .html-display {
            background: #fff;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>公告格式显示测试</h1>
    
    <div class="test-section">
        <div class="test-title">1. 原始内容（从API获取）</div>
        <div class="content-display" id="raw-content"></div>
    </div>
    
    <div class="test-section">
        <div class="test-title">2. 修复前的处理（只处理真正的换行符）</div>
        <div class="html-display" id="old-format"></div>
    </div>
    
    <div class="test-section">
        <div class="test-title">3. 修复后的处理（处理字面量\n）</div>
        <div class="html-display" id="new-format"></div>
    </div>

    <script>
        // 模拟从API获取的公告内容
        const sampleContent = " (2025年6月9日) - 热门商品智能识别系统上线\\n- 🚀 **重大功能**: 全新热门商品自动识别系统正式上线\\n- ✨ **智能识别算法**:\\n  - 基于兑换量的自动热门商品识别机制\\n  - 支持四个时间维度：累积/30天/7天/今日热门\\n  - 智能评分算法：兑换量权重 + 库存奖励机制";

        // 修复前的格式化函数（只处理真正的换行符）
        function oldFormatContent(content) {
            if (!content) return '';
            return content
                .replace(/\n/g, '<br>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>');
        }

        // 修复后的格式化函数（处理字面量\n）
        function newFormatContent(content) {
            if (!content) return '';
            return content
                // 首先处理字面量的\n字符串（反斜杠+n）
                .replace(/\\n/g, '<br>')
                // 然后处理真正的换行符
                .replace(/\n/g, '<br>')
                // 处理markdown格式的粗体
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                // 处理markdown格式的斜体
                .replace(/\*(.*?)\*/g, '<em>$1</em>');
        }

        // 显示测试结果
        document.getElementById('raw-content').textContent = sampleContent;
        document.getElementById('old-format').innerHTML = oldFormatContent(sampleContent);
        document.getElementById('new-format').innerHTML = newFormatContent(sampleContent);
    </script>
</body>
</html>
