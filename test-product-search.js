/**
 * 测试商品搜索功能
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:3000/api';

// 获取管理员token
async function getAdminToken() {
  try {
    const loginData = {
      username: '超管',
      email: 'ch<PERSON><PERSON><PERSON>@guanghe.tv',
      password: '654321',
      userType: 'admin'
    };
    
    const response = await axios.post(`${BASE_URL}/auth/login`, loginData);
    return response.data.token;
  } catch (error) {
    console.error('❌ 获取token失败:', error.response?.data?.message || error.message);
    return null;
  }
}

// 测试商品搜索功能
async function testProductSearch(token) {
  try {
    const config = {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };
    
    console.log('🔍 测试商品搜索功能...\n');
    
    // 1. 测试按商品ID搜索
    console.log('1. 按商品ID搜索（ID=15）:');
    const idSearchResponse = await axios.get(`${BASE_URL}/stocks/operation-logs`, {
      ...config,
      params: {
        productId: 15,
        page: 1,
        limit: 5
      }
    });
    
    console.log('搜索结果:', {
      success: idSearchResponse.data.success,
      count: idSearchResponse.data.data?.logs?.length || 0,
      total: idSearchResponse.data.data?.pagination?.total || 0
    });
    
    if (idSearchResponse.data.data?.logs?.length > 0) {
      const firstLog = idSearchResponse.data.data.logs[0];
      console.log('第一条记录:', {
        id: firstLog.id,
        productId: firstLog.productId,
        productName: firstLog.product?.name || '未知',
        operationType: firstLog.operationType,
        reason: firstLog.reason
      });
    }
    
    // 2. 测试按商品名称搜索
    console.log('\n2. 按商品名称搜索（"哪吒"）:');
    const nameSearchResponse = await axios.get(`${BASE_URL}/stocks/operation-logs`, {
      ...config,
      params: {
        productName: '哪吒',
        page: 1,
        limit: 5
      }
    });
    
    console.log('搜索结果:', {
      success: nameSearchResponse.data.success,
      count: nameSearchResponse.data.data?.logs?.length || 0,
      total: nameSearchResponse.data.data?.pagination?.total || 0
    });
    
    if (nameSearchResponse.data.data?.logs?.length > 0) {
      const firstLog = nameSearchResponse.data.data.logs[0];
      console.log('第一条记录:', {
        id: firstLog.id,
        productId: firstLog.productId,
        productName: firstLog.product?.name || '未知',
        operationType: firstLog.operationType,
        reason: firstLog.reason
      });
    }
    
    // 3. 测试按商品名称模糊搜索
    console.log('\n3. 按商品名称模糊搜索（"日"）:');
    const fuzzySearchResponse = await axios.get(`${BASE_URL}/stocks/operation-logs`, {
      ...config,
      params: {
        productName: '日',
        page: 1,
        limit: 5
      }
    });
    
    console.log('搜索结果:', {
      success: fuzzySearchResponse.data.success,
      count: fuzzySearchResponse.data.data?.logs?.length || 0,
      total: fuzzySearchResponse.data.data?.pagination?.total || 0
    });
    
    if (fuzzySearchResponse.data.data?.logs?.length > 0) {
      console.log('匹配的商品:');
      fuzzySearchResponse.data.data.logs.forEach((log, index) => {
        console.log(`  ${index + 1}. ${log.product?.name || '未知'} (ID: ${log.productId})`);
      });
    }
    
    // 4. 测试不存在的商品名称
    console.log('\n4. 搜索不存在的商品名称（"不存在的商品"）:');
    const notFoundResponse = await axios.get(`${BASE_URL}/stocks/operation-logs`, {
      ...config,
      params: {
        productName: '不存在的商品',
        page: 1,
        limit: 5
      }
    });
    
    console.log('搜索结果:', {
      success: notFoundResponse.data.success,
      count: notFoundResponse.data.data?.logs?.length || 0,
      total: notFoundResponse.data.data?.pagination?.total || 0
    });
    
    return true;
  } catch (error) {
    console.error('❌ 测试商品搜索失败:', error.response?.data || error.message);
    return false;
  }
}

// 主测试函数
async function runProductSearchTest() {
  console.log('🧪 开始测试商品搜索功能...\n');
  
  // 1. 获取token
  const token = await getAdminToken();
  if (!token) {
    console.log('❌ 无法获取token，测试终止');
    return;
  }
  console.log('✅ 获取token成功\n');
  
  // 2. 测试搜索功能
  const success = await testProductSearch(token);
  
  if (success) {
    console.log('\n🎉 商品搜索功能测试成功！');
    console.log('💡 现在前端可以支持以下搜索方式：');
    console.log('   - 按商品ID精确搜索');
    console.log('   - 按商品名称模糊搜索');
    console.log('   - 前端界面支持搜索类型切换');
  } else {
    console.log('\n❌ 商品搜索功能测试失败');
  }
  
  console.log('\n📋 测试完成');
}

// 运行测试
runProductSearchTest().catch(error => {
  console.error('测试执行失败:', error);
});
