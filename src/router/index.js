import { createRouter, createWebHistory } from 'vue-router';
import { useAuthStore } from '../stores/auth';

// 页面组件
const Login = () => import('../views/Login.vue');
// 暂时使用Home作为商品展示页面
const Home = () => import('../views/Home.vue');
// 飞书登录回调页面
const FeishuCallback = () => import('../views/FeishuCallback.vue');
// Admin布局和页面
const AdminLayout = () => import('../views/admin/AdminLayout.vue');
const Dashboard = () => import('../views/admin/Dashboard.vue');
const ProductManagement = () => import('../views/admin/ProductManagement.vue');
const CategoryManagement = () => import('../views/admin/CategoryManagement.vue');
const UserManagement = () => import('../views/admin/UserManagement.vue');
const ExchangeManagement = () => import('../views/admin/ExchangeManagement.vue');
const FeedbackManagement = () => import('../views/admin/FeedbackManagement.vue');
const AnnouncementManagement = () => import('../views/admin/AnnouncementManagement.vue');
const LogManagement = () => import('../views/admin/LogManagement.vue');
const NotificationsPage = () => import('../views/admin/NotificationsPage.vue');
const SystemSettings = () => import('../views/admin/SystemSettings.vue');
const HelpCenter = () => import('../views/admin/HelpCenter.vue');
const StockManagement = () => import('../views/admin/StockManagement.vue');
const StockDashboard = () => import('../views/admin/StockDashboard.vue');

// 用户页面
const UserProfile = () => import('../views/user/Profile.vue');
const UserExchanges = () => import('../views/user/Exchanges.vue');

// 飞书群管理高级功能
const FeishuMessageTemplates = () => import('../views/admin/system/FeishuMessageTemplates.vue');
const IntelligentSchedule = () => import('../views/admin/system/IntelligentSchedule.vue');
const DiagnosticTools = () => import('../views/admin/system/DiagnosticTools.vue');

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { title: '商品展示' }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { title: '登录', guest: true }
  },
  // 飞书登录回调路由
  {
    path: '/feishu/callback',
    name: 'FeishuCallback',
    component: FeishuCallback,
    meta: { title: '飞书登录处理', guest: true }
  },
  // 用户路由
  {
    path: '/user',
    meta: { requiresAuth: true },
    children: [
      // 个人中心
      {
        path: 'profile',
        name: 'UserProfile',
        component: UserProfile,
        meta: { requiresAuth: true, title: '个人中心' }
      },
      // 我的兑换
      {
        path: 'exchanges',
        name: 'UserExchanges',
        component: UserExchanges,
        meta: { requiresAuth: true, title: '我的兑换' }
      }
    ]
  },
  // 管理员路由
  {
    path: '/admin',
    component: AdminLayout,
    meta: { requiresAuth: true, requiresAdmin: true },
    children: [
      // 数据仪表盘
      {
        path: 'dashboard',
        component: Dashboard,
        meta: { requiresAuth: true, requiresAdmin: true, title: '数据仪表盘' }
      },
      // 商品管理
      {
        path: 'products',
        component: ProductManagement,
        meta: { requiresAuth: true, requiresAdmin: true, title: '商品管理' }
      },
      // 热门商品管理
      {
        path: 'hot-products',
        component: () => import('../views/admin/HotProductManagement.vue'),
        meta: { requiresAuth: true, requiresAdmin: true, title: '热门商品管理' }
      },
      // 库存管理
      {
        path: 'stock-management',
        component: StockManagement,
        meta: { requiresAuth: true, requiresAdmin: true, title: '库存管理' }
      },
      // 库存统计
      {
        path: 'stock-dashboard',
        component: StockDashboard,
        meta: { requiresAuth: true, requiresAdmin: true, title: '库存统计' }
      },

      // 订单管理
      {
        path: 'exchanges',
        component: ExchangeManagement,
        meta: { requiresAuth: true, requiresAdmin: true, title: '订单管理' }
      },
      // 分类管理
      {
        path: 'categories',
        component: () => import('../views/admin/CategoryManagement.vue'),
        meta: { requiresAuth: true, requiresAdmin: true, title: '分类管理' }
      },
      // 公告管理
      {
        path: 'announcements',
        component: AnnouncementManagement,
        meta: { requiresAuth: true, requiresAdmin: true, title: '公告管理' }
      },
      // 用户管理
      {
        path: 'users',
        component: UserManagement,
        meta: { requiresAuth: true, requiresAdmin: true, title: '用户管理' }
      },
      // 反馈管理
      {
        path: 'feedbacks',
        component: FeedbackManagement,
        meta: { requiresAuth: true, requiresAdmin: true, title: '反馈管理' }
      },
      // 日志管理
      {
        path: 'logs',
        component: LogManagement,
        meta: { requiresAuth: true, requiresAdmin: true, title: '日志管理' }
      },
      // 帮助中心
      {
        path: 'help',
        component: HelpCenter,
        meta: { requiresAuth: true, requiresAdmin: true, title: '帮助中心' }
      },
      // 通知中心
      {
        path: 'notifications',
        component: NotificationsPage,
        meta: { requiresAuth: true, requiresAdmin: true, title: '通知中心' }
      },
      // 系统设置主页面（重定向到通知管理）
      {
        path: 'system',
        redirect: '/admin/system/notification'
      },
      // 系统设置子模块
      {
        path: 'system/notification',
        component: () => import('../views/admin/system/NotificationSettings.vue'),
        meta: { requiresAuth: true, requiresAdmin: true, title: '通知管理' }
      },
      {
        path: 'system/payment',
        component: () => import('../views/admin/system/PaymentSettings.vue'),
        meta: { requiresAuth: true, requiresAdmin: true, title: '支付设置' }
      },
      {
        path: 'system/workplace',
        component: () => import('../views/admin/system/WorkplaceSettings.vue'),
        meta: { requiresAuth: true, requiresAdmin: true, title: '职场管理' }
      },
      {
        path: 'system/maintenance',
        component: () => import('../views/admin/system/MaintenanceSettings.vue'),
        meta: { requiresAuth: true, requiresAdmin: true, title: '系统维护' }
      },
      {
        path: 'system/advanced',
        component: () => import('../views/admin/system/AdvancedSettings.vue'),
        meta: { requiresAuth: true, requiresAdmin: true, title: '高级功能' }
      },
      // 飞书群管理高级功能
      {
        path: 'system/message-templates',
        component: FeishuMessageTemplates,
        meta: { requiresAuth: true, requiresAdmin: true, title: '自定义消息模板' }
      },
      {
        path: 'system/intelligent-schedule',
        component: IntelligentSchedule,
        meta: { requiresAuth: true, requiresAdmin: true, title: '智能发送时间控制' }
      },
      {
        path: 'system/diagnostic-tools',
        component: DiagnosticTools,
        meta: { requiresAuth: true, requiresAdmin: true, title: '高级诊断工具' }
      },
      // 默认重定向到仪表盘
      {
        path: '',
        redirect: '/admin/dashboard'
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 光年小卖部` : '光年小卖部';

  const authStore = useAuthStore();
  const isAuthenticated = authStore.isAuthenticated;

  // 如果用户已登录但没有用户信息，尝试获取用户资料
  if (isAuthenticated && !authStore.user) {
    try {
      await authStore.getProfile();
    } catch (error) {
      console.error('获取用户资料失败:', error);
      // 如果获取用户资料失败，清除认证状态
      authStore.logout();
      if (to.meta.requiresAuth || to.meta.requiresAdmin) {
        return next({ name: 'Login', query: { redirect: to.fullPath } });
      }
    }
  }

  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    if (!isAuthenticated) {
      // 未登录，跳转到登录页
      return next({ name: 'Login', query: { redirect: to.fullPath } });
    }
  }

  // 检查是否需要管理员权限
  if (to.meta.requiresAdmin) {
    if (!isAuthenticated) {
      // 未登录，跳转到登录页
      return next({ name: 'Login', query: { redirect: to.fullPath } });
    } else if (!authStore.isAdmin) {
      // 已登录但不是管理员，跳转到首页并显示错误信息
      console.warn('非管理员用户尝试访问管理页面:', to.path);
      return next({ name: 'Home' });
    }
  }

  // 访客页面（如登录页）已登录用户不应访问
  if (to.meta.guest && isAuthenticated) {
    return next({ name: 'Home' });
  }

  next();
});

// 全局后置守卫 - 处理路由错误
router.afterEach((to, from, failure) => {
  if (failure) {
    console.error('路由跳转失败:', failure);
    // 可以在这里添加错误处理逻辑
  }
});

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error);
  // 可以在这里添加全局错误处理
});

export default router;
