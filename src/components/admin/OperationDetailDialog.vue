<template>
  <el-dialog
    v-model="dialogVisible"
    title="操作详情"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="operation-detail" v-if="operation">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="fas fa-info-circle"></i>
          基本信息
        </h3>
        <div class="detail-grid">
          <div class="detail-item">
            <label>操作ID:</label>
            <span class="operation-id">{{ operation.id }}</span>
          </div>
          <div class="detail-item">
            <label>操作时间:</label>
            <span>{{ formatDateTime(operation.timestamp) }}</span>
          </div>
          <div class="detail-item">
            <label>操作用户:</label>
            <span class="user-info">
              <i class="fas fa-user"></i>
              {{ operation.userName || '未知用户' }}
            </span>
          </div>
          <div class="detail-item">
            <label>用户ID:</label>
            <span>{{ operation.userId || '未知' }}</span>
          </div>
          <div class="detail-item">
            <label>操作类型:</label>
            <el-tag :type="getOperationTypeColor(operation.type)">
              {{ getOperationTypeText(operation.type) }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>操作状态:</label>
            <el-tag :type="getStatusColor(operation.status)">
              {{ getStatusText(operation.status) }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 商品信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="fas fa-box"></i>
          商品信息
        </h3>
        <div class="detail-grid">
          <div class="detail-item">
            <label>商品名称:</label>
            <span class="product-name">
              <i class="fas fa-tag"></i>
              {{ operation.productName || '未知商品' }}
            </span>
          </div>
          <div class="detail-item">
            <label>商品ID:</label>
            <span>{{ operation.productId }}</span>
          </div>
          <div class="detail-item" v-if="operation.productCount">
            <label>商品数量:</label>
            <span class="product-count">{{ operation.productCount }} 个</span>
          </div>
          <div class="detail-item full-width" v-if="operation.productNames">
            <label>涉及商品:</label>
            <span class="product-names">{{ operation.productNames }}</span>
          </div>
        </div>
      </div>

      <!-- 职场信息 -->
      <div class="detail-section" v-if="operation.workplaceId || operation.fromWorkplaceId || operation.toWorkplaceId">
        <h3 class="section-title">
          <i class="fas fa-building"></i>
          职场信息
        </h3>
        <div class="detail-grid">
          <div class="detail-item" v-if="operation.workplaceId">
            <label>操作职场:</label>
            <span class="workplace-info">
              <i class="fas fa-map-marker-alt"></i>
              {{ operation.workplaceName || '未知职场' }}
            </span>
          </div>
          <div class="detail-item" v-if="operation.fromWorkplaceId">
            <label>源职场:</label>
            <span class="workplace-info">
              <i class="fas fa-arrow-right"></i>
              {{ getWorkplaceName(operation.fromWorkplaceId) }}
            </span>
          </div>
          <div class="detail-item" v-if="operation.toWorkplaceId">
            <label>目标职场:</label>
            <span class="workplace-info">
              <i class="fas fa-bullseye"></i>
              {{ getWorkplaceName(operation.toWorkplaceId) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 库存变化 -->
      <div class="detail-section" v-if="hasStockChange">
        <h3 class="section-title">
          <i class="fas fa-exchange-alt"></i>
          库存变化
        </h3>
        <div class="stock-change-detail">
          <div class="stock-item" v-if="operation.oldStock !== null && operation.newStock !== null">
            <div class="stock-label">库存变化:</div>
            <div class="stock-values">
              <span class="old-value">{{ operation.oldStock }}</span>
              <i class="fas fa-arrow-right"></i>
              <span class="new-value" :class="getStockChangeClass(operation.oldStock, operation.newStock)">
                {{ operation.newStock }}
              </span>
              <span class="change-amount" :class="getStockChangeClass(operation.oldStock, operation.newStock)">
                ({{ operation.newStock - operation.oldStock > 0 ? '+' : '' }}{{ operation.newStock - operation.oldStock }})
              </span>
            </div>
          </div>
          <div class="stock-item" v-if="operation.quantity">
            <div class="stock-label">变化数量:</div>
            <div class="stock-values">
              <span class="quantity-value" :class="operation.operation === 'add' ? 'increase' : 'decrease'">
                {{ operation.operation === 'add' ? '+' : '-' }}{{ operation.quantity }}
              </span>
            </div>
          </div>
          <div class="stock-item" v-if="operation.amount">
            <div class="stock-label">调整数量:</div>
            <div class="stock-values">
              <span class="amount-value">{{ operation.amount }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作原因 -->
      <div class="detail-section" v-if="operation.reason">
        <h3 class="section-title">
          <i class="fas fa-comment-alt"></i>
          操作原因
        </h3>
        <div class="reason-content">
          <p>{{ operation.reason }}</p>
        </div>
      </div>

      <!-- 技术信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="fas fa-cog"></i>
          技术信息
        </h3>
        <div class="detail-grid">
          <div class="detail-item">
            <label>IP地址:</label>
            <span>{{ operation.ipAddress || '未知' }}</span>
          </div>
          <div class="detail-item">
            <label>会话ID:</label>
            <span class="session-id">{{ operation.sessionId || '未知' }}</span>
          </div>
          <div class="detail-item full-width" v-if="operation.userAgent">
            <label>用户代理:</label>
            <span class="user-agent">{{ operation.userAgent }}</span>
          </div>
          <div class="detail-item" v-if="operation.errorMessage">
            <label>错误信息:</label>
            <span class="error-message">{{ operation.errorMessage }}</span>
          </div>
        </div>
      </div>

      <!-- 详细数据 -->
      <div class="detail-section" v-if="operation.details && Object.keys(operation.details).length > 0">
        <h3 class="section-title">
          <i class="fas fa-database"></i>
          详细数据
        </h3>
        <div class="details-content">
          <pre>{{ JSON.stringify(operation.details, null, 2) }}</pre>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="exportDetail">导出详情</el-button>
        <el-button type="info" @click="copyToClipboard">复制信息</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { format } from 'date-fns'

export default {
  name: 'OperationDetailDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    operation: {
      type: Object,
      default: null
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const dialogVisible = ref(false)

    // 计算属性
    const hasStockChange = computed(() => {
      return props.operation && (
        (props.operation.oldStock !== null && props.operation.newStock !== null) ||
        props.operation.quantity ||
        props.operation.amount
      )
    })

    // 操作类型映射
    const operationTypeMap = {
      stock_update: { text: '库存更新', color: 'primary' },
      stock_transfer: { text: '库存转移', color: 'info' },
      quick_adjust: { text: '快速调整', color: 'success' },
      batch_quick_adjust: { text: '批量调整', color: 'warning' },
      batch_edit: { text: '批量编辑', color: 'danger' }
    }

    const getOperationTypeText = (type) => {
      return operationTypeMap[type]?.text || type
    }

    const getOperationTypeColor = (type) => {
      return operationTypeMap[type]?.color || 'info'
    }

    const getStatusText = (status) => {
      const statusMap = {
        completed: '成功',
        failed: '失败',
        processing: '进行中'
      }
      return statusMap[status] || status
    }

    const getStatusColor = (status) => {
      const colorMap = {
        completed: 'success',
        failed: 'danger',
        processing: 'warning'
      }
      return colorMap[status] || 'info'
    }

    const getStockChangeClass = (oldStock, newStock) => {
      if (newStock > oldStock) return 'increase'
      if (newStock < oldStock) return 'decrease'
      return 'no-change'
    }

    const getWorkplaceName = (workplaceId) => {
      // 这里应该从职场列表中查找名称
      return `职场${workplaceId}`
    }

    const formatDateTime = (dateTime) => {
      if (!dateTime) return ''
      try {
        return format(new Date(dateTime), 'yyyy-MM-dd HH:mm:ss')
      } catch (error) {
        return dateTime
      }
    }

    // 方法
    const handleClose = () => {
      emit('update:modelValue', false)
    }

    const exportDetail = () => {
      try {
        const detailText = [
          '操作详情报告',
          '=' * 50,
          `操作ID: ${props.operation.id}`,
          `操作时间: ${formatDateTime(props.operation.timestamp)}`,
          `操作用户: ${props.operation.userName || '未知用户'}`,
          `操作类型: ${getOperationTypeText(props.operation.type)}`,
          `商品名称: ${props.operation.productName || '未知商品'}`,
          `商品ID: ${props.operation.productId}`,
          `职场: ${props.operation.workplaceName || '全局'}`,
          `操作原因: ${props.operation.reason || '无'}`,
          `IP地址: ${props.operation.ipAddress || '未知'}`,
          `会话ID: ${props.operation.sessionId || '未知'}`,
          '',
          '详细数据:',
          JSON.stringify(props.operation.details || {}, null, 2)
        ].join('\n')

        const blob = new Blob([detailText], { type: 'text/plain;charset=utf-8;' })
        const link = document.createElement('a')
        const url = URL.createObjectURL(blob)
        link.setAttribute('href', url)
        link.setAttribute('download', `操作详情_${props.operation.id}.txt`)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        ElMessage.success('详情导出成功')
      } catch (error) {
        console.error('导出详情失败:', error)
        ElMessage.error('导出详情失败')
      }
    }

    const copyToClipboard = async () => {
      try {
        const text = `操作ID: ${props.operation.id}\n操作时间: ${formatDateTime(props.operation.timestamp)}\n操作用户: ${props.operation.userName || '未知用户'}\n操作类型: ${getOperationTypeText(props.operation.type)}\n商品: ${props.operation.productName || '未知商品'}`
        
        await navigator.clipboard.writeText(text)
        ElMessage.success('信息已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        ElMessage.error('复制失败')
      }
    }

    // 监听器
    watch(() => props.modelValue, (newVal) => {
      dialogVisible.value = newVal
    })

    watch(dialogVisible, (newVal) => {
      if (!newVal) {
        emit('update:modelValue', false)
      }
    })

    return {
      dialogVisible,
      hasStockChange,
      getOperationTypeText,
      getOperationTypeColor,
      getStatusText,
      getStatusColor,
      getStockChangeClass,
      getWorkplaceName,
      formatDateTime,
      handleClose,
      exportDetail,
      copyToClipboard
    }
  }
}
</script>

<style scoped>
.operation-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.section-title i {
  color: #409eff;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
  flex-direction: column;
  align-items: flex-start;
}

.detail-item label {
  font-weight: 600;
  color: #606266;
  min-width: 100px;
  font-size: 14px;
}

.detail-item span {
  color: #303133;
  flex: 1;
}

.operation-id {
  font-family: monospace;
  background: #ecf5ff;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.user-info, .workplace-info, .product-name {
  display: flex;
  align-items: center;
  gap: 6px;
}

.user-info i, .workplace-info i, .product-name i {
  color: #409eff;
}

.session-id {
  font-family: monospace;
  font-size: 12px;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
}

.user-agent {
  font-size: 12px;
  color: #909399;
  word-break: break-all;
}

.error-message {
  color: #f56c6c;
  font-weight: 500;
}

.stock-change-detail {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stock-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.stock-label {
  font-weight: 600;
  color: #606266;
  min-width: 100px;
}

.stock-values {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
}

.old-value {
  color: #909399;
}

.new-value.increase, .quantity-value.increase, .change-amount.increase {
  color: #67c23a;
}

.new-value.decrease, .quantity-value.decrease, .change-amount.decrease {
  color: #f56c6c;
}

.new-value.no-change {
  color: #409eff;
}

.change-amount {
  font-size: 14px;
}

.reason-content {
  background: white;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.reason-content p {
  margin: 0;
  line-height: 1.6;
  color: #303133;
}

.details-content {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.details-content pre {
  margin: 0;
  padding: 16px;
  font-size: 12px;
  color: #606266;
  background: #f8f9fa;
  overflow-x: auto;
}

@media (max-width: 768px) {
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .stock-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .stock-label {
    min-width: auto;
  }
}
</style>
