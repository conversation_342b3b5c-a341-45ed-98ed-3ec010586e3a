<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>职场库存管理系统 - 功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-header {
            border-bottom: 2px solid #409eff;
            padding-bottom: 16px;
            margin-bottom: 20px;
        }
        .test-title {
            color: #303133;
            margin: 0 0 8px 0;
            font-size: 24px;
            font-weight: 600;
        }
        .test-description {
            color: #606266;
            margin: 0;
            font-size: 14px;
        }
        .test-section {
            margin-bottom: 24px;
        }
        .section-title {
            color: #303133;
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 12px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .test-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            margin-bottom: 8px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #e4e7ed;
        }
        .test-item.success {
            border-left-color: #67c23a;
            background: #f0f9ff;
        }
        .test-item.error {
            border-left-color: #f56c6c;
            background: #fef0f0;
        }
        .test-item.pending {
            border-left-color: #e6a23c;
            background: #fdf6ec;
        }
        .test-name {
            flex: 1;
            color: #303133;
            font-weight: 500;
        }
        .test-status {
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-success {
            background: #67c23a;
            color: white;
        }
        .status-error {
            background: #f56c6c;
            color: white;
        }
        .status-pending {
            background: #e6a23c;
            color: white;
        }
        .test-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-left: 8px;
        }
        .test-button:hover {
            background: #337ecc;
        }
        .test-button:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }
        .test-result {
            margin-top: 8px;
            padding: 8px 12px;
            background: #f0f2f5;
            border-radius: 4px;
            font-size: 12px;
            color: #606266;
            display: none;
        }
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }
        .quick-link {
            display: block;
            padding: 16px;
            background: linear-gradient(135deg, #409eff, #67c23a);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
            transition: transform 0.2s;
        }
        .quick-link:hover {
            transform: translateY(-2px);
            color: white;
        }
        .icon {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">
                <span class="icon">🧪</span>
                职场库存管理系统 - 功能测试
            </h1>
            <p class="test-description">
                验证职场库存管理系统的前端界面和API接口功能
            </p>
        </div>

        <!-- 环境状态检查 -->
        <div class="test-section">
            <h2 class="section-title">
                <span class="icon">🔧</span>
                环境状态检查
            </h2>
            <div class="test-item" id="frontend-status">
                <span class="test-name">前端服务状态 (http://localhost:5174)</span>
                <span class="test-status status-pending">检查中</span>
                <button class="test-button" onclick="checkFrontendStatus()">检查</button>
            </div>
            <div class="test-item" id="backend-status">
                <span class="test-name">后端API状态 (http://localhost:3000)</span>
                <span class="test-status status-pending">检查中</span>
                <button class="test-button" onclick="checkBackendStatus()">检查</button>
            </div>
            <div class="test-item" id="api-categories">
                <span class="test-name">分类API测试 (/api/categories)</span>
                <span class="test-status status-pending">待测试</span>
                <button class="test-button" onclick="testCategoriesAPI()">测试</button>
            </div>
            <div class="test-item" id="api-workplace-stocks">
                <span class="test-name">职场库存API测试 (/api/test-products/3/workplace-stocks)</span>
                <span class="test-status status-pending">待测试</span>
                <button class="test-button" onclick="testWorkplaceStocksAPI()">测试</button>
            </div>
        </div>

        <!-- 前端页面测试 -->
        <div class="test-section">
            <h2 class="section-title">
                <span class="icon">🖥️</span>
                前端页面测试
            </h2>
            <div class="test-item">
                <span class="test-name">用户主页加载</span>
                <span class="test-status status-pending">手动测试</span>
                <button class="test-button" onclick="openPage('http://localhost:5174')">打开</button>
            </div>
            <div class="test-item">
                <span class="test-name">管理员登录页面</span>
                <span class="test-status status-pending">手动测试</span>
                <button class="test-button" onclick="openPage('http://localhost:5174/admin')">打开</button>
            </div>
            <div class="test-item">
                <span class="test-name">库存管理页面</span>
                <span class="test-status status-pending">需要登录</span>
                <button class="test-button" onclick="openPage('http://localhost:5174/admin/stock-management')">打开</button>
            </div>
            <div class="test-item">
                <span class="test-name">库存统计仪表板</span>
                <span class="test-status status-pending">需要登录</span>
                <button class="test-button" onclick="openPage('http://localhost:5174/admin/stock-dashboard')">打开</button>
            </div>
        </div>

        <!-- 功能组件测试 -->
        <div class="test-section">
            <h2 class="section-title">
                <span class="icon">🧩</span>
                功能组件测试指南
            </h2>
            <div class="test-item">
                <span class="test-name">库存详情面板 - 展开/收起功能</span>
                <span class="test-status status-pending">手动测试</span>
            </div>
            <div class="test-item">
                <span class="test-name">库存编辑对话框 - 批量编辑功能</span>
                <span class="test-status status-pending">手动测试</span>
            </div>
            <div class="test-item">
                <span class="test-name">库存转移对话框 - 转移预览功能</span>
                <span class="test-status status-pending">手动测试</span>
            </div>
            <div class="test-item">
                <span class="test-name">操作日志对话框 - 筛选和分页</span>
                <span class="test-status status-pending">手动测试</span>
            </div>
        </div>

        <!-- 快速链接 -->
        <div class="quick-links">
            <a href="http://localhost:5174" target="_blank" class="quick-link">
                <span class="icon">🏠</span>
                用户主页
            </a>
            <a href="http://localhost:5174/admin" target="_blank" class="quick-link">
                <span class="icon">👨‍💼</span>
                管理员登录
            </a>
            <a href="http://localhost:5174/admin/stock-management" target="_blank" class="quick-link">
                <span class="icon">📦</span>
                库存管理
            </a>
            <a href="http://localhost:5174/admin/stock-dashboard" target="_blank" class="quick-link">
                <span class="icon">📊</span>
                库存统计
            </a>
        </div>
    </div>

    <script>
        // 检查前端服务状态
        async function checkFrontendStatus() {
            const item = document.getElementById('frontend-status');
            const status = item.querySelector('.test-status');
            
            try {
                const response = await fetch('http://localhost:5174');
                if (response.ok) {
                    updateTestStatus(item, 'success', '✅ 正常运行');
                } else {
                    updateTestStatus(item, 'error', '❌ 响应异常');
                }
            } catch (error) {
                updateTestStatus(item, 'error', '❌ 连接失败');
            }
        }

        // 检查后端API状态
        async function checkBackendStatus() {
            const item = document.getElementById('backend-status');
            const status = item.querySelector('.test-status');
            
            try {
                const response = await fetch('http://localhost:3000/api/categories');
                if (response.ok) {
                    updateTestStatus(item, 'success', '✅ 正常运行');
                } else {
                    updateTestStatus(item, 'error', '❌ API异常');
                }
            } catch (error) {
                updateTestStatus(item, 'error', '❌ 连接失败');
            }
        }

        // 测试分类API
        async function testCategoriesAPI() {
            const item = document.getElementById('api-categories');
            
            try {
                const response = await fetch('http://localhost:3000/api/categories');
                const data = await response.json();
                
                if (response.ok && Array.isArray(data)) {
                    updateTestStatus(item, 'success', `✅ 成功 (${data.length}个分类)`);
                } else {
                    updateTestStatus(item, 'error', '❌ 数据格式错误');
                }
            } catch (error) {
                updateTestStatus(item, 'error', '❌ 请求失败');
            }
        }

        // 测试职场库存API
        async function testWorkplaceStocksAPI() {
            const item = document.getElementById('api-workplace-stocks');
            
            try {
                const response = await fetch('http://localhost:3000/api/test-products/3/workplace-stocks');
                const data = await response.json();
                
                if (response.ok && data.success && data.data.workplaceStocks) {
                    const count = data.data.workplaceStocks.length;
                    updateTestStatus(item, 'success', `✅ 成功 (${count}个职场)`);
                } else {
                    updateTestStatus(item, 'error', '❌ 数据格式错误');
                }
            } catch (error) {
                updateTestStatus(item, 'error', '❌ 请求失败');
            }
        }

        // 更新测试状态
        function updateTestStatus(item, type, text) {
            const status = item.querySelector('.test-status');
            status.className = `test-status status-${type}`;
            status.textContent = text;
            
            item.className = `test-item ${type}`;
        }

        // 打开页面
        function openPage(url) {
            window.open(url, '_blank');
        }

        // 页面加载时自动检查环境状态
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkFrontendStatus();
                checkBackendStatus();
            }, 1000);
        });
    </script>
</body>
</html>
