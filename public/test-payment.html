<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>支付码测试页面</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .container {
      max-width: 800px;
      width: 100%;
      margin: 0 auto;
    }
    h1 {
      color: #333;
      text-align: center;
    }
    .test-section {
      background: #f5f5f5;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
    }
    .test-title {
      font-weight: bold;
      margin-bottom: 10px;
    }
    .image-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      justify-content: center;
    }
    .image-card {
      border: 1px solid #ddd;
      padding: 10px;
      border-radius: 4px;
      background: white;
      text-align: center;
      max-width: 300px;
    }
    .image-card img {
      max-width: 100%;
      height: auto;
      margin-bottom: 10px;
    }
    .image-url {
      word-break: break-all;
      font-size: 12px;
      color: #666;
      margin-top: 5px;
    }
    .success {
      color: green;
    }
    .failure {
      color: red;
    }
    button {
      background: #4CAF50;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      margin-top: 10px;
    }
    button:hover {
      background: #45a049;
    }
    .env-info {
      background: #e8f5e9;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 20px;
      width: 100%;
    }
    .env-title {
      font-weight: bold;
      margin-bottom: 5px;
    }
    .env-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }
    .env-label {
      font-weight: 500;
    }
    .env-value {
      font-family: monospace;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>支付码测试页面</h1>
    
    <div class="env-info" id="env-info">
      <div class="env-title">环境信息</div>
      <div id="env-data">加载中...</div>
    </div>
    
    <div class="test-section">
      <div class="test-title">默认支付码图片测试</div>
      <div class="image-container">
        <div class="image-card">
          <div>默认图片 (相对路径)</div>
          <img src="images/payment/pay.png" alt="默认支付码" id="default-image">
          <div class="image-url">images/payment/pay.png</div>
          <div id="default-status"></div>
        </div>
        
        <div class="image-card">
          <div>默认图片 (绝对路径)</div>
          <img src="/images/payment/pay.png" alt="默认支付码" id="default-absolute-image">
          <div class="image-url">/images/payment/pay.png</div>
          <div id="default-absolute-status"></div>
        </div>
      </div>
    </div>
    
    <div class="test-section">
      <div class="test-title">动态获取支付码测试</div>
      <div id="dynamic-test-container" class="image-container">
        <div class="image-card">
          <div>API获取的支付码</div>
          <div id="dynamic-image-container">
            <img src="images/payment/pay.png" alt="加载中..." id="dynamic-image">
          </div>
          <div class="image-url" id="dynamic-url">加载中...</div>
          <div id="dynamic-status">加载中...</div>
          <button id="refresh-button">刷新支付码</button>
        </div>
      </div>
    </div>
    
    <div class="test-section">
      <div class="test-title">服务器路径测试</div>
      <div class="image-container">
        <div class="image-card">
          <div>服务器支付码路径</div>
          <img src="http://localhost:3000/uploads/payment/qrcode_2e161dc4-7505-4655-975c-d0425aeece34.png" alt="服务器支付码" id="server-image">
          <div class="image-url">http://localhost:3000/uploads/payment/qrcode_2e161dc4-7505-4655-975c-d0425aeece34.png</div>
          <div id="server-status"></div>
        </div>
        
        <div class="image-card">
          <div>API路径支付码</div>
          <img src="http://localhost:3000/api/uploads/payment/qrcode_2e161dc4-7505-4655-975c-d0425aeece34.png" alt="API支付码" id="api-image">
          <div class="image-url">http://localhost:3000/api/uploads/payment/qrcode_2e161dc4-7505-4655-975c-d0425aeece34.png</div>
          <div id="api-status"></div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 检查图片加载状态
    function checkImageStatus(imageId, statusId) {
      const img = document.getElementById(imageId);
      const statusElement = document.getElementById(statusId);
      
      img.onload = function() {
        statusElement.textContent = `✅ 加载成功 (${img.naturalWidth}x${img.naturalHeight})`;
        statusElement.className = 'success';
      };
      
      img.onerror = function() {
        statusElement.textContent = '❌ 加载失败';
        statusElement.className = 'failure';
      };
    }
    
    // 获取环境信息
    async function getEnvironmentInfo() {
      try {
        const response = await fetch('http://localhost:3000/api/env-check');
        const data = await response.json();
        
        let html = '';
        for (const [key, value] of Object.entries(data)) {
          html += `
            <div class="env-item">
              <span class="env-label">${key}:</span>
              <span class="env-value">${value}</span>
            </div>
          `;
        }
        
        document.getElementById('env-data').innerHTML = html;
      } catch (error) {
        document.getElementById('env-data').innerHTML = `<div class="failure">无法获取环境信息: ${error.message}</div>`;
      }
    }
    
    // 从API获取支付码
    async function fetchPaymentQRCode() {
      const dynamicStatus = document.getElementById('dynamic-status');
      const dynamicUrl = document.getElementById('dynamic-url');
      const dynamicImage = document.getElementById('dynamic-image');
      
      try {
        dynamicStatus.textContent = '加载中...';
        dynamicStatus.className = '';
        
        const response = await fetch('http://localhost:3000/api/system/payment-qrcode');
        const data = await response.json();
        
        console.log('API响应:', data);
        
        if (data && data.qrcodeUrl) {
          // 添加时间戳避免缓存
          const timestamp = new Date().getTime();
          const url = data.qrcodeUrl.includes('?') 
            ? `${data.qrcodeUrl}&t=${timestamp}` 
            : `${data.qrcodeUrl}?t=${timestamp}`;
            
          // 修正URL，确保使用本地地址
          let fixedUrl = url;
          if (url.includes('**************')) {
            fixedUrl = url.replace(/http:\/\/47\.122\.122\.245(?::\d+)?/g, 'http://localhost:3000');
            console.log('检测到生产URL，已修正为本地URL');
          }
            
          dynamicUrl.textContent = fixedUrl;
          dynamicImage.src = fixedUrl;
          
          dynamicImage.onload = function() {
            dynamicStatus.textContent = `✅ 加载成功 (${dynamicImage.naturalWidth}x${dynamicImage.naturalHeight})`;
            dynamicStatus.className = 'success';
          };
          
          dynamicImage.onerror = function() {
            dynamicStatus.textContent = '❌ 加载失败';
            dynamicStatus.className = 'failure';
            dynamicImage.src = 'images/payment/pay.png';
          };
        } else {
          dynamicStatus.textContent = '❌ API未返回有效URL';
          dynamicStatus.className = 'failure';
          dynamicUrl.textContent = '无效URL';
          dynamicImage.src = 'images/payment/pay.png';
        }
      } catch (error) {
        console.error('获取支付码失败:', error);
        dynamicStatus.textContent = `❌ 获取失败: ${error.message || '未知错误'}`;
        dynamicStatus.className = 'failure';
        dynamicUrl.textContent = '获取失败';
        dynamicImage.src = 'images/payment/pay.png';
      }
    }
    
    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 获取环境信息
      getEnvironmentInfo();
      
      // 检查静态图片
      checkImageStatus('default-image', 'default-status');
      checkImageStatus('default-absolute-image', 'default-absolute-status');
      checkImageStatus('server-image', 'server-status');
      checkImageStatus('api-image', 'api-status');
      
      // 获取动态支付码
      fetchPaymentQRCode();
      
      // 刷新按钮事件
      document.getElementById('refresh-button').addEventListener('click', fetchPaymentQRCode);
    });
  </script>
</body>
</html> 