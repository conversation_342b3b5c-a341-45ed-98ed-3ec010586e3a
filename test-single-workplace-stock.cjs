/**
 * 单职场环境库存更新功能测试脚本
 * 验证在只有1个职场的情况下，库存更新功能是否正常工作
 */

const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'password',
  database: 'feishu_mall'
};

async function testSingleWorkplaceStock() {
  console.log('🧪 开始单职场环境库存更新功能测试...\n');

  let connection;
  try {
    // 连接数据库
    connection = await mysql.createConnection(dbConfig);

    // 1. 验证职场数量
    console.log('1️⃣ 验证职场数量...');
    const [workplaces] = await connection.execute('SELECT COUNT(*) as count FROM workplaces');
    const workplaceCount = workplaces[0].count;
    console.log(`✅ 职场数量: ${workplaceCount}`);
    
    if (workplaceCount !== 1) {
      console.log('❌ 测试环境不正确，应该只有1个职场');
      return;
    }

    // 2. 验证商品库存管理模式
    console.log('\n2️⃣ 验证商品库存管理模式...');
    const [modes] = await connection.execute(
      'SELECT stockManagementType, COUNT(*) as count FROM products GROUP BY stockManagementType'
    );
    
    modes.forEach(mode => {
      console.log(`   ${mode.stockManagementType}: ${mode.count} 个商品`);
    });

    const singleModeCount = modes.find(m => m.stockManagementType === 'single')?.count || 0;
    const workplaceModeCount = modes.find(m => m.stockManagementType === 'workplace')?.count || 0;

    if (singleModeCount > 0) {
      console.log('✅ 存在单一库存模式商品，符合单职场环境');
    }

    // 3. 测试库存数据一致性
    console.log('\n3️⃣ 测试库存数据一致性...');
    const [stockCheck] = await connection.execute(`
      SELECT 
        p.id,
        p.name,
        p.stock as product_stock,
        p.stockManagementType,
        COALESCE(pws.stock, 0) as workplace_stock,
        CASE 
          WHEN p.stock = COALESCE(pws.stock, 0) THEN 'OK'
          ELSE 'ERROR'
        END as status
      FROM products p
      LEFT JOIN product_workplace_stocks pws ON p.id = pws.productId AND pws.workplaceId = 1
      WHERE p.stock != COALESCE(pws.stock, 0)
      LIMIT 5
    `);

    if (stockCheck.length === 0) {
      console.log('✅ 所有商品的库存数据一致');
    } else {
      console.log('❌ 发现库存不一致的商品:');
      stockCheck.forEach(item => {
        console.log(`   商品 ${item.id}: 总库存=${item.product_stock}, 职场库存=${item.workplace_stock}`);
      });
    }

    // 4. 模拟库存更新测试
    console.log('\n4️⃣ 模拟库存更新测试...');
    
    // 选择一个商品进行测试
    const [testProducts] = await connection.execute(
      'SELECT id, name, stock FROM products WHERE stockManagementType = "single" LIMIT 1'
    );

    if (testProducts.length > 0) {
      const testProduct = testProducts[0];
      const originalStock = testProduct.stock;
      const newStock = originalStock + 5;

      console.log(`测试商品: ${testProduct.name} (ID: ${testProduct.id})`);
      console.log(`原始库存: ${originalStock}`);

      // 更新商品库存
      await connection.execute(
        'UPDATE products SET stock = ? WHERE id = ?',
        [newStock, testProduct.id]
      );

      // 同步到职场库存
      await connection.execute(
        'UPDATE product_workplace_stocks SET stock = ? WHERE productId = ? AND workplaceId = 1',
        [newStock, testProduct.id]
      );

      // 验证更新结果
      const [updatedProduct] = await connection.execute(
        `SELECT 
          p.stock as product_stock,
          pws.stock as workplace_stock
        FROM products p
        LEFT JOIN product_workplace_stocks pws ON p.id = pws.productId AND pws.workplaceId = 1
        WHERE p.id = ?`,
        [testProduct.id]
      );

      const result = updatedProduct[0];
      if (result.product_stock === newStock && result.workplace_stock === newStock) {
        console.log(`✅ 库存更新成功: ${originalStock} → ${newStock}`);
        console.log(`✅ 总库存和职场库存保持同步`);
      } else {
        console.log(`❌ 库存更新失败: 总库存=${result.product_stock}, 职场库存=${result.workplace_stock}`);
      }

      // 恢复原始库存
      await connection.execute(
        'UPDATE products SET stock = ? WHERE id = ?',
        [originalStock, testProduct.id]
      );
      await connection.execute(
        'UPDATE product_workplace_stocks SET stock = ? WHERE productId = ? AND workplaceId = 1',
        [originalStock, testProduct.id]
      );

      console.log(`✅ 已恢复原始库存: ${originalStock}`);
    }

    // 5. 总结测试结果
    console.log('\n📊 测试结果总结:');
    console.log(`   职场数量: ${workplaceCount} (单职场环境)`);
    console.log(`   单一库存模式商品: ${singleModeCount} 个`);
    console.log(`   职场分配模式商品: ${workplaceModeCount} 个`);
    console.log(`   库存数据一致性: ${stockCheck.length === 0 ? '正常' : '存在问题'}`);
    
    if (workplaceCount === 1 && singleModeCount > 0 && stockCheck.length === 0) {
      console.log('\n🎉 单职场环境库存管理功能测试通过！');
      console.log('   ✅ 批量库存更新应该可以正常工作');
      console.log('   ✅ 单个商品编辑库存应该可以正常工作');
      console.log('   ✅ 总库存和职场库存保持同步');
    } else {
      console.log('\n⚠️ 测试发现问题，需要进一步检查');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行测试
if (require.main === module) {
  testSingleWorkplaceStock();
}

module.exports = { testSingleWorkplaceStock };
