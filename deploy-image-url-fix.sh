#!/bin/bash

# 图片URL修复部署脚本
# 用于将修复后的代码部署到生产环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 生产服务器配置
PROD_SERVER="47.122.122.245"
PROD_USER="root"
PROD_PASSWORD="Aa@123456"
DEPLOY_DIR="/www/wwwroot/workyy"

echo -e "${BLUE}====== 图片URL修复部署脚本 ======${NC}"
echo -e "${BLUE}目标服务器: ${PROD_SERVER}${NC}"
echo -e "${BLUE}部署目录: ${DEPLOY_DIR}${NC}"

# 检查本地修改
echo -e "\n${YELLOW}检查本地修改状态...${NC}"
if [ -n "$(git status --porcelain)" ]; then
    echo -e "${YELLOW}发现未提交的修改，正在提交...${NC}"
    git add .
    git commit -m "修复生产环境图片URL问题

- 后端: 在生产环境中强制使用正确的服务器地址生成图片URL
- 前端: 所有图片组件都使用fixImageUrl函数处理URL
- 环境检测: 增强URL修复逻辑，支持相对路径处理
- 组件更新: ProductDetailDialog、NewProductDialog、ImageGallery已修复

修复内容:
1. server/controllers/productImageController.js - 修复商品图片上传URL生成
2. server/controllers/uploadController.js - 修复通用图片上传URL生成  
3. server/routes/announcements.js - 修复公告图片上传URL生成
4. src/components/ProductDetailDialog.vue - 添加fixImageUrl处理
5. src/components/NewProductDialog.vue - 添加fixImageUrl处理
6. src/components/ImageGallery.vue - 添加fixImageUrl处理
7. src/utils/environmentDetector.js - 增强URL修复逻辑"
else
    echo -e "${GREEN}本地代码已是最新状态${NC}"
fi

# 推送到远程仓库
echo -e "\n${YELLOW}推送代码到远程仓库...${NC}"
git push origin feat/reset

# 连接到生产服务器并部署
echo -e "\n${YELLOW}连接到生产服务器进行部署...${NC}"

sshpass -p "${PROD_PASSWORD}" ssh -o StrictHostKeyChecking=no ${PROD_USER}@${PROD_SERVER} << 'ENDSSH'
set -e

echo "====== 开始生产环境部署 ======"

# 进入部署目录
cd /www/wwwroot/workyy

# 备份当前版本
echo "备份当前版本..."
if [ -d "backup-$(date +%Y%m%d)" ]; then
    rm -rf "backup-$(date +%Y%m%d)"
fi
cp -r . "backup-$(date +%Y%m%d)" || echo "备份失败，继续部署..."

# 拉取最新代码
echo "拉取最新代码..."
git fetch origin
git reset --hard origin/feat/reset

# 检查关键文件是否存在
echo "检查修复文件..."
if [ -f "server/controllers/productImageController.js" ]; then
    echo "✅ productImageController.js 存在"
else
    echo "❌ productImageController.js 不存在"
    exit 1
fi

if [ -f "src/components/ProductDetailDialog.vue" ]; then
    echo "✅ ProductDetailDialog.vue 存在"
else
    echo "❌ ProductDetailDialog.vue 不存在"
    exit 1
fi

# 安装依赖（如果需要）
echo "检查并安装依赖..."
cd server
if [ -f "package.json" ]; then
    npm install --production
fi

# 重启服务
echo "重启后端服务..."
pm2 restart feishu-mall-api || pm2 start server.js --name feishu-mall-api

# 等待服务启动
echo "等待服务启动..."
sleep 5

# 检查服务状态
echo "检查服务状态..."
if pm2 list | grep -q "feishu-mall-api.*online"; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败"
    pm2 logs feishu-mall-api --lines 20
    exit 1
fi

# 构建前端（如果需要）
echo "构建前端..."
cd /www/wwwroot/workyy
if [ -f "package.json" ]; then
    npm run build
fi

echo "====== 部署完成 ======"
ENDSSH

# 验证部署结果
echo -e "\n${YELLOW}验证部署结果...${NC}"

# 测试API健康状态
echo -e "${BLUE}测试API健康状态...${NC}"
if curl -s "http://${PROD_SERVER}:3000/api/health" > /dev/null; then
    echo -e "${GREEN}✅ API服务正常${NC}"
else
    echo -e "${RED}❌ API服务异常${NC}"
fi

# 测试环境检查接口
echo -e "${BLUE}测试环境配置...${NC}"
ENV_CHECK=$(curl -s "http://${PROD_SERVER}:3000/api/env-check" 2>/dev/null || echo "")
if echo "$ENV_CHECK" | grep -q "production"; then
    echo -e "${GREEN}✅ 生产环境配置正确${NC}"
else
    echo -e "${YELLOW}⚠️  环境配置可能需要检查${NC}"
fi

echo -e "\n${GREEN}====== 部署完成 ======${NC}"
echo -e "${GREEN}图片URL修复已部署到生产环境${NC}"
echo -e "${BLUE}请在浏览器中访问 http://${PROD_SERVER} 验证修复效果${NC}"

echo -e "\n${YELLOW}验证步骤:${NC}"
echo -e "1. 打开商品列表页面，检查商品图片是否正常显示"
echo -e "2. 点击商品详情，检查详情页图片是否正常显示"
echo -e "3. 上传新的商品图片，检查生成的URL是否为生产地址"
echo -e "4. 检查浏览器开发者工具，确认图片URL不包含localhost"

# 清理测试文件
echo -e "\n${YELLOW}清理测试文件...${NC}"
if [ -f "test-image-url-fix.js" ]; then
    rm test-image-url-fix.js
    echo -e "${GREEN}✅ 测试文件已清理${NC}"
fi
