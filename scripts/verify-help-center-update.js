#!/usr/bin/env node

/**
 * 帮助中心更新验证脚本
 * 用于验证帮助中心内容是否已根据项目实际功能正确更新
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 开始验证帮助中心更新...\n');

// 检查项目列表
const checks = [
  {
    name: '检查系统介绍更新',
    check: () => {
      const filePath = path.join(__dirname, '../src/views/admin/help/SystemIntro.vue');
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查是否包含最新版本信息
      const hasLatestVersion = content.includes('v1.4.1');
      
      // 检查是否包含新功能描述
      const hasNewFeatures = content.includes('错误边界处理') && 
                             content.includes('智能发送时间控制') &&
                             content.includes('高级诊断工具');
      
      // 检查是否有高亮框
      const hasHighlightBox = content.includes('highlight-box');
      
      return hasLatestVersion && hasNewFeatures && hasHighlightBox;
    }
  },
  {
    name: '检查FAQ新增问题',
    check: () => {
      const filePath = path.join(__dirname, '../src/views/admin/help/FAQ.vue');
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查是否添加了系统设置相关问题
      const hasSystemSettingsFAQ = content.includes('系统设置页面出现空白怎么办？');
      
      // 检查是否添加了高级诊断工具问题
      const hasDiagnosticFAQ = content.includes('高级诊断工具如何使用？');
      
      // 检查是否添加了智能发送时间控制问题
      const hasScheduleFAQ = content.includes('智能发送时间控制如何配置？');
      
      return hasSystemSettingsFAQ && hasDiagnosticFAQ && hasScheduleFAQ;
    }
  },
  {
    name: '检查系统更新记录',
    check: () => {
      const filePath = path.join(__dirname, '../src/views/admin/help/SystemUpdates.vue');
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查是否添加了v1.4.1版本记录
      const hasV141 = content.includes('版本 1.4.1');
      
      // 检查是否包含修复内容
      const hasFixContent = content.includes('修复管理后台系统设置模块前端渲染问题') &&
                           content.includes('onUnmounted钩子嵌套错误');
      
      // 检查是否包含功能增强内容
      const hasEnhancement = content.includes('ErrorBoundary错误边界包装') &&
                             content.includes('路由错误处理机制');
      
      return hasV141 && hasFixContent && hasEnhancement;
    }
  },
  {
    name: '检查故障排查指南文件',
    check: () => {
      const filePath = path.join(__dirname, '../src/views/admin/help/TroubleShooting.vue');
      return fs.existsSync(filePath);
    }
  },
  {
    name: '检查故障排查指南内容',
    check: () => {
      const filePath = path.join(__dirname, '../src/views/admin/help/TroubleShooting.vue');
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查是否包含前端页面问题
      const hasFrontendIssues = content.includes('前端页面问题') &&
                               content.includes('页面显示空白或加载失败');
      
      // 检查是否包含系统设置问题
      const hasSystemSettingsIssues = content.includes('系统设置页面功能异常') &&
                                     content.includes('点击高级诊断工具后页面变空白');
      
      // 检查是否包含飞书集成问题
      const hasFeishuIssues = content.includes('飞书集成问题') &&
                             content.includes('飞书群收不到推送消息');
      
      return hasFrontendIssues && hasSystemSettingsIssues && hasFeishuIssues;
    }
  },
  {
    name: '检查帮助中心菜单配置',
    check: () => {
      const filePath = path.join(__dirname, '../src/views/admin/HelpCenter.vue');
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查是否添加了故障排查菜单项
      const hasTroubleshootingMenu = content.includes('troubleshooting') &&
                                    content.includes('故障排查');
      
      // 检查是否导入了TroubleShooting组件
      const hasTroubleshootingImport = content.includes('import TroubleShooting from \'./help/TroubleShooting.vue\'');
      
      // 检查是否在componentMap中添加了映射
      const hasComponentMapping = content.includes('\'troubleshooting\': TroubleShooting');
      
      // 检查是否导入了Tools图标
      const hasToolsIcon = content.includes('Tools') && content.includes('@element-plus/icons-vue');
      
      return hasTroubleshootingMenu && hasTroubleshootingImport && hasComponentMapping && hasToolsIcon;
    }
  }
];

// 执行检查
let passedChecks = 0;
let totalChecks = checks.length;

checks.forEach((check, index) => {
  try {
    const result = check.check();
    if (result) {
      console.log(`✅ ${check.name}`);
      passedChecks++;
    } else {
      console.log(`❌ ${check.name}`);
    }
  } catch (error) {
    console.log(`❌ ${check.name} - 检查失败: ${error.message}`);
  }
});

console.log(`\n📊 检查结果: ${passedChecks}/${totalChecks} 项通过`);

if (passedChecks === totalChecks) {
  console.log('🎉 帮助中心更新验证通过！');
  console.log('\n📋 更新总结:');
  console.log('1. ✅ 系统介绍已更新，突出最新功能和稳定性改进');
  console.log('2. ✅ FAQ新增了系统设置、高级诊断工具等相关问题');
  console.log('3. ✅ 系统更新记录已同步v1.4.1版本信息');
  console.log('4. ✅ 新增故障排查指南，提供详细的问题诊断步骤');
  console.log('5. ✅ 帮助中心菜单已更新，添加故障排查入口');
  
  console.log('\n🔧 建议测试步骤:');
  console.log('1. 启动项目: npm run dev');
  console.log('2. 登录管理后台');
  console.log('3. 进入帮助中心页面');
  console.log('4. 检查各个菜单项是否正常显示');
  console.log('5. 验证故障排查指南内容是否完整');
  console.log('6. 确认系统介绍和FAQ内容已更新');
  
  process.exit(0);
} else {
  console.log('⚠️  部分更新项目未通过验证，请检查相关文件');
  process.exit(1);
}
