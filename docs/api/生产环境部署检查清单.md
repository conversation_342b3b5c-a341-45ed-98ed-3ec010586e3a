# 生产环境部署检查清单

## 🔧 配置文件检查

### 1. 环境变量配置 (.env.production)
- ✅ **服务器配置**
  - `PORT=3000` - 服务端口
  - `NODE_ENV=production` - 生产环境
  - `SERVER_URL=http://**************` - 服务器地址

- ✅ **数据库配置**
  - `DB_NAME=feishu_mall` - 数据库名称
  - `DB_USER=root` - 数据库用户
  - `DB_PASSWORD=password` - ⚠️ **请确认实际数据库密码**
  - `DB_HOST=localhost` - 数据库主机
  - `DB_PORT=3306` - 数据库端口

- ✅ **JWT配置**
  - `JWT_SECRET` - 已配置安全密钥
  - `JWT_EXPIRES_IN=1d` - Token过期时间
  - `JWT_LONG_EXPIRES_IN=30d` - 长期Token过期时间

- ✅ **飞书配置**
  - `FEISHU_APP_ID` - 飞书应用ID
  - `FEISHU_APP_SECRET` - 飞书应用密钥
  - `FEISHU_REDIRECT_URI` - 飞书回调地址
  - `FEISHU_BOT_WEBHOOK_URL` - 飞书群机器人Webhook地址
  - `FRONTEND_URL` - 前端系统地址

### 2. 路由配置
- ✅ **已添加系统路由** - `/api/system` 路径下的扩展功能接口

## 🚀 新功能验证

### 1. 定时报告服务
- ✅ **服务启动** - `scheduledReportService.start()` 已配置
- ✅ **节假日识别** - 2024-2025年节假日配置完整
- ✅ **报告类型** - 每日、每周、月度报告

### 2. 系统事件通知
- ✅ **维护通知** - 支持4种维护类型
- ✅ **错误告警** - 4级严重程度分级
- ✅ **频率控制** - 智能防止告警风暴

### 3. 运营数据推送
- ✅ **飞书集成** - 新用户欢迎、销售里程碑等
- ✅ **异常预警** - 大额订单、频繁下单检测
- ✅ **反馈通知** - 重要反馈实时推送

## 📋 部署前验证步骤

### 1. 数据库检查
```bash
# 连接数据库验证
mysql -u root -p -h localhost feishu_mall

# 检查表结构是否完整
SHOW TABLES;
```

### 2. 依赖安装
```bash
cd server
npm install --production
```

### 3. 环境变量加载
```bash
# 确保使用生产环境配置
cp .env.production .env

# 验证环境变量
node -e "require('dotenv').config(); console.log('NODE_ENV:', process.env.NODE_ENV);"
```

### 4. 服务启动测试
```bash
# 启动服务
npm start

# 检查服务状态
curl http://**************:3000/api/system/holiday-status
```

## ⚠️ 重要注意事项

### 1. 安全配置
- [ ] **数据库密码** - 请确认并更新实际的数据库密码
- [ ] **JWT密钥** - 生产环境使用独立的安全密钥
- [ ] **文件上传** - 检查上传目录权限 (755)

### 2. 性能优化
- ✅ **压缩中间件** - 已启用响应压缩
- ✅ **CORS配置** - 已配置生产域名
- ✅ **静态文件** - 已配置静态资源服务

### 3. 监控配置
- [ ] **PM2进程管理** - 建议使用PM2管理Node.js进程
- [ ] **日志监控** - 配置生产环境日志收集
- [ ] **服务监控** - 配置健康检查和告警

## 🧪 功能测试接口

部署后可使用以下接口验证新功能：

### 手动测试接口 (需要管理员权限)
```bash
# 测试月度报告
POST /api/system/trigger-monthly-report

# 测试系统维护通知  
POST /api/system/trigger-maintenance-notification

# 测试错误告警
POST /api/system/trigger-error-alert

# 测试节假日识别
GET /api/system/holiday-status?date=2024-10-01

# 测试工作日统计
GET /api/system/working-days?year=2024&month=10
```

## 📦 PM2 部署配置

创建 `ecosystem.config.js` 文件：

```javascript
module.exports = {
  apps: [{
    name: 'feishu-mall-server',
    script: 'server.js',
    instances: 1,
    exec_mode: 'cluster',
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    log_file: './logs/combined.log',
    out_file: './logs/out.log',
    error_file: './logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm Z',
    restart_delay: 1000,
    max_restarts: 5,
    min_uptime: '10s'
  }]
};
```

## ✅ 部署成功验证

部署完成后，确认以下功能正常：

1. **基础功能** - 用户登录、商品兑换等
2. **飞书集成** - 新用户注册、兑换申请通知
3. **定时报告** - 查看服务状态接口返回正常
4. **新增接口** - 系统管理接口可正常访问
5. **错误告警** - 可手动触发测试告警

---

**部署前必须检查**: 数据库密码、飞书配置、前端域名设置 