#!/bin/bash

# =====================================================
# 多职场库存分配功能 - 数据库迁移执行脚本
# 创建时间: 2025-07-28
# 版本: v1.0.0
# =====================================================

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 数据库连接配置
DB_HOST="localhost"
DB_USER="root"
DB_PASSWORD="password"
DB_NAME="feishu_mall"

# 迁移脚本目录
MIGRATION_DIR="$(dirname "$0")"
LOG_FILE="$MIGRATION_DIR/migration_$(date +%Y%m%d_%H%M%S).log"

# 日志函数
log() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

# 错误处理函数
handle_error() {
    log "${RED}❌ 错误: $1${NC}"
    log "${YELLOW}⚠️  迁移过程中出现错误，请检查日志文件: $LOG_FILE${NC}"
    exit 1
}

# 检查数据库连接
check_database_connection() {
    log "${BLUE}🔍 检查数据库连接...${NC}"
    mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" "$DB_NAME" >/dev/null 2>&1
    if [ $? -ne 0 ]; then
        handle_error "无法连接到数据库 $DB_NAME"
    fi
    log "${GREEN}✅ 数据库连接正常${NC}"
}

# 执行SQL文件
execute_sql_file() {
    local sql_file="$1"
    local description="$2"
    
    log "${BLUE}📄 执行: $description${NC}"
    log "   文件: $sql_file"
    
    if [ ! -f "$sql_file" ]; then
        handle_error "SQL文件不存在: $sql_file"
    fi
    
    mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$sql_file" 2>&1 | tee -a "$LOG_FILE"
    
    if [ ${PIPESTATUS[0]} -ne 0 ]; then
        handle_error "执行SQL文件失败: $sql_file"
    fi
    
    log "${GREEN}✅ $description 完成${NC}"
    log ""
}

# 创建备份
create_backup() {
    log "${BLUE}💾 创建迁移前备份...${NC}"
    local backup_file="../../backups/feishu_mall_pre_workplace_stocks_$(date +%Y%m%d_%H%M%S).sql"
    
    mysqldump -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASSWORD" --single-transaction --routines --triggers "$DB_NAME" > "$backup_file" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        log "${GREEN}✅ 备份创建成功: $backup_file${NC}"
    else
        handle_error "备份创建失败"
    fi
    log ""
}

# 验证迁移结果
verify_migration() {
    log "${BLUE}🔍 验证迁移结果...${NC}"
    
    # 检查新表是否创建成功
    local table_count=$(mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASSWORD" -e "
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_schema = '$DB_NAME' 
        AND table_name IN ('product_workplace_stocks', 'stock_operation_logs');" 2>/dev/null | tail -1)
    
    if [ "$table_count" != "2" ]; then
        handle_error "新表创建验证失败，期望2个表，实际创建了 $table_count 个表"
    fi
    
    # 检查exchanges表字段是否添加成功
    local column_count=$(mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASSWORD" -e "
        SELECT COUNT(*) 
        FROM information_schema.columns 
        WHERE table_schema = '$DB_NAME' 
        AND table_name = 'exchanges' 
        AND column_name IN ('workplaceId', 'stockSnapshot');" 2>/dev/null | tail -1)
    
    if [ "$column_count" != "2" ]; then
        handle_error "exchanges表字段添加验证失败，期望2个字段，实际添加了 $column_count 个字段"
    fi
    
    # 检查数据迁移结果
    local stock_records=$(mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASSWORD" -e "
        SELECT COUNT(*) FROM product_workplace_stocks;" "$DB_NAME" 2>/dev/null | tail -1)
    
    log "${GREEN}✅ 迁移验证完成${NC}"
    log "   - 新表创建: 2个表"
    log "   - 字段添加: 2个字段"
    log "   - 库存记录: $stock_records 条"
    log ""
}

# 主执行流程
main() {
    log "${BLUE}🚀 开始执行多职场库存分配功能数据库迁移${NC}"
    log "开始时间: $(date)"
    log "日志文件: $LOG_FILE"
    log ""
    
    # 1. 检查数据库连接
    check_database_connection
    
    # 2. 创建备份
    create_backup
    
    # 3. 执行迁移脚本
    execute_sql_file "$MIGRATION_DIR/20250728_001_create_product_workplace_stocks.sql" "创建商品职场库存表"
    execute_sql_file "$MIGRATION_DIR/20250728_002_create_stock_operation_logs.sql" "创建库存操作日志表"
    execute_sql_file "$MIGRATION_DIR/20250728_003_extend_exchanges_table.sql" "扩展兑换表字段"
    execute_sql_file "$MIGRATION_DIR/20250728_004_migrate_existing_stock_data_fixed.sql" "迁移现有库存数据"
    
    # 4. 验证迁移结果
    verify_migration
    
    log "${GREEN}🎉 多职场库存分配功能数据库迁移完成！${NC}"
    log "完成时间: $(date)"
    log ""
    log "${YELLOW}📋 下一步操作:${NC}"
    log "1. 检查迁移日志确认无错误"
    log "2. 验证数据完整性"
    log "3. 开始第二阶段：后端API接口开发"
}

# 执行主流程
main "$@"
