const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const HotProductConfig = sequelize.define('HotProductConfig', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  timeRange: {
    type: DataTypes.STRING(20),
    allowNull: false,
    unique: true,
    validate: {
      isIn: [['all', '30d', '7d', '1d']]
    },
    comment: '时间维度：all/30d/7d/1d'
  },
  enabled: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '是否启用该时间维度'
  },
  maxCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 10,
    validate: {
      min: 1,
      max: 50
    },
    comment: '该时间维度的热门商品数量上限'
  },
  minExchangeCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    validate: {
      min: 0
    },
    comment: '最小兑换量要求'
  },
  exchangeWeight: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: false,
    defaultValue: 1.0,
    validate: {
      min: 0,
      max: 10
    },
    comment: '兑换量权重'
  },
  stockWeight: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: false,
    defaultValue: 0.1,
    validate: {
      min: 0,
      max: 10
    },
    comment: '库存权重'
  },
  autoUpdateEnabled: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '是否自动更新'
  },
  updateFrequency: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: '0 */1 * * *',
    comment: '更新频率(cron表达式)'
  }
}, {
  tableName: 'hot_product_configs',
  timestamps: true,
  indexes: [
    {
      unique: true,
      fields: ['timeRange']
    }
  ]
});

// 静态方法：获取时间维度的中文名称
HotProductConfig.getTimeRangeLabel = function(timeRange) {
  const labels = {
    'all': '累积热门',
    '30d': '30天热门',
    '7d': '7天热门',
    '1d': '今日热门'
  };
  return labels[timeRange] || timeRange;
};

// 静态方法：获取所有支持的时间维度
HotProductConfig.getSupportedTimeRanges = function() {
  return [
    { value: 'all', label: '累积热门', description: '基于全部累积兑换量', icon: 'Trophy' },
    { value: '30d', label: '30天热门', description: '基于近30天兑换量', icon: 'Calendar' },
    { value: '7d', label: '7天热门', description: '基于近7天兑换量', icon: 'Clock' },
    { value: '1d', label: '今日热门', description: '基于当天兑换量', icon: 'Sunny' }
  ];
};

// 实例方法：验证cron表达式
HotProductConfig.prototype.validateCronExpression = function() {
  const cron = require('node-cron');
  return cron.validate(this.updateFrequency);
};

// 实例方法：获取时间范围的SQL条件
HotProductConfig.prototype.getTimeRangeCondition = function() {
  const now = new Date();

  switch (this.timeRange) {
    case 'all':
      return null; // 不限制时间，使用全部数据
    case '30d':
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      return { createdAt: { [require('sequelize').Op.gte]: thirtyDaysAgo } };
    case '7d':
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      return { createdAt: { [require('sequelize').Op.gte]: sevenDaysAgo } };
    case '1d':
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      return {
        createdAt: {
          [require('sequelize').Op.between]: [today, tomorrow]
        }
      };
    default:
      return null;
  }
};

module.exports = HotProductConfig;
