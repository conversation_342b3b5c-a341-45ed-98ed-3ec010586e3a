#!/bin/bash

# ====================================
# 数据库恢复脚本
# ====================================
# 此脚本用于从备份文件恢复数据库
# 使用方法：./scripts/restore_database.sh [备份文件路径]

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_PASSWORD="password"
DB_NAME="feishu_mall"

echo -e "${BLUE}=====================================${NC}"
echo -e "${BLUE}     数据库恢复脚本启动${NC}"
echo -e "${BLUE}=====================================${NC}"

# 检查参数
if [ $# -eq 0 ]; then
    echo -e "${YELLOW}请选择要恢复的备份文件:${NC}"
    echo ""
    
    # 显示可用的备份文件
    if [ -d "database_backups" ] && [ "$(ls -A database_backups/*.sql 2>/dev/null)" ]; then
        echo -e "${BLUE}可用的备份文件:${NC}"
        ls -lht database_backups/*.sql | nl
        echo ""
        echo -e "${YELLOW}使用方法: $0 <备份文件路径>${NC}"
        echo -e "${YELLOW}例如: $0 database_backups/feishu_mall_backup_20250723_170609.sql${NC}"
    else
        echo -e "${RED}未找到备份文件！${NC}"
        echo -e "${YELLOW}请先运行备份脚本: ./scripts/backup_database.sh${NC}"
    fi
    exit 1
fi

BACKUP_FILE="$1"

# 检查备份文件是否存在
if [ ! -f "$BACKUP_FILE" ]; then
    echo -e "${RED}错误: 备份文件不存在: $BACKUP_FILE${NC}"
    exit 1
fi

# 检查备份文件是否为空
if [ ! -s "$BACKUP_FILE" ]; then
    echo -e "${RED}错误: 备份文件为空: $BACKUP_FILE${NC}"
    exit 1
fi

echo -e "${YELLOW}准备恢复数据库...${NC}"
echo -e "${YELLOW}备份文件: $BACKUP_FILE${NC}"
echo -e "${YELLOW}目标数据库: $DB_NAME${NC}"

# 获取文件信息
FILE_SIZE=$(ls -lh "$BACKUP_FILE" | awk '{print $5}')
LINE_COUNT=$(wc -l < "$BACKUP_FILE")
echo -e "${BLUE}文件大小: $FILE_SIZE${NC}"
echo -e "${BLUE}行数: $LINE_COUNT${NC}"

# 确认操作
echo ""
echo -e "${RED}警告: 此操作将覆盖现有数据库！${NC}"
echo -e "${YELLOW}请确认是否继续? (y/N): ${NC}"
read -r CONFIRM

if [[ ! "$CONFIRM" =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}操作已取消${NC}"
    exit 0
fi

# 创建恢复前备份
echo -e "${YELLOW}创建恢复前备份...${NC}"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_BEFORE_RESTORE="database_backups/before_restore_${TIMESTAMP}.sql"

mysqldump -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" > "$BACKUP_BEFORE_RESTORE" 2>/dev/null

if [ $? -eq 0 ]; then
    echo -e "${GREEN}恢复前备份已创建: $BACKUP_BEFORE_RESTORE${NC}"
else
    echo -e "${YELLOW}警告: 无法创建恢复前备份（可能数据库不存在）${NC}"
fi

# 执行恢复
echo -e "${YELLOW}开始恢复数据库...${NC}"

mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$BACKUP_FILE"

# 检查恢复是否成功
if [ $? -eq 0 ]; then
    echo -e "${GREEN}=====================================${NC}"
    echo -e "${GREEN}     数据库恢复成功！${NC}"
    echo -e "${GREEN}=====================================${NC}"
    echo -e "${GREEN}恢复文件: $BACKUP_FILE${NC}"
    echo -e "${GREEN}目标数据库: $DB_NAME${NC}"
    echo -e "${GREEN}恢复时间: $(date)${NC}"
    
    if [ -f "$BACKUP_BEFORE_RESTORE" ]; then
        echo -e "${BLUE}恢复前备份: $BACKUP_BEFORE_RESTORE${NC}"
    fi
    
else
    echo -e "${RED}=====================================${NC}"
    echo -e "${RED}     数据库恢复失败！${NC}"
    echo -e "${RED}=====================================${NC}"
    echo -e "${RED}请检查数据库连接和备份文件格式${NC}"
    
    if [ -f "$BACKUP_BEFORE_RESTORE" ]; then
        echo -e "${YELLOW}如需回滚，可使用: $0 $BACKUP_BEFORE_RESTORE${NC}"
    fi
    
    exit 1
fi

echo -e "\n${BLUE}恢复脚本执行完成${NC}"
