const { HotProductConfig, HotProductHistory, Product } = require('../models');
const hotProductService = require('../services/hotProductService');

/**
 * 获取所有热门商品配置
 */
exports.getConfigs = async (req, res) => {
  try {
    const configs = await HotProductConfig.findAll({
      order: [
        ['timeRange', 'ASC']
      ]
    });

    // 添加时间维度的详细信息
    const configsWithDetails = configs.map(config => {
      const timeRanges = HotProductConfig.getSupportedTimeRanges();
      const timeRangeInfo = timeRanges.find(tr => tr.value === config.timeRange);
      
      return {
        ...config.toJSON(),
        timeRangeLabel: HotProductConfig.getTimeRangeLabel(config.timeRange),
        timeRangeInfo
      };
    });

    res.json(configsWithDetails);
  } catch (error) {
    console.error('获取热门商品配置失败:', error);
    res.status(500).json({ message: '获取配置失败', error: error.message });
  }
};

/**
 * 更新热门商品配置
 */
exports.updateConfigs = async (req, res) => {
  try {
    const { configs } = req.body;

    if (!Array.isArray(configs)) {
      return res.status(400).json({ message: '配置数据格式错误' });
    }

    const updatePromises = configs.map(async (configData) => {
      const { timeRange, ...updateData } = configData;
      
      // 验证cron表达式
      if (updateData.updateFrequency) {
        const cron = require('node-cron');
        if (!cron.validate(updateData.updateFrequency)) {
          throw new Error(`时间维度 ${timeRange} 的更新频率格式错误`);
        }
      }

      return await HotProductConfig.update(updateData, {
        where: { timeRange }
      });
    });

    await Promise.all(updatePromises);

    res.json({ 
      message: '配置更新成功',
      updatedCount: configs.length 
    });
  } catch (error) {
    console.error('更新热门商品配置失败:', error);
    res.status(500).json({ message: '更新配置失败', error: error.message });
  }
};

/**
 * 手动触发热门商品更新
 */
exports.triggerUpdate = async (req, res) => {
  try {
    const { timeRange } = req.body;

    let results;
    if (timeRange && timeRange !== 'all') {
      // 更新指定时间维度
      results = [await hotProductService.updateHotProducts(timeRange)];
    } else {
      // 更新所有时间维度
      results = await hotProductService.updateAllTimeRanges();
    }

    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;

    res.json({
      message: `热门商品更新完成，成功 ${successCount}/${totalCount} 个时间维度`,
      results
    });
  } catch (error) {
    console.error('手动触发热门商品更新失败:', error);
    res.status(500).json({ message: '更新失败', error: error.message });
  }
};

/**
 * 获取热门商品历史记录
 */
exports.getHistory = async (req, res) => {
  try {
    const { 
      timeRange, 
      productId, 
      page = 1, 
      limit = 20,
      startDate,
      endDate 
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {};

    if (timeRange) {
      whereClause.timeRange = timeRange;
    }

    if (productId) {
      whereClause.productId = productId;
    }

    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) {
        whereClause.createdAt[require('sequelize').Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereClause.createdAt[require('sequelize').Op.lte] = new Date(endDate);
      }
    }

    const { count, rows } = await HotProductHistory.findAndCountAll({
      where: whereClause,
      include: [{
        model: Product,
        attributes: ['id', 'name', 'lyPrice', 'rmbPrice', 'stock', 'status']
      }],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset
    });

    res.json({
      history: rows,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('获取热门商品历史失败:', error);
    res.status(500).json({ message: '获取历史记录失败', error: error.message });
  }
};

/**
 * 获取指定时间维度的热门商品
 */
exports.getHotProducts = async (req, res) => {
  try {
    const { timeRange = 'all', limit = 10 } = req.query;

    const hotProducts = await hotProductService.getHotProductsByTimeRange(
      timeRange, 
      parseInt(limit)
    );

    res.json({
      timeRange,
      timeRangeLabel: HotProductConfig.getTimeRangeLabel(timeRange),
      products: hotProducts
    });
  } catch (error) {
    console.error('获取热门商品失败:', error);
    res.status(500).json({ message: '获取热门商品失败', error: error.message });
  }
};

/**
 * 获取所有时间维度的热门商品
 */
exports.getAllHotProducts = async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    const timeRanges = ['all', '30d', '7d', '1d'];
    
    const results = {};
    
    for (const timeRange of timeRanges) {
      const products = await hotProductService.getHotProductsByTimeRange(
        timeRange, 
        parseInt(limit)
      );
      
      results[timeRange] = {
        timeRangeLabel: HotProductConfig.getTimeRangeLabel(timeRange),
        products
      };
    }

    res.json(results);
  } catch (error) {
    console.error('获取所有热门商品失败:', error);
    res.status(500).json({ message: '获取所有热门商品失败', error: error.message });
  }
};

/**
 * 获取热门商品统计信息
 */
exports.getStats = async (req, res) => {
  try {
    const stats = {};
    const timeRanges = ['all', '30d', '7d', '1d'];

    for (const timeRange of timeRanges) {
      const config = await HotProductConfig.findOne({ where: { timeRange } });
      const hotProductCount = await Product.count({
        where: {
          isAutoHot: true,
          hotTimeRange: timeRange,
          status: 'active'
        }
      });

      stats[timeRange] = {
        timeRangeLabel: HotProductConfig.getTimeRangeLabel(timeRange),
        enabled: config ? config.enabled : false,
        maxCount: config ? config.maxCount : 0,
        currentCount: hotProductCount,
        lastUpdate: config ? config.updatedAt : null
      };
    }

    res.json(stats);
  } catch (error) {
    console.error('获取热门商品统计失败:', error);
    res.status(500).json({ message: '获取统计信息失败', error: error.message });
  }
};

/**
 * 清理历史记录
 */
exports.cleanupHistory = async (req, res) => {
  try {
    const { daysToKeep = 90 } = req.body;
    
    const deletedCount = await hotProductService.cleanupHistory(parseInt(daysToKeep));
    
    res.json({
      message: `清理完成，删除了 ${deletedCount} 条历史记录`,
      deletedCount
    });
  } catch (error) {
    console.error('清理历史记录失败:', error);
    res.status(500).json({ message: '清理失败', error: error.message });
  }
};
