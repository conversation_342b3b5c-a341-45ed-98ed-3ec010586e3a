# ====================================
# 前端本地开发环境配置文件 (.env)
# ====================================
# 此文件用于前端本地开发环境，请勿在生产环境使用
# 生产环境请使用 .env.production 文件
# 对应后端配置文件：server/.env.production
# 运行环境：development（开发）/ test（测试）/ production（生产）
# ====================================
# 移动设备测试说明：
# 当需要在手机等移动设备上测试时，请：
# 1. 将本机IP地址（如**************）替换配置中的示例IP
# 2. 注释掉localhost相关配置，启用对应的IP地址配置
# 3. 确保手机和电脑在同一局域网内
# 4. 启动前端开发服务器时使用：npm run dev -- --host 0.0.0.0
# ====================================
# 前端开发服务器配置
# ====================================
# 前端API基础URL（指向本地后端服务）
VITE_API_URL=http://localhost:3000/api
# 移动设备测试配置：当需要在手机等移动设备上测试时，请注释掉上面的localhost配置，启用下面的IP地址配置
#VITE_API_URL=http://**************:3000/api

# 应用标题
VITE_APP_TITLE=光年小卖部

# 应用基础路径
VITE_APP_BASE_URL=/

# ====================================
# 开发环境特定配置
# ====================================
# 运行环境标识
VITE_NODE_ENV=development

# 是否启用开发工具
VITE_DEV_TOOLS=true

# 是否显示调试信息
VITE_DEBUG=true

# API请求超时时间（毫秒）
VITE_API_TIMEOUT=10000

# ====================================
# 后端服务配置（用于前端调用）
# ====================================
# 后端服务器地址
VITE_SERVER_URL=http://localhost:3000
# 移动设备测试配置：启用IP地址访问
#VITE_SERVER_URL=http://**************:3000

# WebSocket连接地址（如果需要）
VITE_WS_URL=ws://localhost:3000
# 移动设备测试配置：启用IP地址访问
#VITE_WS_URL=ws://**************:3000

# ====================================
# 飞书相关配置（前端使用）
# ====================================
# 飞书应用ID（前端可能需要）
VITE_FEISHU_APP_ID=cli_a66b3b2dcab8d013

# 飞书登录回调地址（前端路由）
VITE_FEISHU_CALLBACK_URL=http://localhost:5173/feishu/callback
# 移动设备测试配置：启用IP地址访问（注意：需要同时修改前端开发服务器的host配置）
#VITE_FEISHU_CALLBACK_URL=http://**************:5173/feishu/callback

# ====================================
# 环境检测配置（前端使用）
# ====================================
# 本地开发环境URL配置
VITE_DEV_SERVER_URL=http://localhost:3000
VITE_DEV_API_URL=http://localhost:3000/api
# 移动设备测试配置：启用IP地址访问
#VITE_DEV_SERVER_URL=http://**************:3000
#VITE_DEV_API_URL=http://**************:3000/api

# 生产环境URL配置（用于环境检测和URL修正）
VITE_PROD_SERVER_URL=https://store-api.chongyangqisi.com
VITE_PROD_API_URL=https://store-api.chongyangqisi.com/api
VITE_PROD_FRONTEND_URL=https://store.chongyangqisi.com

# 旧服务器IP（用于兼容性检测）
VITE_OLD_SERVER_IP=**************
