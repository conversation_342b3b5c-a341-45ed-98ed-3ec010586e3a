# 活动促销功能业务逻辑设计

## 1. 服务层架构

### 1.1 核心服务类

```
server/services/
├── promotionService.js              # 活动管理服务
├── promotionRuleEngine.js           # 促销规则引擎
├── promotionCalculatorService.js    # 优惠计算服务
├── promotionStatisticsService.js    # 统计分析服务
└── promotionNotificationService.js  # 活动通知服务
```

### 1.2 控制器设计

```
server/controllers/
└── promotionController.js           # 活动控制器
```

### 1.3 中间件

```
server/middlewares/
├── promotionValidation.js           # 活动数据验证
└── promotionAuth.js                 # 活动权限验证
```

## 2. 核心业务逻辑

### 2.1 活动管理服务 (promotionService.js)

```javascript
class PromotionService {
  /**
   * 创建活动
   */
  async createPromotion(promotionData, createdBy) {
    // 1. 验证活动数据
    await this.validatePromotionData(promotionData);

    // 2. 检查时间冲突
    await this.checkTimeConflict(promotionData);

    // 3. 创建活动记录
    const promotion = await Promotion.create({
      ...promotionData,
      created_by: createdBy,
      status: 'draft'
    });

    // 4. 处理商品关联
    if (promotionData.applicable_products) {
      await this.addPromotionProducts(promotion.id, promotionData.applicable_products);
    }

    // 5. 记录操作日志
    await this.logPromotionAction('CREATE', promotion, createdBy);

    return promotion;
  }

  /**
   * 更新活动状态
   */
  async updatePromotionStatus(promotionId, status, userId) {
    const promotion = await Promotion.findByPk(promotionId);
    if (!promotion) {
      throw new Error('活动不存在');
    }

    // 状态转换验证
    await this.validateStatusTransition(promotion.status, status);

    const oldStatus = promotion.status;
    promotion.status = status;

    // 特殊状态处理
    if (status === 'active') {
      await this.activatePromotion(promotion);
    } else if (status === 'ended') {
      await this.endPromotion(promotion);
    }

    await promotion.save();

    // 发送状态变更通知
    await this.notifyStatusChange(promotion, oldStatus, status);

    return promotion;
  }

  /**
   * 验证活动数据
   */
  async validatePromotionData(data) {
    // 时间验证
    if (new Date(data.start_time) >= new Date(data.end_time)) {
      throw new Error('结束时间必须晚于开始时间');
    }

    // 规则验证
    await this.validatePromotionRules(data.rules);

    // 商品验证
    if (data.applicable_products) {
      await this.validateApplicableProducts(data.applicable_products);
    }
  }

  /**
   * 检查时间冲突
   */
  async checkTimeConflict(promotionData) {
    const conflictingPromotions = await Promotion.findAll({
      where: {
        status: ['active', 'draft'],
        [Op.or]: [
          {
            start_time: {
              [Op.between]: [promotionData.start_time, promotionData.end_time]
            }
          },
          {
            end_time: {
              [Op.between]: [promotionData.start_time, promotionData.end_time]
            }
          }
        ]
      }
    });

    if (conflictingPromotions.length > 0) {
      // 检查是否有商品重叠
      const hasProductConflict = await this.checkProductConflict(
        conflictingPromotions,
        promotionData.applicable_products
      );

      if (hasProductConflict) {
        throw new Error('存在时间和商品重叠的活动');
      }
    }
  }
}
```

### 2.2 促销规则引擎 (promotionRuleEngine.js)

```javascript
class PromotionRuleEngine {
  /**
   * 计算优惠金额
   */
  async calculateDiscount(promotion, orderData, userId) {
    // 1. 检查活动有效性
    if (!await this.isPromotionValid(promotion)) {
      return { eligible: false, reason: '活动无效或已过期' };
    }

    // 2. 检查用户参与资格
    if (!await this.checkUserEligibility(promotion, userId)) {
      return { eligible: false, reason: '用户不符合参与条件' };
    }

    // 3. 检查商品适用性
    if (!await this.checkProductEligibility(promotion, orderData.products)) {
      return { eligible: false, reason: '商品不在活动范围内' };
    }

    // 4. 根据规则类型计算优惠
    const calculator = this.getCalculator(promotion.type);
    const result = await calculator.calculate(promotion.rules, orderData);

    return {
      eligible: true,
      ...result
    };
  }

  /**
   * 获取计算器
   */
  getCalculator(type) {
    const calculators = {
      'discount': new DiscountCalculator(),
      'full_reduction': new FullReductionCalculator(),
      'limited_time': new LimitedTimeCalculator(),
      'bundle': new BundleCalculator(),
      'points_multiplier': new PointsMultiplierCalculator()
    };

    return calculators[type];
  }

  /**
   * 检查活动有效性
   */
  async isPromotionValid(promotion) {
    const now = new Date();
    return promotion.status === 'active' &&
           new Date(promotion.start_time) <= now &&
           new Date(promotion.end_time) >= now;
  }

  /**
   * 检查用户参与资格
   */
  async checkUserEligibility(promotion, userId) {
    // 检查参与次数限制
    const participationCount = await PromotionParticipation.count({
      where: {
        promotion_id: promotion.id,
        user_id: userId,
        status: 'completed'
      }
    });

    if (participationCount >= promotion.max_uses_per_user) {
      return false;
    }

    // 检查总参与人数限制
    if (promotion.max_participants) {
      if (promotion.current_participants >= promotion.max_participants) {
        return false;
      }
    }

    return true;
  }
}
```

### 2.3 优惠计算器实现

```javascript
// 折扣计算器
class DiscountCalculator {
  async calculate(rules, orderData) {
    const { config } = rules;
    const originalAmount = this.calculateOriginalAmount(orderData.products);

    // 检查最小金额要求
    if (config.min_amount && originalAmount < config.min_amount) {
      return {
        original_amount: originalAmount,
        discount_amount: 0,
        final_amount: originalAmount,
        reason: `订单金额需满${config.min_amount}元`
      };
    }

    let discountAmount = 0;

    if (config.discount_type === 'percentage') {
      discountAmount = originalAmount * (config.discount_value / 100);
    } else if (config.discount_type === 'fixed_amount') {
      discountAmount = config.discount_value;
    }

    // 应用最大优惠限制
    if (config.max_discount && discountAmount > config.max_discount) {
      discountAmount = config.max_discount;
    }

    return {
      original_amount: originalAmount,
      discount_amount: discountAmount,
      final_amount: originalAmount - discountAmount,
      discount_details: {
        type: config.discount_type,
        value: config.discount_value,
        description: this.getDiscountDescription(config)
      }
    };
  }

  getDiscountDescription(config) {
    if (config.discount_type === 'percentage') {
      return `${config.discount_value}折优惠`;
    } else {
      return `减${config.discount_value}元`;
    }
  }
}

// 满减计算器
class FullReductionCalculator {
  async calculate(rules, orderData) {
    const { config } = rules;
    const originalAmount = this.calculateOriginalAmount(orderData.products);

    // 找到适用的满减档位
    const applicableTier = config.tiers
      .filter(tier => originalAmount >= tier.min_amount)
      .sort((a, b) => b.min_amount - a.min_amount)[0];

    if (!applicableTier) {
      return {
        original_amount: originalAmount,
        discount_amount: 0,
        final_amount: originalAmount,
        reason: '未达到满减条件'
      };
    }

    return {
      original_amount: originalAmount,
      discount_amount: applicableTier.reduction,
      final_amount: originalAmount - applicableTier.reduction,
      discount_details: {
        type: 'full_reduction',
        tier: applicableTier,
        description: `满${applicableTier.min_amount}减${applicableTier.reduction}`
      }
    };
  }
}
```

### 2.4 活动统计服务 (promotionStatisticsService.js)

```javascript
class PromotionStatisticsService {
  /**
   * 更新活动统计数据
   */
  async updatePromotionStats(promotionId, date = new Date()) {
    const statDate = date.toISOString().split('T')[0];

    // 获取当日数据
    const dailyData = await this.calculateDailyStats(promotionId, statDate);

    // 获取累计数据
    const totalData = await this.calculateTotalStats(promotionId, statDate);

    // 更新或创建统计记录
    await PromotionStatistics.upsert({
      promotion_id: promotionId,
      stat_date: statDate,
      ...dailyData,
      ...totalData
    });
  }

  /**
   * 计算当日统计数据
   */
  async calculateDailyStats(promotionId, date) {
    const startOfDay = new Date(`${date}T00:00:00Z`);
    const endOfDay = new Date(`${date}T23:59:59Z`);

    const [participants, orders, revenue, discount] = await Promise.all([
      // 当日参与人数
      PromotionParticipation.count({
        where: {
          promotion_id: promotionId,
          participation_time: { [Op.between]: [startOfDay, endOfDay] }
        },
        distinct: true,
        col: 'user_id'
      }),

      // 当日订单数
      PromotionParticipation.count({
        where: {
          promotion_id: promotionId,
          participation_time: { [Op.between]: [startOfDay, endOfDay] },
          status: 'completed'
        }
      }),

      // 当日收入
      PromotionParticipation.sum('final_amount', {
        where: {
          promotion_id: promotionId,
          participation_time: { [Op.between]: [startOfDay, endOfDay] },
          status: 'completed'
        }
      }),

      // 当日优惠金额
      PromotionParticipation.sum('discount_amount', {
        where: {
          promotion_id: promotionId,
          participation_time: { [Op.between]: [startOfDay, endOfDay] },
          status: 'completed'
        }
      })
    ]);

    return {
      daily_participants: participants || 0,
      daily_orders: orders || 0,
      daily_revenue: revenue || 0,
      daily_discount: discount || 0
    };
  }

  /**
   * 生成活动报表
   */
  async generatePromotionReport(promotionId, startDate, endDate) {
    const promotion = await Promotion.findByPk(promotionId);
    if (!promotion) {
      throw new Error('活动不存在');
    }

    // 获取统计数据
    const stats = await PromotionStatistics.findAll({
      where: {
        promotion_id: promotionId,
        stat_date: { [Op.between]: [startDate, endDate] }
      },
      order: [['stat_date', 'ASC']]
    });

    // 计算汇总数据
    const summary = this.calculateSummary(stats);

    // 获取热门商品
    const topProducts = await this.getTopProducts(promotionId, startDate, endDate);

    return {
      promotion,
      summary,
      daily_stats: stats,
      top_products: topProducts
    };
  }
}
```

## 3. 数据验证和安全

### 3.1 输入验证

```javascript
const promotionValidationRules = {
  name: {
    required: true,
    type: 'string',
    min: 2,
    max: 100
  },
  type: {
    required: true,
    enum: ['discount', 'full_reduction', 'limited_time', 'bundle', 'points_multiplier']
  },
  start_time: {
    required: true,
    type: 'date',
    custom: (value) => new Date(value) > new Date()
  },
  end_time: {
    required: true,
    type: 'date',
    custom: (value, data) => new Date(value) > new Date(data.start_time)
  }
};
```

### 3.2 权限控制

```javascript
// 活动权限中间件
const checkPromotionPermission = (action) => {
  return async (req, res, next) => {
    const { user } = req;
    const { id } = req.params;

    // 管理员拥有所有权限
    if (user.role === 'admin') {
      return next();
    }

    // 普通用户只能查看和参与活动
    if (['view', 'participate'].includes(action)) {
      return next();
    }

    return res.status(403).json({ message: '权限不足' });
  };
};
```

## 4. 系统集成方案

### 4.1 与现有模块集成

**商品管理模块集成**:
- 商品页面显示促销标签
- 商品列表支持促销筛选
- 商品价格显示原价和促销价

**订单处理模块集成**:
- 订单创建时自动应用促销优惠
- 订单详情显示促销信息
- 支持促销订单的特殊处理流程

**用户管理模块集成**:
- 用户中心显示参与的活动
- 用户权限控制活动参与
- 用户积分与促销活动关联

**通知系统集成**:
- 活动开始/结束通知
- 用户参与活动通知
- 活动效果报告推送

### 4.2 数据库扩展

**现有表扩展**:
```sql
-- 扩展exchanges表，添加促销相关字段
ALTER TABLE exchanges
ADD COLUMN promotion_id INT NULL COMMENT '关联的活动ID',
ADD COLUMN promotion_discount DECIMAL(10,2) DEFAULT 0.00 COMMENT '促销优惠金额',
ADD FOREIGN KEY (promotion_id) REFERENCES promotions(id);

-- 扩展products表，添加促销显示字段
ALTER TABLE products
ADD COLUMN has_promotion BOOLEAN DEFAULT FALSE COMMENT '是否有促销活动',
ADD COLUMN promotion_price DECIMAL(10,2) NULL COMMENT '促销价格';
```

### 4.3 定时任务集成

```javascript
// 活动状态自动更新任务
const promotionScheduler = {
  // 每分钟检查活动状态
  '* * * * *': async () => {
    await promotionService.updateExpiredPromotions();
    await promotionService.activateScheduledPromotions();
  },

  // 每小时更新统计数据
  '0 * * * *': async () => {
    await promotionStatisticsService.updateAllPromotionStats();
  },

  // 每日生成报告
  '0 9 * * *': async () => {
    await promotionReportService.generateDailyReports();
  }
};
```
