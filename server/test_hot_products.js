/**
 * 热门商品功能测试脚本
 */

const hotProductService = require('./services/hotProductService');

async function testHotProducts() {
  try {
    console.log('🚀 开始测试热门商品功能...\n');

    // 测试更新所有时间维度的热门商品
    console.log('📊 更新所有时间维度的热门商品...');
    const results = await hotProductService.updateAllTimeRanges();
    
    console.log('\n✅ 更新结果:');
    results.forEach(result => {
      if (result.success) {
        console.log(`  ✓ ${result.timeRange}: 成功更新 ${result.hotProductsCount} 个热门商品`);
        console.log(`    评估了 ${result.totalProductsEvaluated} 个商品`);
        if (result.hotProducts && result.hotProducts.length > 0) {
          console.log(`    前3名热门商品:`);
          result.hotProducts.slice(0, 3).forEach(product => {
            console.log(`      ${product.rank}. ${product.productName} (评分: ${product.hotScore}, 兑换量: ${product.exchangeCount})`);
          });
        }
      } else {
        console.log(`  ✗ ${result.timeRange}: ${result.message}`);
      }
      console.log('');
    });

    // 测试获取热门商品
    console.log('📋 获取各时间维度的热门商品...');
    const timeRanges = ['all', '30d', '7d', '1d'];
    
    for (const timeRange of timeRanges) {
      const hotProducts = await hotProductService.getHotProductsByTimeRange(timeRange, 5);
      console.log(`\n🔥 ${timeRange} 热门商品 (前5名):`);
      if (hotProducts.length > 0) {
        hotProducts.forEach((product, index) => {
          console.log(`  ${index + 1}. ${product.name} (排名: ${product.hotRank}, 评分: ${product.hotScore})`);
        });
      } else {
        console.log('  暂无热门商品');
      }
    }

    console.log('\n🎉 热门商品功能测试完成!');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    // 退出进程
    process.exit(0);
  }
}

// 运行测试
testHotProducts();
