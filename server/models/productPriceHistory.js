const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ProductPriceHistory = sequelize.define('ProductPriceHistory', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  productId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  oldLyPrice: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '变更前的光年币价格'
  },
  newLyPrice: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '变更后的光年币价格'
  },
  oldRmbPrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '变更前的人民币价格'
  },
  newRmbPrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '变更后的人民币价格'
  },
  changeReason: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '价格变更原因'
  },
  changedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '价格变更操作人'
  },
  effectiveDate: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '价格生效时间'
  }
}, {
  tableName: 'product_price_history',
  timestamps: true,
  indexes: [
    {
      fields: ['productId']
    },
    {
      fields: ['effectiveDate']
    },
    {
      fields: ['changedBy']
    }
  ]
});

module.exports = ProductPriceHistory;
