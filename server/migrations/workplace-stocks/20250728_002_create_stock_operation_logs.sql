-- =====================================================
-- 多职场库存分配功能 - 第一阶段迁移脚本
-- 创建库存操作日志表 (stock_operation_logs)
-- 创建时间: 2025-07-28
-- 版本: v1.0.0
-- =====================================================

-- 检查表是否已存在，如果存在则跳过创建
SET @table_exists = (
    SELECT COUNT(*) 
    FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name = 'stock_operation_logs'
);

-- 只有在表不存在时才创建
SET @sql = IF(@table_exists = 0, 
    'CREATE TABLE stock_operation_logs (
        id INT AUTO_INCREMENT PRIMARY KEY COMMENT ''主键ID'',
        productId INT NOT NULL COMMENT ''商品ID'',
        workplaceId INT NULL COMMENT ''职场ID（NULL表示总库存操作）'',
        operationType ENUM(''add'', ''subtract'', ''set'', ''transfer'', ''sync'', ''exchange_deduct'', ''exchange_restore'', ''migration'') NOT NULL COMMENT ''操作类型'',
        beforeStock INT NOT NULL COMMENT ''操作前库存'',
        afterStock INT NOT NULL COMMENT ''操作后库存'',
        changeAmount INT NOT NULL COMMENT ''变更数量'',
        relatedOrderId INT NULL COMMENT ''关联订单ID（兑换扣减时）'',
        operatorId INT NULL COMMENT ''操作员ID'',
        operatorName VARCHAR(50) NULL COMMENT ''操作员姓名'',
        reason VARCHAR(200) NULL COMMENT ''操作原因'',
        batchId VARCHAR(50) NULL COMMENT ''批次ID（批量操作时）'',
        ipAddress VARCHAR(45) NULL COMMENT ''操作IP地址'',
        userAgent TEXT NULL COMMENT ''用户代理信息'',
        metadata JSON NULL COMMENT ''扩展元数据'',
        createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',
        
        -- 索引设计
        INDEX idx_product_id (productId),
        INDEX idx_workplace_id (workplaceId),
        INDEX idx_operation_type (operationType),
        INDEX idx_related_order (relatedOrderId),
        INDEX idx_operator (operatorId),
        INDEX idx_batch_id (batchId),
        INDEX idx_created_at (createdAt),
        INDEX idx_product_workplace_time (productId, workplaceId, createdAt),
        INDEX idx_operation_time (operationType, createdAt),
        
        -- 外键约束
        CONSTRAINT fk_sol_product FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE ON UPDATE CASCADE,
        CONSTRAINT fk_sol_workplace FOREIGN KEY (workplaceId) REFERENCES workplaces(id) ON DELETE SET NULL ON UPDATE CASCADE,
        CONSTRAINT fk_sol_order FOREIGN KEY (relatedOrderId) REFERENCES exchanges(id) ON DELETE SET NULL ON UPDATE CASCADE,
        CONSTRAINT fk_sol_operator FOREIGN KEY (operatorId) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    COMMENT=''库存操作日志表 - 记录所有库存变更操作的详细日志'';',
    'SELECT ''Table stock_operation_logs already exists, skipping creation'' as message;'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证表创建结果
SELECT 
    CASE 
        WHEN @table_exists = 0 THEN '✅ 表 stock_operation_logs 创建成功'
        ELSE '⚠️  表 stock_operation_logs 已存在，跳过创建'
    END as result;

-- 显示表结构
DESCRIBE stock_operation_logs;
