<template>
  <div class="workplace-management">
    <div class="workplace-header">
      <div class="header-title">
        <h2>职场位置管理</h2>
        <p class="subtitle">管理系统中的职场位置，用于用户注册和订单管理</p>
      </div>
      <div class="header-actions">
        <el-button
          type="primary"
          @click="showCreateDialog"
          :loading="submitting"
          class="add-workplace-btn"
        >
          <el-icon><plus /></el-icon>添加职场
        </el-button>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-input
        v-model="searchQuery"
        placeholder="搜索职场名称或代码"
        clearable
        @clear="handleSearch"
        @keyup.enter="handleSearch"
      >
        <template #append>
          <el-button @click="handleSearch">
            <el-icon><search /></el-icon>
          </el-button>
        </template>
      </el-input>
    </div>

    <!-- 职场列表 -->
    <el-card class="workplace-list" v-loading="loading">
      <el-table :data="workplaces" style="width: 100%" border>
        <el-table-column label="ID" prop="id" width="80" />
        <el-table-column label="名称" prop="name" />
        <el-table-column label="代码" prop="code" width="120" />
        <el-table-column label="描述" prop="description" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.description || '无描述' }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isActive ? 'success' : 'danger'">
              {{ row.isActive ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button size="small" type="primary" @click="showEditDialog(row)">
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(row)"
                :disabled="row.userCount > 0 || row.exchangeCount > 0"
              >
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑职场对话框 -->
    <el-dialog
      :title="dialogType === 'create' ? '添加职场' : '编辑职场'"
      v-model="dialogVisible"
      width="500px"
      destroy-on-close
      append-to-body
      :z-index="3000"
      center
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      class="workplace-dialog"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="top"
      >
        <el-form-item label="职场名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入职场名称" />
        </el-form-item>
        <el-form-item label="职场代码" prop="code">
          <el-input v-model="form.code" placeholder="请输入职场代码，如：BJ、SH" />
        </el-form-item>
        <el-form-item label="职场描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入职场描述（可选）"
          />
        </el-form-item>
        <el-form-item label="状态" prop="isActive">
          <el-switch
            v-model="form.isActive"
            active-text="启用"
            inactive-text="禁用"
            :active-value="true"
            :inactive-value="false"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ dialogType === 'create' ? '创建' : '更新' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 显示职场关联数据对话框 -->
    <el-dialog
      title="职场关联数据"
      v-model="deleteConfirmVisible"
      width="500px"
      append-to-body
      :z-index="3100"
      center
      :close-on-click-modal="false"
      class="workplace-delete-dialog"
    >
      <div v-if="selectedWorkplace" class="delete-confirm-content">
        <el-alert
          v-if="selectedWorkplace.userCount > 0 || selectedWorkplace.exchangeCount > 0"
          type="warning"
          :closable="false"
          show-icon
        >
          <p>该职场关联了以下数据，无法删除：</p>
          <ul>
            <li v-if="selectedWorkplace.userCount > 0">
              <strong>{{ selectedWorkplace.userCount }}</strong> 个用户
            </li>
            <li v-if="selectedWorkplace.exchangeCount > 0">
              <strong>{{ selectedWorkplace.exchangeCount }}</strong> 个订单
            </li>
          </ul>
          <p>请考虑将其设置为"禁用"状态，而不是删除。</p>
        </el-alert>

        <el-alert
          v-else
          type="warning"
          :closable="false"
          show-icon
        >
          <p>确定要删除职场 <strong>{{ selectedWorkplace.name }}</strong> 吗？</p>
          <p>此操作不可撤销。</p>
        </el-alert>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deleteConfirmVisible = false">取消</el-button>
          <el-button
            v-if="selectedWorkplace && selectedWorkplace.userCount === 0 && selectedWorkplace.exchangeCount === 0"
            type="danger"
            @click="confirmDelete"
            :loading="deleting"
          >
            确认删除
          </el-button>
          <el-button
            v-else
            type="primary"
            @click="showEditDialog(selectedWorkplace)"
          >
            编辑状态
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import {
  getWorkplaces,
  getWorkplace,
  createWorkplace,
  updateWorkplace,
  deleteWorkplace
} from '@/api/system.js';
import { ElMessage } from 'element-plus';
import { Plus, Search } from '@element-plus/icons-vue';

// 数据
const workplaces = ref([]);
const loading = ref(false);
const submitting = ref(false);
const deleting = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const searchQuery = ref('');

// 对话框控制
const dialogVisible = ref(false);
const dialogType = ref('create');
const deleteConfirmVisible = ref(false);
const selectedWorkplace = ref(null);
const formRef = ref(null);

// 表单数据
const form = reactive({
  name: '',
  code: '',
  description: '',
  isActive: true
});

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入职场名称', trigger: 'blur' },
    { min: 1, max: 50, message: '职场名称长度应在1到50个字符之间', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入职场代码', trigger: 'blur' },
    { min: 1, max: 20, message: '职场代码长度应在1到20个字符之间', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '职场描述不能超过200个字符', trigger: 'blur' }
  ]
};

// 生命周期钩子
onMounted(() => {
  fetchWorkplaces();
});

// 方法
const fetchWorkplaces = async () => {
  loading.value = true;
  try {
    const response = await getWorkplaces(currentPage.value, pageSize.value, searchQuery.value);
    workplaces.value = response.data;
    total.value = response.total;
  } catch (error) {
    ElMessage.error('获取职场列表失败');
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  currentPage.value = 1;
  fetchWorkplaces();
};

const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchWorkplaces();
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchWorkplaces();
};

const resetForm = () => {
  // 先重置表单验证状态
  if (formRef.value) {
    formRef.value.resetFields();
  }

  // 然后重置表单数据
  Object.assign(form, {
    name: '',
    code: '',
    description: '',
    isActive: true
  });

  // 删除可能存在的id字段
  if (form.id) {
    delete form.id;
  }
};

const showCreateDialog = () => {
  // 防止重复点击
  if (dialogVisible.value || submitting.value) return;

  dialogType.value = 'create';
  resetForm();
  // 使用 nextTick 确保 DOM 更新完成后再显示弹窗
  nextTick(() => {
    dialogVisible.value = true;
  });
};

const showEditDialog = (workplace) => {
  dialogType.value = 'edit';
  resetForm();
  // 使用 nextTick 确保表单重置完成后再填充数据
  nextTick(() => {
    form.name = workplace.name;
    form.code = workplace.code;
    form.description = workplace.description || '';
    form.isActive = workplace.isActive;
    form.id = workplace.id;
    dialogVisible.value = true;
    deleteConfirmVisible.value = false;
  });
};

const handleCancel = () => {
  // 重置表单状态
  resetForm();
  // 关闭弹窗
  dialogVisible.value = false;
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.warning('请检查表单填写是否正确');
      return;
    }
    
    submitting.value = true;
    
    try {
      if (dialogType.value === 'create') {
        await createWorkplace({
          name: form.name,
          code: form.code,
          description: form.description,
          isActive: form.isActive
        });
        ElMessage.success('职场创建成功');
      } else {
        await updateWorkplace(form.id, {
          name: form.name,
          code: form.code,
          description: form.description,
          isActive: form.isActive
        });
        ElMessage.success('职场更新成功');
      }

      // 先关闭弹窗，再刷新数据
      dialogVisible.value = false;
      resetForm();

      // 延迟刷新数据，避免闪烁
      setTimeout(() => {
        fetchWorkplaces();
      }, 100);
    } catch (error) {
      const message = error.message || (dialogType.value === 'create' ? '创建职场失败' : '更新职场失败');
      ElMessage.error(message);
    } finally {
      submitting.value = false;
    }
  });
};

const handleDelete = async (workplace) => {
  try {
    // 获取详细信息，包括关联的用户和订单数量
    const detailResponse = await getWorkplace(workplace.id);
    selectedWorkplace.value = {
      ...workplace,
      ...detailResponse.stats
    };
    
    deleteConfirmVisible.value = true;
  } catch (error) {
    ElMessage.error('获取职场详情失败');
  }
};

const confirmDelete = async () => {
  if (!selectedWorkplace.value) return;
  
  deleting.value = true;
  
  try {
    await deleteWorkplace(selectedWorkplace.value.id);
    ElMessage.success('职场删除成功');
    deleteConfirmVisible.value = false;
    fetchWorkplaces();
  } catch (error) {
    ElMessage.error(error.message || '删除职场失败');
  } finally {
    deleting.value = false;
  }
};

// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};
</script>

<style scoped>
.workplace-management {
  padding: 20px;
}

.workplace-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.subtitle {
  color: #606266;
  margin-top: 5px;
}

.search-bar {
  margin-bottom: 20px;
  max-width: 400px;
}

.workplace-list {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.delete-confirm-content {
  margin: 20px 0;
}

:deep(.el-alert__content) {
  display: block;
}

/* 弹窗样式优化 */
:deep(.workplace-dialog) {
  .el-dialog {
    margin-top: 5vh !important;
    margin-bottom: 5vh !important;
    max-height: 90vh;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    animation: dialogFadeIn 0.3s ease-out;
    transform-origin: center;
  }

  @keyframes dialogFadeIn {
    from {
      opacity: 0;
      transform: scale(0.9) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  .el-dialog__header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e5e7eb;
    background-color: #fafafa;
  }

  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
  }

  .el-dialog__body {
    padding: 24px;
    max-height: calc(90vh - 140px);
    overflow-y: auto;
  }

  .el-dialog__footer {
    padding: 16px 24px 20px;
    border-top: 1px solid #e5e7eb;
    background-color: #fafafa;
  }
}

:deep(.workplace-delete-dialog) {
  .el-dialog {
    margin-top: 10vh !important;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
}

/* 确保弹窗遮罩层正确显示 */
:deep(.el-overlay) {
  z-index: 2999 !important;
}

:deep(.el-overlay-dialog) {
  z-index: 3000 !important;
}

/* 表单样式优化 */
.workplace-dialog :deep(.el-form-item) {
  margin-bottom: 20px;
}

.workplace-dialog :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.workplace-dialog :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.2s ease;
}

.workplace-dialog :deep(.el-input__wrapper:hover) {
  border-color: #2563eb;
}

.workplace-dialog :deep(.el-input__wrapper.is-focus) {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.workplace-dialog :deep(.el-textarea__wrapper) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.2s ease;
}

.workplace-dialog :deep(.el-textarea__wrapper:hover) {
  border-color: #2563eb;
}

.workplace-dialog :deep(.el-textarea__wrapper.is-focus) {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* 按钮样式优化 */
.add-workplace-btn {
  border-radius: 8px;
  padding: 12px 20px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
}

.add-workplace-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
}

.add-workplace-btn:active {
  transform: translateY(0);
}

/* 表格操作按钮优化 */
:deep(.el-button-group .el-button) {
  border-radius: 6px;
  margin-right: 4px;
  transition: all 0.2s ease;
}

:deep(.el-button-group .el-button:hover) {
  transform: translateY(-1px);
}

/* 对话框按钮优化 */
.dialog-footer .el-button {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.dialog-footer .el-button:hover {
  transform: translateY(-1px);
}

/* 搜索框优化 */
.search-bar :deep(.el-input__wrapper) {
  border-radius: 8px;
  transition: all 0.2s ease;
}

.search-bar :deep(.el-input__wrapper:hover) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-bar :deep(.el-input-group__append .el-button) {
  border-radius: 0 8px 8px 0;
}
</style> 