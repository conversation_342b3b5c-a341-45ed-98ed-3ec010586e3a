# 活动促销功能API设计

## 1. API路由规划

### 1.1 管理员接口 (需要admin权限)

```
POST   /api/promotions                    # 创建活动
GET    /api/promotions                    # 获取活动列表（管理员）
GET    /api/promotions/:id                # 获取活动详情
PUT    /api/promotions/:id                # 更新活动
DELETE /api/promotions/:id                # 删除活动
POST   /api/promotions/:id/toggle-status  # 切换活动状态

GET    /api/promotions/:id/statistics     # 获取活动统计数据
GET    /api/promotions/:id/participants   # 获取活动参与者列表
POST   /api/promotions/:id/products       # 添加活动商品
DELETE /api/promotions/:id/products/:productId  # 移除活动商品

GET    /api/promotions/analytics          # 活动分析报表
POST   /api/promotions/batch-update       # 批量更新活动
```

### 1.2 用户接口 (需要用户认证)

```
GET    /api/promotions/active             # 获取活跃活动列表
GET    /api/promotions/:id/details        # 获取活动详情（用户视角）
POST   /api/promotions/:id/participate    # 参与活动
GET    /api/promotions/my-participations  # 我的参与记录

GET    /api/products/:id/promotions       # 获取商品相关活动
POST   /api/promotions/calculate-discount # 计算优惠金额
```

## 2. 详细接口设计

### 2.1 创建活动

**接口**: `POST /api/promotions`

**请求体**:
```json
{
  "name": "双十一大促",
  "description": "全场商品8折优惠",
  "type": "discount",
  "start_time": "2024-11-11 00:00:00",
  "end_time": "2024-11-11 23:59:59",
  "max_participants": 1000,
  "max_uses_per_user": 1,
  "rules": {
    "type": "discount",
    "config": {
      "discount_type": "percentage",
      "discount_value": 20,
      "min_amount": 100
    }
  },
  "applicable_products": [1, 2, 3],
  "applicable_categories": [1, 2],
  "banner_image": "/uploads/promotions/banner.jpg",
  "priority": 10,
  "is_featured": true
}
```

**响应**:
```json
{
  "success": true,
  "message": "活动创建成功",
  "data": {
    "id": 1,
    "name": "双十一大促",
    "status": "draft",
    "created_at": "2024-01-15T10:00:00Z"
  }
}
```

### 2.2 获取活跃活动列表

**接口**: `GET /api/promotions/active`

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `type`: 活动类型筛选
- `featured`: 是否只显示精选活动

**响应**:
```json
{
  "success": true,
  "data": {
    "promotions": [
      {
        "id": 1,
        "name": "双十一大促",
        "description": "全场商品8折优惠",
        "type": "discount",
        "start_time": "2024-11-11T00:00:00Z",
        "end_time": "2024-11-11T23:59:59Z",
        "banner_image": "/uploads/promotions/banner.jpg",
        "is_featured": true,
        "current_participants": 156,
        "max_participants": 1000,
        "rules": {
          "type": "discount",
          "config": {
            "discount_type": "percentage",
            "discount_value": 20
          }
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 5,
      "totalPages": 1
    }
  }
}
```

### 2.3 参与活动

**接口**: `POST /api/promotions/:id/participate`

**请求体**:
```json
{
  "exchange_id": 123,
  "products": [
    {
      "product_id": 1,
      "quantity": 2,
      "price": 50.00
    }
  ]
}
```

**响应**:
```json
{
  "success": true,
  "message": "参与活动成功",
  "data": {
    "participation_id": 456,
    "original_amount": 100.00,
    "discount_amount": 20.00,
    "final_amount": 80.00,
    "applied_rule": {
      "type": "discount",
      "discount_type": "percentage",
      "discount_value": 20
    }
  }
}
```

### 2.4 计算优惠金额

**接口**: `POST /api/promotions/calculate-discount`

**请求体**:
```json
{
  "promotion_id": 1,
  "products": [
    {
      "product_id": 1,
      "quantity": 2,
      "price": 50.00
    }
  ],
  "user_id": 123
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "eligible": true,
    "original_amount": 100.00,
    "discount_amount": 20.00,
    "final_amount": 80.00,
    "discount_details": {
      "type": "percentage",
      "value": 20,
      "description": "8折优惠"
    },
    "remaining_uses": 0
  }
}
```

### 2.5 获取活动统计

**接口**: `GET /api/promotions/:id/statistics`

**查询参数**:
- `start_date`: 开始日期
- `end_date`: 结束日期
- `granularity`: 统计粒度 (day/week/month)

**响应**:
```json
{
  "success": true,
  "data": {
    "summary": {
      "total_participants": 1250,
      "total_orders": 890,
      "total_revenue": 45600.00,
      "total_discount": 9120.00,
      "conversion_rate": 71.2
    },
    "daily_stats": [
      {
        "date": "2024-11-11",
        "participants": 156,
        "orders": 98,
        "revenue": 4560.00,
        "discount": 912.00
      }
    ],
    "top_products": [
      {
        "product_id": 1,
        "product_name": "商品A",
        "sales_count": 45,
        "revenue": 2250.00
      }
    ]
  }
}
```

## 3. 错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE",
  "details": {
    "field": "具体错误信息"
  }
}
```

## 4. 常见错误码

- `PROMOTION_NOT_FOUND`: 活动不存在
- `PROMOTION_EXPIRED`: 活动已过期
- `PROMOTION_NOT_STARTED`: 活动未开始
- `PROMOTION_FULL`: 活动参与人数已满
- `USER_LIMIT_EXCEEDED`: 用户参与次数超限
- `INVALID_PRODUCTS`: 商品不符合活动条件
- `INSUFFICIENT_AMOUNT`: 金额不满足活动要求

## 5. 权限控制

### 5.1 管理员权限
- 所有活动管理接口
- 活动统计和分析接口
- 活动商品管理接口

### 5.2 用户权限
- 查看活跃活动
- 参与活动
- 查看个人参与记录
- 计算优惠金额

### 5.3 公开接口
- 获取商品相关活动信息（用于商品页面展示）
