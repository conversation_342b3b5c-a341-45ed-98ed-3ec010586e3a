{"name": "exchange-mall", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .js,.vue --ignore-path .gitignore", "lint:fix": "eslint . --ext .js,.vue --fix --ignore-path .gitignore", "format": "prettier --write \"src/**/*.{js,vue,css,scss,html,json,md}\" --config .prettierrc", "format:check": "prettier --check \"src/**/*.{js,vue,css,scss,html,json,md}\" --config .prettierrc", "format:all": "./scripts/format-code.sh", "code-quality": "npm run lint && npm run format:check"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@tiptap/core": "^2.11.7", "@tiptap/extension-image": "^2.11.7", "@tiptap/pm": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@tiptap/vue-3": "^2.11.7", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.8.4", "date-fns": "^4.1.0", "echarts": "^5.6.0", "element-plus": "^2.3.8", "pinia": "^3.0.2", "uuid": "^11.1.0", "vue": "^3.3.4", "vue-echarts": "6.6.8", "vue-router": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.28.0", "@vitejs/plugin-vue": "^4.6.2", "eslint": "^9.28.0", "eslint-plugin-vue": "^10.2.0", "globals": "^16.2.0", "prettier": "^3.0.0", "sass": "^1.63.6", "vite": "^4.4.0"}}