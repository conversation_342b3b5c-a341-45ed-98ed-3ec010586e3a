const axios = require('axios');

/**
 * 飞书群机器人服务
 */
class FeishuBotService {
  constructor() {
    // 飞书群机器人webhook地址
    this.webhookUrl = process.env.FEISHU_BOT_WEBHOOK_URL || 'https://open.feishu.cn/open-apis/bot/v2/hook/e6cff700-4172-4039-a700-43c8f43765fc';
  }

  /**
   * 发送卡片消息到飞书群
   * @param {Object} cardData - 卡片数据
   * @returns {Promise<Object>} - 发送结果
   */
  async sendCard(cardData) {
    try {
      console.log('飞书机器人: 准备发送卡片消息...');
      console.log('卡片数据:', JSON.stringify(cardData, null, 2));

      const response = await axios.post(this.webhookUrl, cardData, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000 // 设置10秒超时，防止飞书服务延迟影响主流程
      });

      console.log('飞书机器人: 消息发送成功', response.data);
      return response.data;
    } catch (error) {
      console.error('飞书机器人: 发送消息失败', error.response?.data || error.message);
      throw new Error('发送飞书群消息失败');
    }
  }

  /**
   * 发送兑换申请通知
   * @param {Object} exchange - 兑换记录
   * @param {Object} product - 商品信息
   * @param {Object} user - 用户信息
   * @returns {Promise<Object>} - 发送结果
   */
  async sendExchangeNotification(exchange, product, user) {
    try {
      // 格式化支付方式
      const paymentMethodText = exchange.paymentMethod === 'ly' ? '光年币' : '人民币';
      const priceText = exchange.paymentMethod === 'ly'
        ? `${product.lyPrice} 光年币`
        : `¥${product.rmbPrice}`;

      // 构建用户信息文本
      let userInfo = `**${user.username}**`;
      if (user.departmentPath) {
        userInfo += ` (${user.departmentPath})`;
      } else if (user.department) {
        userInfo += ` (${user.department})`;
      }

      // 构建联系方式信息
      let contactInfo = '';
      if (exchange.contactInfo && exchange.contactInfo !== '无需联系') {
        contactInfo = `\n**联系方式**: ${exchange.contactInfo}`;
      }
      if (user.mobile) {
        contactInfo += contactInfo ? ` | ${user.mobile}` : `\n**手机号**: ${user.mobile}`;
      }
      if (user.email) {
        contactInfo += contactInfo ? `` : `\n**邮箱**: ${user.email}`;
      }

      // 构建配送信息
      let locationInfo = '';
      if (exchange.location && exchange.location !== '无需配送') {
        locationInfo = `\n**所在位置**: ${exchange.location}`;
      }

      // 构建备注信息
      let remarksInfo = '';
      if (exchange.remarks) {
        remarksInfo = `\n**备注**: ${exchange.remarks}`;
      }

      const cardData = {
        msg_type: "interactive",
        card: {
          elements: [
            {
              tag: "div",
              text: {
                content: `🎁 **新兑换申请提醒**\n\n` +
                        `**申请人**: ${userInfo}\n` +
                        `**商品**: ${product.name}\n` +
                        `**数量**: ${exchange.quantity} 个\n` +
                        `**单价**: ${priceText}\n` +
                        `**总价**: ${exchange.paymentMethod === 'ly' ? (product.lyPrice * exchange.quantity) + ' 光年币' : '¥' + (product.rmbPrice * exchange.quantity)}\n` +
                        `**支付方式**: ${paymentMethodText}\n` +
                        `**申请时间**: ${new Date(exchange.createdAt).toLocaleString('zh-CN')}` +
                        contactInfo +
                        locationInfo +
                        remarksInfo,
                tag: "lark_md"
              }
            },
            {
              tag: "action",
              actions: [
                {
                  tag: "button",
                  text: {
                    content: "🔗 查看详情",
                    tag: "plain_text"
                  },
                  type: "primary",
                  url: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/admin/exchanges`,
                  value: {}
                }
              ]
            }
          ],
          header: {
            title: {
              content: `🛍️ 光年小卖部 - 兑换申请`,
              tag: "plain_text"
            },
            template: "blue"
          }
        }
      };

      return await this.sendCard(cardData);
    } catch (error) {
      console.error('飞书机器人: 发送兑换申请通知失败', error);
      throw error;
    }
  }

  /**
   * 发送库存告警通知
   * @param {Object} product - 商品信息
   * @param {number} alertThreshold - 告警阈值
   * @returns {Promise<Object>} - 发送结果
   */
  async sendStockAlert(product, alertThreshold) {
    try {
      // 判断库存状态
      let statusIcon = '⚠️';
      let statusText = '库存不足';
      let templateColor = 'orange';

      if (product.stock === 0) {
        statusIcon = '🚫';
        statusText = '库存耗尽';
        templateColor = 'red';
      } else if (product.stock <= alertThreshold * 0.5) {
        statusIcon = '🔴';
        statusText = '库存紧急';
        templateColor = 'red';
      }

      const cardData = {
        msg_type: "interactive",
        card: {
          elements: [
            {
              tag: "div",
              text: {
                content: `${statusIcon} **库存告警通知**\n\n` +
                        `**商品名称**: ${product.name}\n` +
                        `**当前库存**: ${product.stock} 个\n` +
                        `**告警阈值**: ${alertThreshold} 个\n` +
                        `**状态**: ${statusText}\n` +
                        `**光年币价格**: ${product.lyPrice} 光年币\n` +
                        `**人民币价格**: ¥${product.rmbPrice}\n` +
                        `**已兑换数量**: ${product.exchangeCount || 0} 个\n` +
                        `**告警时间**: ${new Date().toLocaleString('zh-CN')}\n\n` +
                        `💡 **建议**: ${product.stock === 0 ? '请立即补充库存' : '请考虑及时补充库存以满足兑换需求'}`,
                tag: "lark_md"
              }
            },
            {
              tag: "action",
              actions: [
                {
                  tag: "button",
                  text: {
                    content: "📦 管理库存",
                    tag: "plain_text"
                  },
                  type: "primary",
                  url: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/admin/products`,
                  value: {}
                }
              ]
            }
          ],
          header: {
            title: {
              content: `📦 光年小卖部 - 库存告警`,
              tag: "plain_text"
            },
            template: templateColor
          }
        }
      };

      return await this.sendCard(cardData);
    } catch (error) {
      console.error('飞书机器人: 发送库存告警通知失败', error);
      throw error;
    }
  }

  /**
   * 发送文本消息（备用方法）
   * @param {string} text - 文本内容
   * @returns {Promise<Object>} - 发送结果
   */
  async sendText(text) {
    try {
      const textData = {
        msg_type: "text",
        content: {
          text: text
        }
      };

      return await this.sendCard(textData);
    } catch (error) {
      console.error('飞书机器人: 发送文本消息失败', error);
      throw error;
    }
  }

  /**
   * 发送每日销售汇总报告
   * @param {Object} reportData - 每日报告数据
   * @returns {Promise<Object>} - 发送结果
   */
  async sendDailyReport(reportData) {
    try {
      const {
        date,
        weekday,
        totalOrders,
        lyOrders,
        rmbOrders,
        lyAmount,
        rmbAmount,
        totalQuantity,
        uniqueUsers,
        topProductsList,
        topDepartments
      } = reportData;

      // 构建热门商品信息
      let topProductsText = '';
      if (topProductsList && topProductsList.length > 0) {
        topProductsText = topProductsList
          .map((product, index) => `${index + 1}. ${product.name} (${product.quantity}件)`)
          .join('\n');
      } else {
        topProductsText = '暂无数据';
      }

      // 构建活跃部门信息
      let topDepartmentsText = '';
      if (topDepartments && topDepartments.length > 0) {
        topDepartmentsText = topDepartments
          .map((dept, index) => `${index + 1}. ${dept.name} (${dept.quantity}件)`)
          .join('\n');
      } else {
        topDepartmentsText = '暂无数据';
      }

      // 判断工作状态
      const isWorkDay = weekday !== '周六' && weekday !== '周日';
      const statusIcon = totalOrders > 0 ? '📈' : (isWorkDay ? '📊' : '😴');
      const statusText = totalOrders > 0 ? '今日已有订单' : (isWorkDay ? '今日暂无订单' : '今日休息日');

      // 获取当前时间
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

      const cardData = {
        msg_type: "interactive",
        card: {
          elements: [
            {
              tag: "div",
              text: {
                content: `${statusIcon} **今日销售汇总 - ${date} ${weekday} (截至${currentTime})**\n\n` +
                        `📋 **订单概况**\n` +
                        `• 总订单数：${totalOrders} 单\n` +
                        `• 光年币订单：${lyOrders} 单 (${lyAmount} 光年币)\n` +
                        `• 人民币订单：${rmbOrders} 单 (¥${rmbAmount.toFixed(2)})\n` +
                        `• 商品总数量：${totalQuantity} 件\n` +
                        `• 参与用户：${uniqueUsers} 人\n\n` +
                        `🏆 **热门商品TOP3**\n${topProductsText}\n\n` +
                        `🏢 **活跃部门TOP3**\n${topDepartmentsText}\n\n` +
                        `📊 **状态**：${statusText}`,
                tag: "lark_md"
              }
            },
            {
              tag: "action",
              actions: [
                {
                  tag: "button",
                  text: {
                    content: "📈 查看详细数据",
                    tag: "plain_text"
                  },
                  type: "primary",
                  url: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/admin/dashboard`,
                  value: {}
                }
              ]
            }
          ],
          header: {
            title: {
              content: `📅 光年小卖部 - 今日销售汇总`,
              tag: "plain_text"
            },
            template: totalOrders > 0 ? "blue" : "grey"
          }
        }
      };

      return await this.sendCard(cardData);
    } catch (error) {
      console.error('飞书机器人: 发送每日报告失败', error);
      throw error;
    }
  }

  /**
   * 发送每周兑换汇总报告
   * @param {Object} reportData - 每周报告数据
   * @returns {Promise<Object>} - 发送结果
   */
  async sendWeeklyReport(reportData) {
    try {
      const {
        weekStart,
        weekEnd,
        weekNumber,
        totalOrders,
        lyOrders,
        rmbOrders,
        lyAmount,
        rmbAmount,
        totalQuantity,
        uniqueUsers,
        dailyAverage,
        workDays,
        topProductsList,
        topDepartments,
        growthRate
      } = reportData;

      // 构建热门商品信息
      let topProductsText = '';
      if (topProductsList && topProductsList.length > 0) {
        topProductsText = topProductsList
          .map((product, index) => `${index + 1}. ${product.name} (${product.quantity}件)`)
          .join('\n');
      } else {
        topProductsText = '暂无数据';
      }

      // 构建活跃部门信息
      let topDepartmentsText = '';
      if (topDepartments && topDepartments.length > 0) {
        topDepartmentsText = topDepartments
          .map((dept, index) => `${index + 1}. ${dept.name} (${dept.quantity}件)`)
          .join('\n');
      } else {
        topDepartmentsText = '暂无数据';
      }

      // 计算支付方式占比
      const lyPercentage = totalOrders > 0 ? Math.round((lyOrders / totalOrders) * 100) : 0;
      const rmbPercentage = totalOrders > 0 ? Math.round((rmbOrders / totalOrders) * 100) : 0;

      // 增长趋势显示
      const growthIcon = growthRate > 0 ? '📈' : (growthRate < 0 ? '📉' : '➡️');
      const growthText = growthRate !== 0 ? `${Math.abs(growthRate)}%` : '持平';

      const cardData = {
        msg_type: "interactive",
        card: {
          elements: [
            {
              tag: "div",
              text: {
                content: `🎉 **第${weekNumber}周兑换汇总报告**\n` +
                        `📅 **统计周期**：${weekStart} - ${weekEnd}\n\n` +
                        `📊 **整体数据**\n` +
                        `• 总订单数：${totalOrders} 单\n` +
                        `• 工作日均：${dailyAverage} 单/天 (共${workDays}个工作日)\n` +
                        `• 商品总数量：${totalQuantity} 件\n` +
                        `• 参与用户：${uniqueUsers} 人\n` +
                        `• 周环比：${growthIcon} ${growthText}\n\n` +
                        `💰 **支付分析**\n` +
                        `• 光年币：${lyOrders} 单 (${lyPercentage}%) - ${lyAmount} 光年币\n` +
                        `• 人民币：${rmbOrders} 单 (${rmbPercentage}%) - ¥${rmbAmount.toFixed(2)}\n\n` +
                        `🏆 **本周热门商品**\n${topProductsText}\n\n` +
                        `🏢 **活跃部门排行**\n${topDepartmentsText}`,
                tag: "lark_md"
              }
            },
            {
              tag: "action",
              actions: [
                {
                  tag: "button",
                  text: {
                    content: "📊 查看详细报表",
                    tag: "plain_text"
                  },
                  type: "primary",
                  url: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/admin/analytics`,
                  value: {}
                },
                {
                  tag: "button",
                  text: {
                    content: "🛍️ 管理商品",
                    tag: "plain_text"
                  },
                  url: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/admin/products`,
                  value: {}
                }
              ]
            }
          ],
          header: {
            title: {
              content: `📈 光年小卖部 - 第${weekNumber}周数据报告`,
              tag: "plain_text"
            },
            template: "green"
          }
        }
      };

      return await this.sendCard(cardData);
    } catch (error) {
      console.error('飞书机器人: 发送每周报告失败', error);
      throw error;
    }
  }

  /**
   * 发送新用户欢迎通知
   * @param {Object} user - 新注册的用户信息
   * @returns {Promise<Object>} - 发送结果
   */
  async sendNewUserWelcome(user) {
    try {
      // 构建用户信息
      let userInfo = `**${user.username}**`;
      if (user.departmentPath) {
        userInfo += ` (${user.departmentPath})`;
      } else if (user.department) {
        userInfo += ` (${user.department})`;
      }

      // 获取注册时间
      const registerTime = new Date(user.createdAt).toLocaleString('zh-CN');

      // 构建联系信息
      let contactInfo = '';
      if (user.mobile) {
        contactInfo += `\n**手机**: ${user.mobile}`;
      }
      if (user.email) {
        contactInfo += `\n**邮箱**: ${user.email}`;
      }

      const cardData = {
        msg_type: "interactive",
        card: {
          elements: [
            {
              tag: "div",
              text: {
                content: `🎉 **新用户加入光年小卖部**\n\n` +
                        `👤 **用户信息**\n` +
                        `• 姓名：${userInfo}\n` +
                        `• 注册时间：${registerTime}\n` +
                        `• 认证方式：飞书登录` +
                        contactInfo + `\n\n` +
                        `🛍️ **欢迎加入**\n` +
                        `新成员已成功通过飞书登录系统，可以开始兑换商品啦！`,
                tag: "lark_md"
              }
            },
            {
              tag: "action",
              actions: [
                {
                  tag: "button",
                  text: {
                    content: "👥 查看用户管理",
                    tag: "plain_text"
                  },
                  type: "primary",
                  url: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/admin/users`,
                  value: {}
                }
              ]
            }
          ],
          header: {
            title: {
              content: `🎊 光年小卖部 - 新用户欢迎`,
              tag: "plain_text"
            },
            template: "green"
          }
        }
      };

      return await this.sendCard(cardData);
    } catch (error) {
      console.error('飞书机器人: 发送新用户欢迎通知失败', error);
      throw error;
    }
  }

  /**
   * 发送销售里程碑庆祝通知
   * @param {Object} milestoneData - 里程碑数据
   * @returns {Promise<Object>} - 发送结果
   */
  async sendSalesMilestone(milestoneData) {
    try {
      const {
        milestoneType,
        currentValue,
        milestoneValue,
        nextMilestone,
        totalOrders,
        totalRevenue,
        topProduct
      } = milestoneData;

      // 根据里程碑类型设置图标和描述
      let milestoneIcon = '🎉';
      let milestoneTitle = '';
      let milestoneDesc = '';

      switch (milestoneType) {
        case 'orders':
          milestoneIcon = '📦';
          milestoneTitle = `订单数量突破 ${milestoneValue} 单`;
          milestoneDesc = `恭喜！光年小卖部订单总数已达到 ${currentValue} 单`;
          break;
        case 'revenue_ly':
          milestoneIcon = '💰';
          milestoneTitle = `光年币收入突破 ${milestoneValue}`;
          milestoneDesc = `恭喜！光年币总收入已达到 ${currentValue}`;
          break;
        case 'revenue_rmb':
          milestoneIcon = '💴';
          milestoneTitle = `人民币收入突破 ¥${milestoneValue}`;
          milestoneDesc = `恭喜！人民币总收入已达到 ¥${currentValue}`;
          break;
        case 'users':
          milestoneIcon = '👥';
          milestoneTitle = `活跃用户突破 ${milestoneValue} 人`;
          milestoneDesc = `恭喜！活跃用户数已达到 ${currentValue} 人`;
          break;
      }

      // 构建下一个目标信息
      let nextGoalText = '';
      if (nextMilestone) {
        nextGoalText = `\n🎯 **下一个目标**: ${nextMilestone}`;
      }

      // 构建热门商品信息
      let topProductText = '';
      if (topProduct) {
        topProductText = `\n🏆 **当前最热门**: ${topProduct.name} (${topProduct.count}次兑换)`;
      }

      const cardData = {
        msg_type: "interactive",
        card: {
          elements: [
            {
              tag: "div",
              text: {
                content: `${milestoneIcon} **里程碑达成**\n\n` +
                        `🎊 **${milestoneTitle}**\n\n` +
                        `${milestoneDesc}\n\n` +
                        `📊 **当前统计**\n` +
                        `• 总订单数：${totalOrders} 单\n` +
                        `• 总收入：${totalRevenue}\n` +
                        `• 达成时间：${new Date().toLocaleString('zh-CN')}` +
                        topProductText +
                        nextGoalText + `\n\n` +
                        `🚀 **继续加油，向下一个目标冲刺！**`,
                tag: "lark_md"
              }
            },
            {
              tag: "action",
              actions: [
                {
                  tag: "button",
                  text: {
                    content: "📈 查看数据详情",
                    tag: "plain_text"
                  },
                  type: "primary",
                  url: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/admin/dashboard`,
                  value: {}
                }
              ]
            }
          ],
          header: {
            title: {
              content: `🏆 光年小卖部 - 里程碑庆祝`,
              tag: "plain_text"
            },
            template: "orange"
          }
        }
      };

      return await this.sendCard(cardData);
    } catch (error) {
      console.error('飞书机器人: 发送销售里程碑通知失败', error);
      throw error;
    }
  }

  /**
   * 发送异常订单预警通知
   * @param {Object} alertData - 预警数据
   * @returns {Promise<Object>} - 发送结果
   */
  async sendOrderAlert(alertData) {
    try {
      const {
        alertType,
        exchange,
        product,
        user,
        alertReason,
        riskLevel
      } = alertData;

      // 根据风险级别设置图标和颜色
      let alertIcon = '⚠️';
      let alertLevel = '中等';
      let templateColor = 'orange';

      switch (riskLevel) {
        case 'high':
          alertIcon = '🚨';
          alertLevel = '高风险';
          templateColor = 'red';
          break;
        case 'medium':
          alertIcon = '⚠️';
          alertLevel = '中等风险';
          templateColor = 'orange';
          break;
        case 'low':
          alertIcon = '🔔';
          alertLevel = '低风险';
          templateColor = 'yellow';
          break;
      }

      // 构建用户信息
      let userInfo = `**${user.username}**`;
      if (user.departmentPath) {
        userInfo += ` (${user.departmentPath})`;
      } else if (user.department) {
        userInfo += ` (${user.department})`;
      }

      // 计算订单金额
      const orderAmount = exchange.paymentMethod === 'ly'
        ? `${product.lyPrice * exchange.quantity} 光年币`
        : `¥${product.rmbPrice * exchange.quantity}`;

      // 构建联系信息
      let contactInfo = '';
      if (exchange.contactInfo && exchange.contactInfo !== '无需联系') {
        contactInfo = `\n**联系方式**: ${exchange.contactInfo}`;
      }
      if (user.mobile) {
        contactInfo += contactInfo ? ` | ${user.mobile}` : `\n**手机号**: ${user.mobile}`;
      }

      const cardData = {
        msg_type: "interactive",
        card: {
          elements: [
            {
              tag: "div",
              text: {
                content: `${alertIcon} **异常订单预警**\n\n` +
                        `🚨 **风险级别**: ${alertLevel}\n` +
                        `⚡ **预警原因**: ${alertReason}\n\n` +
                        `📋 **订单详情**\n` +
                        `• 订单ID：${exchange.id}\n` +
                        `• 用户：${userInfo}\n` +
                        `• 商品：${product.name}\n` +
                        `• 数量：${exchange.quantity} 个\n` +
                        `• 金额：${orderAmount}\n` +
                        `• 支付方式：${exchange.paymentMethod === 'ly' ? '光年币' : '人民币'}\n` +
                        `• 下单时间：${new Date(exchange.createdAt).toLocaleString('zh-CN')}` +
                        contactInfo + `\n\n` +
                        `⚠️ **建议**: 请及时核实订单详情，必要时联系用户确认`,
                tag: "lark_md"
              }
            },
            {
              tag: "action",
              actions: [
                {
                  tag: "button",
                  text: {
                    content: "🔍 查看订单详情",
                    tag: "plain_text"
                  },
                  type: "primary",
                  url: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/admin/exchanges`,
                  value: {}
                },
                {
                  tag: "button",
                  text: {
                    content: "👥 查看用户信息",
                    tag: "plain_text"
                  },
                  url: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/admin/users`,
                  value: {}
                }
              ]
            }
          ],
          header: {
            title: {
              content: `🚨 光年小卖部 - 异常订单预警`,
              tag: "plain_text"
            },
            template: templateColor
          }
        }
      };

      return await this.sendCard(cardData);
    } catch (error) {
      console.error('飞书机器人: 发送异常订单预警失败', error);
      throw error;
    }
  }

  /**
   * 发送用户反馈汇总通知
   * @param {Object} feedbackData - 反馈数据
   * @returns {Promise<Object>} - 发送结果
   */
  async sendFeedbackAlert(feedbackData) {
    try {
      const {
        feedback,
        user,
        isImportant,
        feedbackCount
      } = feedbackData;

      // 根据重要程度设置图标和颜色
      let feedbackIcon = '💬';
      let priorityText = '普通反馈';
      let templateColor = 'blue';

      if (isImportant) {
        feedbackIcon = '⭐';
        priorityText = '重要反馈';
        templateColor = 'red';
      }

      // 构建用户信息
      let userInfo = `**${user.username}**`;
      if (user.departmentPath) {
        userInfo += ` (${user.departmentPath})`;
      } else if (user.department) {
        userInfo += ` (${user.department})`;
      }

      // 反馈类型映射
      const typeMap = {
        'suggestion': '功能建议',
        'bug': 'Bug反馈',
        'complaint': '投诉建议',
        'praise': '表扬建议',
        'other': '其他反馈'
      };

      const feedbackType = typeMap[feedback.type] || '其他反馈';

      // 处理反馈内容长度
      let contentPreview = feedback.content;
      if (contentPreview.length > 100) {
        contentPreview = contentPreview.substring(0, 100) + '...';
      }

      // 构建联系信息
      let contactInfo = '';
      if (feedback.contactInfo) {
        contactInfo = `\n**联系方式**: ${feedback.contactInfo}`;
      }
      if (user.mobile) {
        contactInfo += contactInfo ? ` | ${user.mobile}` : `\n**手机号**: ${user.mobile}`;
      }
      if (user.email) {
        contactInfo += contactInfo ? `` : `\n**邮箱**: ${user.email}`;
      }

      const cardData = {
        msg_type: "interactive",
        card: {
          elements: [
            {
              tag: "div",
              text: {
                content: `${feedbackIcon} **用户反馈提醒**\n\n` +
                        `📋 **反馈详情**\n` +
                        `• 反馈人：${userInfo}\n` +
                        `• 反馈类型：${feedbackType}\n` +
                        `• 优先级：${priorityText}\n` +
                        `• 提交时间：${new Date(feedback.createdAt).toLocaleString('zh-CN')}\n\n` +
                        `💭 **反馈内容**\n${contentPreview}` +
                        contactInfo + `\n\n` +
                        `📊 **统计信息**\n` +
                        `• 本月反馈总数：${feedbackCount} 条\n` +
                        `• 该用户反馈次数：${feedback.userFeedbackCount || 1} 次`,
                tag: "lark_md"
              }
            },
            {
              tag: "action",
              actions: [
                {
                  tag: "button",
                  text: {
                    content: "📝 查看反馈管理",
                    tag: "plain_text"
                  },
                  type: "primary",
                  url: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/admin/feedback`,
                  value: {}
                },
                {
                  tag: "button",
                  text: {
                    content: "👤 查看用户信息",
                    tag: "plain_text"
                  },
                  url: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/admin/users`,
                  value: {}
                }
              ]
            }
          ],
          header: {
            title: {
              content: `💬 光年小卖部 - 用户反馈`,
              tag: "plain_text"
            },
            template: templateColor
          }
        }
      };

      return await this.sendCard(cardData);
    } catch (error) {
      console.error('飞书机器人: 发送用户反馈通知失败', error);
      throw error;
    }
  }

  /**
   * 发送新品上架通知
   * @param {Object} productData - 新商品数据
   * @returns {Promise<Object>} - 发送结果
   */
  async sendNewProductNotification(productData) {
    try {
      const {
        product,
        admin
      } = productData;

      // 构建价格信息
      let priceInfo = '';
      if (product.lyPrice && product.rmbPrice) {
        priceInfo = `💰 **兑换价格**\n• 光年币：${product.lyPrice} 光年币\n• 人民币：¥${product.rmbPrice}`;
      } else if (product.lyPrice) {
        priceInfo = `💰 **兑换价格**\n• 光年币：${product.lyPrice} 光年币`;
      } else if (product.rmbPrice) {
        priceInfo = `💰 **兑换价格**\n• 人民币：¥${product.rmbPrice}`;
      }

      // 构建库存信息
      let stockInfo = '';
      if (product.stock > 0) {
        if (product.stock >= 100) {
          stockInfo = `📦 **库存状态**\n• 充足库存：${product.stock} 件`;
        } else if (product.stock >= 20) {
          stockInfo = `📦 **库存状态**\n• 正常库存：${product.stock} 件`;
        } else {
          stockInfo = `📦 **库存状态**\n• 有限库存：${product.stock} 件 ⚠️`;
        }
      } else {
        stockInfo = `📦 **库存状态**\n• 暂无库存 🚫`;
      }

      // 构建商品描述
      let descriptionText = '';
      if (product.description && product.description.trim() !== '') {
        // 限制描述长度
        const maxLength = 150;
        let shortDesc = product.description;
        if (shortDesc.length > maxLength) {
          shortDesc = shortDesc.substring(0, maxLength) + '...';
        }
        descriptionText = `\n📝 **商品介绍**\n${shortDesc}`;
      }

      // 构建分类信息
      let categoryText = '';
      if (product.Category && product.Category.name) {
        categoryText = `\n🏷️ **商品分类**：${product.Category.name}`;
      }

      // 构建上架人信息
      let adminInfo = '';
      if (admin && admin.username) {
        adminInfo = `\n👤 **上架人员**：${admin.username}`;
      }

      // 判断库存状态用于设置卡片颜色
      let templateColor = 'green';
      if (product.stock === 0) {
        templateColor = 'grey';
      } else if (product.stock < 20) {
        templateColor = 'orange';
      }

      const cardData = {
        msg_type: "interactive",
        card: {
          elements: [
            {
              tag: "div",
              text: {
                content: `🎁 **新品上架通知**\n\n` +
                        `✨ **商品名称**：${product.name}\n` +
                        `⏰ **上架时间**：${new Date(product.createdAt).toLocaleString('zh-CN')}\n\n` +
                        priceInfo + `\n\n` +
                        stockInfo +
                        categoryText +
                        descriptionText +
                        adminInfo + `\n\n` +
                        `🛒 **立即兑换**\n快来光年小卖部兑换新商品吧！`,
                tag: "lark_md"
              }
            },
            {
              tag: "action",
              actions: [
                {
                  tag: "button",
                  text: {
                    content: "🛍️ 立即兑换",
                    tag: "plain_text"
                  },
                  type: "primary",
                  url: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/products`,
                  value: {}
                },
                {
                  tag: "button",
                  text: {
                    content: "📋 管理商品",
                    tag: "plain_text"
                  },
                  url: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/admin/products`,
                  value: {}
                }
              ]
            }
          ],
          header: {
            title: {
              content: `🆕 光年小卖部 - 新品上架`,
              tag: "plain_text"
            },
            template: templateColor
          }
        }
      };

      return await this.sendCard(cardData);
    } catch (error) {
      console.error('飞书机器人: 发送新品上架通知失败', error);
      throw error;
    }
  }

  /**
   * 发送月度报告
   * @param {Object} reportData - 月度报告数据
   * @returns {Promise<Object>} - 发送结果
   */
  async sendMonthlyReport(reportData) {
    try {
      const {
        month,
        year,
        totalOrders,
        lyOrders,
        rmbOrders,
        lyAmount,
        rmbAmount,
        totalQuantity,
        uniqueUsers,
        avgDailyOrders,
        workDays,
        topProductsList,
        topDepartments,
        topUsers,
        growthRate,
        conversionRate,
        peakDay,
        insights
      } = reportData;

      // 构建热门商品信息
      let topProductsText = '';
      if (topProductsList && topProductsList.length > 0) {
        topProductsText = topProductsList
          .map((product, index) => `${index + 1}. ${product.name} (${product.quantity}件)`)
          .join('\n');
      } else {
        topProductsText = '暂无数据';
      }

      // 构建活跃部门信息
      let topDepartmentsText = '';
      if (topDepartments && topDepartments.length > 0) {
        topDepartmentsText = topDepartments
          .map((dept, index) => `${index + 1}. ${dept.name} (${dept.quantity}件)`)
          .join('\n');
      } else {
        topDepartmentsText = '暂无数据';
      }

      // 构建活跃用户信息
      let topUsersText = '';
      if (topUsers && topUsers.length > 0) {
        topUsersText = topUsers
          .map((user, index) => `${index + 1}. ${user.name} (${user.orders}单)`)
          .join('\n');
      } else {
        topUsersText = '暂无数据';
      }

      // 计算支付方式占比
      const lyPercentage = totalOrders > 0 ? Math.round((lyOrders / totalOrders) * 100) : 0;
      const rmbPercentage = totalOrders > 0 ? Math.round((rmbOrders / totalOrders) * 100) : 0;

      // 增长趋势显示
      const growthIcon = growthRate > 0 ? '📈' : (growthRate < 0 ? '📉' : '➡️');
      const growthText = growthRate !== 0 ? `${Math.abs(growthRate)}%` : '持平';

      // 构建数据洞察
      let insightsText = '';
      if (insights && insights.length > 0) {
        insightsText = '\n\n📊 **数据洞察**\n' + insights.map(insight => `• ${insight}`).join('\n');
      }

      const cardData = {
        msg_type: "interactive",
        card: {
          elements: [
            {
              tag: "div",
              text: {
                content: `📈 **${year}年${month}月运营数据报告**\n\n` +
                        `📅 **统计周期**：${year}年${month}月 (${workDays}个工作日)\n\n` +
                        `📊 **核心指标**\n` +
                        `• 总订单数：${totalOrders} 单\n` +
                        `• 日均订单：${avgDailyOrders} 单\n` +
                        `• 商品总量：${totalQuantity} 件\n` +
                        `• 活跃用户：${uniqueUsers} 人\n` +
                        `• 转化率：${conversionRate}%\n` +
                        `• 月环比：${growthIcon} ${growthText}\n\n` +
                        `💰 **收入分析**\n` +
                        `• 光年币：${lyOrders} 单 (${lyPercentage}%) - ${lyAmount} 光年币\n` +
                        `• 人民币：${rmbOrders} 单 (${rmbPercentage}%) - ¥${rmbAmount.toFixed(2)}\n\n` +
                        `🏆 **热门商品TOP5**\n${topProductsText}\n\n` +
                        `🏢 **活跃部门TOP5**\n${topDepartmentsText}\n\n` +
                        `👤 **活跃用户TOP5**\n${topUsersText}\n\n` +
                        `📍 **峰值日期**：${peakDay.date} (${peakDay.orders}单)` +
                        insightsText,
                tag: "lark_md"
              }
            },
            {
              tag: "action",
              actions: [
                {
                  tag: "button",
                  text: {
                    content: "📊 查看详细数据",
                    tag: "plain_text"
                  },
                  type: "primary",
                  url: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/admin/analytics`,
                  value: {}
                },
                {
                  tag: "button",
                  text: {
                    content: "💹 趋势分析",
                    tag: "plain_text"
                  },
                  url: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/admin/dashboard`,
                  value: {}
                }
              ]
            }
          ],
          header: {
            title: {
              content: `📈 光年小卖部 - ${year}年${month}月数据报告`,
              tag: "plain_text"
            },
            template: "blue"
          }
        }
      };

      return await this.sendCard(cardData);
    } catch (error) {
      console.error('飞书机器人: 发送月度报告失败', error);
      throw error;
    }
  }

  /**
   * 发送系统维护通知
   * @param {Object} maintenanceData - 维护通知数据
   * @returns {Promise<Object>} - 发送结果
   */
  async sendMaintenanceNotification(maintenanceData) {
    try {
      const {
        type,
        title,
        startTime,
        endTime,
        duration,
        reason,
        impact,
        preparations,
        contactInfo
      } = maintenanceData;

      // 根据维护类型设置图标和颜色
      let maintenanceIcon = '🔧';
      let templateColor = 'orange';

      switch (type) {
        case 'scheduled':
          maintenanceIcon = '📅';
          templateColor = 'blue';
          break;
        case 'emergency':
          maintenanceIcon = '🚨';
          templateColor = 'red';
          break;
        case 'update':
          maintenanceIcon = '🆙';
          templateColor = 'green';
          break;
        case 'hotfix':
          maintenanceIcon = '🔥';
          templateColor = 'red';
          break;
      }

      // 构建影响说明
      let impactText = '';
      if (impact && impact.length > 0) {
        impactText = '\n\n⚠️ **影响范围**\n' + impact.map(item => `• ${item}`).join('\n');
      }

      // 构建准备事项
      let preparationsText = '';
      if (preparations && preparations.length > 0) {
        preparationsText = '\n\n📋 **准备事项**\n' + preparations.map(item => `• ${item}`).join('\n');
      }

      // 构建联系信息
      let contactText = '';
      if (contactInfo) {
        contactText = `\n\n📞 **技术支持**：${contactInfo}`;
      }

      const cardData = {
        msg_type: "interactive",
        card: {
          elements: [
            {
              tag: "div",
              text: {
                content: `${maintenanceIcon} **系统维护通知**\n\n` +
                        `📋 **维护主题**：${title}\n` +
                        `⏰ **维护时间**：${startTime} - ${endTime}\n` +
                        `⏱️ **预计耗时**：${duration}\n` +
                        `🔍 **维护原因**：${reason}` +
                        impactText +
                        preparationsText +
                        contactText + `\n\n` +
                        `💡 **温馨提示**：请提前做好相关准备，维护期间可能影响系统正常使用，感谢您的理解与配合！`,
                tag: "lark_md"
              }
            },
            {
              tag: "action",
              actions: [
                {
                  tag: "button",
                  text: {
                    content: "🏠 访问首页",
                    tag: "plain_text"
                  },
                  type: "primary",
                  url: `${process.env.FRONTEND_URL || 'http://localhost:5173'}`,
                  value: {}
                },
                {
                  tag: "button",
                  text: {
                    content: "📞 联系技术支持",
                    tag: "plain_text"
                  },
                  url: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/admin/feedback`,
                  value: {}
                }
              ]
            }
          ],
          header: {
            title: {
              content: `🔧 光年小卖部 - 系统维护通知`,
              tag: "plain_text"
            },
            template: templateColor
          }
        }
      };

      return await this.sendCard(cardData);
    } catch (error) {
      console.error('飞书机器人: 发送系统维护通知失败', error);
      throw error;
    }
  }

  /**
   * 发送错误告警推送
   * @param {Object} alertData - 错误告警数据
   * @returns {Promise<Object>} - 发送结果
   */
  async sendErrorAlert(alertData) {
    try {
      const {
        severity,
        errorType,
        errorMessage,
        timestamp,
        affectedUsers,
        systemComponent,
        stackTrace,
        resolution,
        monitorUrl
      } = alertData;

      // 根据严重程度设置图标和颜色
      let severityIcon = '⚠️';
      let severityText = '中等';
      let templateColor = 'orange';

      switch (severity) {
        case 'critical':
          severityIcon = '🚨';
          severityText = '严重';
          templateColor = 'red';
          break;
        case 'high':
          severityIcon = '🔴';
          severityText = '高';
          templateColor = 'red';
          break;
        case 'medium':
          severityIcon = '🟡';
          severityText = '中等';
          templateColor = 'orange';
          break;
        case 'low':
          severityIcon = '🟢';
          severityText = '低';
          templateColor = 'yellow';
          break;
      }

      // 构建错误详情
      let errorDetails = '';
      if (stackTrace && stackTrace.length > 500) {
        errorDetails = stackTrace.substring(0, 500) + '...';
      } else {
        errorDetails = stackTrace || '详细错误信息请查看系统日志';
      }

      // 构建解决方案
      let resolutionText = '';
      if (resolution) {
        resolutionText = `\n\n🔧 **解决方案**：${resolution}`;
      }

      const cardData = {
        msg_type: "interactive",
        card: {
          elements: [
            {
              tag: "div",
              text: {
                content: `${severityIcon} **系统错误告警**\n\n` +
                        `🚨 **严重程度**：${severityText}\n` +
                        `🏷️ **错误类型**：${errorType}\n` +
                        `⏰ **发生时间**：${timestamp}\n` +
                        `🖥️ **受影响组件**：${systemComponent}\n` +
                        `👥 **影响用户数**：${affectedUsers || '未知'}\n\n` +
                        `📝 **错误信息**：\n${errorMessage}\n\n` +
                        `🔍 **错误详情**：\n\`\`\`\n${errorDetails}\n\`\`\`` +
                        resolutionText + `\n\n` +
                        `⚡ **请及时处理此告警，确保系统稳定运行！**`,
                tag: "lark_md"
              }
            },
            {
              tag: "action",
              actions: [
                {
                  tag: "button",
                  text: {
                    content: "🔍 查看监控",
                    tag: "plain_text"
                  },
                  type: "primary",
                  url: monitorUrl || `${process.env.FRONTEND_URL || 'http://localhost:5173'}/admin/logs`,
                  value: {}
                },
                {
                  tag: "button",
                  text: {
                    content: "📊 系统状态",
                    tag: "plain_text"
                  },
                  url: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/admin/dashboard`,
                  value: {}
                }
              ]
            }
          ],
          header: {
            title: {
              content: `🚨 光年小卖部 - 系统错误告警`,
              tag: "plain_text"
            },
            template: templateColor
          }
        }
      };

      return await this.sendCard(cardData);
    } catch (error) {
      console.error('飞书机器人: 发送错误告警失败', error);
      throw error;
    }
  }
}

module.exports = new FeishuBotService();
