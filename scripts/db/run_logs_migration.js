// 日志表迁移脚本
const { Sequelize } = require('sequelize');
const path = require('path');
const fs = require('fs');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

async function runLogsMigration() {
  console.log('开始执行日志表迁移...');
  
  // 使用SQLite数据库
  const sequelize = new Sequelize({
    dialect: 'sqlite',
    storage: path.join(__dirname, '../database.sqlite')
  });
  
  try {
    // 验证数据库连接
    await sequelize.authenticate();
    console.log('数据库连接已建立');
    
    // 检查logs表是否已存在
    const [tables] = await sequelize.query(`
      SELECT name FROM sqlite_master WHERE type='table' AND name='logs'
    `);
    
    if (tables.length > 0) {
      console.log('logs表已存在，无需创建');
      return;
    }
    
    // 创建logs表
    await sequelize.query(`
      CREATE TABLE logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        action VARCHAR(255) NOT NULL,
        entityType VARCHAR(255) NOT NULL,
        entityId INTEGER NOT NULL,
        oldValue TEXT,
        newValue TEXT,
        userId INTEGER,
        username VARCHAR(255),
        ipAddress VARCHAR(255),
        description TEXT,
        createdAt DATETIME NOT NULL,
        updatedAt DATETIME NOT NULL
      )
    `);
    
    console.log('成功创建logs表');
    
    // 添加索引
    await sequelize.query(`CREATE INDEX logs_action ON logs (action)`);
    await sequelize.query(`CREATE INDEX logs_entityType ON logs (entityType)`);
    await sequelize.query(`CREATE INDEX logs_entityId ON logs (entityId)`);
    await sequelize.query(`CREATE INDEX logs_userId ON logs (userId)`);
    await sequelize.query(`CREATE INDEX logs_username ON logs (username)`);
    await sequelize.query(`CREATE INDEX logs_createdAt ON logs (createdAt)`);
    
    console.log('成功添加索引，日志表迁移完成');
    
  } catch (error) {
    console.error('迁移失败:', error);
  } finally {
    // 关闭连接
    await sequelize.close();
    console.log('迁移脚本执行完毕');
  }
}

// 执行迁移
runLogsMigration(); 