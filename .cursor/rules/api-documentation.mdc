---
description:
globs:
alwaysApply: false
---
# 光年小卖部API文档规范

## Swagger文档配置
项目使用Swagger自动生成API文档，配置文件：[server/config/swagger.js](mdc:server/config/swagger.js)

## API文档访问
- API文档可通过 `http://localhost:3000/api-docs` 访问
- API首页入口：[server/public/index.html](mdc:server/public/index.html)

## API文档编写规范

### API注释格式
API文档使用JSDoc格式编写，需要包含以下内容：
- 接口描述
- 请求方法
- 请求参数
- 响应格式
- 错误码
- 权限要求

### 示例
商品管理API已添加完整Swagger文档：[server/routes/products.js](mdc:server/routes/products.js)

```javascript
/**
 * @swagger
 * /products:
 *   get:
 *     summary: 获取商品列表
 *     tags: [Products]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *     responses:
 *       200:
 *         description: 返回商品列表
 */
```

## 数据模型定义
API文档中的数据模型使用Swagger组件定义：

```javascript
/**
 * @swagger
 * components:
 *   schemas:
 *     Product:
 *       type: object
 *       required:
 *         - name
 *         - price
 *       properties:
 *         name:
 *           type: string
 */
```

## API认证
敏感API使用JWT认证，Swagger中通过bearerAuth定义：

```javascript
/**
 * @swagger
 * /admin/products:
 *   post:
 *     security:
 *       - bearerAuth: []
 */
```

## 文档维护责任
- 每次新增或修改API时，必须同步更新Swagger文档
- 文档内容应清晰准确，与实际实现保持一致
