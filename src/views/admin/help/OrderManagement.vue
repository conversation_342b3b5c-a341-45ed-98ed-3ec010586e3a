<template>
  <div class="help-doc order-management">
    <h2>订单管理</h2>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>订单管理概述</h3>
        </div>
      </template>
      <div class="card-content">
        <p>订单管理是光年小卖部的核心功能，用于处理用户的商品兑换申请。系统支持光年币和人民币两种支付方式，提供完整的订单生命周期管理。</p>
        
        <div class="info-block">
          <h4>支付方式</h4>
          <ul>
            <li><strong>光年币支付</strong>：使用企业内部积分进行兑换，无需额外支付凭证</li>
            <li><strong>人民币支付</strong>：用户需要上传支付凭证，管理员审核后发货</li>
          </ul>
        </div>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>订单状态管理</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>订单状态说明</h4>
        <el-table :data="orderStatuses" style="width: 100%">
          <el-table-column prop="status" label="状态" width="120">
            <template #default="scope">
              <el-tag :type="scope.row.type">{{ scope.row.status }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="说明" />
          <el-table-column prop="actions" label="可执行操作" />
        </el-table>
        
        <h4>状态流转</h4>
        <p>订单状态按以下流程进行流转：</p>
        <div class="flow-container">
          <el-steps :active="5" finish-status="success">
            <el-step title="待处理" description="用户提交兑换申请" />
            <el-step title="处理中" description="管理员开始处理订单" />
            <el-step title="已发货" description="商品已发出" />
            <el-step title="已完成" description="用户确认收货" />
          </el-steps>
        </div>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>订单处理流程</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>光年币订单处理</h4>
        <ol>
          <li>用户提交兑换申请，系统自动扣除光年币</li>
          <li>管理员在订单列表中查看新订单</li>
          <li>确认商品库存充足后，将订单状态改为"处理中"</li>
          <li>准备商品并发货，更新状态为"已发货"</li>
          <li>用户收货后，订单自动完成</li>
        </ol>
        
        <h4>人民币订单处理</h4>
        <ol>
          <li>用户提交兑换申请并上传支付凭证</li>
          <li>管理员查看支付凭证，验证支付信息</li>
          <li>确认支付无误后，将订单状态改为"处理中"</li>
          <li>准备商品并发货，更新状态为"已发货"</li>
          <li>用户收货后，订单完成</li>
        </ol>
        
        <el-alert
          title="提示：人民币订单需要特别注意验证支付凭证的真实性"
          type="warning"
          :closable="false"
          show-icon
          style="margin: 15px 0;"
        />
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>订单管理操作</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>批量操作</h4>
        <ul>
          <li><strong>批量发货</strong>：选择多个"处理中"状态的订单，批量更新为"已发货"</li>
          <li><strong>批量完成</strong>：选择多个"已发货"状态的订单，批量标记为"已完成"</li>
          <li><strong>批量导出</strong>：导出选中订单的详细信息，便于物流管理</li>
        </ul>
        
        <h4>订单搜索与筛选</h4>
        <ul>
          <li><strong>按状态筛选</strong>：快速查看特定状态的订单</li>
          <li><strong>按支付方式筛选</strong>：区分光年币和人民币订单</li>
          <li><strong>按时间范围筛选</strong>：查看特定时间段的订单</li>
          <li><strong>按用户搜索</strong>：查找特定用户的订单记录</li>
          <li><strong>按商品搜索</strong>：查看特定商品的兑换情况</li>
        </ul>
        
        <h4>订单详情查看</h4>
        <p>点击订单可查看详细信息，包括：</p>
        <ul>
          <li>用户信息（姓名、部门、联系方式）</li>
          <li>商品信息（名称、规格、数量、价格）</li>
          <li>支付信息（支付方式、支付凭证）</li>
          <li>配送信息（收货地址、联系方式）</li>
          <li>订单备注和特殊要求</li>
          <li>操作日志（状态变更记录）</li>
        </ul>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>常见问题</h3>
        </div>
      </template>
      <div class="card-content">
        <el-collapse>
          <el-collapse-item title="如何处理库存不足的订单？" name="1">
            <p>当商品库存不足时：</p>
            <ol>
              <li>立即联系用户说明情况</li>
              <li>提供替代商品选择或退款方案</li>
              <li>如用户同意替换，修改订单商品信息</li>
              <li>如用户要求退款，将订单状态改为"已取消"并处理退款</li>
            </ol>
          </el-collapse-item>
          
          <el-collapse-item title="如何处理支付凭证有问题的订单？" name="2">
            <p>发现支付凭证问题时：</p>
            <ol>
              <li>将订单状态标记为"待确认"</li>
              <li>通过系统消息或飞书联系用户</li>
              <li>要求用户重新提供正确的支付凭证</li>
              <li>确认无误后继续处理订单</li>
            </ol>
          </el-collapse-item>
          
          <el-collapse-item title="如何取消已提交的订单？" name="3">
            <p>取消订单的步骤：</p>
            <ol>
              <li>在订单详情页面点击"取消订单"按钮</li>
              <li>填写取消原因</li>
              <li>如果是光年币订单，系统会自动退还光年币</li>
              <li>如果是人民币订单，需要手动处理退款</li>
              <li>通知用户订单取消情况</li>
            </ol>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
  </div>
</template>

<script setup>
// 订单状态数据
const orderStatuses = [
  {
    status: '待处理',
    type: 'warning',
    description: '用户刚提交的兑换申请，等待管理员处理',
    actions: '确认订单、取消订单'
  },
  {
    status: '处理中',
    type: 'primary',
    description: '管理员已确认订单，正在准备商品',
    actions: '发货、取消订单'
  },
  {
    status: '已发货',
    type: 'success',
    description: '商品已发出，等待用户确认收货',
    actions: '标记完成、查看物流'
  },
  {
    status: '已完成',
    type: 'info',
    description: '订单已完成，用户已确认收货',
    actions: '查看详情、导出数据'
  },
  {
    status: '已取消',
    type: 'danger',
    description: '订单已取消，可能因为库存不足或用户要求',
    actions: '查看取消原因'
  }
];
</script>

<style scoped>
.order-management h2 {
  margin-bottom: 20px;
  font-weight: bold;
  color: #303133;
}

.help-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.card-content {
  line-height: 1.6;
}

.card-content h4 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-size: 16px;
  color: #303133;
}

.card-content p {
  margin-bottom: 12px;
  color: #606266;
}

.card-content ul, .card-content ol {
  padding-left: 20px;
  margin-bottom: 16px;
}

.card-content li {
  margin-bottom: 6px;
  color: #606266;
}

.info-block {
  background-color: #f0f9ff;
  border-left: 4px solid #409EFF;
  padding: 12px 16px;
  margin: 16px 0;
  border-radius: 4px;
}

.flow-container {
  margin: 20px 0;
}
</style>
