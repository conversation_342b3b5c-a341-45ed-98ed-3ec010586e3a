#!/bin/bash

# 光年小卖部 - 依赖清理脚本
# 清理重复和不必要的依赖

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧹 开始依赖清理...${NC}"

# 项目根目录
PROJECT_ROOT=$(pwd)

# 备份package.json文件
echo -e "${YELLOW}📋 备份package.json文件...${NC}"
cp package.json package.json.backup
cp server/package.json server/package.json.backup

echo -e "${GREEN}✅ 备份完成${NC}"

# 清理前端不必要的依赖
echo -e "${BLUE}🔧 清理前端不必要的依赖...${NC}"

# 前端不应该有的后端依赖
FRONTEND_REMOVE_DEPS=(
    "multer"
    "csv-parser" 
    "csv-writer"
    "exceljs"
    "mysql2"
    "dotenv"
)

for dep in "${FRONTEND_REMOVE_DEPS[@]}"; do
    if grep -q "\"$dep\"" package.json; then
        echo -e "${YELLOW}移除前端依赖: $dep${NC}"
        npm uninstall $dep
    fi
done

# 清理后端不必要的依赖
echo -e "${BLUE}🔧 清理后端不必要的依赖...${NC}"

cd server

# 后端不应该有的前端依赖
BACKEND_REMOVE_DEPS=(
    "pinia"
    "vue-router"
)

for dep in "${BACKEND_REMOVE_DEPS[@]}"; do
    if grep -q "\"$dep\"" package.json; then
        echo -e "${YELLOW}移除后端依赖: $dep${NC}"
        npm uninstall $dep
    fi
done

cd "$PROJECT_ROOT"

# 统一版本的依赖
echo -e "${BLUE}🔄 统一依赖版本...${NC}"

# 更新后端的dotenv版本以匹配前端
cd server
if grep -q "\"dotenv\"" package.json; then
    echo -e "${YELLOW}更新后端dotenv版本...${NC}"
    npm install dotenv@^16.5.0
fi

# 更新后端的mysql2版本以匹配前端
if grep -q "\"mysql2\"" package.json; then
    echo -e "${YELLOW}更新后端mysql2版本...${NC}"
    npm install mysql2@^3.14.1
fi

cd "$PROJECT_ROOT"

# 检查安全漏洞
echo -e "${BLUE}🔍 检查安全漏洞...${NC}"
npm audit --audit-level=moderate || true

cd server
npm audit --audit-level=moderate || true
cd "$PROJECT_ROOT"

# 清理node_modules并重新安装
echo -e "${BLUE}🧹 清理并重新安装依赖...${NC}"

echo -e "${YELLOW}清理前端node_modules...${NC}"
rm -rf node_modules package-lock.json
npm install

echo -e "${YELLOW}清理后端node_modules...${NC}"
cd server
rm -rf node_modules package-lock.json
npm install
cd "$PROJECT_ROOT"

# 生成依赖报告
echo -e "${BLUE}📊 生成依赖报告...${NC}"

mkdir -p temp/reports

# 前端依赖报告
echo "# 前端依赖报告" > temp/reports/frontend-dependencies.md
echo "" >> temp/reports/frontend-dependencies.md
echo "## 生产依赖" >> temp/reports/frontend-dependencies.md
npm list --depth=0 --prod --json | jq -r '.dependencies | keys[]' | sort >> temp/reports/frontend-dependencies.md
echo "" >> temp/reports/frontend-dependencies.md
echo "## 开发依赖" >> temp/reports/frontend-dependencies.md
npm list --depth=0 --dev --json | jq -r '.dependencies | keys[]' | sort >> temp/reports/frontend-dependencies.md

# 后端依赖报告
echo "# 后端依赖报告" > temp/reports/backend-dependencies.md
echo "" >> temp/reports/backend-dependencies.md
echo "## 生产依赖" >> temp/reports/backend-dependencies.md
cd server
npm list --depth=0 --prod --json | jq -r '.dependencies | keys[]' | sort >> ../temp/reports/backend-dependencies.md
echo "" >> ../temp/reports/backend-dependencies.md
echo "## 开发依赖" >> ../temp/reports/backend-dependencies.md
npm list --depth=0 --dev --json | jq -r '.dependencies | keys[]' | sort >> ../temp/reports/backend-dependencies.md
cd "$PROJECT_ROOT"

# 创建依赖优化总结
cat > temp/reports/dependency-cleanup-summary.md << EOF
# 依赖清理总结

## 已清理的依赖

### 前端移除的依赖
- multer (后端文件上传库)
- csv-parser (后端数据处理)
- csv-writer (后端数据处理)
- exceljs (后端Excel处理)
- mysql2 (后端数据库连接)
- dotenv (后端环境变量)

### 后端移除的依赖
- pinia (前端状态管理)
- vue-router (前端路由)

## 版本统一

### 已统一的依赖版本
- dotenv: 统一为 ^16.5.0
- mysql2: 统一为 ^3.14.1

## 建议

1. 定期运行 \`npm audit\` 检查安全漏洞
2. 使用 \`npm outdated\` 检查过时的依赖
3. 在添加新依赖前，确认是否真的需要
4. 避免在前后端重复安装相同功能的包

## 下一步

1. 测试应用功能确保清理没有破坏任何功能
2. 如果发现问题，可以从备份文件恢复：
   - cp package.json.backup package.json
   - cp server/package.json.backup server/package.json
   - npm install && cd server && npm install
EOF

echo -e "${GREEN}✅ 依赖清理完成！${NC}"
echo -e "${BLUE}📋 报告文件：${NC}"
echo -e "  - temp/reports/frontend-dependencies.md"
echo -e "  - temp/reports/backend-dependencies.md" 
echo -e "  - temp/reports/dependency-cleanup-summary.md"
echo -e "${YELLOW}⚠️  请测试应用功能确保清理没有破坏任何功能${NC}"
echo -e "${BLUE}💡 如有问题，可从备份恢复：${NC}"
echo -e "  cp package.json.backup package.json"
echo -e "  cp server/package.json.backup server/package.json"
