#!/usr/bin/env node

/**
 * 配置文件验证脚本
 * 验证不同环境下配置文件的完整性和正确性
 */

const fs = require('fs');
const path = require('path');

// 配置文件路径
const CONFIG_FILES = {
  development: {
    frontend: path.join(__dirname, '../.env'),
    backend: path.join(__dirname, '../server/.env')
  },
  production: {
    frontend: path.join(__dirname, '../.env.production'),
    backend: path.join(__dirname, '../server/.env.production')
  }
};

// 必需的配置项
const REQUIRED_CONFIGS = {
  backend: [
    'NODE_ENV',
    'PORT',
    'SERVER_URL',
    'DB_HOST',
    'DB_PORT',
    'DB_USER',
    'DB_PASSWORD',
    'DB_NAME',
    'JWT_SECRET',
    'FEISHU_APP_ID',
    'FEISHU_APP_SECRET',
    'FEISHU_REDIRECT_URI',
    'CORS_ORIGIN'
  ],
  frontend: [
    'VITE_API_URL'
  ]
};

// 环境特定的配置值验证
const ENV_SPECIFIC_VALUES = {
  development: {
    NODE_ENV: 'development',
    SERVER_URL: 'http://localhost:3000',
    FEISHU_REDIRECT_URI: 'http://localhost:3000/api/feishu/callback'
  },
  production: {
    NODE_ENV: 'production',
    SERVER_URL: 'https://store-api.chongyangqisi.com',
    FEISHU_REDIRECT_URI: 'https://store-api.chongyangqisi.com/api/feishu/callback'
  }
};

/**
 * 解析.env文件
 * @param {string} filePath - 文件路径
 * @returns {Object} - 配置对象
 */
function parseEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return null;
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const config = {};

  content.split('\n').forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#') && line.includes('=')) {
      const [key, ...valueParts] = line.split('=');
      const value = valueParts.join('=').trim();
      config[key.trim()] = value;
    }
  });

  return config;
}

/**
 * 验证配置文件
 * @param {string} env - 环境名称
 * @param {string} type - 配置类型 (frontend/backend)
 * @param {string} filePath - 文件路径
 */
function validateConfigFile(env, type, filePath) {
  console.log(`\n📋 验证 ${env} 环境 ${type} 配置文件: ${path.basename(filePath)}`);
  
  const config = parseEnvFile(filePath);
  
  if (!config) {
    console.log(`❌ 配置文件不存在: ${filePath}`);
    return false;
  }

  console.log(`✅ 配置文件存在`);

  // 检查必需配置项
  const requiredConfigs = REQUIRED_CONFIGS[type] || [];
  const missingConfigs = [];
  const presentConfigs = [];

  requiredConfigs.forEach(key => {
    if (!config[key] || config[key] === '') {
      missingConfigs.push(key);
    } else {
      presentConfigs.push(key);
    }
  });

  console.log(`✅ 已配置项 (${presentConfigs.length}/${requiredConfigs.length}):`);
  presentConfigs.forEach(key => {
    const value = config[key];
    const displayValue = key.includes('SECRET') || key.includes('PASSWORD') 
      ? '***已设置***' 
      : value;
    console.log(`   ✓ ${key}: ${displayValue}`);
  });

  if (missingConfigs.length > 0) {
    console.log(`❌ 缺失配置项 (${missingConfigs.length}):`);
    missingConfigs.forEach(key => {
      console.log(`   ✗ ${key}`);
    });
  }

  // 验证环境特定的配置值
  if (ENV_SPECIFIC_VALUES[env]) {
    console.log(`\n🔍 验证环境特定配置值:`);
    const envValues = ENV_SPECIFIC_VALUES[env];
    let valueErrors = 0;

    Object.keys(envValues).forEach(key => {
      const expected = envValues[key];
      const actual = config[key];
      
      if (actual === expected) {
        console.log(`   ✓ ${key}: ${actual}`);
      } else {
        console.log(`   ❌ ${key}: 期望 "${expected}", 实际 "${actual}"`);
        valueErrors++;
      }
    });

    if (valueErrors === 0) {
      console.log(`✅ 环境特定配置值验证通过`);
    } else {
      console.log(`❌ 发现 ${valueErrors} 个配置值错误`);
    }
  }

  return missingConfigs.length === 0;
}

/**
 * 主验证函数
 */
function main() {
  console.log('🔧 配置文件验证工具');
  console.log('==========================================');

  let allValid = true;

  // 验证所有配置文件
  Object.keys(CONFIG_FILES).forEach(env => {
    console.log(`\n🌍 验证 ${env.toUpperCase()} 环境配置`);
    console.log('------------------------------------------');

    Object.keys(CONFIG_FILES[env]).forEach(type => {
      const filePath = CONFIG_FILES[env][type];
      const isValid = validateConfigFile(env, type, filePath);
      if (!isValid) {
        allValid = false;
      }
    });
  });

  // 验证配置文件一致性
  console.log('\n🔄 验证配置文件一致性');
  console.log('------------------------------------------');

  const devBackend = parseEnvFile(CONFIG_FILES.development.backend);
  const prodBackend = parseEnvFile(CONFIG_FILES.production.backend);

  if (devBackend && prodBackend) {
    const devKeys = Object.keys(devBackend).sort();
    const prodKeys = Object.keys(prodBackend).sort();

    const onlyInDev = devKeys.filter(key => !prodKeys.includes(key));
    const onlyInProd = prodKeys.filter(key => !devKeys.includes(key));

    if (onlyInDev.length === 0 && onlyInProd.length === 0) {
      console.log('✅ 配置文件结构一致');
    } else {
      console.log('⚠️  配置文件结构不一致:');
      if (onlyInDev.length > 0) {
        console.log(`   仅在开发环境: ${onlyInDev.join(', ')}`);
      }
      if (onlyInProd.length > 0) {
        console.log(`   仅在生产环境: ${onlyInProd.join(', ')}`);
      }
    }
  }

  // 总结
  console.log('\n📊 验证结果');
  console.log('==========================================');
  if (allValid) {
    console.log('✅ 所有配置文件验证通过');
    console.log('\n🚀 可以安全部署到生产环境');
    process.exit(0);
  } else {
    console.log('❌ 配置文件验证失败');
    console.log('\n🛠️  请修复上述问题后重新验证');
    process.exit(1);
  }
}

// 运行验证
if (require.main === module) {
  main();
}

module.exports = {
  parseEnvFile,
  validateConfigFile
};
