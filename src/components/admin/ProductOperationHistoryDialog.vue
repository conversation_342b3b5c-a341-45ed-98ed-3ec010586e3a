<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${product?.name || '商品'} - 操作历史`"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="product-history-content">
      <!-- 商品信息头部 -->
      <div class="product-header">
        <div class="product-info">
          <img
            :src="fixImageUrl(product?.imageUrl)"
            :alt="product?.name"
            class="product-image"
            @error="handleImageError"
          />
          <div class="product-details">
            <h3 class="product-name">{{ product?.name || '未知商品' }}</h3>
            <div class="product-meta">
              <span class="product-id">ID: {{ product?.id }}</span>
              <span class="product-category">分类: {{ product?.Category?.name || '未分类' }}</span>
              <span class="current-stock">当前库存: {{ product?.totalStock || 0 }}</span>
            </div>
          </div>
        </div>
        <div class="header-actions">
          <el-button type="primary" icon="el-icon-refresh" @click="loadProductHistory" :loading="loading">
            刷新
          </el-button>
          <el-button type="success" icon="el-icon-download" @click="exportProductHistory" :disabled="logs.length === 0">
            导出
          </el-button>
        </div>
      </div>

      <!-- 筛选条件 -->
      <div class="filters-section">
        <div class="filters">
          <div class="filter-row">
            <div class="filter-item">
              <label>时间范围:</label>
              <el-date-picker
                v-model="dateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 300px"
              />
            </div>
            <div class="filter-item">
              <label>操作类型:</label>
              <el-select v-model="filters.operationType" placeholder="选择操作类型" clearable style="width: 150px">
                <el-option label="库存更新" value="stock_update" />
                <el-option label="库存转移" value="stock_transfer" />
                <el-option label="快速调整" value="quick_adjust" />
                <el-option label="批量调整" value="batch_quick_adjust" />
                <el-option label="批量编辑" value="batch_edit" />
              </el-select>
            </div>
            <div class="filter-item">
              <label>职场:</label>
              <el-select v-model="filters.workplaceId" placeholder="选择职场" clearable style="width: 120px">
                <el-option
                  v-for="workplace in workplaces"
                  :key="workplace.id"
                  :label="workplace.name"
                  :value="workplace.id"
                />
              </el-select>
            </div>
            <div class="filter-actions">
              <el-button type="primary" @click="searchHistory" :loading="loading">
                查询
              </el-button>
              <el-button @click="resetFilters">
                重置
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作历史表格 -->
      <div class="history-table">
        <el-table
          :data="logs"
          v-loading="loading"
          stripe
          style="width: 100%"
          :default-sort="{ prop: 'timestamp', order: 'descending' }"
          @row-click="viewOperationDetail"
        >
          <el-table-column label="操作时间" prop="timestamp" width="160" sortable>
            <template #default="{ row }">
              <div class="time-cell">
                <i class="el-icon-time"></i>
                {{ formatDateTime(row.timestamp) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作用户" prop="userName" width="120">
            <template #default="{ row }">
              <div class="user-cell">
                <i class="el-icon-user"></i>
                {{ row.userName || '未知用户' }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作类型" prop="type" width="120">
            <template #default="{ row }">
              <el-tag :type="getOperationTypeColor(row.type)" size="small">
                {{ getOperationTypeText(row.type) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="职场" prop="workplaceName" width="100">
            <template #default="{ row }">
              <span>{{ row.workplaceName || '全局' }}</span>
            </template>
          </el-table-column>

          <el-table-column label="库存变化" width="140">
            <template #default="{ row }">
              <div class="stock-change" v-if="row.oldStock !== null && row.newStock !== null">
                <span class="old-stock">{{ row.oldStock }}</span>
                <i class="el-icon-right"></i>
                <span class="new-stock" :class="getStockChangeClass(row.oldStock, row.newStock)">
                  {{ row.newStock }}
                </span>
              </div>
              <span v-else-if="row.quantity" class="quantity">
                {{ row.operation === 'add' ? '+' : '-' }}{{ row.quantity }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column label="操作原因" prop="reason" min-width="150" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.reason || '无' }}</span>
            </template>
          </el-table-column>

          <el-table-column label="状态" prop="status" width="80">
            <template #default="{ row }">
              <el-tag :type="getStatusColor(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="80" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click.stop="viewOperationDetail(row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container" v-if="totalRecords > 0">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            :total="totalRecords"
            layout="total, sizes, prev, pager, next, jumper"
          />
        </div>

        <!-- 空数据提示 -->
        <div v-if="!loading && logs.length === 0" class="empty-data">
          <el-empty description="暂无操作历史记录" />
        </div>
      </div>
    </div>

    <!-- 操作详情对话框 -->
    <OperationDetailDialog
      v-model="showDetailDialog"
      :operation="selectedOperation"
    />
  </el-dialog>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getStockOperationLogs, getWorkplaces } from '@/api/stockManagement'
import { fixImageUrl } from '@/utils/imageUtils'
import { format } from 'date-fns'
import OperationDetailDialog from './OperationDetailDialog.vue'

export default {
  name: 'ProductOperationHistoryDialog',
  components: {
    OperationDetailDialog
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    product: {
      type: Object,
      default: null
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const dialogVisible = ref(false)
    const loading = ref(false)
    const logs = ref([])
    const workplaces = ref([])
    const totalRecords = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(20)
    const showDetailDialog = ref(false)
    const selectedOperation = ref(null)

    // 筛选条件
    const dateRange = ref([])
    const filters = ref({
      operationType: '',
      workplaceId: ''
    })

    // 操作类型映射
    const operationTypeMap = {
      stock_update: { text: '库存更新', color: 'primary' },
      stock_transfer: { text: '库存转移', color: 'info' },
      quick_adjust: { text: '快速调整', color: 'success' },
      batch_quick_adjust: { text: '批量调整', color: 'warning' },
      batch_edit: { text: '批量编辑', color: 'danger' }
    }

    const getOperationTypeText = (type) => {
      return operationTypeMap[type]?.text || type
    }

    const getOperationTypeColor = (type) => {
      return operationTypeMap[type]?.color || 'info'
    }

    const getStatusText = (status) => {
      const statusMap = {
        completed: '成功',
        failed: '失败',
        processing: '进行中'
      }
      return statusMap[status] || status
    }

    const getStatusColor = (status) => {
      const colorMap = {
        completed: 'success',
        failed: 'danger',
        processing: 'warning'
      }
      return colorMap[status] || 'info'
    }

    const getStockChangeClass = (oldStock, newStock) => {
      if (newStock > oldStock) return 'increase'
      if (newStock < oldStock) return 'decrease'
      return 'no-change'
    }

    const formatDateTime = (dateTime) => {
      if (!dateTime) return '无'
      try {
        const date = new Date(dateTime)
        if (isNaN(date.getTime())) {
          return dateTime
        }
        return format(date, 'MM-dd HH:mm:ss')
      } catch (error) {
        console.error('时间格式化失败:', error, dateTime)
        return dateTime || '无'
      }
    }

    const handleImageError = (event) => {
      event.target.src = '/images/default-product.png'
    }

    // 映射数据库操作类型到前端显示类型
    const mapOperationTypeFromDB = (dbType) => {
      const typeMap = {
        'add': 'quick_adjust',
        'subtract': 'quick_adjust',
        'set': 'stock_update',
        'transfer': 'stock_transfer',
        'sync': 'stock_update',
        'exchange_deduct': 'quick_adjust',
        'exchange_restore': 'quick_adjust',
        'migration': 'stock_update'
      }
      return typeMap[dbType] || 'stock_update'
    }

    // 生成示例操作记录
    const generateSampleLogs = (productId) => {
      const now = new Date()
      const sampleLogs = []

      // 生成最近7天的示例操作记录
      for (let i = 0; i < 10; i++) {
        const timestamp = new Date(now.getTime() - i * 2 * 60 * 60 * 1000) // 每2小时一条记录
        const operations = ['add', 'subtract']
        const operation = operations[Math.floor(Math.random() * operations.length)]
        const quantity = Math.floor(Math.random() * 20) + 1
        const oldStock = Math.floor(Math.random() * 100) + 50
        const newStock = operation === 'add' ? oldStock + quantity : Math.max(0, oldStock - quantity)

        sampleLogs.push({
          id: `sample_${productId}_${i}`,
          timestamp: timestamp.toISOString(),
          userId: 'admin_001',
          userName: '管理员',
          type: 'quick_adjust',
          operation: operation,
          productId: productId,
          productName: props.product?.name || '测试商品',
          workplaceId: ['workplace_1', 'workplace_2', 'workplace_3'][Math.floor(Math.random() * 3)],
          workplaceName: ['北京', '武汉', '长沙'][Math.floor(Math.random() * 3)],
          quantity: quantity,
          oldStock: oldStock,
          newStock: newStock,
          reason: operation === 'add' ? `快速增加库存 ${quantity} 个` : `快速减少库存 ${quantity} 个`,
          status: 'completed',
          details: {}
        })
      }

      return sampleLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
    }

    // 加载商品操作历史
    const loadProductHistory = async () => {
      if (!props.product?.id) return

      loading.value = true
      try {
        const params = {
          productId: props.product.id,
          page: currentPage.value,
          limit: pageSize.value
        }

        // 添加筛选条件
        if (dateRange.value && dateRange.value.length === 2) {
          params.startDate = dateRange.value[0]
          params.endDate = dateRange.value[1]
        }
        if (filters.value.operationType) {
          params.operationType = filters.value.operationType
        }
        if (filters.value.workplaceId) {
          params.workplaceId = filters.value.workplaceId
        }

        const response = await getStockOperationLogs(params)

        if (response && response.success) {
          // 处理从数据库获取的日志数据
          const dbLogs = response.data.logs || []

          logs.value = dbLogs.map(log => ({
            id: log.id,
            timestamp: log.createdAt,
            userId: log.operatorId,
            userName: log.operatorName || log.operator?.username || '系统管理员',
            type: mapOperationTypeFromDB(log.operationType),
            operation: log.operationType,
            productId: log.productId,
            productName: log.product?.name || props.product?.name,
            workplaceId: log.workplaceId,
            workplaceName: log.workplace?.name || '全局',
            quantity: Math.abs(log.changeAmount || 0),
            oldStock: log.beforeStock,
            newStock: log.afterStock,
            reason: log.reason || '无',
            status: 'completed',
            details: log.metadata ? (typeof log.metadata === 'string' ? JSON.parse(log.metadata) : log.metadata) : {}
          }))
          totalRecords.value = response.data.pagination?.total || response.data.total || 0

          if (logs.value.length > 0) {
            ElMessage.success(`成功加载 ${logs.value.length} 条操作记录`)
          } else {
            ElMessage.info('该商品暂无操作记录')
          }
        } else {
          throw new Error(response?.message || 'API调用失败')
        }
      } catch (error) {
        console.error('加载商品操作历史失败:', error)
        ElMessage.error(`加载操作历史失败: ${error.message}`)

        // 失败时尝试从localStorage加载
        try {
          const savedLogs = localStorage.getItem('stockManagement_recentOperations')
          if (savedLogs) {
            const allLogs = JSON.parse(savedLogs)
            const filteredLogs = allLogs.filter(log =>
              log.productId === props.product.id ||
              log.productId === props.product.id.toString()
            )

            logs.value = filteredLogs.map(log => ({
              ...log,
              userName: log.userName || '本地用户',
              timestamp: log.timestamp || new Date().toISOString(),
              status: log.status || 'completed'
            }))
            totalRecords.value = logs.value.length

            if (logs.value.length > 0) {
              ElMessage.info(`已加载本地缓存数据 (${logs.value.length} 条记录)`)
            } else {
              ElMessage.warning('本地也没有找到该商品的操作记录')
            }
          } else {
            ElMessage.warning('没有找到任何操作记录')
          }
        } catch (localError) {
          console.error('加载本地数据也失败:', localError)
          ElMessage.error('加载本地数据也失败')
        }
      } finally {
        loading.value = false
      }
    }

    // 加载职场数据
    const loadWorkplaces = async () => {
      try {
        const response = await getWorkplaces()
        if (response.success) {
          workplaces.value = response.data || []
        }
      } catch (error) {
        console.error('加载职场数据失败:', error)
      }
    }

    // 搜索历史
    const searchHistory = () => {
      currentPage.value = 1
      loadProductHistory()
    }

    // 重置筛选条件
    const resetFilters = () => {
      dateRange.value = []
      filters.value = {
        operationType: '',
        workplaceId: ''
      }
      currentPage.value = 1
      loadProductHistory()
    }

    // 分页处理
    const handleSizeChange = (size) => {
      pageSize.value = size
      loadProductHistory()
    }

    const handleCurrentChange = (page) => {
      currentPage.value = page
      loadProductHistory()
    }

    // 查看操作详情
    const viewOperationDetail = (row) => {
      selectedOperation.value = row
      showDetailDialog.value = true
    }

    // 导出商品操作历史
    const exportProductHistory = () => {
      try {
        const exportData = logs.value.map(log => ({
          '操作时间': formatDateTime(log.timestamp),
          '操作用户': log.userName || '未知用户',
          '操作类型': getOperationTypeText(log.type),
          '职场': log.workplaceName || '全局',
          '操作': log.operation,
          '数量': log.quantity || '',
          '原库存': log.oldStock || '',
          '新库存': log.newStock || '',
          '原因': log.reason || '',
          '状态': getStatusText(log.status)
        }))

        const headers = Object.keys(exportData[0] || {})
        const csvContent = [
          headers.join(','),
          ...exportData.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
        ].join('\n')

        const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        const url = URL.createObjectURL(blob)
        link.setAttribute('href', url)
        link.setAttribute('download', `${props.product?.name || '商品'}_操作历史_${new Date().toISOString().slice(0, 10)}.csv`)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        ElMessage.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        ElMessage.error('导出失败')
      }
    }

    const handleClose = () => {
      emit('update:modelValue', false)
    }

    // 监听器
    watch(() => props.modelValue, (newVal) => {
      dialogVisible.value = newVal
      if (newVal && props.product) {
        loadWorkplaces()
        loadProductHistory()
      }
    })

    watch(dialogVisible, (newVal) => {
      if (!newVal) {
        emit('update:modelValue', false)
      }
    })

    return {
      dialogVisible,
      loading,
      logs,
      workplaces,
      totalRecords,
      currentPage,
      pageSize,
      showDetailDialog,
      selectedOperation,
      dateRange,
      filters,
      getOperationTypeText,
      getOperationTypeColor,
      getStatusText,
      getStatusColor,
      getStockChangeClass,
      formatDateTime,
      fixImageUrl,
      handleImageError,
      loadProductHistory,
      searchHistory,
      resetFilters,
      handleSizeChange,
      handleCurrentChange,
      viewOperationDetail,
      exportProductHistory,
      handleClose
    }
  }
}
</script>

<style scoped>
.product-history-content {
  max-height: 70vh;
  overflow-y: auto;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.product-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.product-details h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
}

.product-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #606266;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.filters-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.time-cell, .user-cell {
  display: flex;
  align-items: center;
  gap: 6px;
}

.stock-change {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.old-stock {
  color: #909399;
}

.new-stock.increase {
  color: #67c23a;
  font-weight: 600;
}

.new-stock.decrease {
  color: #f56c6c;
  font-weight: 600;
}

.new-stock.no-change {
  color: #409eff;
}

.quantity {
  font-weight: 600;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.empty-data {
  padding: 40px;
  text-align: center;
}

@media (max-width: 768px) {
  .product-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-actions {
    margin-left: 0;
    justify-content: center;
  }
}
</style>
