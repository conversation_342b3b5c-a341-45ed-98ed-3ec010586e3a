#!/usr/bin/env node

/**
 * 库存管理API测试脚本
 * 测试职场库存分布数据的API返回格式
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:3000/api';

// 测试配置
const testConfig = {
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
};

/**
 * 管理员登录获取token
 */
async function adminLogin() {
  try {
    const loginData = {
      username: '超管',
      email: '<EMAIL>',
      password: '654321'
    };
    
    const response = await axios.post(`${BASE_URL}/auth/login`, loginData, testConfig);
    
    if (response.data.token) {
      console.log('✅ 管理员登录成功');
      return response.data.token;
    } else {
      console.log('❌ 登录失败：无token');
      return null;
    }
  } catch (error) {
    console.log('❌ 登录失败:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试商品库存API
 */
async function testProductsWithStocks(token) {
  if (!token) {
    console.log('❌ 无有效token，跳过测试');
    return;
  }
  
  try {
    const config = {
      ...testConfig,
      headers: {
        ...testConfig.headers,
        'Authorization': `Bearer ${token}`
      }
    };
    
    console.log('\n🔍 测试商品库存API...');
    const response = await axios.get(`${BASE_URL}/products?includeWorkplaceStocks=true&limit=50`, config);
    
    console.log('API响应结构:', {
      hasData: !!response.data,
      dataType: typeof response.data,
      isArray: Array.isArray(response.data),
      keys: response.data ? Object.keys(response.data) : null
    });

    const products = response.data?.data || response.data;

    if (products && Array.isArray(products)) {
      console.log(`✅ 成功获取 ${products.length} 个商品的库存数据`);
      
      // 查找"哪吒捏捏乐"商品
      const nezhaProduct = products.find(p => p.name.includes('哪吒捏捏乐'));
      
      if (nezhaProduct) {
        console.log('\n📦 哪吒捏捏乐商品信息:');
        console.log(`- 商品ID: ${nezhaProduct.id}`);
        console.log(`- 商品名称: ${nezhaProduct.name}`);
        console.log(`- 库存管理类型: ${nezhaProduct.stockManagementType}`);
        console.log(`- 总库存: ${nezhaProduct.stock}`);
        console.log(`- 总可用库存: ${nezhaProduct.totalAvailableStock}`);
        
        if (nezhaProduct.workplaceStocks && nezhaProduct.workplaceStocks.length > 0) {
          console.log('\n🏢 职场库存分布:');
          nezhaProduct.workplaceStocks.forEach(stock => {
            console.log(`- ${stock.workplaceName}: ${stock.availableStock} (库存: ${stock.stock}, 预留: ${stock.reservedStock})`);
          });
        } else {
          console.log('❌ 没有职场库存数据');
        }
      } else {
        console.log('❌ 未找到"哪吒捏捏乐"商品');
        
        // 显示前5个商品的信息作为参考
        console.log('\n📋 前5个商品的职场库存信息:');
        products.slice(0, 5).forEach(product => {
          console.log(`\n商品: ${product.name} (ID: ${product.id})`);
          console.log(`- 库存管理类型: ${product.stockManagementType}`);
          if (product.workplaceStocks && product.workplaceStocks.length > 0) {
            console.log('- 职场库存:');
            product.workplaceStocks.forEach(stock => {
              console.log(`  * ${stock.workplaceName}: ${stock.availableStock}`);
            });
          } else {
            console.log('- 无职场库存数据');
          }
        });
      }
    } else {
      console.log('❌ API返回数据格式异常');
      console.log('实际返回数据:', JSON.stringify(response.data, null, 2));
    }
  } catch (error) {
    console.log('❌ API调用失败:', error.response?.data?.message || error.message);
  }
}

/**
 * 测试职场列表API
 */
async function testWorkplacesAPI(token) {
  if (!token) {
    console.log('❌ 无有效token，跳过测试');
    return;
  }
  
  try {
    const config = {
      ...testConfig,
      headers: {
        ...testConfig.headers,
        'Authorization': `Bearer ${token}`
      }
    };
    
    console.log('\n🔍 测试职场列表API...');
    const response = await axios.get(`${BASE_URL}/workplaces`, config);
    
    if (response.data && response.data.data && Array.isArray(response.data.data)) {
      console.log(`✅ 成功获取 ${response.data.data.length} 个职场`);
      response.data.data.forEach(workplace => {
        console.log(`- ${workplace.name} (${workplace.code}) - ${workplace.isActive ? '活跃' : '非活跃'}`);
      });
    } else {
      console.log('❌ 职场API返回数据格式异常');
    }
  } catch (error) {
    console.log('❌ 职场API调用失败:', error.response?.data?.message || error.message);
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始库存管理API测试...\n');
  
  // 1. 管理员登录
  const token = await adminLogin();
  
  // 2. 测试职场列表API
  await testWorkplacesAPI(token);
  
  // 3. 测试商品库存API
  await testProductsWithStocks(token);
  
  console.log('\n📊 测试完成');
}

// 运行测试
runTests().catch(error => {
  console.error('测试执行失败:', error);
  process.exit(1);
});
