# ====================================
# 前端生产环境配置文件 (.env.production)
# ====================================
# 此文件用于前端生产环境构建和部署，请勿在本地开发环境使用
# 本地开发请使用 .env 文件
# 对应后端配置文件：server/.env.production

# ====================================
# 前端生产服务器配置
# ====================================
# 前端API基础URL（指向生产环境后端服务）
# 注意：根据实际部署情况选择合适的URL
# 选项1：通过Nginx代理访问(80端口) - 推荐
VITE_API_URL=http://**************/api
# 选项2：直接访问后端服务(3000端口)
# VITE_API_URL=http://**************:3000/api

# 应用标题
VITE_APP_TITLE=光年小卖部

# 应用基础路径
VITE_APP_BASE_URL=/

# ====================================
# 生产环境特定配置
# ====================================
# 运行环境标识
VITE_NODE_ENV=production

# 是否启用开发工具（生产环境建议关闭）
VITE_DEV_TOOLS=false

# 是否显示调试信息（生产环境建议关闭）
VITE_DEBUG=false

# API请求超时时间（毫秒）
VITE_API_TIMEOUT=15000

# ====================================
# 后端服务配置（用于前端调用）
# ====================================
# 后端服务器地址
VITE_SERVER_URL=http://**************:3000

# WebSocket连接地址（如果需要）
VITE_WS_URL=ws://**************:3000

# ====================================
# 飞书相关配置（前端使用）
# ====================================
# 飞书应用ID（前端可能需要）
VITE_FEISHU_APP_ID=cli_a66b3b2dcab8d013

# 飞书登录回调地址（前端路由）
VITE_FEISHU_CALLBACK_URL=http://**************/feishu/callback

# ====================================
# 性能优化配置
# ====================================
# 是否启用Gzip压缩
VITE_ENABLE_GZIP=true

# 是否启用代码分割
VITE_CODE_SPLITTING=true

# 是否启用Tree Shaking
VITE_TREE_SHAKING=true

# ====================================
# 安全配置
# ====================================
# 是否启用HTTPS（如果配置了SSL证书）
VITE_ENABLE_HTTPS=false

# CSP策略（内容安全策略）
VITE_CSP_ENABLED=true




