---
type: "manual"
---

# Project Details
- Project uses MySQL database named 'feishu_mall' with password 'password'.
- User prefers systematic file cleanup with careful preservation of core business logic and important documentation.
- User prefers systematic approach to bug fixing with detailed analysis phases, problem classification by severity, comprehensive testing, and maintaining code consistency while preserving database integrity.
- Project requires database version management with automatic backups for each schema update and maintains SQL update files under version control to prevent rollback issues.
- User prefers using existing real database data for analysis features instead of creating mock test data, with graceful handling when certain data types are missing.

# Naming Conventions and Sequelize
- Project uses mixed naming conventions (camelCase in frontend, snake_case in database) requiring Sequelize field mapping with 'field' attribute for proper data transmission between frontend and backend.

# Admin & Server Management
- Admin account credentials: name=超管, email=<EMAIL>, password=654321.
- User prefers to always use the restart.sh script in project root directory for restarting services instead of manual startup commands.
- Project deployment server: IP **************, user root, password Aa@123456, deploy directory /www/wwwroot/workyy, requires Gitee repository setup for version control and MySQL database synchronization.
- User prefers git-based deployment strategy (cloning from repository on server) over file upload methods for faster and more efficient deployments.
- User prefers deploying from feat/reset branch of Gitee repository, uses PM2 with application name 'feishu-mall-api', and requires removing nodejieba dependency during deployment to avoid compilation issues.
- Production environment uses server IP ************** with frontend on port 80 and backend API on port 3000, requiring CORS configuration updates while preserving all other settings like Feishu app config and database connections.

# Database Documentation
- User prefers database documentation to be split into two files: workflow documentation (docs/数据库开发工作流.md) for step-by-step processes and rules documentation (docs/数据库开发规则.md) for mandatory standards, both should include command examples, complete dev-to-deploy coverage, and emergency procedures.

# Debugging
- User prefers systematic debugging approach for dashboard issues with 4 phases: problem investigation (frontend/backend/API analysis), problem identification (error type/severity classification), fix implementation (maintaining architecture consistency and Sequelize mapping), and testing validation (unit tests + functional tests + full test suite).

# Dashboard Optimization
- User prefers comprehensive dashboard optimization following systematic standards: responsive layouts, consistent styling, proper error handling, performance optimization, and maintaining visual consistency across all modules.
- User prefers comprehensive dashboard statistics enhancement with focus on management decision-making value, dual payment system analysis (光年币/人民币), user behavior analytics, product operation optimization, and performance-conscious implementation based on existing database structure.
- User clarified that mixed payment refers to users using different payment methods across different orders (not within single orders).
- User prefers clear data validity indicators when data is insufficient.
- User wants accurate business logic reflection in payment preference analysis displays.
- User prefers enhanced UI for data visualization components with optimized spacing between cards, improved chart colors/gradients, better tooltips, unified styling across charts, responsive layouts, and polished loading/empty states for better user experience.
- User prefers table components to fully utilize available space with proper responsive behavior, complete content visibility through scrolling rather than truncation, and consistent visual styling across dashboard components.
- User prefers admin dashboard layout with sales workplace distribution cards side-by-side in same row, and user activity analysis card expanded to full width for better space utilization.