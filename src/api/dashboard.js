import api from './index';
import axios from 'axios';

// 获取原始API配置
const baseURL = api.defaults.baseURL || '';

// 创建带超时的请求实例
const apiWithTimeout = axios.create({
  baseURL: baseURL,
  timeout: 10000, // 10秒超时
  headers: {
    ...api.defaults.headers?.common, // 复制通用headers
    'Content-Type': 'application/json'
  }
});

// 添加请求拦截器来复制授权token
apiWithTimeout.interceptors.request.use(config => {
  // 从sessionStorage获取token，而不是localStorage
  const token = sessionStorage.getItem('token');
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`;
    console.log('[API] 正在发送带Token的请求:', config.url);
  } else {
    console.log('[API] 警告: 未找到Token，发送未授权请求:', config.url);
  }
  return config;
}, error => {
  return Promise.reject(error);
});

/**
 * 获取仪表盘综合统计数据
 * @returns {Promise<Object>} 返回销售额、活跃用户、商品总数、反馈数量等统计数据
 */
export function getDashboardStats() {
  console.log('[API] 调用仪表盘综合统计数据API');
  return apiWithTimeout.get('/exchanges/dashboard-stats')
    .then(response => {
      console.log('[API] 仪表盘统计数据API返回成功, 数据类型:', typeof response, '首条数据:',
                  JSON.stringify(response).substring(0, 100) + '...');

      // 确保返回的数据格式正确
      if (typeof response !== 'object') {
        console.error('[API] 仪表盘统计数据格式错误:', response);
        throw new Error('仪表盘统计数据格式错误');
      }

      return response.data || response;
    })
    .catch(error => {
      console.error('[API] 仪表盘统计数据API错误:', error);
      if (error.code === 'ECONNABORTED') {
        console.error('[API] 请求超时，返回默认值');
        // 返回默认数据而不是抛出错误
        return {
          totalSales: { value: 0, growth: 0, trend: 'up' },
          rmbSales: { value: 0, growth: 0, trend: 'up' },
          lySales: { value: 0, growth: 0, trend: 'up' },
          activeUsers: { value: 0, growth: 0, trend: 'up' },
          totalProducts: { value: 0, growth: 0, trend: 'up' },
          totalFeedbacks: { value: 0, growth: 0, trend: 'up' }
        };
      }
      throw error;
    });
}

/**
 * 获取销售趋势数据
 * @param {Object} params - 查询参数
 * @param {string} params.period - 时间周期，可选值：week, month, year
 * @returns {Promise<Object>} 返回销售趋势数据
 */
export function getSalesTrend(params = {}) {
  console.log('[API] 调用销售趋势数据API，参数:', params);
  return apiWithTimeout.get('/exchanges/sales-trend', { params })
    .then(response => {
      console.log('[API] 销售趋势数据API返回成功:', response);
      return response.data || response;
    })
    .catch(error => {
      console.error('[API] 销售趋势数据API错误:', error);
      if (error.code === 'ECONNABORTED') {
        // 返回默认的空数据
        return {
          timeLabels: [],
          lySalesData: [],
          rmbSalesData: [],
          orderData: [],
          period: params.period || 'month'
        };
      }
      throw error;
    });
}

/**
 * 获取库存变化趋势数据
 * @param {Object} params - 查询参数
 * @param {string} params.period - 时间周期，可选值：week, month, quarter
 * @param {string} params.category - 分类筛选，可选值：all 或具体分类ID
 * @returns {Promise<Object>} 返回库存趋势数据
 */
export function getStockTrend(params = {}) {
  console.log('[API] 调用库存趋势数据API，参数:', params);
  return apiWithTimeout.get('/exchanges/stock-trend', { params })
    .then(response => {
      console.log('[API] 库存趋势数据API返回成功:', response);
      return response.data || response;
    })
    .catch(error => {
      console.error('[API] 库存趋势数据API错误:', error);
      if (error.code === 'ECONNABORTED') {
        // 返回默认的空数据
        return {
          timeLabels: [],
          totalStockData: [],
          stockIncreaseData: [],
          stockDecreaseData: [],
          netChangeData: [],
          period: params.period || 'month',
          currentTotalStock: 0,
          categoryId: 'all'
        };
      }
      throw error;
    });
}

/**
 * 获取支付方式偏好分析数据
 * @param {Object} params - 查询参数
 * @param {string} params.period - 时间周期，可选值：week, month, quarter, year
 * @param {string} params.groupBy - 分组方式，可选值：category, user, time
 * @returns {Promise<Object>} 返回支付偏好分析数据
 */
export function getPaymentPreference(params = {}) {
  console.log('[API] 调用支付偏好分析数据API，参数:', params);
  return apiWithTimeout.get('/exchanges/payment-preference', { params })
    .then(response => {
      console.log('[API] 支付偏好分析数据API返回成功');

      // 检查响应结构，后端返回 {success: true, data: {...}}
      if (response.data && response.data.success && response.data.data) {
        return response.data.data;
      } else {
        console.warn('[API] 响应结构异常，返回原始数据');
        return response.data || response;
      }
    })
    .catch(error => {
      console.error('[API] 支付偏好分析数据API错误:', error);
      if (error.code === 'ECONNABORTED') {
        // 返回默认的空数据
        return {
          overview: {
            totalOrders: 0,
            rmbOrders: 0,
            lyOrders: 0,
            rmbPercentage: 0,
            lyPercentage: 0,
            rmbAmount: 0,
            lyAmount: 0
          },
          paymentTrend: [],
          categoryPreference: [],
          userSegments: {
            rmbOnly: 0,
            lyOnly: 0,
            mixed: 0,
            preferences: {
              highValueRmb: 0,
              frequentLy: 0
            }
          },
          period: params.period || 'month'
        };
      }
      throw error;
    });
}

/**
 * 获取用户活跃度数据
 * @param {Object} params - 查询参数
 * @param {string} params.period - 时间周期，可选值：7days, 30days, 90days
 * @returns {Promise<Object>} 返回用户活跃度数据
 */
export function getUserActivity(params = {}) {
  console.log('[API] 调用用户活跃度数据API，参数:', params);
  return apiWithTimeout.get('/users/activity', { params })
    .then(response => {
      console.log('[API] 用户活跃度数据API返回成功:', response);
      return response.data || response;
    })
    .catch(error => {
      console.error('[API] 用户活跃度数据API错误:', error);
      if (error.code === 'ECONNABORTED') {
        // 返回默认的空数据
        return {
          timeLabels: [],
          activeUsers: [],
          newUsers: []
        };
      }
      throw error;
    });
}

/**
 * 导出仪表盘数据
 * @param {Object} params - 导出参数
 * @param {string} params.format - 导出格式，可选值：excel, pdf
 * @param {string} params.period - 时间周期，可选值：week, month, year
 * @returns {Promise<Object>} 返回导出文件
 */
export function exportDashboardData(params = {}) {
  console.log('[API-DEBUG] 导出仪表盘数据API调用开始，参数:', JSON.stringify(params));

  // 验证参数
  if (!params.format || (params.format !== 'excel' && params.format !== 'pdf')) {
    console.error('[API-DEBUG] 导出格式无效:', params.format);
    return Promise.reject(new Error('导出格式必须是excel或pdf'));
  }

  // 从api获取实际使用的baseURL
  const currentBaseURL = api.defaults.baseURL || 'http://localhost:3000/api';
  console.log('[API-DEBUG] 导出使用的baseURL:', currentBaseURL);

  // 完整URL便于调试
  const fullUrl = `${currentBaseURL}/exports/dashboard?format=${params.format}&period=${params.period || 'month'}`;
  console.log('[API-DEBUG] 导出完整URL:', fullUrl);

  // 设置请求配置
  const config = {
    method: 'GET',
    url: '/exports/dashboard', // 不需要/api前缀，因为baseURL已经包含
    baseURL: currentBaseURL,
    params,
    responseType: 'blob',
    timeout: 30000, // 延长超时时间为30秒，因为文件生成可能需要更长时间
    headers: {
      ...api.defaults.headers?.common, // 直接使用api的headers
      'Accept': 'application/pdf,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/json'  // 明确指定接受的类型
    }
  };

  // 添加授权令牌 - 从sessionStorage获取token
  const token = sessionStorage.getItem('token');
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`;
    console.log('[API-DEBUG] 使用令牌授权，令牌长度:', token.length);
  } else {
    console.warn('[API-DEBUG] 警告: 未找到令牌，请求将失败!');
  }

  // 请求详情打印
  console.log('[API-DEBUG] 导出请求详情:', {
    url: config.url,
    baseURL: config.baseURL,
    method: config.method,
    params: config.params,
    responseType: config.responseType,
    timeout: config.timeout,
    headers: { ...config.headers, Authorization: config.headers.Authorization ? '已设置(已隐藏)' : '未设置' }
  });

  console.log('[API-DEBUG] 开始发送导出请求...');

  return axios(config)
    .then(response => {
      console.log('[API-DEBUG] 导出API响应状态:', response.status);
      console.log('[API-DEBUG] 导出API响应头:', JSON.stringify(response.headers));
      console.log('[API-DEBUG] 导出API响应类型:', response.headers['content-type']);
      console.log('[API-DEBUG] 导出API响应大小:', response.data ? response.data.size : '未知');

      // 检查响应类型
      const contentType = response.headers['content-type'];
      if (contentType && contentType.includes('application/json')) {
        console.error('[API-DEBUG] 错误: 导出返回JSON数据而不是文件');
        // 如果是JSON响应而不是文件，可能是错误消息
        return response.data.text().then(text => {
          console.error('[API-DEBUG] JSON错误响应内容:', text);
          try {
            const errorData = JSON.parse(text);
            throw new Error(errorData.message || '导出失败');
          } catch (e) {
            throw new Error('导出失败，服务器返回无效的响应');
          }
        });
      }

      // 成功返回Blob数据
      console.log('[API-DEBUG] 导出API调用成功，返回数据类型:', typeof response.data);
      return response;
    })
    .catch(error => {
      console.error('[API-DEBUG] 导出仪表盘数据API错误:', error.message);
      console.error('[API-DEBUG] 错误详情:', error);

      // 处理错误响应
      if (error.response) {
        console.error('[API-DEBUG] 错误状态码:', error.response.status);
        console.error('[API-DEBUG] 错误响应头:', JSON.stringify(error.response.headers || {}));

        // 使用Blob类型响应时，尝试读取内容
        if (error.response.data instanceof Blob) {
          const reader = new FileReader();
          reader.onload = function() {
            try {
              console.error('[API-DEBUG] Blob错误响应内容:', reader.result);
            } catch (e) {
              console.error('[API-DEBUG] 无法读取Blob错误响应内容');
            }
          };
          reader.readAsText(error.response.data);
        } else {
          console.error('[API-DEBUG] 错误响应内容:', error.response.data);
        }

        // 如果是401未授权错误，可能是令牌过期
        if (error.response.status === 401) {
          console.error('[API-DEBUG] 认证失败，令牌可能已过期');
          throw new Error('用户未授权，请重新登录');
        }

        // 500服务器错误
        if (error.response.status === 500) {
          console.error('[API-DEBUG] 服务器内部错误');
          throw new Error('服务器内部错误，导出失败');
        }
      }

      // 超时错误
      if (error.code === 'ECONNABORTED') {
        console.error('[API-DEBUG] 请求超时');
        throw new Error('导出请求超时，请稍后重试');
      }

      // 网络错误
      if (error.message.includes('Network Error')) {
        console.error('[API-DEBUG] 网络连接错误');
        throw new Error('网络错误，请检查您的网络连接');
      }

      throw error;
    });
}
