const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const HotProductHistory = sequelize.define('HotProductHistory', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  productId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  timeRange: {
    type: DataTypes.STRING(20),
    allowNull: false,
    validate: {
      isIn: [['all', '30d', '7d', '1d']]
    },
    comment: '时间维度'
  },
  hotScore: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '热门度评分'
  },
  rank: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '排名'
  },
  exchangeCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '该时间段内兑换量'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'hot_product_history',
  timestamps: false, // 只使用createdAt
  indexes: [
    {
      fields: ['productId', 'timeRange']
    },
    {
      fields: ['timeRange', 'rank']
    },
    {
      fields: ['createdAt']
    }
  ]
});

// 静态方法：清理过期历史记录
HotProductHistory.cleanupOldRecords = async function(daysToKeep = 90) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
  
  const deletedCount = await this.destroy({
    where: {
      createdAt: {
        [require('sequelize').Op.lt]: cutoffDate
      }
    }
  });
  
  console.log(`清理了 ${deletedCount} 条过期的热门商品历史记录`);
  return deletedCount;
};

// 静态方法：获取商品的历史排名趋势
HotProductHistory.getProductRankTrend = async function(productId, timeRange, days = 7) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return await this.findAll({
    where: {
      productId,
      timeRange,
      createdAt: {
        [require('sequelize').Op.gte]: startDate
      }
    },
    order: [['createdAt', 'ASC']],
    attributes: ['rank', 'hotScore', 'exchangeCount', 'createdAt']
  });
};

// 静态方法：获取时间维度的排行榜历史
HotProductHistory.getTimeRangeRankings = async function(timeRange, limit = 10, date = null) {
  const whereClause = { timeRange };
  
  if (date) {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);
    
    whereClause.createdAt = {
      [require('sequelize').Op.between]: [startOfDay, endOfDay]
    };
  }
  
  return await this.findAll({
    where: whereClause,
    include: [{
      model: require('./product'),
      attributes: ['id', 'name', 'lyPrice', 'rmbPrice', 'stock']
    }],
    order: [['rank', 'ASC']],
    limit
  });
};

module.exports = HotProductHistory;
