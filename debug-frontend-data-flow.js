/**
 * 前端数据流调试脚本
 * 用于检查库存转移后前端数据更新的完整流程
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:3000/api';

// 模拟前端请求配置
const testConfig = {
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  timeout: 15000
};

/**
 * 获取管理员token
 */
async function getAdminToken() {
  try {
    const loginData = {
      username: '超管',
      email: '<EMAIL>',
      password: '654321',
      userType: 'admin'
    };
    
    const response = await axios.post(`${BASE_URL}/auth/login`, loginData, testConfig);
    
    if (response.data && response.data.token) {
      console.log('✅ 获取管理员token成功');
      return response.data.token;
    } else {
      console.log('❌ 登录响应格式异常:', response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ 获取管理员token失败:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试前端API获取商品数据
 */
async function testFrontendProductsAPI(token) {
  try {
    console.log('\n🔍 测试前端商品API...');
    
    const config = {
      ...testConfig,
      headers: {
        ...testConfig.headers,
        'Authorization': `Bearer ${token}`
      }
    };
    
    // 模拟前端调用 getProductsWithStocks
    const response = await axios.get(`${BASE_URL}/products`, {
      ...config,
      params: {
        includeWorkplaceStocks: true,
        page: 1,
        limit: 50
      }
    });
    
    console.log('✅ 前端API调用成功');
    console.log('📊 响应数据结构:', {
      dataLength: response.data?.data?.length || 0,
      total: response.data?.total,
      hasData: !!response.data?.data
    });
    
    // 查找哪吒捏捏乐
    const nezhaProduct = response.data?.data?.find(p => p.name && p.name.includes('哪吒捏捏乐'));
    
    if (nezhaProduct) {
      console.log('✅ 找到哪吒捏捏乐商品');
      console.log('📦 商品基本信息:', {
        id: nezhaProduct.id,
        name: nezhaProduct.name,
        stockManagementType: nezhaProduct.stockManagementType,
        totalAvailableStock: nezhaProduct.totalAvailableStock
      });
      
      if (nezhaProduct.workplaceStocks && nezhaProduct.workplaceStocks.length > 0) {
        console.log('📍 职场库存分布:');
        nezhaProduct.workplaceStocks.forEach(stock => {
          console.log(`  - ${stock.workplaceName}: ${stock.availableStock}个 (总库存: ${stock.stock}, 预留: ${stock.reservedStock})`);
        });
        
        return nezhaProduct;
      } else {
        console.log('❌ 商品没有职场库存数据');
        return null;
      }
    } else {
      console.log('❌ 未找到哪吒捏捏乐商品');
      return null;
    }
  } catch (error) {
    console.log('❌ 前端API调用失败:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 执行库存转移测试
 */
async function performStockTransferTest(token, productId) {
  try {
    console.log('\n🔄 执行库存转移测试...');
    
    const config = {
      ...testConfig,
      headers: {
        ...testConfig.headers,
        'Authorization': `Bearer ${token}`
      }
    };
    
    const transferData = {
      productId: productId,
      fromWorkplaceId: 14, // 武汉
      toWorkplaceId: 1,    // 北京
      quantity: 1,
      reason: '前端数据流测试'
    };
    
    console.log('📤 发送转移请求:', transferData);
    
    const response = await axios.post(`${BASE_URL}/stocks/transfer`, transferData, config);
    
    if (response.data && response.data.success) {
      console.log('✅ 库存转移成功:', response.data.message);
      return true;
    } else {
      console.log('❌ 库存转移失败:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ 库存转移失败:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTest() {
  console.log('🚀 开始前端数据流调试测试...\n');
  
  // 1. 获取管理员token
  const token = await getAdminToken();
  if (!token) {
    console.log('❌ 无法获取token，测试终止');
    return;
  }
  
  // 2. 获取转移前的数据
  console.log('\n📋 步骤1: 获取转移前的商品数据');
  const beforeProduct = await testFrontendProductsAPI(token);
  if (!beforeProduct) {
    console.log('❌ 无法获取商品数据，测试终止');
    return;
  }
  
  // 3. 执行库存转移
  console.log('\n📋 步骤2: 执行库存转移');
  const transferSuccess = await performStockTransferTest(token, beforeProduct.id);
  if (!transferSuccess) {
    console.log('❌ 库存转移失败，测试终止');
    return;
  }
  
  // 4. 等待一下确保数据更新
  console.log('\n⏳ 等待2秒确保数据更新...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 5. 获取转移后的数据
  console.log('\n📋 步骤3: 获取转移后的商品数据');
  const afterProduct = await testFrontendProductsAPI(token);
  if (!afterProduct) {
    console.log('❌ 无法获取转移后的数据');
    return;
  }
  
  // 6. 对比数据变化
  console.log('\n📊 数据变化对比:');
  
  const beforeWuhan = beforeProduct.workplaceStocks.find(s => s.workplaceId === 14);
  const afterWuhan = afterProduct.workplaceStocks.find(s => s.workplaceId === 14);
  const beforeBeijing = beforeProduct.workplaceStocks.find(s => s.workplaceId === 1);
  const afterBeijing = afterProduct.workplaceStocks.find(s => s.workplaceId === 1);
  
  console.log('武汉职场:');
  console.log(`  转移前: ${beforeWuhan?.availableStock || 0}个`);
  console.log(`  转移后: ${afterWuhan?.availableStock || 0}个`);
  console.log(`  变化: ${(afterWuhan?.availableStock || 0) - (beforeWuhan?.availableStock || 0)}`);
  
  console.log('北京职场:');
  console.log(`  转移前: ${beforeBeijing?.availableStock || 0}个`);
  console.log(`  转移后: ${afterBeijing?.availableStock || 0}个`);
  console.log(`  变化: ${(afterBeijing?.availableStock || 0) - (beforeBeijing?.availableStock || 0)}`);
  
  // 7. 验证数据一致性
  const expectedWuhanDecrease = -1;
  const expectedBeijingIncrease = 1;
  const actualWuhanChange = (afterWuhan?.availableStock || 0) - (beforeWuhan?.availableStock || 0);
  const actualBeijingChange = (afterBeijing?.availableStock || 0) - (beforeBeijing?.availableStock || 0);
  
  if (actualWuhanChange === expectedWuhanDecrease && actualBeijingChange === expectedBeijingIncrease) {
    console.log('\n✅ 前端数据流测试通过！数据更新正确。');
  } else {
    console.log('\n❌ 前端数据流测试失败！数据更新不正确。');
    console.log(`期望武汉变化: ${expectedWuhanDecrease}, 实际: ${actualWuhanChange}`);
    console.log(`期望北京变化: ${expectedBeijingIncrease}, 实际: ${actualBeijingChange}`);
  }
  
  console.log('\n📋 测试完成');
}

// 运行测试
runTest().catch(error => {
  console.error('测试执行失败:', error);
});
