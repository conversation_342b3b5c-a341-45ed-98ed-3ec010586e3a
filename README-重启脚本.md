# 生产环境重启脚本

## 概述

本项目包含了光年小卖部生产环境的重启脚本，用于安全、可靠地重启生产服务。脚本设计为在生产服务器上直接执行，提供完整的服务管理功能。

## 文件结构

```
├── restart-production.sh              # 完整版重启脚本
├── restart-production-simple.sh       # 简化版重启脚本
├── deploy-restart-scripts.sh          # 脚本部署工具
└── docs/
    └── 生产环境重启脚本使用说明.md    # 详细使用说明
```

## 快速开始

### 1. 部署脚本到服务器

```bash
# 在本地执行，将脚本上传到服务器
./deploy-restart-scripts.sh
```

### 2. 在服务器上执行重启

```bash
# SSH连接到服务器
ssh root@**************

# 进入项目目录
cd /www/wwwroot/workyy

# 执行重启脚本
./restart-production-simple.sh    # 快速重启
# 或
./restart-production.sh           # 完整重启
```

## 脚本功能对比

| 功能 | 简化版 | 完整版 |
|------|--------|--------|
| 基本重启 | ✅ | ✅ |
| 状态检查 | ✅ | ✅ |
| 配置备份 | ❌ | ✅ |
| 数据库检查 | ❌ | ✅ |
| 健康验证 | 基础 | 完整 |
| 失败回滚 | ❌ | ✅ |
| 执行时间 | ~30秒 | ~2分钟 |

## 使用场景

### 日常维护
- 使用 `restart-production-simple.sh`
- 适合定期重启、配置更新后重启

### 重要更新
- 使用 `restart-production.sh`
- 适合版本发布、重大配置变更

### 故障恢复
- 优先使用 `restart-production.sh`
- 包含完整的诊断和回滚机制

## 服务架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Nginx       │    │      PM2        │    │     MySQL       │
│   (端口: 80)    │────│   (workyy)      │────│  (端口: 3306)   │
│   前端静态文件   │    │   后端API服务    │    │    数据库       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 监控命令

```bash
# PM2状态监控
pm2 list
pm2 monit
pm2 logs workyy --lines 50

# Nginx状态检查
systemctl status nginx
nginx -t

# 系统资源监控
htop
df -h
free -h

# 网络端口检查
netstat -tlnp | grep -E ':(80|3000)'
```

## 故障排除

### 常见问题

1. **权限问题**
   ```bash
   chmod +x restart-production*.sh
   ```

2. **PM2应用异常**
   ```bash
   pm2 logs workyy --lines 100
   pm2 restart workyy
   ```

3. **Nginx配置错误**
   ```bash
   nginx -t
   systemctl reload nginx
   ```

4. **端口占用**
   ```bash
   netstat -tlnp | grep :3000
   kill -9 <PID>
   ```

### 紧急恢复

```bash
# 手动重启所有服务
pm2 restart all
systemctl restart nginx

# 如果仍有问题，重启服务器
reboot
```

## 安全注意事项

1. **权限控制**: 脚本只能由root用户执行
2. **备份机制**: 完整版脚本会自动备份配置
3. **回滚保护**: 失败时自动回滚到之前状态
4. **日志记录**: 所有操作都有详细日志

## 性能优化

1. **定期维护**
   ```bash
   # 每周执行一次完整重启
   ./restart-production.sh
   
   # 清理日志文件
   pm2 flush
   ```

2. **资源监控**
   ```bash
   # 监控应用性能
   pm2 monit
   
   # 检查系统资源
   htop
   ```

## 技术支持

如遇到问题，请：

1. 查看详细日志
2. 参考故障排除指南
3. 保存错误信息
4. 联系技术团队

## 更新日志

- **2025-07-16**: 初始版本发布
  - 创建完整版和简化版重启脚本
  - 改为服务器本地执行模式
  - 添加完整的错误处理和回滚机制
  - 包含详细的使用说明和故障排除指南

---

**注意**: 在生产环境中执行任何重启操作前，请确保：
- 已通知相关人员
- 选择合适的维护时间窗口
- 确认数据已备份
- 准备好回滚方案
