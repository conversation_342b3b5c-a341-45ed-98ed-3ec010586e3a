# 数据库版本管理指南

## 📋 概述

为了避免数据库回退问题再次发生，本项目建立了完善的数据库版本管理机制，包括自动备份、迁移前检查和版本控制流程。

## 🔧 核心组件

### 1. 自动备份脚本
**文件**: `scripts/db/auto_backup.sh`

**功能**:
- 在每次数据库结构更新前自动创建备份
- 自动清理旧备份文件（保留最近30个）
- 记录备份日志到数据库和文件
- 支持备份文件大小统计

**使用方法**:
```bash
# 手动执行备份
./scripts/db/auto_backup.sh

# 备份文件位置
./backups/feishu_mall_backup_YYYYMMDD_HHMMSS.sql
```

### 2. 迁移前预检查脚本
**文件**: `scripts/db/pre_migration_check.sh`

**功能**:
- 检查数据库连接状态
- 验证目标数据库存在性
- 统计当前表数量和迁移记录
- 执行迁移前自动备份
- 显示当前Git状态

**使用方法**:
```bash
# 在执行迁移前运行
./scripts/db/pre_migration_check.sh

# 检查通过后执行迁移
cd server && npm run migrate
```

### 3. 数据库回退脚本
**文件**: `scripts/db/database_rollback_to_c3fa2fb.sql`

**功能**:
- 安全回退到指定版本的数据库状态
- 使用事务确保操作原子性
- 记录回退操作日志
- 验证回退后的数据完整性

## 📝 标准操作流程

### 数据库迁移标准流程

1. **迁移前检查**:
   ```bash
   ./scripts/db/pre_migration_check.sh
   ```

2. **执行迁移**:
   ```bash
   cd server
   npm run migrate
   ```

3. **验证迁移结果**:
   ```bash
   mysql -u root -ppassword feishu_mall -e "SELECT * FROM SequelizeMeta ORDER BY name;"
   ```

### 创建新迁移文件流程

1. **生成迁移文件**:
   ```bash
   cd server
   npx sequelize-cli migration:generate --name your-migration-name
   ```

2. **编写迁移内容**:
   - 在`up`方法中编写正向迁移逻辑
   - 在`down`方法中编写回退逻辑
   - 使用事务确保操作安全性

3. **测试迁移**:
   ```bash
   # 执行迁移前检查
   ./scripts/db/pre_migration_check.sh
   
   # 执行迁移
   npm run migrate
   
   # 测试回退
   npm run migrate:undo
   ```

## 🗂️ 备份管理

### 备份文件命名规范
```
feishu_mall_backup_YYYYMMDD_HHMMSS.sql
feishu_mall_backup_YYYYMMDD_HHMMSS_description.sql
```

### 备份保留策略
- **自动备份**: 保留最近30个备份文件
- **手动备份**: 重要操作前的备份永久保留
- **定期清理**: 每次备份时自动清理旧文件

### 备份恢复方法
```bash
# 恢复指定备份
mysql -u root -ppassword feishu_mall < backups/backup_file.sql

# 查看备份文件信息
ls -lah backups/ | grep feishu_mall_backup
```

## 📊 监控和日志

### 日志文件位置
- **备份日志**: `./logs/backup.log`
- **迁移日志**: `./logs/migration.log`
- **数据库日志**: 存储在`logs`表中

### 日志查询
```sql
-- 查看最近的数据库操作日志
SELECT * FROM logs 
WHERE action IN ('DATABASE_BACKUP', 'DATABASE_ROLLBACK', 'PRE_MIGRATION_CHECK') 
ORDER BY createdAt DESC 
LIMIT 10;
```

## ⚠️ 重要注意事项

### 迁移文件编写规范
1. **必须包含回退逻辑**: 每个`up`操作都要有对应的`down`操作
2. **使用事务**: 确保操作的原子性
3. **添加注释**: 详细说明迁移的目的和影响
4. **测试验证**: 在开发环境充分测试后再应用到生产环境

### 数据安全原则
1. **备份优先**: 任何结构性变更前必须备份
2. **分步执行**: 复杂迁移分解为多个小步骤
3. **验证确认**: 每次操作后验证结果
4. **回退准备**: 确保有可靠的回退方案

## 🚀 快速开始

### 首次使用设置
```bash
# 1. 确保脚本可执行
chmod +x scripts/db/*.sh

# 2. 创建必要目录
mkdir -p logs backups

# 3. 执行首次备份
./scripts/db/auto_backup.sh

# 4. 验证当前状态
./scripts/db/pre_migration_check.sh
```

### 日常使用
```bash
# 开发新功能前
./scripts/db/pre_migration_check.sh

# 执行迁移
cd server && npm run migrate

# 部署前备份
./scripts/db/auto_backup.sh
```

## 📞 故障处理

### 常见问题解决
1. **备份失败**: 检查磁盘空间和数据库连接
2. **迁移失败**: 查看迁移日志，必要时执行回退
3. **权限问题**: 确保脚本有执行权限和数据库访问权限

### 紧急恢复流程
1. 停止应用服务
2. 恢复最近的备份
3. 验证数据完整性
4. 重启应用服务
5. 记录故障原因和解决方案
