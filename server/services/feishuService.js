const axios = require('axios');
const feishuConfig = require('../config/feishu');
const { User, Workplace } = require('../models');
const bcrypt = require('bcryptjs');

/**
 * 飞书服务类 - 处理与飞书API的交互
 */
class FeishuService {
  /**
   * 生成飞书授权URL
   * @param {String} state - 状态参数，用于防止CSRF攻击
   * @returns {String} - 授权URL
   */
  generateAuthUrl(state = '') {
    console.log('飞书服务: 生成授权URL, 应用凭证:', {
      appId: feishuConfig.appId,
      redirectUri: feishuConfig.redirectUri
    });

    // 使用配置文件中的重定向URI
    const redirectUri = feishuConfig.redirectUri;

    const params = new URLSearchParams({
      app_id: feishuConfig.appId,
      redirect_uri: redirectUri,
      state: state
    });

    const fullUrl = `${feishuConfig.authorizeUrl}?${params.toString()}`;
    console.log('飞书服务: 完整授权URL:', fullUrl);

    return fullUrl;
  }

  /**
   * 使用授权码获取访问令牌
   * @param {String} code - 授权码
   * @returns {Promise<Object>} - 包含访问令牌的对象
   */
  async getAccessToken(code) {
    try {
      console.log('飞书服务: 正在用授权码获取访问令牌...');

      // 构建请求数据
      const requestData = {
        grant_type: feishuConfig.grantType,
        code: code,
        app_id: feishuConfig.appId,
        app_secret: feishuConfig.appSecret
      };

      console.log('飞书服务: 请求数据:', JSON.stringify(requestData));

      const response = await axios.post(feishuConfig.accessTokenUrl, requestData, {
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        }
      });

      console.log('飞书服务: 访问令牌响应:', JSON.stringify(response.data));

      if (response.data.code !== 0) {
        throw new Error(`飞书API错误: ${response.data.msg}, 错误码: ${response.data.code}`);
      }

      return response.data;
    } catch (error) {
      console.error('飞书服务: 获取访问令牌失败', error.response?.data || error.message);
      throw new Error('获取飞书访问令牌失败');
    }
  }

  /**
   * 获取用户信息
   * @param {String} accessToken - 用户访问令牌
   * @returns {Promise<Object>} - 用户信息
   */
  async getUserInfo(accessToken) {
    try {
      console.log('飞书服务: 正在获取用户信息...');

      const response = await axios.get(feishuConfig.userInfoUrl, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('飞书服务: 用户信息响应状态码:', response.status);
      console.log('飞书服务: 用户信息完整响应:', JSON.stringify(response.data, null, 2));

      if (response.data.code !== 0) {
        throw new Error(`飞书API错误: ${response.data.msg}, 错误码: ${response.data.code}`);
      }

      // 特别检查城市相关字段
      if (response.data.data) {
        const userData = response.data.data;
        console.log('飞书服务: 检查城市相关字段:');

        // 特别检查手机号字段
        console.log(`- mobile: ${userData.mobile || '未设置'}`);
        console.log(`- mobile_visible: ${userData.mobile_visible || '未设置'}`);

        if (!userData.mobile && userData.mobile_visible) {
          console.log('飞书服务: 警告 - mobile_visible为true但没有mobile字段，可能权限不足');
        }

        // 检查工作城市字段（只检查实际使用的字段）
        console.log(`- city: ${userData.city || '未设置'}`);

        // 记录城市信息
        if (userData.city) {
          console.log(`飞书服务: ✅ 找到工作城市信息: ${userData.city}`);
          userData.user_city = userData.city;
        } else {
          console.log('飞书服务: ⚠️  用户信息API中没有城市信息');
        }
      }

      // 尝试获取用户的详细信息（包括部门信息）
      if (response.data.data && response.data.data.open_id) {
        try {
          console.log('飞书服务: 使用open_id获取用户详细信息:', response.data.data.open_id);
          const detailInfo = await this.getUserDetailInfo(accessToken, response.data.data.open_id);
          // 合并详细信息到基本信息中
          if (detailInfo && detailInfo.data && detailInfo.data.user) {
            console.log('飞书服务: 获取到用户详细信息，合并数据');
            const detailUser = detailInfo.data.user;

            // 检查企业邮箱是否存在
            if (detailUser.enterprise_email) {
              console.log(`飞书服务: 找到企业邮箱: ${detailUser.enterprise_email}`);
            } else {
              console.log('飞书服务: 企业邮箱为空，将使用基本信息中的邮箱');
            }

            // 检查城市/工作地点是否存在
            if (detailUser.city) {
              console.log(`飞书服务: 找到城市/工作地点: ${detailUser.city}`);
            } else {
              console.log('飞书服务: 城市/工作地点为空');
            }

            // 保留原有信息，合并详细信息
            response.data.data = {
              ...response.data.data,
              ...detailUser,
              // 以企业邮箱为优先，如果企业邮箱为空，则保留原来的个人邮箱
              email: detailUser.enterprise_email || response.data.data.email || detailUser.email,
              // 确保正确记录企业邮箱
              enterprise_email: detailUser.enterprise_email,
              // 记录部门IDs和名称到返回信息中
              department_ids: detailUser.department_ids || [],
              // 记录职务信息
              job_title: detailUser.job_title,
              // 记录城市/工作地点信息
              city: detailUser.city
            };

            console.log('飞书服务: 合并后的用户数据:', JSON.stringify(response.data.data, null, 2));
            console.log('飞书服务: 最终使用的邮箱:', response.data.data.email);
            console.log('飞书服务: 最终使用的城市/工作地点:', response.data.data.city || '未提供');
          }
        } catch (detailError) {
          console.error('飞书服务: 获取用户详细信息失败，继续使用基本信息', detailError.message);
          console.log('飞书服务: 错误详情:', detailError);

          // 如果是权限问题，记录特别提示
          if (detailError.message.includes('Access denied') || detailError.response?.data?.code === 99991672) {
            console.warn('飞书服务: 权限不足，无法获取用户详细信息，请在飞书开放平台为应用添加通讯录相关权限，如contact:contact.base:readonly');
          }

          // 权限不足时设置默认值
          response.data.data = {
            ...response.data.data,
            department_ids: [],
            enterprise_email: null,
            city: null
          };

          // 获取详细信息失败不影响主流程，继续使用基本信息
        }
      }

      return response.data;
    } catch (error) {
      console.error('飞书服务: 获取用户信息失败', error.response?.data || error.message);
      throw new Error('获取飞书用户信息失败');
    }
  }

  /**
   * 获取用户详细信息
   * @param {String} accessToken - 访问令牌
   * @param {String} openId - 用户的Open ID
   * @returns {Promise<Object>} - 用户详细信息
   */
  async getUserDetailInfo(accessToken, openId) {
    try {
      console.log('飞书服务: 正在获取用户详细信息...');

      // 构建正确的API请求参数，确保获取部门ID
      const response = await axios.get(`${feishuConfig.userDetailUrl}/${openId}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        params: {
          user_id_type: 'open_id',
          department_id_type: 'open_department_id'  // 使用open_department_id格式的部门ID
        }
      });

      console.log('飞书服务: 用户详细信息响应:', JSON.stringify(response.data));

      if (response.data.code !== 0) {
        throw new Error(`飞书API错误: ${response.data.msg}, 错误码: ${response.data.code}`);
      }

      // 检查并记录企业邮箱和部门信息
      if (response.data.data && response.data.data.user) {
        const user = response.data.data.user;
        console.log('飞书服务: 用户详细信息中的关键字段:');
        console.log(`- 用户ID: ${user.user_id}`);
        console.log(`- 姓名: ${user.name}`);
        console.log(`- 企业邮箱: ${user.enterprise_email || '未提供'}`);
        console.log(`- 个人邮箱: ${user.email || '未提供'}`);
        console.log(`- 城市/工作地点: ${user.city || '未提供'}`);

        // 特别记录部门ID信息
        if (user.department_ids && user.department_ids.length > 0) {
          console.log(`- 部门IDs: ${JSON.stringify(user.department_ids)}`);
          console.log(`- 主要部门ID: ${user.department_ids[0]}`);
        } else {
          console.log('- 部门IDs: 未提供或为空');
        }

        console.log(`- 职务: ${user.job_title || '未提供'}`);
        console.log(`- 手机号: ${user.mobile || '未提供'}`);
        console.log(`- 手机号可见性: ${user.mobile_visible || '未设置'}`);

        // 特别检查手机号字段
        if (!user.mobile) {
          console.log('飞书服务: 警告 - 用户详细信息中没有手机号，这可能是因为:');
          console.log('1. 该用户在飞书中未设置手机号');
          console.log('2. 应用权限不足，需要申请contact:user.phone:readonly权限');
          console.log('3. 用户隐私设置不允许显示手机号');
        } else {
          console.log(`飞书服务: ✅ 成功获取用户手机号: ${user.mobile}`);
        }

        // 记录所有返回字段，帮助排查问题
        console.log('飞书服务: 用户详细信息完整数据:', JSON.stringify(user, null, 2));

        // 特别检查部门ID
        if (!user.department_ids || user.department_ids.length === 0) {
          console.log('飞书服务: 警告 - 用户没有部门ID信息，这可能是因为:');
          console.log('1. 该用户在飞书中未被分配到任何部门');
          console.log('2. 应用权限不足，需要申请通讯录相关权限');
        }

        if (!user.enterprise_email) {
          console.log('飞书服务: 警告 - 用户没有企业邮箱，这可能是因为:');
          console.log('1. 该用户在飞书中未设置企业邮箱');
          console.log('2. 应用权限不足，需要申请mail:user_mailbox:readonly权限');
        }
      }

      return response.data;
    } catch (error) {
      console.error('飞书服务: 获取用户详细信息失败', error.response?.data || error.message);
      throw new Error('获取飞书用户详细信息失败');
    }
  }

  /**
   * 通过飞书OpenID查找用户
   * @param {String} openId - 飞书OpenID
   * @returns {Promise<Object|null>} - 用户对象或null
   */
  async findUserByFeishuOpenId(openId) {
    return await User.findOne({ where: { feishuOpenId: openId } });
  }

  /**
   * 通过飞书UnionID查找用户
   * @param {String} unionId - 飞书UnionID
   * @returns {Promise<Object|null>} - 用户对象或null
   */
  async findUserByFeishuUnionId(unionId) {
    return await User.findOne({ where: { feishuUnionId: unionId } });
  }

  /**
   * 通过飞书邮箱查找用户
   * @param {String} email - 飞书用户邮箱
   * @returns {Promise<Object|null>} - 用户对象或null
   */
  async findUserByEmail(email) {
    return await User.findOne({ where: { email } });
  }

  /**
   * 获取部门名称
   * @param {string} departmentId 部门ID
   * @param {string} tenantAccessToken 企业自建应用的tenant_access_token
   * @returns {Promise<string>} 部门名称，如果获取失败则返回部门ID
   */
  async getDepartmentName(departmentId, tenantAccessToken) {
    try {
      // 尝试获取部门详情信息
      console.log(`飞书服务: 尝试获取部门名称, 部门ID: ${departmentId}`);

      // 调用修改后的API，包含"user_id_type"参数
      const departmentUrl = `${feishuConfig.departmentInfoUrl}/${departmentId}`;
      const response = await axios.get(departmentUrl, {
        headers: {
          'Authorization': `Bearer ${tenantAccessToken}`,
          'Content-Type': 'application/json; charset=utf-8'
        },
        params: {
          'department_id_type': 'open_department_id',
          'user_id_type': 'open_id'  // 添加用户ID类型参数
        }
      });

      if (!response.data || response.data.code !== 0) {
        console.error(`飞书服务: 获取部门名称失败, 部门ID: ${departmentId}`, JSON.stringify(response.data));
        return `部门ID: ${departmentId}`;
      }

      // 检查部门名称是否存在
      if (response.data.data && response.data.data.department && response.data.data.department.name) {
        const name = response.data.data.department.name;
        console.log(`飞书服务: 成功获取部门名称: ${name}`);
        return name;
      } else {
        // 尝试I18n字段中的名称
        if (response.data.data && response.data.data.department && response.data.data.department.i18n_name &&
            response.data.data.department.i18n_name.zh_cn) {
          const name = response.data.data.department.i18n_name.zh_cn;
          console.log(`飞书服务: 从i18n字段获取部门名称: ${name}`);
          return name;
        }

        // 如果找不到名称，则记录完整的部门信息用于排查
        console.log(`飞书服务: 部门数据中没有name字段:`, JSON.stringify(response.data.data.department, null, 2));

        // 尝试通过部门搜索API获取部门名称
        try {
          const searchUrl = 'https://open.feishu.cn/open-apis/contact/v3/departments/search';
          const searchResponse = await axios.post(searchUrl, {
            "query": departmentId,
            "page_size": 1
          }, {
            headers: {
              'Authorization': `Bearer ${tenantAccessToken}`,
              'Content-Type': 'application/json; charset=utf-8'
            },
            params: {
              'department_id_type': 'open_department_id',
              'user_id_type': 'open_id'
            }
          });

          if (searchResponse.data.code === 0 &&
              searchResponse.data.data &&
              searchResponse.data.data.items &&
              searchResponse.data.data.items.length > 0 &&
              searchResponse.data.data.items[0].name) {
            const name = searchResponse.data.data.items[0].name;
            console.log(`飞书服务: 通过搜索API获取到部门名称: ${name}`);
            return name;
          }
        } catch (searchError) {
          console.error('飞书服务: 搜索部门名称失败:', searchError.message);
        }

        // 如果还是获取不到，则返回部门ID作为名称
        return `部门ID: ${departmentId}`;
      }
    } catch (error) {
      console.error(`飞书服务: 获取部门名称出错:`, error.message);
      return `部门ID: ${departmentId}`;
    }
  }

  /**
   * 获取用户部门信息
   * @param {string} userId 用户ID
   * @param {string} tenantAccessToken 企业自建应用的tenant_access_token
   * @returns {Promise<Object>} 部门信息
   */
  async getDepartmentInfo(userId, tenantAccessToken) {
    try {
      console.log('飞书服务: 开始获取用户部门信息, 用户ID:', userId);

      // 首先获取用户所在的部门ID列表
      const userDetailUrl = `${feishuConfig.userDetailUrl}/${userId}`;
      console.log('飞书服务: 请求用户详情URL:', userDetailUrl);

      // 正确设置user_id_type和department_id_type参数
      const userDetailResponse = await axios.get(userDetailUrl, {
        headers: {
          'Authorization': `Bearer ${tenantAccessToken}`,
          'Content-Type': 'application/json; charset=utf-8'
        },
        params: {
          'user_id_type': 'open_id',  // 指定使用open_id类型的用户ID
          'department_id_type': 'open_department_id'  // 使用open_department_id格式的部门ID
        }
      });

      console.log('飞书服务: 获取用户部门响应:', JSON.stringify(userDetailResponse.data));

      if (!userDetailResponse.data || userDetailResponse.data.code !== 0) {
        console.error('飞书服务: 获取用户部门失败:', JSON.stringify(userDetailResponse.data));
        return {
          departmentId: null,
          departmentName: '未知部门',
          departmentPath: '未知部门路径'
        };
      }

      // 获取用户的部门ID列表 - 检查并确保数据结构正确
      let departmentIds = [];
      if (userDetailResponse.data.data &&
          userDetailResponse.data.data.user &&
          userDetailResponse.data.data.user.department_ids) {
        departmentIds = userDetailResponse.data.data.user.department_ids;
      }

      console.log('飞书服务: 获取到用户部门ID列表:', departmentIds);

      if (departmentIds.length === 0) {
        console.log('飞书服务: 用户没有部门信息');
        return {
          departmentId: null,
          departmentName: '未分配部门',
          departmentPath: '未分配部门'
        };
      }

      // 获取主部门信息（取第一个部门作为主部门）
      const primaryDeptId = departmentIds[0];

      try {
        // 获取部门名称
        const departmentName = await this.getDepartmentName(primaryDeptId, tenantAccessToken);
        console.log(`飞书服务: 获取到部门名称: ${departmentName}`);

        // 尝试获取部门路径
        let departmentPath = null;
        try {
          departmentPath = await this.getDepartmentPath(primaryDeptId, tenantAccessToken);
          console.log('飞书服务: 获取到部门路径:', departmentPath);
        } catch (pathError) {
          console.error('飞书服务: 获取部门路径失败:', pathError.message);

          // 权限错误时设置友好消息
          if (pathError.response && pathError.response.data && pathError.response.data.code === 40004) {
            console.log('飞书服务: 部门路径获取权限不足，请检查应用权限范围设置');
            departmentPath = departmentName; // 使用部门名称作为路径
          } else {
            departmentPath = departmentName; // 使用部门名称作为路径
          }
        }

        // 返回部门信息
        const result = {
          departmentId: primaryDeptId,
          departmentName: departmentName,
          departmentPath: departmentPath || departmentName
        };

        console.log('飞书服务: 最终返回的部门信息:', JSON.stringify(result));
        return result;
      } catch (deptError) {
        console.error('飞书服务: 获取部门详情出错:', deptError.message);

        // 权限错误时记录特别提示
        if (deptError.response && deptError.response.data && deptError.response.data.code === 40004) {
          console.log('飞书服务: 部门访问权限不足，请在飞书开放平台检查应用的"可访问的数据范围"设置');
          console.log('飞书服务: 请在飞书开放平台 -> 开发者后台 -> 权限管理 -> 可访问的数据范围中，将应用设置为"全部部门"或添加特定部门');
        }

        // 返回一个只包含部门ID的结果
        return {
          departmentId: primaryDeptId,
          departmentName: `部门ID: ${primaryDeptId}`,
          departmentPath: `部门ID: ${primaryDeptId}`
        };
      }
    } catch (error) {
      console.error('飞书服务: 获取部门信息出错:', error);
      console.error('飞书服务: 详细错误:', error.response?.data || error.message);

      // 返回默认值
      return {
        departmentId: null,
        departmentName: '未知部门',
        departmentPath: '未知部门路径'
      };
    }
  }

  /**
   * 获取部门路径（完整层级）
   * @param {string} departmentId 部门ID
   * @param {string} tenantAccessToken 企业自建应用的tenant_access_token
   * @returns {Promise<string>} 完整的部门路径，如"公司/技术部/后端组"
   */
  async getDepartmentPath(departmentId, tenantAccessToken) {
    try {
      console.log(`飞书服务: 开始获取部门路径, 部门ID: ${departmentId}`);
      const departmentPath = [];
      let currentDeptId = departmentId;

      // 循环获取父部门，直到根部门
      let maxIterations = 10; // 防止无限循环
      let iteration = 0;

      while (currentDeptId && maxIterations > 0) {
        iteration++;
        console.log(`飞书服务: 获取部门路径迭代 ${iteration}/${maxIterations}, 当前部门ID: ${currentDeptId}`);

        // 获取当前部门的名称
        const deptName = await this.getDepartmentName(currentDeptId, tenantAccessToken);
        console.log(`飞书服务: 当前部门名称: ${deptName}`);

        // 添加当前部门名称到路径前面（因为我们是从下到上获取）
        departmentPath.unshift(deptName);

        try {
          // 获取部门详情以获取父部门ID
          const departmentUrl = `${feishuConfig.departmentInfoUrl}/${currentDeptId}`;
          console.log(`飞书服务: 请求部门信息URL: ${departmentUrl}`);

          const response = await axios.get(departmentUrl, {
            headers: {
              'Authorization': `Bearer ${tenantAccessToken}`,
              'Content-Type': 'application/json; charset=utf-8'
            },
            params: {
              'department_id_type': 'open_department_id'
            }
          });

          if (!response.data || response.data.code !== 0) {
            console.error(`飞书服务: 获取部门信息失败, 部门ID: ${currentDeptId}`, JSON.stringify(response.data));
            break;
          }

          // 获取父部门ID，如果没有父部门则退出循环
          const parentDepartmentId = response.data.data.department.parent_department_id;

          if (!parentDepartmentId || parentDepartmentId === '0') {
            console.log('飞书服务: 已到达组织根部门，路径获取完成');
            break;
          }

          // 更新当前部门ID为父部门ID，继续循环
          currentDeptId = parentDepartmentId;
          maxIterations--;
        } catch (pathError) {
          console.error(`飞书服务: 获取部门 ${currentDeptId} 父级信息失败:`, pathError.message);

          // 权限错误特别处理
          if (pathError.response && pathError.response.data && pathError.response.data.code === 40004) {
            console.log('飞书服务: 部门访问权限不足，无法获取完整的部门路径');
            console.log('飞书服务: 请检查应用在飞书开放平台的"可访问的数据范围"设置');

            // 如果已经获取了一些部门信息，则返回已有的部门路径
            if (departmentPath.length > 0) {
              break; // 中断循环，使用已有的部分路径
            } else {
              // 如果一个部门也没获取到，则抛出权限错误
              throw new Error('部门访问权限不足，请检查应用权限范围设置');
            }
          }

          // 其他错误则中断路径获取
          break;
        }
      }

      // 如果路径为空，返回null
      if (departmentPath.length === 0) {
        console.log('飞书服务: 未能获取到有效的部门路径');
        return null;
      }

      // 将部门路径数组使用"/"连接成字符串
      const pathString = departmentPath.join('/');
      console.log(`飞书服务: 获取到完整部门路径: ${pathString}`);

      return pathString;
    } catch (error) {
      console.error('飞书服务: 获取部门路径出错:', error.message);
      if (error.response) {
        console.error('飞书服务: 错误状态码:', error.response.status);
        console.error('飞书服务: 错误响应数据:', JSON.stringify(error.response.data));
      }
      console.error('飞书服务: 详细错误:', error);

      // 重新抛出错误，让调用者决定如何处理
      throw error;
    }
  }

  /**
   * 获取用户所在一级部门信息
   * @param {String} accessToken - 访问令牌
   * @param {Array} departmentIds - 用户的部门ID列表
   * @returns {Promise<String>} - 部门名称
   */
  async getUserDepartmentName(accessToken, departmentIds) {
    if (!departmentIds || !departmentIds.length) {
      console.log('飞书服务: 用户没有关联部门信息');
      return null;
    }

    try {
      // 存储部门树结构
      const departmentMap = new Map();
      // 记录一级部门
      let rootDepartment = null;

      // 依次获取每个部门的信息，找到一级部门
      for (let i = 0; i < departmentIds.length; i++) {
        const deptId = departmentIds[i];
        const department = await this.getDepartmentInfo(accessToken, deptId);

        if (!department) {
          continue;
        }

        departmentMap.set(department.department_id, department);

        // 如果是一级部门（parent_department_id为0或不存在）
        if (!department.parent_department_id || department.parent_department_id === '0') {
          console.log(`飞书服务: 找到用户所在一级部门: ${department.name}`);
          rootDepartment = department;
          break;
        }

        // 如果不是一级部门，查找其父部门直到找到一级部门
        let currentDeptId = department.parent_department_id;
        let maxIterations = 10; // 防止循环

        while (currentDeptId && currentDeptId !== '0' && maxIterations > 0) {
          // 检查是否已经获取过该部门
          if (departmentMap.has(currentDeptId)) {
            const parentDept = departmentMap.get(currentDeptId);
            if (!parentDept.parent_department_id || parentDept.parent_department_id === '0') {
              rootDepartment = parentDept;
              break;
            }
            currentDeptId = parentDept.parent_department_id;
          } else {
            // 未获取过，则获取该部门信息
            const parentDept = await this.getDepartmentInfo(accessToken, currentDeptId);
            if (!parentDept) {
              break;
            }

            departmentMap.set(parentDept.department_id, parentDept);

            if (!parentDept.parent_department_id || parentDept.parent_department_id === '0') {
              rootDepartment = parentDept;
              break;
            }
            currentDeptId = parentDept.parent_department_id;
          }

          maxIterations--;
        }

        if (rootDepartment) {
          break;
        }
      }

      if (rootDepartment) {
        console.log(`飞书服务: 用户所在一级部门名称: ${rootDepartment.name}, ID: ${rootDepartment.department_id}`);
        return rootDepartment.name;
      }

      // 如果没有找到一级部门，返回第一个部门的名称
      const firstDeptId = departmentIds[0];
      const firstDept = await this.getDepartmentInfo(accessToken, firstDeptId);

      if (firstDept) {
        console.log(`飞书服务: 未找到一级部门，使用直接部门名称: ${firstDept.name}`);
        return firstDept.name;
      }

      console.log('飞书服务: 未能获取到有效的部门信息');
      return null;
    } catch (error) {
      console.error('飞书服务: 获取用户部门名称失败', error.message);
      return null;
    }
  }

  /**
   * 创建或更新飞书用户
   * @param {Object} userInfo - 飞书用户信息
   * @param {Object} tokenInfo - 飞书令牌信息
   * @param {Object} req - 请求对象，用于获取IP地址
   * @returns {Promise<Object>} - 创建或更新的用户对象
   */
  async createOrUpdateFeishuUser(userInfo, tokenInfo, req = null) {
    const { open_id, union_id, name, en_name, email, avatar_url, mobile } = userInfo;
    const { access_token, refresh_token, expires_in } = tokenInfo;

    console.log('飞书服务: 正在创建或更新用户...');
    console.log('用户信息:', JSON.stringify(userInfo, null, 2));

    // 获取客户端IP地址
    let ipAddress = '';
    if (req) {
      ipAddress = req.headers['x-forwarded-for'] ||
                  req.headers['x-real-ip'] ||
                  req.ip ||
                  req.connection.remoteAddress ||
                  '';

      // 处理IPv6格式的IP地址（如 ::ffff:127.0.0.1）
      if (ipAddress.includes('::ffff:')) {
        ipAddress = ipAddress.split('::ffff:')[1];
      }

      // 处理本地IPv6地址
      if (ipAddress === '::1') {
        ipAddress = '127.0.0.1';
      }

      // 如果是逗号分隔的多个IP（X-Forwarded-For可能包含多个IP），取第一个
      if (ipAddress && ipAddress.includes(',')) {
        ipAddress = ipAddress.split(',')[0].trim();
      }
    }

    console.log(`飞书服务: 获取到登录IP: ${ipAddress}`);

    // 记录关键邮箱信息
    console.log('飞书服务: 用户邮箱信息检查:');
    console.log(`- 基础信息中的邮箱: ${email || '未提供'}`);
    console.log(`- 企业邮箱: ${userInfo.enterprise_email || '未提供'}`);
    console.log(`- 手机号码: ${mobile || '未提供'}`);

    // 检查工作城市字段（只检查实际使用的字段）
    console.log('飞书服务: 工作城市信息检查:');
    let userWorkPlace = userInfo.city || null;

    if (userWorkPlace) {
      console.log(`✅ 找到工作城市: ${userWorkPlace}`);
    } else {
      console.log('⚠️  用户信息中未找到工作城市字段');
    }

    // 确定最终使用的邮箱 - 优先使用企业邮箱
    const userEmail = userInfo.enterprise_email || email;
    console.log(`飞书服务: 将使用邮箱: ${userEmail || '未找到有效邮箱，将生成临时邮箱'}`);

    // 设置令牌过期时间
    const expiresTime = new Date();
    expiresTime.setSeconds(expiresTime.getSeconds() + expires_in);

    // 首先尝试通过OpenID查找用户
    let user = await this.findUserByFeishuOpenId(open_id);

    // 如果找不到，则尝试通过UnionID查找
    if (!user && union_id) {
      user = await this.findUserByFeishuUnionId(union_id);
    }

    // 如果找不到，则尝试通过邮箱查找（优先使用企业邮箱）
    if (!user && userEmail) {
      user = await this.findUserByEmail(userEmail);
    }

    // 获取用户部门信息
    let departmentName = null;
    let departmentPath = null;

    try {
      // 获取tenant_access_token
      console.log('飞书服务: 开始获取tenant_access_token...');

      const tenantTokenURL = 'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal';
      console.log('飞书服务: 请求tenant_access_token的URL:', tenantTokenURL);
      console.log('飞书服务: 使用的应用ID:', feishuConfig.appId);

      const tenantTokenResponse = await axios.post(tenantTokenURL, {
        app_id: feishuConfig.appId,
        app_secret: feishuConfig.appSecret
      }, {
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        }
      });

      console.log('飞书服务: tenant_access_token响应状态码:', tenantTokenResponse.status);
      console.log('飞书服务: tenant_access_token响应:', JSON.stringify(tenantTokenResponse.data));

      if (tenantTokenResponse.data.code === 0 && tenantTokenResponse.data.tenant_access_token) {
        const tenantAccessToken = tenantTokenResponse.data.tenant_access_token;
        console.log('飞书服务: 成功获取tenant_access_token');

        // 通过企业访问令牌获取用户详细信息（包括部门、手机号等）
        console.log('飞书服务: 获取用户完整详细信息...');
        try {
          const userDetailUrl = `https://open.feishu.cn/open-apis/contact/v3/users/${open_id}`;
          const userDetailResponse = await axios.get(userDetailUrl, {
            headers: {
              'Authorization': `Bearer ${tenantAccessToken}`,
              'Content-Type': 'application/json; charset=utf-8'
            },
            params: {
              'user_id_type': 'open_id',
              'department_id_type': 'open_department_id'
            }
          });

          if (userDetailResponse.data.code === 0 && userDetailResponse.data.data?.user) {
            const detailUser = userDetailResponse.data.data.user;
            console.log('飞书服务: 用户详细信息:', JSON.stringify(detailUser, null, 2));

            // 获取部门信息
            if (detailUser.department_ids && detailUser.department_ids.length > 0) {
              const primaryDepartmentId = detailUser.department_ids[0];
          console.log('飞书服务: 获取用户部门信息，主要部门ID:', primaryDepartmentId);

          // 获取部门详情
          const departmentResult = await this.getDepartmentInfo(open_id, tenantAccessToken);
          departmentName = departmentResult.departmentName;
          departmentPath = departmentResult.departmentPath;

          console.log(`飞书服务: 获取到用户部门名称: ${departmentName}`);
          console.log(`飞书服务: 获取到用户部门路径: ${departmentPath}`);
        } else {
          console.log('飞书服务: 用户没有部门ID信息，无法获取部门名称');
            }

            // 检查并更新手机号码信息
            if (detailUser.mobile && detailUser.mobile.trim() !== '') {
              console.log(`飞书服务: 从详细信息中找到手机号码: ${detailUser.mobile}`);
              // 将手机号码信息合并到userInfo中，以便后续使用
              userInfo.mobile = detailUser.mobile;
            }

            // 检查并更新职务信息
            if (detailUser.job_title && detailUser.job_title.trim() !== '') {
              console.log(`飞书服务: 从详细信息中找到职务: ${detailUser.job_title}`);
              userInfo.job_title = detailUser.job_title;
            }

            // 检查工作城市字段（如果之前没有找到）
            if (!userWorkPlace && detailUser.city) {
              userWorkPlace = detailUser.city;
              console.log(`✅ 通过企业访问令牌找到工作城市: ${userWorkPlace}`);
            }
          }
        } catch (detailError) {
          console.error('飞书服务: 获取用户详细信息失败:', detailError.message);
        }

        // 原有的工作地点获取逻辑保留作为备用
        if (!userWorkPlace) {
          console.log('飞书服务: 尝试通过企业访问令牌重新获取用户工作地点信息...');
          try {
            const userDetailUrl = `https://open.feishu.cn/open-apis/contact/v3/users/${open_id}`;
            const userDetailResponse = await axios.get(userDetailUrl, {
              headers: {
                'Authorization': `Bearer ${tenantAccessToken}`,
                'Content-Type': 'application/json; charset=utf-8'
              },
              params: {
                'user_id_type': 'open_id',
                'department_id_type': 'open_department_id'
              }
            });

            if (userDetailResponse.data.code === 0 && userDetailResponse.data.data?.user) {
              const detailUser = userDetailResponse.data.data.user;

              // 检查工作城市字段
              if (detailUser.city) {
                userWorkPlace = detailUser.city;
                console.log(`✅ 通过企业访问令牌找到工作城市: ${userWorkPlace}`);
              } else {
                console.log('⚠️  企业访问令牌也无法获取到工作城市信息');
              }
            }
          } catch (detailError) {
            console.error('飞书服务: 通过企业访问令牌获取工作地点失败:', detailError.message);
          }
        }
      } else {
        console.error('飞书服务: 获取tenant_access_token失败:', tenantTokenResponse.data);
      }
    } catch (error) {
      console.error('飞书服务: 获取部门信息过程中出错:', error.message);
      console.log('飞书服务: 继续处理用户，但无法设置部门信息');
    }

    // 确定用户所在职场（城市） - 直接使用飞书返回的城市信息
    let workplace = null;
    let workplaceId = null;

    console.log('飞书服务: 开始职场获取流程...');

    // 直接使用飞书返回的工作地点信息
    if (userWorkPlace) {
      console.log(`飞书服务: 从飞书API获取到工作地点: ${userWorkPlace}`);

      try {
        // 查找是否存在匹配的职场
        const matchingWorkplace = await Workplace.findOne({
          where: {
            name: userWorkPlace,
            isActive: true
          }
        });

        if (matchingWorkplace) {
          workplace = matchingWorkplace.name;
          workplaceId = matchingWorkplace.id;
          console.log(`飞书服务: ✅ 直接匹配到职场: ${workplace}, ID: ${workplaceId}`);
        } else {
          // 查找可能的部分匹配
          const workplaces = await Workplace.findAll({
            where: { isActive: true }
          });

          for (const wp of workplaces) {
            if (userWorkPlace.includes(wp.name) || wp.name.includes(userWorkPlace)) {
              workplace = wp.name;
              workplaceId = wp.id;
              console.log(`飞书服务: ✅ 部分匹配到职场: ${workplace}, ID: ${workplaceId} (原始工作地点: ${userWorkPlace})`);
              break;
            }
          }

          // 如果仍未匹配到，记录原始工作地点值但不设置ID
          if (!workplace) {
            workplace = userWorkPlace;
            console.log(`飞书服务: ⚠️  未找到匹配的职场记录，使用原始工作地点: ${workplace}`);
          }
        }
      } catch (error) {
        console.error('飞书服务: 查找匹配职场出错:', error.message);
        workplace = userWorkPlace; // 使用原始值作为备用
      }
      } else {
      console.log('飞书服务: ⚠️  未能从飞书API获取到工作地点信息');
    }

    // 如果用户存在，则更新用户
    if (user) {
      console.log('飞书服务: 找到现有用户，正在更新...');

      // 检查用户是否被禁用
      if (user.isActive === false) {
        console.log(`飞书服务: 用户已被禁用，拒绝登录: ID=${user.id}, 用户名=${user.username}`);
        throw new Error('账号已被禁用，请联系管理员');
      }

      user.feishuOpenId = open_id;
      user.feishuUnionId = union_id;
      user.feishuAvatar = avatar_url;
      user.feishuAccessToken = access_token;
      user.feishuRefreshToken = refresh_token;
      user.feishuTokenExpireTime = expiresTime;

      // 更新用户信息（如果提供）
      if (name && !user.username) user.username = name;

      // 优先使用企业邮箱更新用户邮箱
      if (userEmail && (!user.email || user.email.includes('@guanghe.tv'))) {
        user.email = userEmail;
        console.log(`飞书服务: 更新用户邮箱为: ${userEmail}`);
      }

      // 更新手机号码（如果提供）
      const currentMobile = userInfo.mobile || mobile; // 使用最新的手机号码信息
      if (currentMobile && (!user.mobile || user.mobile === '')) {
        user.mobile = currentMobile.startsWith('+86') ? currentMobile.substring(3) : currentMobile;
        console.log(`飞书服务: 更新用户手机号为: ${user.mobile}`);
      }

      // 更新部门信息
      if (departmentName) {
        user.department = departmentName;
        console.log(`飞书服务: 更新用户部门为: ${departmentName}`);
      } else if (!user.department) {
        user.department = '飞书用户';
      }

      // 更新部门路径信息
      if (departmentPath) {
        user.departmentPath = departmentPath;
        console.log(`飞书服务: 更新用户部门路径为: ${departmentPath}`);
      } else if (departmentName && !user.departmentPath) {
        user.departmentPath = departmentName; // 如果没有完整路径，使用部门名称
      }

      // 更新职场信息（如果确定了职场）
      if (workplace) {
        user.workplace = workplace;
        console.log(`飞书服务: 更新用户职场为: ${workplace}`);

        if (workplaceId) {
          user.workplaceId = workplaceId;
          console.log(`飞书服务: 更新用户职场ID为: ${workplaceId}`);
        }
      }

      // 更新职务信息
      if (userInfo.job_title && (!user.jobTitle || user.jobTitle === '')) {
        user.jobTitle = userInfo.job_title;
        console.log(`飞书服务: 更新用户职务为: ${userInfo.job_title}`);
      }

      // 如果原来是密码用户，保持不变，否则设为飞书用户
      if (!user.authType || user.authType === 'password') {
        // 保持原有的认证类型
      } else {
        user.authType = 'feishu';
      }

      // 更新登录时间和IP
      user.lastLoginAt = new Date();
      if (ipAddress) {
        user.lastLoginIp = ipAddress;
      }

      console.log(`飞书服务: 更新用户登录信息: 时间=${new Date().toISOString()}, IP=${ipAddress}`);

      await user.save();
      console.log('飞书服务: 用户更新成功, ID:', user.id);
    } else {
      // 如果用户不存在，则创建新用户
      console.log('飞书服务: 未找到现有用户，创建新用户...');

      // 构建用户对象
      const userData = {
        username: name || `飞书用户_${open_id.substring(0, 8)}`,
        email: userEmail || `feishu_${open_id.substring(0, 8)}@temp.com`,
        password: await bcrypt.hash(Math.random().toString(36).substring(2, 15), 10),
        role: 'user',
        feishuOpenId: open_id,
        feishuUnionId: union_id,
        feishuAvatar: avatar_url,
        feishuAccessToken: access_token,
        feishuRefreshToken: refresh_token,
        feishuTokenExpireTime: expiresTime,
        authType: 'feishu',
        department: departmentName || '飞书用户',
        workplace: workplace,
        workplaceId: workplaceId,
        lastLoginAt: new Date()
      };

      // 添加部门路径（如果存在）
      if (departmentPath) {
        userData.departmentPath = departmentPath;
      }

      // 添加手机号码（如果存在）
      const currentMobile = userInfo.mobile || mobile; // 使用最新的手机号码信息
      if (currentMobile) {
        userData.mobile = currentMobile.startsWith('+86') ? currentMobile.substring(3) : currentMobile;
      }

      // 添加职务信息（如果存在）
      if (userInfo.job_title) {
        userData.jobTitle = userInfo.job_title;
      }

      // 添加登录IP（如果存在）
      if (ipAddress) {
        userData.lastLoginIp = ipAddress;
      }

      // 创建用户
      user = await User.create(userData);
      console.log('飞书服务: 用户创建成功, ID:', user.id);

      // 触发新用户欢迎通知
      try {
        const operationalNotificationService = require('./operationalNotificationService');
        await operationalNotificationService.sendNewUserWelcome(user);
      } catch (notificationError) {
        console.error('飞书服务: 发送新用户欢迎通知失败:', notificationError);
        // 不抛出错误，避免影响用户创建流程
      }
    }

    return user;
  }
}

module.exports = new FeishuService();
