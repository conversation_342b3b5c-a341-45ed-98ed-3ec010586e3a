'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // 1. 扩展products表，添加自动热门商品相关字段
      await queryInterface.addColumn('products', 'isAutoHot', {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '自动识别的热门商品'
      }, { transaction });

      await queryInterface.addColumn('products', 'hotTimeRange', {
        type: Sequelize.STRING(20),
        allowNull: true,
        comment: '热门时间维度：all/30d/7d/1d'
      }, { transaction });

      await queryInterface.addColumn('products', 'hotScore', {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0,
        comment: '热门度评分'
      }, { transaction });

      await queryInterface.addColumn('products', 'hotRank', {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '热门排名'
      }, { transaction });

      await queryInterface.addColumn('products', 'lastHotUpdate', {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '最后热门状态更新时间'
      }, { transaction });

      // 2. 创建热门商品配置表
      await queryInterface.createTable('hot_product_configs', {
        id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true
        },
        timeRange: {
          type: Sequelize.STRING(20),
          allowNull: false,
          comment: '时间维度：all/30d/7d/1d'
        },
        enabled: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: true,
          comment: '是否启用该时间维度'
        },
        maxCount: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 10,
          comment: '该时间维度的热门商品数量上限'
        },
        minExchangeCount: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 1,
          comment: '最小兑换量要求'
        },
        exchangeWeight: {
          type: Sequelize.DECIMAL(3, 2),
          allowNull: false,
          defaultValue: 1.0,
          comment: '兑换量权重'
        },
        stockWeight: {
          type: Sequelize.DECIMAL(3, 2),
          allowNull: false,
          defaultValue: 0.1,
          comment: '库存权重'
        },
        autoUpdateEnabled: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: true,
          comment: '是否自动更新'
        },
        updateFrequency: {
          type: Sequelize.STRING(50),
          allowNull: false,
          defaultValue: '0 */1 * * *',
          comment: '更新频率(cron表达式)'
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
        }
      }, { transaction });

      // 3. 创建热门商品历史记录表
      await queryInterface.createTable('hot_product_history', {
        id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true
        },
        productId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          references: {
            model: 'products',
            key: 'id'
          },
          onDelete: 'CASCADE'
        },
        timeRange: {
          type: Sequelize.STRING(20),
          allowNull: false,
          comment: '时间维度'
        },
        hotScore: {
          type: Sequelize.DECIMAL(10, 2),
          allowNull: false,
          comment: '热门度评分'
        },
        rank: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: '排名'
        },
        exchangeCount: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: '该时间段内兑换量'
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        }
      }, { transaction });

      // 4. 添加索引
      await queryInterface.addIndex('hot_product_configs', ['timeRange'], {
        unique: true,
        name: 'unique_time_range',
        transaction
      });

      await queryInterface.addIndex('hot_product_history', ['productId', 'timeRange'], {
        name: 'idx_product_time',
        transaction
      });

      await queryInterface.addIndex('hot_product_history', ['timeRange', 'rank'], {
        name: 'idx_time_rank',
        transaction
      });

      await queryInterface.addIndex('products', ['isAutoHot'], {
        name: 'idx_is_auto_hot',
        transaction
      });

      await queryInterface.addIndex('products', ['hotTimeRange'], {
        name: 'idx_hot_time_range',
        transaction
      });

      // 5. 插入默认配置数据
      await queryInterface.bulkInsert('hot_product_configs', [
        {
          timeRange: 'all',
          enabled: true,
          maxCount: 15,
          minExchangeCount: 1,
          exchangeWeight: 1.0,
          stockWeight: 0.1,
          autoUpdateEnabled: true,
          updateFrequency: '0 */2 * * *',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          timeRange: '30d',
          enabled: true,
          maxCount: 10,
          minExchangeCount: 1,
          exchangeWeight: 1.0,
          stockWeight: 0.1,
          autoUpdateEnabled: true,
          updateFrequency: '0 */1 * * *',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          timeRange: '7d',
          enabled: true,
          maxCount: 8,
          minExchangeCount: 1,
          exchangeWeight: 1.0,
          stockWeight: 0.1,
          autoUpdateEnabled: true,
          updateFrequency: '0 */1 * * *',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          timeRange: '1d',
          enabled: true,
          maxCount: 5,
          minExchangeCount: 1,
          exchangeWeight: 1.0,
          stockWeight: 0.1,
          autoUpdateEnabled: true,
          updateFrequency: '0 */1 * * *',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ], { transaction });

      await transaction.commit();
      console.log('✅ 热门商品功能数据库迁移完成');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ 热门商品功能数据库迁移失败:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // 删除表
      await queryInterface.dropTable('hot_product_history', { transaction });
      await queryInterface.dropTable('hot_product_configs', { transaction });

      // 删除products表的新增字段
      await queryInterface.removeColumn('products', 'isAutoHot', { transaction });
      await queryInterface.removeColumn('products', 'hotTimeRange', { transaction });
      await queryInterface.removeColumn('products', 'hotScore', { transaction });
      await queryInterface.removeColumn('products', 'hotRank', { transaction });
      await queryInterface.removeColumn('products', 'lastHotUpdate', { transaction });

      await transaction.commit();
      console.log('✅ 热门商品功能数据库回滚完成');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ 热门商品功能数据库回滚失败:', error);
      throw error;
    }
  }
};
