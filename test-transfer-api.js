#!/usr/bin/env node

/**
 * 直接测试库存转移API
 */

import axios from 'axios';

// API配置
const API_BASE = 'http://localhost:3000/api';

// 测试数据
const transferData = {
  productId: 15,
  fromWorkplaceId: 1,
  toWorkplaceId: 14,
  quantity: 1,
  reason: '直接API测试'
};

async function testTransferAPI() {
  console.log('🔍 开始测试库存转移API...');
  console.log('测试数据:', transferData);
  
  try {
    // 1. 首先测试API连接
    console.log('\n1. 测试API连接...');
    const testResponse = await axios.get(`${API_BASE}/test-stock-api`);
    console.log('✅ API连接正常:', testResponse.data);
    
    // 2. 测试转移API（无认证）
    console.log('\n2. 测试转移API（无认证）...');
    try {
      const transferResponse = await axios.post(`${API_BASE}/test-stocks/transfer`, transferData);
      console.log('✅ 转移API响应:', transferResponse.data);
    } catch (error) {
      console.log('❌ 无认证转移失败（预期）:', error.response?.data || error.message);
    }
    
    // 3. 获取管理员token（模拟登录）
    console.log('\n3. 获取管理员token...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: '654321'
    });
    
    if (loginResponse.data.success) {
      const token = loginResponse.data.data.token;
      console.log('✅ 登录成功，token长度:', token.length);
      
      // 4. 使用token测试转移API
      console.log('\n4. 使用token测试转移API...');
      const authTransferResponse = await axios.post(`${API_BASE}/stocks/transfer`, transferData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ 认证转移API响应:', authTransferResponse.data);
      
      if (authTransferResponse.data.success) {
        console.log('🎉 库存转移测试成功！');
        console.log('转移结果:', authTransferResponse.data.data);
      } else {
        console.log('❌ 转移失败:', authTransferResponse.data.message);
      }
      
    } else {
      console.log('❌ 登录失败:', loginResponse.data);
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    if (error.response) {
      console.error('错误响应:', error.response.data);
      console.error('状态码:', error.response.status);
    }
  }
}

// 运行测试
testTransferAPI();
