# 环境配置文件使用指南

## 概述

本项目采用环境特定的配置文件管理方式，确保本地开发和生产环境配置完全分离，避免配置混淆导致的问题。

## 配置文件结构

### 前端配置文件

```
项目根目录/
├── .env                    # 本地开发环境配置
└── .env.production         # 生产环境配置（前端构建用）
```

### 后端配置文件

```
server/
├── .env                    # 本地开发环境配置
├── .env.production         # 生产环境配置
└── config/
    └── envLoader.js        # 环境配置加载器
```

## 配置文件详解

### 1. 本地开发环境配置

**文件：** `.env` 和 `server/.env`

**用途：** 本地开发时使用，包含localhost相关配置

**关键配置项：**
- `NODE_ENV=development`
- `SERVER_URL=http://localhost:3000`
- `FEISHU_REDIRECT_URI=http://localhost:3000/api/feishu/callback`
- `CORS_ORIGIN=http://localhost:5173,http://localhost:5174,...`

### 2. 生产环境配置

**文件：** `.env.production` 和 `server/.env.production`

**用途：** 生产环境部署时使用，包含生产服务器相关配置

**关键配置项：**
- `NODE_ENV=production`
- `SERVER_URL=http://**************:3000`
- `FEISHU_REDIRECT_URI=http://**************:3000/api/feishu/callback`
- `CORS_ORIGIN=http://**************,http://**************:80`

## 配置加载机制

### 自动环境检测

项目使用 `server/config/envLoader.js` 自动检测和加载对应环境的配置：

1. **检测NODE_ENV环境变量**
2. **根据环境加载对应配置文件**
   - `development` → 加载 `.env`
   - `production` → 加载 `.env.production`
3. **验证必需配置项**
4. **显示配置加载状态**

### 配置加载优先级

1. 环境变量（最高优先级）
2. 环境特定配置文件（`.env.production` 或 `.env`）
3. 默认值（最低优先级）

## 使用方法

### 本地开发

```bash
# 1. 确保使用开发环境配置
export NODE_ENV=development

# 2. 启动后端服务
cd server
npm run dev

# 3. 启动前端服务
npm run dev
```

**自动加载：** `server/.env`

### 生产环境部署

```bash
# 1. 设置生产环境
export NODE_ENV=production

# 2. 启动服务
cd server
npm start
# 或使用PM2
pm2 start server.js --name feishu-mall-api
```

**自动加载：** `server/.env.production`

### 手动指定配置文件

如果需要手动指定配置文件：

```bash
# 使用特定配置文件启动
NODE_ENV=production node server.js
```

## 配置文件管理

### 新增配置项

1. **同时更新两个配置文件**
   - `server/.env`（开发环境）
   - `server/.env.production`（生产环境）

2. **保持配置结构一致**
   - 相同的配置项名称
   - 相同的分组和注释结构

3. **更新配置验证**
   - 在 `envLoader.js` 中添加必需配置项验证

### 敏感信息处理

1. **开发环境**
   - 使用测试用的密钥和凭证
   - 可以提交到版本控制

2. **生产环境**
   - 使用真实的密钥和凭证
   - 不要提交到版本控制
   - 在服务器上手动创建或通过CI/CD部署

## 常见问题

### Q1: 配置文件不生效怎么办？

**检查步骤：**
1. 确认NODE_ENV环境变量设置正确
2. 检查配置文件路径和文件名
3. 查看控制台的配置加载日志
4. 验证配置文件语法是否正确

### Q2: 如何切换环境？

**方法一：设置环境变量**
```bash
export NODE_ENV=production  # 切换到生产环境
export NODE_ENV=development # 切换到开发环境
```

**方法二：启动时指定**
```bash
NODE_ENV=production npm start
```

### Q3: 配置文件缺失怎么办？

系统会自动处理：
1. 生产环境配置缺失 → 自动使用开发环境配置作为备用
2. 开发环境配置缺失 → 报错并退出
3. 显示详细的错误信息和建议

### Q4: 如何验证配置是否正确？

启动应用时查看控制台输出：
```
====== 环境配置加载器 ======
当前环境: production
配置文件: .env.production
✅ 成功加载配置文件: .env.production
====== 关键配置信息 ======
NODE_ENV: production
SERVER_URL: http://**************:3000
...
====== 配置加载完成 ======
```

## 最佳实践

### 1. 配置文件维护

- **定期同步**：确保两个环境的配置文件结构保持一致
- **注释完整**：为每个配置项添加清晰的注释说明
- **分组管理**：按功能模块对配置项进行分组

### 2. 安全考虑

- **敏感信息**：生产环境的敏感配置不要提交到版本控制
- **权限控制**：限制生产环境配置文件的访问权限
- **定期更新**：定期更换JWT密钥等安全相关配置

### 3. 部署流程

- **自动化部署**：使用脚本自动处理配置文件切换
- **配置验证**：部署前验证配置文件的完整性
- **回滚准备**：保留配置文件的备份以便快速回滚

## 配置项参考

### 必需配置项

| 配置项 | 说明 | 开发环境示例 | 生产环境示例 |
|--------|------|-------------|-------------|
| NODE_ENV | 运行环境 | development | production |
| PORT | 服务端口 | 3000 | 3000 |
| SERVER_URL | 服务器URL | http://localhost:3000 | http://**************:3000 |
| DB_HOST | 数据库主机 | localhost | localhost |
| DB_NAME | 数据库名称 | feishu_mall | feishu_mall |
| JWT_SECRET | JWT密钥 | 开发用密钥 | 生产用密钥 |
| FEISHU_APP_ID | 飞书应用ID | cli_a66b3b2dcab8d013 | cli_a66b3b2dcab8d013 |
| FEISHU_REDIRECT_URI | 飞书回调URL | http://localhost:3000/api/feishu/callback | http://**************:3000/api/feishu/callback |

### 可选配置项

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| LOG_LEVEL | 日志级别 | debug (开发) / info (生产) |
| MAX_FILE_SIZE | 最大文件大小 | 5242880 |
| UPLOAD_DIR | 上传目录 | uploads |

---

**注意：** 修改配置文件后需要重启应用才能生效。在生产环境中，建议使用PM2等进程管理工具进行无缝重启。
