/**
 * 商品管理系统核心功能综合测试
 * 测试订单编号生成、商品调价、订单导出三个核心功能
 */

const { sequelize } = require('../server/config/database');
const { Product, Exchange, ProductPriceHistory, User, Category } = require('../server/models');

// 测试数据
let testUser, testCategory, testProduct, testExchanges = [];

async function setupTestData() {
  console.log('=== 设置测试数据 ===');

  // 创建测试用户
  testUser = await User.create({
    username: 'test_core_user',
    email: '<EMAIL>',
    password: 'test123',
    role: 'admin',
    points: 1000
  });

  // 创建测试分类
  testCategory = await Category.create({
    name: '测试分类_核心功能',
    description: '用于核心功能测试的分类'
  });

  // 创建测试商品
  testProduct = await Product.create({
    name: '测试商品_核心功能',
    categoryId: testCategory.id,
    lyPrice: 100,
    rmbPrice: 25.00,
    description: '用于测试核心功能的商品',
    stock: 50,
    status: 'active'
  });

  console.log(`测试数据创建完成: 用户ID=${testUser.id}, 商品ID=${testProduct.id}`);
}

async function testOrderNumberGeneration() {
  console.log('\n=== 测试订单编号生成功能 ===');

  try {
    // 1. 测试光年币订单编号生成
    console.log('\n1. 测试光年币订单编号生成...');

    const lyExchange = await Exchange.create({
      userId: testUser.id,
      productId: testProduct.id,
      quantity: 1,
      unitPrice: testProduct.lyPrice,
      priceType: 'ly',
      paymentMethod: 'ly',
      contactInfo: '测试联系方式',
      location: '测试地址',
      status: 'pending'
    });

    await lyExchange.reload();
    testExchanges.push(lyExchange);

    if (!lyExchange.orderNumber) {
      throw new Error('光年币订单编号未生成');
    }

    if (!lyExchange.orderNumber.startsWith('GNB-')) {
      throw new Error(`光年币订单编号格式错误: ${lyExchange.orderNumber}`);
    }

    console.log(`✅ 光年币订单编号生成成功: ${lyExchange.orderNumber}`);

    // 2. 测试人民币订单编号生成
    console.log('\n2. 测试人民币订单编号生成...');

    const rmbExchange = await Exchange.create({
      userId: testUser.id,
      productId: testProduct.id,
      quantity: 2,
      unitPrice: parseFloat(testProduct.rmbPrice),
      priceType: 'rmb',
      paymentMethod: 'rmb',
      contactInfo: '测试联系方式2',
      location: '测试地址2',
      status: 'pending'
    });

    await rmbExchange.reload();
    testExchanges.push(rmbExchange);

    if (!rmbExchange.orderNumber) {
      throw new Error('人民币订单编号未生成');
    }

    if (!rmbExchange.orderNumber.startsWith('RMB-')) {
      throw new Error(`人民币订单编号格式错误: ${rmbExchange.orderNumber}`);
    }

    console.log(`✅ 人民币订单编号生成成功: ${rmbExchange.orderNumber}`);

    // 3. 测试订单编号唯一性
    console.log('\n3. 测试订单编号唯一性...');

    const duplicateCheck = await Exchange.findAll({
      where: {
        orderNumber: [lyExchange.orderNumber, rmbExchange.orderNumber]
      }
    });

    if (duplicateCheck.length !== 2) {
      throw new Error('订单编号唯一性检查失败');
    }

    console.log('✅ 订单编号唯一性验证通过');

    // 4. 测试价格快照
    console.log('\n4. 测试价格快照机制...');

    if (!lyExchange.unitPrice || !rmbExchange.unitPrice) {
      throw new Error('价格快照未保存');
    }

    if (parseFloat(lyExchange.unitPrice) !== testProduct.lyPrice) {
      throw new Error(`光年币价格快照错误: 期望 ${testProduct.lyPrice}, 实际 ${lyExchange.unitPrice}`);
    }

    if (parseFloat(rmbExchange.unitPrice) !== parseFloat(testProduct.rmbPrice)) {
      throw new Error(`人民币价格快照错误: 期望 ${testProduct.rmbPrice}, 实际 ${rmbExchange.unitPrice}`);
    }

    console.log('✅ 价格快照机制验证通过');

    return true;
  } catch (error) {
    console.error('\n❌ 订单编号生成测试失败:', error.message);
    return false;
  }
}

async function testProductPriceAdjustment() {
  console.log('\n=== 测试商品调价功能 ===');

  try {
    // 1. 记录原始价格
    const originalLyPrice = testProduct.lyPrice;
    const originalRmbPrice = testProduct.rmbPrice;

    console.log(`\n1. 原始价格: 光年币=${originalLyPrice}, 人民币=${originalRmbPrice}`);

    // 2. 更新商品价格
    console.log('\n2. 更新商品价格...');

    const newLyPrice = 150;
    const newRmbPrice = 35.00;

    await testProduct.update({
      lyPrice: newLyPrice,
      rmbPrice: newRmbPrice
    }, {
      userId: testUser.id,
      changeReason: '测试价格调整'
    });

    console.log(`价格已更新: 光年币=${newLyPrice}, 人民币=${newRmbPrice}`);

    // 3. 验证价格历史记录
    console.log('\n3. 验证价格历史记录...');

    const priceHistory = await ProductPriceHistory.findOne({
      where: { productId: testProduct.id },
      order: [['createdAt', 'DESC']]
    });

    if (!priceHistory) {
      throw new Error('价格历史记录未创建');
    }

    if (priceHistory.oldLyPrice !== originalLyPrice || priceHistory.newLyPrice !== newLyPrice) {
      throw new Error(`光年币价格历史记录错误`);
    }

    if (parseFloat(priceHistory.oldRmbPrice) !== parseFloat(originalRmbPrice) ||
        parseFloat(priceHistory.newRmbPrice) !== parseFloat(newRmbPrice)) {
      throw new Error(`人民币价格历史记录错误`);
    }

    console.log('✅ 价格历史记录验证通过');

    // 4. 验证历史订单价格不变
    console.log('\n4. 验证历史订单价格不变...');

    for (const exchange of testExchanges) {
      await exchange.reload();

      if (exchange.paymentMethod === 'ly' && parseFloat(exchange.unitPrice) !== originalLyPrice) {
        throw new Error(`历史光年币订单价格被错误修改`);
      }

      if (exchange.paymentMethod === 'rmb' && parseFloat(exchange.unitPrice) !== parseFloat(originalRmbPrice)) {
        throw new Error(`历史人民币订单价格被错误修改`);
      }
    }

    console.log('✅ 历史订单价格保持不变');

    // 5. 创建新订单验证使用新价格
    console.log('\n5. 创建新订单验证使用新价格...');

    const newExchange = await Exchange.create({
      userId: testUser.id,
      productId: testProduct.id,
      quantity: 1,
      unitPrice: parseFloat(newRmbPrice),
      priceType: 'rmb',
      paymentMethod: 'rmb',
      contactInfo: '测试联系方式3',
      location: '测试地址3',
      status: 'pending'
    });

    await newExchange.reload();
    testExchanges.push(newExchange);

    if (parseFloat(newExchange.unitPrice) !== parseFloat(newRmbPrice)) {
      throw new Error(`新订单价格错误: 期望 ${newRmbPrice}, 实际 ${newExchange.unitPrice}`);
    }

    console.log('✅ 新订单使用新价格');

    return true;
  } catch (error) {
    console.error('\n❌ 商品调价测试失败:', error.message);
    return false;
  }
}

async function testOrderExport() {
  console.log('\n=== 测试订单导出功能 ===');

  try {
    // 1. 测试数据完整性
    console.log('\n1. 验证导出数据完整性...');

    const allExchanges = await Exchange.findAll({
      where: { userId: testUser.id },
      include: [
        { model: Product, attributes: ['name', 'lyPrice', 'rmbPrice'] },
        { model: User, attributes: ['username', 'email', 'department'] }
      ]
    });

    if (allExchanges.length !== testExchanges.length) {
      throw new Error(`订单数量不匹配: 期望 ${testExchanges.length}, 实际 ${allExchanges.length}`);
    }

    console.log(`✅ 找到 ${allExchanges.length} 条测试订单`);

    // 2. 验证价格计算逻辑
    console.log('\n2. 验证价格计算逻辑...');

    for (const exchange of allExchanges) {
      // 验证使用价格快照而非当前商品价格
      const expectedPrice = exchange.unitPrice;
      const expectedTotal = expectedPrice * exchange.quantity;

      if (parseFloat(exchange.totalAmount) !== expectedTotal) {
        throw new Error(`订单 ${exchange.id} 总金额计算错误: 期望 ${expectedTotal}, 实际 ${exchange.totalAmount}`);
      }
    }

    console.log('✅ 价格计算逻辑验证通过');

    // 3. 测试订单编号格式
    console.log('\n3. 验证订单编号格式...');

    for (const exchange of allExchanges) {
      if (!exchange.orderNumber) {
        throw new Error(`订单 ${exchange.id} 缺少订单编号`);
      }

      const expectedPrefix = exchange.paymentMethod === 'ly' ? 'GNB-' : 'RMB-';
      if (!exchange.orderNumber.startsWith(expectedPrefix)) {
        throw new Error(`订单 ${exchange.id} 编号格式错误: ${exchange.orderNumber}`);
      }
    }

    console.log('✅ 订单编号格式验证通过');

    return true;
  } catch (error) {
    console.error('\n❌ 订单导出测试失败:', error.message);
    return false;
  }
}

async function cleanupTestData() {
  console.log('\n=== 清理测试数据 ===');

  try {
    // 删除测试订单
    await Exchange.destroy({
      where: { userId: testUser.id }
    });

    // 删除价格历史
    await ProductPriceHistory.destroy({
      where: { productId: testProduct.id }
    });

    // 删除测试商品
    await testProduct.destroy();

    // 删除测试分类
    await testCategory.destroy();

    // 删除测试用户
    await testUser.destroy();

    console.log('✅ 测试数据清理完成');

  } catch (error) {
    console.error('清理测试数据失败:', error);
  }
}

async function runCoreFeatureTests() {
  try {
    console.log('=== 商品管理系统核心功能综合测试 ===\n');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功\n');

    // 设置测试数据
    await setupTestData();

    // 运行测试
    const orderNumberTest = await testOrderNumberGeneration();
    const priceAdjustmentTest = await testProductPriceAdjustment();
    const orderExportTest = await testOrderExport();

    // 清理测试数据
    await cleanupTestData();

    // 汇总结果
    const allTestsPassed = orderNumberTest && priceAdjustmentTest && orderExportTest;

    if (allTestsPassed) {
      console.log('\n🎉 所有核心功能测试通过！');
      console.log('✅ 订单编号生成功能正常');
      console.log('✅ 商品调价功能正常');
      console.log('✅ 订单导出功能正常');
      process.exit(0);
    } else {
      console.log('\n❌ 部分测试失败！');
      console.log(`订单编号生成: ${orderNumberTest ? '✅' : '❌'}`);
      console.log(`商品调价功能: ${priceAdjustmentTest ? '✅' : '❌'}`);
      console.log(`订单导出功能: ${orderExportTest ? '✅' : '❌'}`);
      process.exit(1);
    }

  } catch (error) {
    console.error('\n❌ 测试运行失败:', error);
    await cleanupTestData();
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runCoreFeatureTests();
}

module.exports = {
  setupTestData,
  testOrderNumberGeneration,
  testProductPriceAdjustment,
  testOrderExport,
  cleanupTestData,
  runCoreFeatureTests
};
