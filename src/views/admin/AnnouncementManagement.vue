<template>
  <div class="announcement-management">
    <div class="page-header">
      <h1 class="page-title">公告管理</h1>
      <el-button type="primary" @click="showAddDialog">
        <el-icon><Plus /></el-icon>
        新增公告
      </el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="announcements"
      border
      stripe
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="title" label="标题" min-width="180" show-overflow-tooltip />
      <el-table-column label="内容预览" min-width="200">
        <template #default="{ row }">
          <div class="content-preview-text" :title="formatContentPreview(row.content)">
            {{ formatContentPreview(row.content) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="类型" width="100">
        <template #default="{ row }">
          <el-tag :type="getAnnouncementTypeTag(row.type)">
            {{ getAnnouncementTypeName(row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusTag(row.status)">
            {{ getStatusName(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="图片" width="100">
        <template #default="{ row }">
          <el-popover placement="right" trigger="hover" width="300">
            <template #reference>
              <el-badge :value="getImageCount(row)" class="image-badge">
                <el-button type="info" link>
                  <el-icon><Picture /></el-icon>
                </el-button>
              </el-badge>
            </template>
            <div class="image-preview-container">
              <template v-if="row.imageUrls && row.imageUrls.length">
                <div v-for="(imageUrl, index) in row.imageUrls" :key="index" class="preview-item">
                  <img :src="fixImageUrl(imageUrl)" style="width: 100%; height: auto;" />
                </div>
              </template>
              <template v-else-if="row.imageUrl">
                <img :src="fixImageUrl(row.imageUrl)" style="width: 100%; height: auto;" />
              </template>
              <template v-else>
                <span>无图片</span>
              </template>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.createdAt) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280" fixed="right">
        <template #default="{ row }">
          <el-button type="info" size="small" @click="showContentPreview(row)">
            <el-icon><View /></el-icon>
            预览
          </el-button>
          <el-button type="primary" size="small" @click="showEditDialog(row)">
            编辑
          </el-button>
          <el-button
            type="success"
            size="small"
            v-if="row.status === 'draft'"
            @click="updateStatus(row.id, 'active')"
          >
            发布
          </el-button>
          <el-button
            type="warning"
            size="small"
            v-if="row.status === 'active'"
            @click="updateStatus(row.id, 'draft')"
          >
            撤回
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="handleDelete(row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑公告' : '新增公告'"
      width="700px"
      destroy-on-close
      top="5vh"
      class="announcement-edit-dialog"
    >
      <el-form
        ref="announcementForm"
        :model="form"
        :rules="rules"
        label-position="top"
        class="announcement-form"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入公告标题" />
        </el-form-item>

        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="选择公告类型" class="w-full">
            <el-option label="新品公告" value="新品" />
            <el-option label="促销公告" value="促销" />
            <el-option label="系统公告" value="系统更新" />
          </el-select>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="'draft'">草稿</el-radio>
            <el-radio :label="'active'">发布</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="内容" prop="content">
          <el-input
            type="textarea"
            v-model="form.content"
            :rows="8"
            placeholder="请输入公告内容，支持换行和基本格式"
            maxlength="2000"
            show-word-limit
            resize="vertical"
            class="content-textarea"
          ></el-input>
          <div class="content-help-text">
            <small>支持换行和基本Markdown格式：**粗体** *斜体*</small>
          </div>
        </el-form-item>

        <el-form-item label="公告图片">
          <el-upload
            class="announcement-image-uploader"
            :action="uploadImageUrl"
            :headers="uploadHeaders"
            :show-file-list="true"
            :on-success="handleImageSuccess"
            :before-upload="beforeImageUpload"
            :on-remove="handleImageRemove"
            multiple
            list-type="picture-card"
          >
            <el-icon class="uploader-icon"><Plus /></el-icon>
            <template #file="{ file }">
              <div>
                <img
                  class="el-upload-list__item-thumbnail"
                  :src="file.url"
                  alt=""
                />
                <span class="el-upload-list__item-actions">
                  <span
                    class="el-upload-list__item-preview"
                    @click="handlePictureCardPreview(file)"
                  >
                    <el-icon><zoom-in /></el-icon>
                  </span>
                  <span
                    class="el-upload-list__item-delete"
                    @click="handleImageRemove(file)"
                  >
                    <el-icon><Delete /></el-icon>
                  </span>
                </span>
              </div>
            </template>
          </el-upload>
          <div class="image-preview-list" v-if="form.imageUrls && form.imageUrls.length">
            <div v-for="(url, index) in form.imageUrls" :key="index" class="image-preview-item">
              <img :src="url" class="preview-thumbnail" />
              <div class="preview-actions">
                <el-button type="danger" size="small" circle @click="removeUploadedImage(index)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
          <div class="image-tip">支持jpg、png格式，大小不超过5MB，可上传多张图片</div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">
            {{ isEdit ? '更新' : '新增' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="dialogImageVisible" width="50%">
      <img :src="fixImageUrl(dialogImageUrl)" alt="Preview Image" style="width: 100%;" />
    </el-dialog>

    <!-- 内容预览对话框 -->
      <el-dialog
        v-model="contentPreviewVisible"
        :title="previewAnnouncement?.title || '公告预览'"
        width="600px"
        class="content-preview-dialog"
        top="5vh"
        @close="closeContentPreview"
      >
        <div class="preview-content" v-if="previewAnnouncement">
          <div class="preview-meta">
            <el-tag :type="getAnnouncementTypeTag(previewAnnouncement.type)">
              {{ getAnnouncementTypeName(previewAnnouncement.type) }}
            </el-tag>
            <el-tag :type="getStatusTag(previewAnnouncement.status)" style="margin-left: 10px;">
              {{ getStatusName(previewAnnouncement.status) }}
            </el-tag>
            <span class="preview-time">创建时间: {{ formatDateTime(previewAnnouncement.createdAt) }}</span>
          </div>

          <!-- 图片展示 -->
          <div v-if="previewAnnouncement.imageUrls && previewAnnouncement.imageUrls.length > 0" class="preview-images">
            <el-carousel v-if="previewAnnouncement.imageUrls.length > 1" height="300px" indicator-position="outside" arrow="always">
              <el-carousel-item v-for="(imageUrl, index) in previewAnnouncement.imageUrls" :key="index">
                <img :src="fixImageUrl(imageUrl)" class="carousel-image" />
              </el-carousel-item>
            </el-carousel>

            <div v-else class="single-image">
              <img :src="fixImageUrl(previewAnnouncement.imageUrls[0])" style="max-width: 100%; max-height: 300px;" />
            </div>
          </div>

          <!-- 向后兼容单张图片 -->
          <div v-else-if="previewAnnouncement.imageUrl" class="preview-image">
            <img :src="fixImageUrl(previewAnnouncement.imageUrl)" style="max-width: 100%; max-height: 300px;" />
          </div>

          <div class="preview-text">
            <div v-html="formatPreviewContent(previewAnnouncement.content)" class="formatted-content"></div>
          </div>
        </div>

        <div class="empty-preview" v-else>
          <el-empty description="暂无内容" />
        </div>
      </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Picture, Delete, ZoomIn, View } from '@element-plus/icons-vue';
import { useAuthStore } from '../../stores/auth';
import { getAnnouncements, createAnnouncement, updateAnnouncement, updateAnnouncementStatus, deleteAnnouncement, uploadAnnouncementImage } from '../../api/announcements';
import { fixImageUrl } from '../../utils/imageUtils';

const authStore = useAuthStore();
// 修改API基础URL，确保不重复添加/api
const apiBaseUrl = import.meta.env.VITE_API_URL || '';
// 上传图片的完整URL
const uploadImageUrl = apiBaseUrl.includes('/api')
  ? `${apiBaseUrl}/upload/image`
  : `${apiBaseUrl}/api/upload/image`;

// 公告数据
const announcements = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 对话框控制
const dialogVisible = ref(false);
const isEdit = ref(false);
const submitting = ref(false);

// 图片预览
const dialogImageVisible = ref(false);
const dialogImageUrl = ref('');

// 内容预览
const contentPreviewVisible = ref(false);
const previewAnnouncement = ref(null);

// 表单数据
const announcementForm = ref(null);
const form = reactive({
  id: null,
  title: '',
  content: '',
  imageUrls: [], // 改为图片URL数组
  type: '系统更新',
  status: 'draft'
});

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入公告标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在2-100个字符之间', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入公告内容', trigger: 'blur' },
    { min: 5, message: '内容至少5个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择公告类型', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择公告状态', trigger: 'change' }
  ]
};

// 初始化数据
onMounted(() => {
  fetchAnnouncements();
});

// 获取公告列表
const fetchAnnouncements = async () => {
  loading.value = true;
  try {
    // 实际API调用
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      showAll: true  // 显示所有公告，包括未发布的
    };
    const response = await getAnnouncements(params);
    announcements.value = response.data;
    total.value = response.total;
  } catch (error) {
    console.error('获取公告列表失败', error);
    ElMessage.error('获取公告列表失败');
  } finally {
    loading.value = false;
  }
};

// 显示新增对话框
const showAddDialog = () => {
  isEdit.value = false;
  // 重置表单
  Object.assign(form, {
    id: null,
    title: '',
    content: '',
    imageUrls: [],
    type: '系统更新',
    status: 'draft'
  });
  dialogVisible.value = true;
};

// 显示编辑对话框
const showEditDialog = (row) => {
  isEdit.value = true;
  Object.assign(form, {
    id: row.id,
    title: row.title,
    content: row.content || '',
    imageUrls: row.imageUrls || [],
    // 向后兼容单个imageUrl
    ...(!row.imageUrls && row.imageUrl ? { imageUrls: [row.imageUrl] } : {}),
    type: row.type,
    status: row.status
  });
  dialogVisible.value = true;
};

// 提交表单
const submitForm = async () => {
  if (!announcementForm.value) return;

  await announcementForm.value.validate(async (valid) => {
    if (valid) {
      try {
        submitting.value = true;

        const formData = {
          title: form.title,
          content: form.content,
          imageUrls: form.imageUrls,
          type: form.type,
          status: form.status
        };

        let response;
        if (isEdit.value) {
          response = await updateAnnouncement(form.id, formData);
          ElMessage.success('公告更新成功');
        } else {
          response = await createAnnouncement(formData);
          ElMessage.success('公告创建成功');
        }

        dialogVisible.value = false;
        fetchAnnouncements();
      } catch (error) {
        console.error('提交公告失败:', error);
        ElMessage.error('提交失败，请重试');
      } finally {
        submitting.value = false;
      }
    } else {
      return false;
    }
  });
};

// 获取图片数量
const getImageCount = (row) => {
  if (row.imageUrls && row.imageUrls.length) {
    return row.imageUrls.length;
  } else if (row.imageUrl) {
    return 1;
  }
  return 0;
};

// 更新公告状态
const updateStatus = async (id, status) => {
  try {
    await updateAnnouncementStatus(id, status);
    ElMessage.success(`公告状态已更新为 ${getStatusName(status)}`);

    // 当公告状态变为已发布时，清除缓存
    if (status === 'active') {
      localStorage.removeItem('lastClosedAnnouncementId');
      console.log('公告状态已更新为已发布，缓存已清除');
    }

    fetchAnnouncements();
  } catch (error) {
    console.error('更新公告状态失败:', error);
    ElMessage.error('更新公告状态失败，请稍后重试');
  }
};

// 删除公告
const handleDelete = (id) => {
  ElMessageBox.confirm('确定要删除这条公告吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteAnnouncement(id);
      ElMessage.success('公告已删除');
      fetchAnnouncements();
    } catch (error) {
      console.error('删除公告失败:', error);
      ElMessage.error('删除公告失败，请稍后重试');
    }
  }).catch(() => {});
};

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val;
  fetchAnnouncements();
};

const handlePageChange = (val) => {
  currentPage.value = val;
  fetchAnnouncements();
};

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN');
};

// 格式化内容预览
const formatContentPreview = (content) => {
  if (!content) return '暂无内容';

  // 移除换行符和多余空格，限制长度
  let preview = content
    .replace(/\\n/g, ' ')  // 处理字面量换行符
    .replace(/\n/g, ' ')   // 处理真正的换行符
    .replace(/\s+/g, ' ')  // 合并多个空格
    .trim();

  // 限制预览长度
  if (preview.length > 50) {
    preview = preview.substring(0, 50) + '...';
  }

  return preview;
};

// 显示内容预览
const showContentPreview = (row) => {
  console.log('showContentPreview called with:', row);
  console.log('contentPreviewVisible before:', contentPreviewVisible.value);
  previewAnnouncement.value = { ...row };
  contentPreviewVisible.value = true;
  console.log('contentPreviewVisible after:', contentPreviewVisible.value);
  console.log('previewAnnouncement:', previewAnnouncement.value);
};

// 关闭内容预览
const closeContentPreview = () => {
  contentPreviewVisible.value = false;
  previewAnnouncement.value = null;
};

// 格式化预览内容（用于预览对话框）
const formatPreviewContent = (content) => {
  if (!content) return '';

  return content
    // 首先处理字面量的\n字符串（反斜杠+n）
    .replace(/\\n/g, '<br>')
    // 然后处理真正的换行符
    .replace(/\n/g, '<br>')
    // 处理markdown格式的粗体
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // 处理markdown格式的斜体
    .replace(/\*(.*?)\*/g, '<em>$1</em>');
};

// 获取公告类型名称和标签类型
const getAnnouncementTypeName = (type) => {
  const typeMap = {
    '新品': '新品公告',
    '促销': '促销公告',
    '系统更新': '系统公告'
  };
  return typeMap[type] || type || '未知类型';
};

const getAnnouncementTypeTag = (type) => {
  const tagMap = {
    '新品': 'success',
    '促销': 'danger',
    '系统更新': 'warning'
  };
  return tagMap[type] || 'info';
};

// 获取状态名称和标签类型
const getStatusName = (status) => {
  const statusMap = {
    draft: '草稿',
    active: '已发布'
  };
  return statusMap[status] || '未知状态';
};

const getStatusTag = (status) => {
  const tagMap = {
    draft: 'info',
    active: 'success'
  };
  return tagMap[status] || '';
};

// 获取上传用的认证头信息
const uploadHeaders = computed(() => {
  return {
    Authorization: `Bearer ${sessionStorage.getItem('token')}`
  };
});

// 处理图片上传成功
const handleImageSuccess = (response) => {
  if (response.errno === 0 && response.data) {
    // 添加到图片数组中
    if (!form.imageUrls) form.imageUrls = [];
    form.imageUrls.push(response.data.url);
    ElMessage.success('图片上传成功');
  } else {
    ElMessage.error('图片上传失败');
  }
};

// 移除已上传的图片
const handleImageRemove = (file) => {
  // 上传组件自带的文件移除功能会移除本次上传的临时文件
  ElMessage.info('已移除图片');
};

// 移除已保存的图片
const removeUploadedImage = (index) => {
  if (index >= 0 && index < form.imageUrls.length) {
    form.imageUrls.splice(index, 1);
    ElMessage.info('已移除图片');
  }
};

// 处理图片上传前的验证
const beforeImageUpload = (file) => {
  const isJPG = file.type === 'image/jpeg';
  const isPNG = file.type === 'image/png';
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isJPG && !isPNG) {
    ElMessage.error('上传图片只能是 JPG 或 PNG 格式!');
    return false;
  }
  if (!isLt5M) {
    ElMessage.error('上传图片大小不能超过 5MB!');
    return false;
  }
  return true;
};

// 图片预览
const handlePictureCardPreview = (file) => {
  dialogImageUrl.value = file.url;
  dialogImageVisible.value = true;
};
</script>

<style scoped>
.announcement-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  font-size: 22px;
  font-weight: 600;
  margin: 0;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.announcement-form {
  margin-top: 10px;
}

.w-full {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 添加图片上传组件样式 */
.announcement-image-uploader {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

/* 图片预览列表样式 */
.image-preview-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.image-preview-item {
  position: relative;
  width: 146px;
  height: 146px;
  border: 1px solid #c0c4cc;
  border-radius: 6px;
  overflow: hidden;
}

.preview-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-actions {
  position: absolute;
  top: 0;
  right: 0;
  padding: 5px;
  background-color: rgba(0, 0, 0, 0.5);
  border-bottom-left-radius: 6px;
}

/* 表格中的图片预览 */
.image-preview-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.preview-item {
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #ebeef5;
}

.image-badge :deep(.el-badge__content) {
  background-color: #409eff;
}

/* 编辑对话框样式优化 */
.announcement-edit-dialog .el-dialog__body {
  max-height: 70vh;
  overflow-y: auto;
  padding: 20px;
}

.content-textarea :deep(.el-textarea__inner) {
  min-height: 120px !important;
  max-height: 300px;
  resize: vertical;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.5;
}

.content-help-text {
  margin-top: 5px;
  color: #909399;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .announcement-edit-dialog {
    width: 95% !important;
    margin: 0 !important;
  }

  .announcement-edit-dialog .el-dialog__body {
    padding: 15px;
    max-height: 75vh;
  }

  .content-textarea :deep(.el-textarea__inner) {
    max-height: 200px;
  }
}

/* 表格内容预览优化 */
.announcement-management .el-table .cell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 内容预览列样式 */
.content-preview-text {
  line-height: 1.4;
  font-size: 12px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
}

/* 内容预览对话框样式 */
.content-preview-dialog {
  z-index: 3000 !important;
}

.content-preview-dialog .el-dialog__body {
  max-height: 70vh;
  overflow-y: auto;
  padding: 20px;
}

.preview-meta {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.preview-time {
  color: #909399;
  font-size: 14px;
  margin-left: auto;
}

.preview-images, .preview-image {
  margin: 15px 0;
  text-align: center;
}

.carousel-image {
  height: 300px;
  object-fit: contain;
  width: 100%;
}

.single-image {
  display: flex;
  justify-content: center;
}

.preview-text {
  line-height: 1.6;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;
  margin-top: 15px;
}

.formatted-content {
  color: #333;
  font-size: 14px;
}

.formatted-content strong {
  font-weight: bold;
  color: #409eff;
}

.formatted-content em {
  font-style: italic;
  color: #67c23a;
}

.empty-preview {
  padding: 30px;
  text-align: center;
}

/* 预览文本滚动条样式 */
.preview-text::-webkit-scrollbar {
  width: 6px;
}

.preview-text::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.preview-text::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.preview-text::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-preview-dialog {
    width: 95% !important;
    margin: 0 !important;
  }

  .content-preview-dialog .el-dialog__body {
    padding: 15px;
    max-height: 75vh;
  }

  .preview-text {
    max-height: 300px;
  }

  .preview-meta {
    flex-direction: column;
    align-items: flex-start;
  }

  .preview-time {
    margin-left: 0;
    margin-top: 5px;
  }
}
</style>
