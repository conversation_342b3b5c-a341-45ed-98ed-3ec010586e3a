'use strict';

// 导入迁移文件
const addIsActiveMigration = require('./20250501_add_isActive_to_users');

// 导入数据库配置
const { sequelize } = require('../config/database');

// 运行迁移
(async () => {
  try {
    console.log('开始运行用户isActive字段迁移...');

    // 运行迁移的up方法
    await addIsActiveMigration.up(sequelize.getQueryInterface(), sequelize.Sequelize);

    console.log('用户isActive字段迁移成功完成！');
    process.exit(0);
  } catch (error) {
    console.error('迁移失败:', error);
    process.exit(1);
  }
})();
