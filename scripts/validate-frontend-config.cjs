#!/usr/bin/env node

/**
 * 前端配置文件验证脚本
 * 验证前端配置文件的完整性和与后端配置的一致性
 */

const fs = require('fs');
const path = require('path');

// 配置文件路径
const CONFIG_FILES = {
  frontend: {
    development: path.join(__dirname, '../.env'),
    production: path.join(__dirname, '../.env.production')
  },
  backend: {
    development: path.join(__dirname, '../server/.env'),
    production: path.join(__dirname, '../server/.env.production')
  }
};

// 前端必需的配置项
const REQUIRED_FRONTEND_CONFIGS = [
  'VITE_API_URL',
  'VITE_APP_TITLE',
  'VITE_APP_BASE_URL',
  'VITE_NODE_ENV'
];

// 前后端配置映射关系
const CONFIG_MAPPING = {
  development: {
    'VITE_API_URL': 'http://localhost:3000/api',
    'VITE_SERVER_URL': 'http://localhost:3000',
    'VITE_NODE_ENV': 'development'
  },
  production: {
    'VITE_API_URL': 'https://store-api.chongyangqisi.com/api',
    'VITE_SERVER_URL': 'https://store-api.chongyangqisi.com',
    'VITE_NODE_ENV': 'production'
  }
};

/**
 * 解析.env文件
 * @param {string} filePath - 文件路径
 * @returns {Object} - 配置对象
 */
function parseEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return null;
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const config = {};

  content.split('\n').forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#') && line.includes('=')) {
      const [key, ...valueParts] = line.split('=');
      const value = valueParts.join('=').trim();
      config[key.trim()] = value;
    }
  });

  return config;
}

/**
 * 验证前端配置文件
 * @param {string} env - 环境名称
 * @param {string} filePath - 文件路径
 */
function validateFrontendConfig(env, filePath) {
  console.log(`\n📋 验证 ${env} 环境前端配置文件: ${path.basename(filePath)}`);
  
  const config = parseEnvFile(filePath);
  
  if (!config) {
    console.log(`❌ 配置文件不存在: ${filePath}`);
    return false;
  }

  console.log(`✅ 配置文件存在`);

  // 检查必需配置项
  const missingConfigs = [];
  const presentConfigs = [];

  REQUIRED_FRONTEND_CONFIGS.forEach(key => {
    if (!config[key] || config[key] === '') {
      missingConfigs.push(key);
    } else {
      presentConfigs.push(key);
    }
  });

  console.log(`✅ 已配置项 (${presentConfigs.length}/${REQUIRED_FRONTEND_CONFIGS.length}):`);
  presentConfigs.forEach(key => {
    console.log(`   ✓ ${key}: ${config[key]}`);
  });

  if (missingConfigs.length > 0) {
    console.log(`❌ 缺失配置项 (${missingConfigs.length}):`);
    missingConfigs.forEach(key => {
      console.log(`   ✗ ${key}`);
    });
  }

  // 验证环境特定的配置值
  if (CONFIG_MAPPING[env]) {
    console.log(`\n🔍 验证环境特定配置值:`);
    const expectedValues = CONFIG_MAPPING[env];
    let valueErrors = 0;

    Object.keys(expectedValues).forEach(key => {
      const expected = expectedValues[key];
      const actual = config[key];
      
      if (actual === expected) {
        console.log(`   ✓ ${key}: ${actual}`);
      } else if (key === 'VITE_API_URL' && env === 'production') {
        // 生产环境API URL有两种可能的值
        const alternativeUrl = 'http://47.122.122.245:3000/api';
        if (actual === alternativeUrl) {
          console.log(`   ✓ ${key}: ${actual} (直接访问后端)`);
        } else {
          console.log(`   ❌ ${key}: 期望 "${expected}" 或 "${alternativeUrl}", 实际 "${actual}"`);
          valueErrors++;
        }
      } else {
        console.log(`   ❌ ${key}: 期望 "${expected}", 实际 "${actual || '未设置'}"`);
        valueErrors++;
      }
    });

    if (valueErrors === 0) {
      console.log(`✅ 环境特定配置值验证通过`);
    } else {
      console.log(`❌ 发现 ${valueErrors} 个配置值错误`);
    }
  }

  return missingConfigs.length === 0;
}

/**
 * 验证前后端配置一致性
 * @param {string} env - 环境名称
 */
function validateConsistency(env) {
  console.log(`\n🔄 验证 ${env} 环境前后端配置一致性`);
  
  const frontendConfig = parseEnvFile(CONFIG_FILES.frontend[env]);
  const backendConfig = parseEnvFile(CONFIG_FILES.backend[env]);

  if (!frontendConfig || !backendConfig) {
    console.log(`❌ 无法加载配置文件进行一致性验证`);
    return false;
  }

  let consistencyErrors = 0;

  // 检查API URL一致性
  const frontendApiUrl = frontendConfig['VITE_API_URL'];
  const backendServerUrl = backendConfig['SERVER_URL'];
  
  if (frontendApiUrl && backendServerUrl) {
    const expectedApiUrl = `${backendServerUrl}/api`;
    const alternativeApiUrl = env === 'production' ? 'http://47.122.122.245/api' : null;
    
    if (frontendApiUrl === expectedApiUrl || (alternativeApiUrl && frontendApiUrl === alternativeApiUrl)) {
      console.log(`   ✓ API URL一致: ${frontendApiUrl}`);
    } else {
      console.log(`   ❌ API URL不一致:`);
      console.log(`      前端: ${frontendApiUrl}`);
      console.log(`      期望: ${expectedApiUrl}${alternativeApiUrl ? ` 或 ${alternativeApiUrl}` : ''}`);
      consistencyErrors++;
    }
  }

  // 检查环境标识一致性
  const frontendEnv = frontendConfig['VITE_NODE_ENV'];
  const backendEnv = backendConfig['NODE_ENV'];
  
  if (frontendEnv === backendEnv) {
    console.log(`   ✓ 环境标识一致: ${frontendEnv}`);
  } else {
    console.log(`   ❌ 环境标识不一致:`);
    console.log(`      前端: ${frontendEnv}`);
    console.log(`      后端: ${backendEnv}`);
    consistencyErrors++;
  }

  if (consistencyErrors === 0) {
    console.log(`✅ 前后端配置一致性验证通过`);
  } else {
    console.log(`❌ 发现 ${consistencyErrors} 个一致性错误`);
  }

  return consistencyErrors === 0;
}

/**
 * 主验证函数
 */
function main() {
  console.log('🔧 前端配置文件验证工具');
  console.log('==========================================');

  let allValid = true;

  // 验证前端配置文件
  ['development', 'production'].forEach(env => {
    const isValid = validateFrontendConfig(env, CONFIG_FILES.frontend[env]);
    if (!isValid) {
      allValid = false;
    }
  });

  // 验证前后端配置一致性
  ['development', 'production'].forEach(env => {
    const isConsistent = validateConsistency(env);
    if (!isConsistent) {
      allValid = false;
    }
  });

  // 总结
  console.log('\n📊 验证结果');
  console.log('==========================================');
  if (allValid) {
    console.log('✅ 所有前端配置文件验证通过');
    console.log('✅ 前后端配置一致性验证通过');
    console.log('\n🚀 前端配置正确，可以安全构建和部署');
    process.exit(0);
  } else {
    console.log('❌ 前端配置文件验证失败');
    console.log('\n🛠️  请修复上述问题后重新验证');
    process.exit(1);
  }
}

// 运行验证
if (require.main === module) {
  main();
}

module.exports = {
  parseEnvFile,
  validateFrontendConfig,
  validateConsistency
};
