-- =====================================================
-- 多职场库存分配功能 - 第一阶段迁移脚本
-- 迁移现有商品库存数据到多职场库存结构
-- 创建时间: 2025-07-28
-- 版本: v1.0.0
-- =====================================================

-- 开始事务
START TRANSACTION;

-- 创建临时变量存储迁移信息
SET @migration_batch_id = CONCAT('MIGRATION_', DATE_FORMAT(NOW(), '%Y%m%d_%H%i%s'));
SET @migration_operator_id = 1; -- 系统管理员ID
SET @migration_operator_name = '系统迁移';

-- 检查是否已经进行过迁移（避免重复迁移）
SET @migration_exists = (
    SELECT COUNT(*)
    FROM stock_operation_logs
    WHERE operationType = 'migration'
    AND batchId COLLATE utf8mb4_unicode_ci = @migration_batch_id COLLATE utf8mb4_unicode_ci
);

-- 显示迁移状态
SELECT '📊 开始迁移现有库存数据到多职场结构...' as status;

-- 1. 获取所有活跃的职场
    
    -- 显示迁移前的统计信息
    SELECT 
        COUNT(*) as total_products,
        SUM(stock) as total_stock_amount,
        AVG(stock) as avg_stock_per_product
    FROM products 
    WHERE status = 'active';
    
    SELECT 
        COUNT(*) as total_active_workplaces
    FROM workplaces 
    WHERE isActive = 1;

    -- 2. 为每个活跃商品在每个活跃职场创建库存记录
    -- 使用平均分配策略：将原有库存平均分配到各个职场
    INSERT INTO product_workplace_stocks (
        productId, 
        workplaceId, 
        stock, 
        reservedStock, 
        minStockAlert,
        lastStockUpdate,
        createdAt, 
        updatedAt
    )
    SELECT 
        p.id as productId,
        w.id as workplaceId,
        -- 平均分配库存，最后一个职场分配剩余库存
        CASE 
            WHEN w.id = (
                SELECT MAX(id) 
                FROM workplaces 
                WHERE isActive = 1
            ) THEN 
                p.stock - (
                    FLOOR(p.stock / (SELECT COUNT(*) FROM workplaces WHERE isActive = 1)) * 
                    ((SELECT COUNT(*) FROM workplaces WHERE isActive = 1) - 1)
                )
            ELSE 
                FLOOR(p.stock / (SELECT COUNT(*) FROM workplaces WHERE isActive = 1))
        END as stock,
        0 as reservedStock,
        CASE 
            WHEN p.stock > 50 THEN 10
            WHEN p.stock > 20 THEN 5
            ELSE 2
        END as minStockAlert,
        NOW() as lastStockUpdate,
        NOW() as createdAt,
        NOW() as updatedAt
    FROM products p
    CROSS JOIN workplaces w
    WHERE p.status = 'active' 
    AND w.isActive = 1
    AND NOT EXISTS (
        SELECT 1 
        FROM product_workplace_stocks pws 
        WHERE pws.productId = p.id 
        AND pws.workplaceId = w.id
    );

    -- 3. 记录迁移操作日志
    INSERT INTO stock_operation_logs (
        productId,
        workplaceId,
        operationType,
        beforeStock,
        afterStock,
        changeAmount,
        operatorId,
        operatorName,
        reason,
        batchId,
        ipAddress,
        metadata,
        createdAt
    )
    SELECT 
        pws.productId,
        pws.workplaceId,
        'migration' as operationType,
        0 as beforeStock,
        pws.stock as afterStock,
        pws.stock as changeAmount,
        @migration_operator_id as operatorId,
        @migration_operator_name as operatorName,
        CONCAT('数据迁移：将商品ID ', pws.productId, ' 的库存分配到职场ID ', pws.workplaceId) as reason,
        @migration_batch_id as batchId,
        '127.0.0.1' as ipAddress,
        JSON_OBJECT(
            'migration_type', 'initial_stock_allocation',
            'original_stock', (SELECT stock FROM products WHERE id = pws.productId),
            'workplace_name', (SELECT name FROM workplaces WHERE id = pws.workplaceId),
            'allocation_strategy', 'average_distribution'
        ) as metadata,
        NOW() as createdAt
    FROM product_workplace_stocks pws
    WHERE pws.createdAt >= DATE_SUB(NOW(), INTERVAL 1 MINUTE); -- 只记录刚刚创建的记录

    -- 4. 显示迁移结果统计
    SELECT '✅ 库存数据迁移完成' as status;
    
    SELECT 
        COUNT(*) as total_workplace_stock_records,
        COUNT(DISTINCT productId) as products_migrated,
        COUNT(DISTINCT workplaceId) as workplaces_involved,
        SUM(stock) as total_allocated_stock
    FROM product_workplace_stocks;
    
    -- 验证迁移数据的完整性
    SELECT 
        p.id as productId,
        p.name as productName,
        p.stock as original_stock,
        COALESCE(SUM(pws.stock), 0) as allocated_total_stock,
        (p.stock - COALESCE(SUM(pws.stock), 0)) as stock_difference
    FROM products p
    LEFT JOIN product_workplace_stocks pws ON p.id = pws.productId
    WHERE p.status = 'active'
    GROUP BY p.id, p.name, p.stock
    HAVING stock_difference != 0
    LIMIT 5; -- 显示前5个有差异的记录（如果有的话）

-- 检查迁移状态
SELECT
    CASE
        WHEN @migration_exists > 0 THEN '⚠️  检测到已存在迁移记录，跳过数据迁移'
        ELSE '✅ 数据迁移检查完成'
    END as migration_check_status;

-- 提交事务
COMMIT;

SELECT CONCAT('🎉 迁移批次 ', @migration_batch_id, ' 完成') as final_status;
