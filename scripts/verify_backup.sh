#!/bin/bash

# ====================================
# 数据库备份验证脚本
# ====================================
# 此脚本用于验证备份文件的完整性和可用性
# 使用方法：./scripts/verify_backup.sh [备份文件路径]

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=====================================${NC}"
echo -e "${BLUE}     数据库备份验证脚本${NC}"
echo -e "${BLUE}=====================================${NC}"

# 检查参数
if [ $# -eq 0 ]; then
    echo -e "${YELLOW}请指定要验证的备份文件:${NC}"
    echo ""
    
    # 显示可用的备份文件
    if [ -d "database_backups" ] && [ "$(ls -A database_backups/*.sql 2>/dev/null)" ]; then
        echo -e "${BLUE}可用的备份文件:${NC}"
        ls -lht database_backups/*.sql | nl
        echo ""
        echo -e "${YELLOW}使用方法: $0 <备份文件路径>${NC}"
        echo -e "${YELLOW}例如: $0 database_backups/feishu_mall_backup_20250723_174548.sql${NC}"
    else
        echo -e "${RED}未找到备份文件！${NC}"
    fi
    exit 1
fi

BACKUP_FILE="$1"

# 检查备份文件是否存在
if [ ! -f "$BACKUP_FILE" ]; then
    echo -e "${RED}错误: 备份文件不存在: $BACKUP_FILE${NC}"
    exit 1
fi

echo -e "${YELLOW}开始验证备份文件...${NC}"
echo -e "${YELLOW}备份文件: $BACKUP_FILE${NC}"

# 基本文件信息
echo -e "\n${BLUE}=== 文件基本信息 ===${NC}"
FILE_SIZE=$(ls -lh "$BACKUP_FILE" | awk '{print $5}')
LINE_COUNT=$(wc -l < "$BACKUP_FILE")
echo -e "${GREEN}文件大小: $FILE_SIZE${NC}"
echo -e "${GREEN}行数: $LINE_COUNT${NC}"

# 检查文件是否为空
if [ ! -s "$BACKUP_FILE" ]; then
    echo -e "${RED}错误: 备份文件为空${NC}"
    exit 1
fi

# 验证MySQL dump格式
echo -e "\n${BLUE}=== MySQL Dump 格式验证 ===${NC}"

# 检查文件头
HEADER_CHECK=$(head -5 "$BACKUP_FILE" | grep -c "MySQL dump")
if [ "$HEADER_CHECK" -gt 0 ]; then
    echo -e "${GREEN}✓ MySQL dump 文件头格式正确${NC}"
else
    echo -e "${RED}✗ MySQL dump 文件头格式错误${NC}"
fi

# 检查文件尾
FOOTER_CHECK=$(tail -5 "$BACKUP_FILE" | grep -c "Dump completed")
if [ "$FOOTER_CHECK" -gt 0 ]; then
    echo -e "${GREEN}✓ MySQL dump 文件尾格式正确${NC}"
else
    echo -e "${RED}✗ MySQL dump 文件尾格式错误${NC}"
fi

# 统计表结构
echo -e "\n${BLUE}=== 数据库结构分析 ===${NC}"
CREATE_TABLE_COUNT=$(grep -c "CREATE TABLE" "$BACKUP_FILE")
INSERT_COUNT=$(grep -c "INSERT INTO" "$BACKUP_FILE")
DROP_TABLE_COUNT=$(grep -c "DROP TABLE" "$BACKUP_FILE")

echo -e "${GREEN}CREATE TABLE 语句数量: $CREATE_TABLE_COUNT${NC}"
echo -e "${GREEN}INSERT INTO 语句数量: $INSERT_COUNT${NC}"
echo -e "${GREEN}DROP TABLE 语句数量: $DROP_TABLE_COUNT${NC}"

# 显示包含的表
echo -e "\n${BLUE}=== 包含的数据表 ===${NC}"
grep "CREATE TABLE" "$BACKUP_FILE" | sed 's/CREATE TABLE `\([^`]*\)`.*/\1/' | sort

# 检查关键表是否存在
echo -e "\n${BLUE}=== 关键表检查 ===${NC}"
CRITICAL_TABLES=("users" "exchanges" "products" "categories" "logs" "notifications")

for table in "${CRITICAL_TABLES[@]}"; do
    if grep -q "CREATE TABLE \`$table\`" "$BACKUP_FILE"; then
        echo -e "${GREEN}✓ 表 '$table' 存在${NC}"
    else
        echo -e "${YELLOW}⚠ 表 '$table' 不存在${NC}"
    fi
done

# 检查字符集设置
echo -e "\n${BLUE}=== 字符集和编码检查 ===${NC}"
if grep -q "utf8mb4" "$BACKUP_FILE"; then
    echo -e "${GREEN}✓ 使用 UTF8MB4 字符集${NC}"
else
    echo -e "${YELLOW}⚠ 未检测到 UTF8MB4 字符集${NC}"
fi

# 检查外键约束
if grep -q "FOREIGN_KEY_CHECKS" "$BACKUP_FILE"; then
    echo -e "${GREEN}✓ 包含外键约束设置${NC}"
else
    echo -e "${YELLOW}⚠ 未包含外键约束设置${NC}"
fi

# 语法检查（简单）
echo -e "\n${BLUE}=== 语法完整性检查 ===${NC}"
SYNTAX_ERRORS=0

# 检查是否有未闭合的语句
SEMICOLON_COUNT=$(grep -c ";" "$BACKUP_FILE")
if [ "$SEMICOLON_COUNT" -gt 0 ]; then
    echo -e "${GREEN}✓ 包含SQL语句结束符${NC}"
else
    echo -e "${RED}✗ 缺少SQL语句结束符${NC}"
    SYNTAX_ERRORS=$((SYNTAX_ERRORS + 1))
fi

# 最终结果
echo -e "\n${BLUE}=====================================${NC}"
if [ "$SYNTAX_ERRORS" -eq 0 ] && [ "$HEADER_CHECK" -gt 0 ] && [ "$FOOTER_CHECK" -gt 0 ]; then
    echo -e "${GREEN}     验证通过！${NC}"
    echo -e "${GREEN}=====================================${NC}"
    echo -e "${GREEN}备份文件格式正确，可以用于数据恢复${NC}"
    echo -e "${GREEN}建议在目标环境中进行测试导入${NC}"
else
    echo -e "${RED}     验证失败！${NC}"
    echo -e "${RED}=====================================${NC}"
    echo -e "${RED}备份文件可能存在问题，请检查${NC}"
fi

echo -e "\n${BLUE}验证完成${NC}"
