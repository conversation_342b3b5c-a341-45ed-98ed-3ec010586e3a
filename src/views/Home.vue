<template>
  <div class="home-container" :class="{ 'home-container-loading': pageLoading }">
    <!-- 显示骨架屏直到页面完全加载 -->
    <template v-if="pageLoading">
      <el-skeleton style="width: 100%" animated>
        <template #template>
          <div style="padding: 20px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
              <el-skeleton-item variant="h3" style="width: 30%" />
              <el-skeleton-item variant="button" style="width: 15%" />
            </div>

            <el-skeleton-item variant="p" style="width: 100%; height: 60px; margin-bottom: 20px" />

            <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(240px, 1fr)); gap: 20px;">
              <div v-for="i in 8" :key="i" style="height: 300px;">
                <el-skeleton-item variant="image" style="width: 100%; height: 160px;" />
                <el-skeleton-item variant="h3" style="margin: 12px 0; width: 80%" />
                <el-skeleton-item variant="text" style="width: 60%" />
                <el-skeleton-item variant="text" style="width: 100%" />
              </div>
            </div>
          </div>
        </template>
      </el-skeleton>
    </template>

    <template v-else>
      <header class="header">
        <h1 class="title">光年小卖部商品库</h1>
        <div class="header-actions">
          <el-button v-if="isAdmin" type="success" class="admin-btn" @click="goToAdmin">
            <i class="el-icon-setting"></i>
            管理后台
          </el-button>
          <!-- 未登录时显示登录按钮 -->
          <el-button v-if="!isAuthenticated" type="primary" class="auth-btn" @click="goToLogin">
            <span>登录</span>
            <el-tooltip content="没有账号？点击登录后可以注册" placement="bottom" effect="light">
              <i class="el-icon-question" style="margin-left: 5px;"></i>
            </el-tooltip>
          </el-button>

          <!-- 已登录时显示通知图标、用户头像和下拉菜单 -->
          <div v-else class="header-user-section">
            <!-- 添加通知图标（移到下拉菜单外部） -->
            <UserNotificationIcon v-if="isAuthenticated" class="notification-icon" />

            <el-dropdown @command="handleCommand" trigger="click">
              <div class="user-info">
                <el-avatar :size="32" class="user-avatar" :src="authStore.user?.feishuAvatar">
                  {{ authStore.user?.username?.charAt(0).toUpperCase() }}
                </el-avatar>
                <span class="username">{{ authStore.user?.username }}</span>
                <el-icon><arrow-down /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="userCenter">
                    <el-icon><user /></el-icon>个人中心
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><switch-button /></el-icon>退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </header>

      <div class="filter-container">
        <ProductFilter
          :initialFilters="filterParams"
          :categoriesList="categories"
          @change="handleFilterChange"
          @reset="resetFilters"
          @feedback="handleFeedback"
          @announcement="showAnnouncement"
        />
      </div>



      <div class="content">
        <Transition name="fade" mode="out-in">
          <div>
            <div v-if="loading" class="loading-wrapper" key="loading">
              <el-skeleton :rows="5" animated />
            </div>

            <div v-else-if="products.length > 0" key="products">
              <div class="product-grid">
                <div v-for="(product, index) in products" :key="product.id" class="product-item" :style="{ animationDelay: `${index * 0.05}s` }">
                  <product-card
                    :product="product"
                    @click="showProductDetail(product)"
                    @image-click="handleProductImageClick(product)"
                  />
                </div>
              </div>

              <div class="pagination-wrapper">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[50, 10, 20, 30]"
                  :total="totalItems"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="onPageSizeChange"
                  @current-change="onPageChange"
                  background
                />
              </div>
            </div>

            <div v-else key="empty" class="empty-state">
              <el-empty description="没有找到商品" />
              <el-button type="primary" plain @click="resetFilters">重置筛选条件</el-button>
            </div>
          </div>
        </Transition>
      </div>

      <!-- 商品详情对话框 -->
      <el-dialog
        v-model="productDialogVisible"
        :title="selectedProduct?.name || '商品详情'"
        width="90%"
        max-width="800px"
        destroy-on-close
        class="product-dialog"
        center
      >
        <div v-if="selectedProduct" class="product-detail">
          <div class="product-detail-img">
            <ImageGallery
              :images="productImages"
              :title="selectedProduct.name"
              height="400px"
            />
          </div>

          <div class="product-detail-info">
            <!-- 移除商品名称，已经显示在对话框标题 -->

            <div class="detail-info-row">
            <div class="detail-category">
              <span class="label">类别：</span>
              <el-tag size="small" effect="light">{{ getCategoryName(selectedProduct.categoryId) }}</el-tag>
                <span class="label" style="margin-left: 15px;">库存状态：</span>
                <span :class="{ 'stock-out': (selectedProduct.totalAvailableStock || selectedProduct.stock) === 0, 'stock-in': (selectedProduct.totalAvailableStock || selectedProduct.stock) > 0 }">
                  {{ (selectedProduct.totalAvailableStock || selectedProduct.stock) > 0 ? '有货' : '缺货' }}
                </span>
                <span class="label" style="margin-left: 15px;">已兑换/售卖：</span>
                <span class="exchange-count-value">{{ selectedProduct.exchangeCount || 0 }}+</span>
              </div>
            </div>

            <!-- 热门度信息展示 -->
            <div v-if="selectedProduct.isAutoHot || selectedProduct.isHot" class="detail-hot-info">
              <div class="hot-info-title">
                <el-icon><trophy /></el-icon>
                热门度信息
              </div>
              <div class="hot-info-content">
                <div v-if="selectedProduct.isHot && !selectedProduct.isAutoHot" class="hot-badge manual">
                  <el-tag type="danger" size="small">手动设置热门</el-tag>
                </div>
                <div v-if="selectedProduct.isAutoHot" class="hot-badge auto">
                  <el-tag type="warning" size="small">{{ getHotLabel(selectedProduct.hotTimeRange) }}</el-tag>
                  <div class="hot-stats">
                    <span class="hot-score">评分: {{ selectedProduct.hotScore }}</span>
                    <span class="hot-rank" v-if="selectedProduct.hotRank">排名: #{{ selectedProduct.hotRank }}</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="detail-prices">
              <div class="detail-price">
                <span class="label">光年币：</span>
                <span class="price ly-price">{{ selectedProduct.lyPrice }}</span>
              </div>
              <div class="detail-price">
                <span class="label">人民币：</span>
                <span class="price rmb-price">¥{{ selectedProduct.rmbPrice }}</span>
              </div>
            </div>

            <div class="detail-description">
              <div class="label">商品描述：</div>
              <p>{{ selectedProduct.description || '暂无描述' }}</p>
            </div>

            <!-- 添加兑换指引区域 -->
            <div class="exchange-guide-section">
              <div class="guide-label">兑换指引</div>
              <div class="guide-steps">
                <div class="guide-step">用光年币兑换先点击<strong>申请兑换</strong>按钮登记，用现金（支付宝）购买先点击<strong>申请购买</strong>按钮登记</div>
                <div class="guide-step">移步<strong>前台</strong>——飞书扫码提交兑换</div>
              </div>
              <div class="locations-container">
                <div class="locations-label">前台位置：</div>
                <ul class="location-list">
                  <li>北京、深圳和其他外阜：5D前台@<a href="https://www.feishu.cn/invitation/page/add_contact/?token=17aheda7-c8e5-4a40-bf3f-7711d00e0dd4&unique_id=hoelPrqJoEZmr3q1qVLiwg==" target="_blank" class="staff-link">张祎</a></li>
                  <li>武汉：29F前台@<a href="https://www.feishu.cn/invitation/page/add_contact/?token=e9ag948f-13ef-4e5c-8cea-18cbdb991e2c&unique_id=3FaBM6VfdXaLlORJvfhRJA==" target="_blank" class="staff-link">闫袁媛</a></li>
                  <li>长沙：47F前台@<a href="https://www.feishu.cn/invitation/page/add_contact/?token=468m8670-8195-4db7-b566-084d01f20b94&unique_id=JLzMyoRor7DRTflkurlt_A==" target="_blank" class="staff-link">莫璐</a></li>
                  <!-- <li>西安：20F前台@<a href="https://www.feishu.cn/invitation/page/add_contact/?token=a6fj4fb8-0032-4d48-b380-608b79badd81&unique_id=HYS0tA81UEdZkSebnWRJAQ==" target="_blank" class="staff-link">王宁</a></li> -->
                </ul>
              </div>
            </div>

            <!-- 添加确认阅读复选框 -->
            <div class="confirm-read-wrapper">
              <el-checkbox v-model="hasReadGuide" class="confirm-checkbox">
                我已阅读并理解兑换指引
              </el-checkbox>
            </div>

            <div class="detail-actions">
              <el-button
                type="primary"
                @click="openExchangeForm"
                :disabled="(selectedProduct.totalAvailableStock || selectedProduct.stock) === 0 || !hasReadGuide"
                class="exchange-btn"
                :title="getButtonTitle('exchange')"
              >
                申请兑换
              </el-button>
              <el-button
                type="success"
                @click="openPurchaseForm"
                :disabled="(selectedProduct.totalAvailableStock || selectedProduct.stock) === 0 || !hasReadGuide"
                class="purchase-btn"
                :title="getButtonTitle('purchase')"
              >
                申请购买
              </el-button>
              <el-button type="default" @click="productDialogVisible = false">关闭</el-button>
            </div>
          </div>
        </div>
      </el-dialog>

      <!-- 公告弹窗 -->
      <AnnouncementPopup
        v-model:visible="announcementVisible"
        :announcement="currentAnnouncement"
        @close="handleAnnouncementClose"
      />

      <!-- 反馈弹窗 -->
      <FeedbackDialog
        v-model:visible="feedbackDialogVisible"
        @submitted="handleFeedbackSubmitted"
      />

      <!-- 兑换表单对话框 -->
      <el-dialog
        v-model="exchangeFormVisible"
        :title="exchangePaymentMethod === 'ly' ? '商品兑换申请' : '商品购买申请'"
        width="90%"
        max-width="700px"
        destroy-on-close
        center
      >
        <ExchangeForm
          v-if="exchangeFormVisible && selectedProduct"
          :product="selectedProduct"
          @success="handleExchangeSuccess"
          @cancel="exchangeFormVisible = false"
          :defaultPaymentMethod="exchangePaymentMethod"
        />
      </el-dialog>

      <!-- 页脚 -->
      <footer class="footer">
        <p>2025@洋葱行政出品</p>
      </footer>
    </template>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import { useProductStore } from '../stores/products';
import { ElMessage } from 'element-plus';
import { Bell, ArrowDown, User, SwitchButton, ShoppingCart, Trophy } from '@element-plus/icons-vue';
import ProductCard from '../components/ProductCard.vue';
import ProductFilter from '../components/ProductFilter.vue';
import ImageGallery from '../components/ImageGallery.vue';
import AnnouncementPopup from '../components/AnnouncementPopup.vue';
import FeedbackDialog from '../components/FeedbackDialog.vue';
import ExchangeForm from '../components/ExchangeForm.vue';
import UserNotificationIcon from '../components/UserNotificationIcon.vue';

import { getCategories } from '../api/categories';
import { getProductImages } from '../api/productImages';
import { getAnnouncements } from '../api/announcements';
import { getDefaultProductImage } from '../utils/imageUtils';

const router = useRouter();
const authStore = useAuthStore();
const productStore = useProductStore();

// 获取认证状态
const isAuthenticated = computed(() => authStore.isAuthenticated);
const isAdmin = computed(() => authStore.isAdmin);

// 页面加载状态
const pageLoading = ref(true);
const categoriesLoaded = ref(false);
const productsLoaded = ref(false);
const announcementsLoaded = ref(false);

// 获取商品数据和分页信息
const products = computed(() => productStore.filteredProducts);
const loading = computed(() => productStore.loading);
const totalItems = computed(() => productStore.totalItems);
const currentPage = computed(() => productStore.currentPage);
const pageSize = computed(() => productStore.pageSize);

// 分类数据
const categories = ref([]);

// 筛选参数
const filterParams = reactive({
  search: '',
  category: '',
  sort: 'default',
  inStock: false,
  isNew: false,
  isHot: false
});

// 商品详情对话框
const productDialogVisible = ref(false);
const selectedProduct = ref(null);
const productImages = ref([]);
const hasReadGuide = ref(false);

// 公告弹窗
const announcementVisible = ref(false);
const currentAnnouncement = ref(null);

// 反馈弹窗
const feedbackDialogVisible = ref(false);

// 兑换表单
const exchangeFormVisible = ref(false);
const exchangePaymentMethod = ref('ly');

// 获取类别名称
const getCategoryName = (categoryId) => {
  const category = categories.value.find(c => c.id === categoryId);
  return category ? category.name : '未分类';
};

// 获取热门标签文本
const getHotLabel = (timeRange) => {
  const labels = {
    'all': '累积热门',
    '30d': '30天热门',
    '7d': '7天热门',
    '1d': '今日热门'
  };
  return labels[timeRange] || '热门';
};

// 从API获取分类数据
const fetchCategories = async () => {
  try {
    const response = await getCategories();
    categories.value = response;
    categoriesLoaded.value = true;
  } catch (error) {
    console.error('获取分类列表失败:', error);
    ElMessage.error('获取分类列表失败');
    categoriesLoaded.value = true; // Even in error case, mark as loaded
  }
};

// 处理筛选变化
const handleFilterChange = (filters) => {
  console.log('接收到筛选变化:', filters);

  // 首先更新本地筛选状态
  Object.assign(filterParams, filters);

  // 使用批量更新方法，确保原子性操作
  productStore.updateAllFilters({
    search: filters.search || '',
    category: filters.category || '',
    sort: filters.sort || '',
    filters: {
      minLyPrice: filters.minLyPrice,
      maxLyPrice: filters.maxLyPrice,
      minRmbPrice: filters.minRmbPrice,
      maxRmbPrice: filters.maxRmbPrice,
      inStock: filters.inStock,
      isNew: filters.isNew,
      isHot: filters.isHot,
      startDate: filters.startDate,
      endDate: filters.endDate
    }
  });
};

// 重置筛选
const resetFilters = () => {
  Object.assign(filterParams, {
    search: '',
    category: '',
    sort: 'default',
    inStock: false,
    isNew: false,
    isHot: false
  });

  // 重置产品存储的筛选状态
  productStore.resetFilters();
};

// 处理点击意见反馈按钮
const handleFeedback = () => {
  feedbackDialogVisible.value = true;
};

// 处理反馈提交完成
const handleFeedbackSubmitted = () => {
  ElMessage.success('感谢您的反馈，我们会尽快处理！');
};

// 导航到管理后台
const goToAdmin = () => {
  router.push('/admin/products');
};

// 导航到登录页面
const goToLogin = () => {
  router.push('/login');
};

// 处理退出登录
const handleLogout = () => {
  authStore.logout();
  ElMessage.success('已退出登录');
  router.push('/');
};

// 获取按钮提示文本
const getButtonTitle = (type) => {
  const currentStock = selectedProduct.value?.totalAvailableStock || selectedProduct.value?.stock || 0;
  if (currentStock === 0) {
    return '商品缺货暂不支持兑换和购买，加急采购中，请耐心等待';
  }
  if (!hasReadGuide.value) {
    return '请先阅读并确认兑换指引';
  }
  return '';
};

// 显示商品详情
const showProductDetail = async (product) => {
  console.log('显示商品详情:', product.name, 'ID:', product.id);
  selectedProduct.value = product;
  productDialogVisible.value = true;
  hasReadGuide.value = false;

  // 获取商品图片
  try {
    console.log('开始获取商品图片，商品ID:', product.id);
    const images = await getProductImages(product.id);
    console.log('获取到的图片数据:', images);

    if (images && images.length > 0) {
      // 按照排序顺序排列图片
      productImages.value = images.sort((a, b) => a.sortOrder - b.sortOrder);
      console.log('设置商品图片成功，图片数量:', productImages.value.length);
    } else if (product.imageUrl) {
      // 兼容旧数据结构，如果没有多图但有单图
      productImages.value = [{ imageUrl: product.imageUrl }];
      console.log('使用商品主图作为详情图片:', product.imageUrl);
    } else {
      // 没有图片时使用默认图片
      productImages.value = [{ imageUrl: getDefaultProductImage('400x400') }];
      console.log('没有图片，使用默认占位图');
    }
  } catch (error) {
    console.error('获取商品图片失败:', error);
    ElMessage.error('获取商品图片失败，使用备用图片');

    // 使用默认图片
    if (product.imageUrl) {
      productImages.value = [{ imageUrl: product.imageUrl }];
      console.log('使用商品主图作为备用图片:', product.imageUrl);
    } else {
      productImages.value = [{ imageUrl: getDefaultProductImage('400x400') }];
      console.log('使用默认占位图作为备用图片');
    }
  }
};

// 点击商品信息区域，显示商品详情对话框
const handleProductImageClick = (product) => {
  // El-image组件的preview功能会自动处理全屏预览
  // 此处可以添加额外的逻辑，例如记录用户行为等
  console.log('Product image clicked:', product.name);
};

// 申请兑换商品 - 打开飞书表单
const openExchangeForm = () => {
  if (!isAuthenticated.value) {
    ElMessage.warning('请先登录后再申请兑换');
    router.push('/login');
    return;
  }

  const currentStock = selectedProduct.value.totalAvailableStock || selectedProduct.value.stock || 0;
  if (currentStock === 0) {
    ElMessage.warning('商品缺货暂不支持兑换和购买，加急采购中，请耐心等待');
    return;
  }

  // 显示兑换表单对话框
  exchangeFormVisible.value = true;
  // 设置支付方式为光年币
  exchangePaymentMethod.value = 'ly';
};

// 申请购买商品 - 打开飞书表单
const openPurchaseForm = () => {
  if (!isAuthenticated.value) {
    ElMessage.warning('请先登录后再申请购买');
    router.push('/login');
    return;
  }

  const currentStock = selectedProduct.value.totalAvailableStock || selectedProduct.value.stock || 0;
  if (currentStock === 0) {
    ElMessage.warning('商品缺货暂不支持兑换和购买，加急采购中，请耐心等待');
    return;
  }

  // 显示兑换表单对话框
  exchangeFormVisible.value = true;
  // 设置支付方式为人民币
  exchangePaymentMethod.value = 'rmb';
};

// 分页处理
const onPageChange = (page) => {
  productStore.setPage(page);
};

const onPageSizeChange = (size) => {
  productStore.setPageSize(size);
};

// 显示公告弹窗
const showAnnouncement = () => {
  announcementVisible.value = true;
};

// 获取公告
const fetchLatestAnnouncement = async () => {
  try {
    const params = {
      page: 1,
      limit: 1,
      sort: 'newest',
      status: 'active'
    };

    const response = await getAnnouncements(params);
    if (response.data && response.data.length > 0) {
      // 处理公告数据，确保内容正确
      const announcement = response.data[0];

      // 处理公告中的图片URL
      if (announcement.contentHtml) {
        announcement.contentHtml = processAnnouncementContent(announcement.contentHtml);
      }

      if (announcement.imageUrl) {
        announcement.imageUrl = processImageUrl(announcement.imageUrl);
      }

      currentAnnouncement.value = announcement;

      // 检查是否应该自动显示公告
      const lastClosedAnnouncementId = localStorage.getItem('lastClosedAnnouncementId');

      // 确保比较的值是相同类型的字符串
      const currentId = currentAnnouncement.value.id.toString();

      console.log('公告检查:', {
        currentId,
        lastClosedAnnouncementId,
        shouldShow: !lastClosedAnnouncementId || lastClosedAnnouncementId !== currentId
      });

      // 如果是新公告或用户未关闭过，则显示
      if (!lastClosedAnnouncementId || lastClosedAnnouncementId !== currentId) {
        // 强制显示新公告
        announcementVisible.value = true;
      }
    }
    announcementsLoaded.value = true;
  } catch (error) {
    console.error('获取公告失败:', error);
    announcementsLoaded.value = true; // Even in error case, mark as loaded
  }
};

// 处理公告内容中的图片URL
const processAnnouncementContent = (html) => {
  if (!html) return '';

  // 替换所有img标签中的src属性，处理native-resource格式
  const processedHtml = html.replace(/<img[^>]+src="([^"]+)"[^>]*>/g, (match, src) => {
    let newSrc = src;

    if (src.startsWith('native-resource://')) {
      console.warn('处理公告图片URL:', src);
      newSrc = src.replace('native-resource://', '/');
    }

    // 特别处理/uploads路径，转换为/api/uploads
    if (newSrc.startsWith('/uploads/')) {
      newSrc = newSrc.replace(/^\/uploads\//, '/api/uploads/');
      console.log('转换uploads路径:', src, '->', newSrc);
    }

    return match.replace(src, newSrc);
  });

  return processedHtml;
};

// 处理图片URL
const processImageUrl = (url) => {
  if (!url) return '';

  // 处理native-resource格式
  if (url.startsWith('native-resource://')) {
    return url.replace('native-resource://', '/');
  }

  // 处理uploads路径
  if (url.startsWith('/uploads/')) {
    return url.replace(/^\/uploads\//, '/api/uploads/');
  }

  return url;
};

// 处理公告关闭
const handleAnnouncementClose = (dontShowAgain) => {
  if (currentAnnouncement.value) {
    // 始终存储最后关闭的公告ID
    localStorage.setItem('lastClosedAnnouncementId', currentAnnouncement.value.id.toString());

    // 如果用户不想再次看到，则将其标记为"已读"
    if (dontShowAgain) {
      localStorage.setItem('announcement_' + currentAnnouncement.value.id + '_read', 'true');
    }
  }
  announcementVisible.value = false;
};

// 处理兑换申请成功
const handleExchangeSuccess = (data) => {
  // 关闭表单对话框
  exchangeFormVisible.value = false;

  // 显示成功提示
  ElMessage.success('兑换申请已提交成功！');

  // 不再跳转到兑换记录页面，而是留在当前页面
  // 刷新商品列表以更新库存信息
  productStore.fetchProducts();
};

// 检查所有数据是否加载完成
watch([categoriesLoaded, productsLoaded, announcementsLoaded],
  ([catLoaded, prodLoaded, annLoaded]) => {
    if (catLoaded && prodLoaded && annLoaded) {
      // 所有数据加载完成后，延迟一点时间再移除加载状态，使界面更平滑
      setTimeout(() => {
        pageLoading.value = false;
      }, 100);
    }
  }
);

// 监听产品store的loading状态
watch(() => productStore.loading, (isLoading) => {
  if (!isLoading) {
    productsLoaded.value = true;
  }
});

// 生命周期钩子
onMounted(async () => {
  // 处理飞书登录回调（移动端）
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('feishu_login') === 'success') {
    console.log('检测到飞书登录回调');

    const token = urlParams.get('token');
    const userStr = urlParams.get('user');

    if (token && userStr) {
      try {
        const user = JSON.parse(decodeURIComponent(userStr));

        // 存储token和用户信息
        sessionStorage.setItem('token', token);
        sessionStorage.setItem('user', JSON.stringify(user));

        // 更新auth store
        authStore.token = token;
        authStore.user = user;

        console.log('飞书登录成功，用户信息已保存');
        ElMessage.success('飞书登录成功！');

        // 清理URL参数
        const cleanUrl = window.location.origin + window.location.pathname;
        window.history.replaceState({}, document.title, cleanUrl);
      } catch (error) {
        console.error('处理飞书登录回调失败:', error);
        ElMessage.error('登录信息处理失败，请重新登录');
      }
    }
  }

  // 设置页面加载状态
  pageLoading.value = true;
  categoriesLoaded.value = false;
  productsLoaded.value = false;
  announcementsLoaded.value = false;

  // 并行加载数据，提高加载效率
  Promise.all([
    fetchCategories(),
    productStore.fetchProducts(),
    fetchLatestAnnouncement()
  ]);

  document.addEventListener('fullscreenchange', () => {
    isFullscreen.value = !!document.fullscreenElement;
  });
});

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'userCenter':
      // 跳转到个人中心页面
      router.push('/user/profile');
      break;
    case 'logout':
      handleLogout();
      break;
  }
};
</script>

<style scoped>
.home-container {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 1rem 1.5rem;
  box-sizing: border-box;
}

.home-container-loading {
  opacity: 0;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #edf2f7;
  transition: all 0.3s ease;
}

.title {
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #3490dc, #6574cd);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
  position: relative;
  letter-spacing: -0.5px;
  transition: all 0.3s ease;
}

.title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #3490dc, #6574cd);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.title:hover::after {
  width: 100%;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.admin-btn {
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.auth-btn {
  margin-left: 10px;
}

.header-user-section {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: rgba(64, 158, 255, 0.1);
}

.notification-icon {
  margin-right: 24px;
  padding-right: 8px;
  position: relative;
  z-index: 1;
}

.user-avatar {
  margin-right: 8px;
  background-color: #409eff;
}

.username {
  font-weight: 600;
  color: #4a5568;
  margin-right: 4px;
}

.content {
  padding: 1rem 0;
  max-width: 100%;
  margin: 0 auto;
}

.loading-wrapper {
  padding: 20px;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 1rem;
}

.product-item {
  display: flex;
  transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275),
              opacity 0.4s ease,
              box-shadow 0.4s ease;
  animation: fadeInProduct 0.6s ease-in-out both;
  opacity: 0;
  will-change: transform, opacity;
  min-height: 480px;
}

.product-item:nth-child(1) { animation-delay: 0.05s; }
.product-item:nth-child(2) { animation-delay: 0.1s; }
.product-item:nth-child(3) { animation-delay: 0.15s; }
.product-item:nth-child(4) { animation-delay: 0.2s; }
.product-item:nth-child(5) { animation-delay: 0.25s; }
.product-item:nth-child(6) { animation-delay: 0.3s; }

.product-item:hover {
  transform: translateY(-10px) scale(1.02);
  z-index: 2;
}

@keyframes fadeInProduct {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  padding: 3rem 0;
}

.pagination-wrapper {
  margin-top: 40px;
  display: flex;
  justify-content: center;
  transition: all 0.3s ease;
}

.product-detail {
  display: flex;
  flex-wrap: wrap;
  gap: 40px;
  animation: fadeInDetail 0.5s ease-in-out;
}

@keyframes fadeInDetail {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.product-detail-img {
  flex: 1;
  min-width: 300px;
  max-width: 400px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.product-detail-img:hover {
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
  transform: translateY(-5px);
}

.product-detail-info {
  flex: 1;
  min-width: 300px;
  transition: all 0.3s ease;
  padding: 0 10px;
  margin-top: 0; /* 确保与图片顶部对齐 */
}

.detail-name {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 24px;
  color: #2d3748;
  letter-spacing: -0.5px;
  transition: color 0.3s ease;
  border-bottom: 2px solid #f0f4f8;
  padding-bottom: 12px;
}

.detail-info-row {
  margin-bottom: 20px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.detail-category,
.detail-stock {
  margin-right: 20px;
  transition: all 0.3s ease;
}

.detail-prices {
  margin-bottom: 20px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.label {
  color: #718096;
  margin-right: 10px;
  font-weight: 600;
}

.price {
  font-size: 28px;
  font-weight: 700;
  transition: all 0.3s ease;
}

.ly-price {
  color: #4299e1;
  background: linear-gradient(135deg, #3490dc, #6574cd);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.ly-price:hover {
  text-shadow: 0 0 5px rgba(64, 158, 255, 0.5);
  transform: scale(1.05);
}

.rmb-price {
  color: #f56565;
  background: linear-gradient(135deg, #e53e3e, #ed64a6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.rmb-price:hover {
  text-shadow: 0 0 5px rgba(245, 108, 108, 0.5);
  transform: scale(1.05);
}

.stock-out {
  color: #e53e3e;
  font-weight: 700;
}

.stock-in {
  color: #4299e1;
  font-weight: 700;
}

.exchange-count-value {
  color: #1890ff;
  font-weight: 700;
}

.detail-description {
  margin: 24px 0;
  padding: 20px;
  background-color: #f8fafc;
  border-radius: 12px;
  border-left: 4px solid #4299e1;
  transition: all 0.3s ease;
}

.detail-description:hover {
  background-color: #edf2f7;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.detail-description p {
  line-height: 1.7;
  color: #4a5568;
  white-space: pre-line;
  margin-top: 8px;
}

.exchange-guide-section {
  margin-top: 20px;
  padding: 20px;
  background-color: #f0f7ff;
  border-radius: 12px;
  border-left: 4px solid #409eff;
  transition: all 0.3s ease;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.guide-label {
  font-size: 16px;
  font-weight: 700;
  color: #409eff;
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  position: relative;
  padding-bottom: 8px;
}

.guide-label:before {
  content: "";
  display: inline-block;
  width: 4px;
  height: 16px;
  background-color: #409eff;
  margin-right: 8px;
  border-radius: 2px;
}

.guide-label:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, #409eff, transparent);
}

.guide-steps {
  list-style-type: none;
  padding: 0;
  margin: 0 0 20px 0;
}

.guide-step {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
  padding: 10px 15px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
  color: #303133;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  position: relative;
  padding-left: 40px;
  transition: all 0.3s ease;
}

.guide-step:before {
  content: "1";
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: white;
  font-weight: bold;
  background-color: #409eff;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.guide-step:nth-child(2):before {
  content: "2";
}

.guide-step strong {
  color: #409eff;
  font-weight: 700;
}

.guide-step:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.locations-container {
  margin-top: 20px;
}

.locations-label {
  font-weight: 600;
  color: #303133;
  margin-bottom: 10px;
  position: relative;
  padding-left: 18px;
}

.locations-label:before {
  content: "📍";
  position: absolute;
  left: 0;
  font-size: 14px;
}

.location-list {
  list-style-type: none;
  padding-left: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 10px;
}

.location-list li {
  padding: 8px 12px;
  background-color: #f2f6fc;
  border-radius: 4px;
  color: #606266;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.location-list li:before {
  content: "🏢";
  margin-right: 8px;
}

.location-list li:hover {
  background-color: #ecf5ff;
  color: #409eff;
  transform: translateY(-2px);
}

.confirm-read-wrapper {
  margin: 20px 0;
  padding: 15px;
  background-color: #fff8e6;
  border-radius: 8px;
  border-left: 4px solid #e6a23c;
}

.confirm-checkbox {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #e6a23c;
}

.detail-actions {
  margin-top: 30px;
  display: flex;
  gap: 15px;
}

.exchange-btn {
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s;
  margin-right: 10px;
}

.purchase-btn {
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s;
  background-color: #67c23a;
  border-color: #67c23a;
  color: white;
  margin-right: 10px;
}

.exchange-btn:not(:disabled):hover,
.purchase-btn:not(:disabled):hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(66, 153, 225, 0.3);
}

.exchange-btn:disabled,
.purchase-btn:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.filter-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 20px;
}

/* 产品对话框样式 */
:deep(.product-dialog .el-dialog__header) {
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc, #edf2f7);
  border-bottom: 1px solid #e2e8f0;
  border-radius: 12px 12px 0 0;
  text-align: center;
}

:deep(.product-dialog .el-dialog__body) {
  padding: 30px;
}

:deep(.product-dialog .el-dialog__title) {
  font-weight: 700;
  color: #2d3748;
  font-size: 1.5rem;
}

:deep(.product-dialog .el-dialog) {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

/* 响应式设计 */
@media (min-width: 1920px) {
  .product-grid {
    grid-template-columns: repeat(6, 1fr);
    gap: 20px;
  }

  .content {
    padding: 1.5rem 0;
    max-width: 95%;
    margin: 0 auto;
  }

  .home-container {
    padding: 1rem 1.5rem;
  }

  .location-list {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1600px) and (max-width: 1919px) {
  .product-grid {
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;
  }

  .content {
    padding: 1.5rem 0;
    max-width: 95%;
    margin: 0 auto;
  }

  .home-container {
    padding: 1rem 1.5rem;
  }

  .location-list {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1200px) and (max-width: 1599px) {
  .product-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
  }

  .content {
    max-width: 1600px;
    margin: 0 auto;
  }

  .location-list {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .product-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
  }

  .product-item {
    min-height: 450px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .product-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }

  .product-item {
    min-height: 420px;
  }
}

@media (max-width: 767px) {
  .home-container {
    padding: 1rem 0.75rem;
  }

  .product-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .product-item {
    min-height: 400px;
  }
}

@media (max-width: 576px) {
  .home-container {
    padding: 0.75rem 0.5rem;
  }

  .product-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .product-item {
    min-height: 460px;
  }
}

/* 页脚样式 */
.footer {
  margin-top: 40px;
  text-align: center;
  padding: 30px 20px;
  background-color: #edf2f7;
  border-top: 2px solid #e2e8f0;
}

.footer p {
  color: #4a5568;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  letter-spacing: 1px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #4a5568, #2d3748);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 添加联系人链接样式 */
.staff-link {
  color: #409eff;
  text-decoration: none;
  font-weight: 600;
  position: relative;
  padding: 0 2px;
  transition: all 0.3s ease;
}

.staff-link:hover {
  color: #f56c6c;
  text-decoration: none;
}

.staff-link:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: #409eff;
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.staff-link:hover:after {
  transform: scaleX(1);
  background-color: #f56c6c;
}

/* 热门商品展示区域样式 */
.hot-products-section {
  margin: 20px auto;
  max-width: 1200px;
  padding: 0 20px;
}

.hot-products-placeholder {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 40px;
  text-align: center;
}

.hot-products-placeholder h3 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 20px;
}

.hot-products-placeholder p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

@media (max-width: 768px) {
  .hot-products-section {
    margin: 15px auto;
    padding: 0 15px;
  }
}

/* 商品详情页热门度信息样式 */
.detail-hot-info {
  margin: 20px 0;
  padding: 16px;
  background: linear-gradient(135deg, #fff9e6 0%, #fff2cc 100%);
  border-radius: 8px;
  border: 1px solid #ffd700;
}

.hot-info-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #b8860b;
  margin-bottom: 12px;
}

.hot-info-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.hot-badge {
  display: flex;
  align-items: center;
  gap: 12px;
}

.hot-stats {
  display: flex;
  gap: 16px;
  font-size: 14px;
}

.hot-score {
  color: #409eff;
  font-weight: 600;
}

.hot-rank {
  color: #f39c12;
  font-weight: 600;
}
</style>
