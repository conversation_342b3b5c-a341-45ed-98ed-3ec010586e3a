#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

echo -e "${YELLOW}===== 项目一键优化脚本 =====${NC}"

# 确认是否继续
echo -e "${RED}警告: 此脚本将对项目结构进行大量优化和修改。${NC}"
echo -e "${RED}建议在执行前备份您的项目。${NC}"
echo -e "${BLUE}是否继续? [y/N]${NC}"
read -r response
if [[ ! "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
  echo -e "${YELLOW}操作已取消${NC}"
  exit 0
fi

# 步骤1: 项目结构优化
echo -e "\n${BLUE}===== 步骤1: 项目结构优化 =====${NC}"
./cleanup-project.sh

# 步骤2: 依赖优化
echo -e "\n${BLUE}===== 步骤2: 依赖优化 =====${NC}"
echo -e "${YELLOW}是否检查和优化项目依赖? [y/N]${NC}"
read -r response
if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
  # 安装依赖分析工具
  echo -e "${BLUE}正在安装依赖分析工具...${NC}"
  npm install -g depcheck npm-check
  
  # 运行依赖优化脚本
  ./optimize-dependencies.sh
else
  echo -e "${YELLOW}跳过依赖优化${NC}"
fi

# 步骤3: 代码优化
echo -e "\n${BLUE}===== 步骤3: 代码优化建议 =====${NC}"
echo -e "${YELLOW}以下是项目代码优化建议:${NC}"
echo -e "1. 检查并删除未使用的组件和工具函数"
echo -e "2. 确保所有API调用都有适当的错误处理"
echo -e "3. 优化大型组件，考虑拆分为更小的可复用组件"
echo -e "4. 检查并修复控制台警告和错误"
echo -e "5. 确保所有页面都有适当的加载状态和错误处理"
echo -e "6. 优化图片和静态资源大小"
echo -e "7. 实现懒加载和代码分割以提高性能"

# 步骤4: 创建项目优化报告
echo -e "\n${BLUE}===== 步骤4: 创建优化报告 =====${NC}"

# 创建优化报告目录
mkdir -p "$PROJECT_DIR/docs/reports"

# 创建优化报告
cat > "$PROJECT_DIR/docs/reports/OPTIMIZATION_REPORT.md" << EOF
# 项目优化报告

## 已完成的优化

### 1. 项目结构优化

- 统一了上传目录到 \`server/uploads\`
- 整理了脚本文件到 \`scripts\` 目录
- 清理了临时文件
- 整理了文档到 \`docs\` 目录
- 更新了 \`.gitignore\` 文件
- 创建了符号链接确保兼容性

### 2. 依赖优化

- 分析并移除了未使用的依赖
- 创建了生产环境优化脚本
- 创建了开发环境初始化脚本

## 建议的进一步优化

### 代码优化

1. **组件优化**
   - 检查并删除未使用的组件
   - 将大型组件拆分为更小的可复用组件
   - 确保所有组件都有良好的错误边界

2. **API调用优化**
   - 确保所有API调用都有适当的错误处理
   - 实现请求取消机制
   - 考虑使用缓存机制减少重复请求

3. **性能优化**
   - 实现懒加载和代码分割
   - 优化图片和静态资源
   - 减少不必要的渲染

4. **代码质量**
   - 添加更多单元测试
   - 确保代码风格一致性
   - 减少代码重复

### 部署优化

1. **构建流程**
   - 优化构建脚本
   - 实现自动化部署流程
   - 添加构建版本号和构建时间

2. **服务器配置**
   - 确保正确配置缓存策略
   - 启用GZIP压缩
   - 配置适当的安全头部

## 后续建议

1. 定期运行依赖分析，移除未使用的依赖
2. 保持项目结构清晰，遵循目录规范
3. 定期更新依赖以修复安全漏洞
4. 持续进行代码审查和优化
EOF

echo -e "${GREEN}优化报告已创建: docs/reports/OPTIMIZATION_REPORT.md${NC}"

# 输出完成信息
echo -e "\n${GREEN}===== 项目优化完成 =====${NC}"
echo -e "${BLUE}优化脚本:${NC}"
echo -e "  项目结构优化: ./cleanup-project.sh"
echo -e "  依赖优化: ./optimize-dependencies.sh"
echo -e "${BLUE}新增脚本:${NC}"
echo -e "  生产环境优化: scripts/optimize-production.sh"
echo -e "  开发环境初始化: scripts/dev-setup.sh"
echo -e "${BLUE}优化报告:${NC}"
echo -e "  docs/reports/OPTIMIZATION_REPORT.md"
echo -e "${YELLOW}提示: 请查看优化报告了解更多优化建议${NC}"
echo -e "${YELLOW}重要: 在应用优化更改后，请彻底测试应用确保功能正常${NC}" 