const { DataTypes, Op } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 生成唯一的订单编号
 * @param {string} paymentMethod - 支付方式 ('ly' 或 'rmb')
 * @param {Date} createdAt - 订单创建时间
 * @returns {Promise<string>} 订单编号
 */
async function generateUniqueOrderNumber(paymentMethod, createdAt = new Date()) {
  const maxRetries = 5;
  let attempt = 0;

  while (attempt < maxRetries) {
    try {
      const today = new Date(createdAt);
      const dateStr = today.getFullYear().toString() +
                     (today.getMonth() + 1).toString().padStart(2, '0') +
                     today.getDate().toString().padStart(2, '0');

      const prefix = paymentMethod === 'ly' ? 'GNB-' : 'RMB-';

      // 获取当天该支付方式的最大序列号
      const dayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const dayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

      const lastOrder = await sequelize.query(
        `SELECT orderNumber FROM exchanges
         WHERE paymentMethod = ?
         AND createdAt >= ?
         AND createdAt < ?
         AND orderNumber IS NOT NULL
         ORDER BY orderNumber DESC
         LIMIT 1`,
        {
          replacements: [paymentMethod, dayStart, dayEnd],
          type: sequelize.QueryTypes.SELECT
        }
      );

      let sequenceNumber = 1;
      if (lastOrder.length > 0) {
        const lastOrderNumber = lastOrder[0].orderNumber;
        const lastSequence = parseInt(lastOrderNumber.slice(-8));
        sequenceNumber = lastSequence + 1;
      }

      // 生成8位序列号
      const sequenceStr = sequenceNumber.toString().padStart(8, '0');
      const orderNumber = prefix + dateStr + sequenceStr;

      // 检查订单编号是否已存在
      const existingOrder = await sequelize.query(
        'SELECT id FROM exchanges WHERE orderNumber = ?',
        {
          replacements: [orderNumber],
          type: sequelize.QueryTypes.SELECT
        }
      );

      if (existingOrder.length === 0) {
        console.log(`生成订单编号: ${orderNumber} (尝试 ${attempt + 1}/${maxRetries})`);
        return orderNumber;
      } else {
        console.warn(`订单编号 ${orderNumber} 已存在，重新生成...`);
        attempt++;
      }
    } catch (error) {
      console.error(`生成订单编号失败 (尝试 ${attempt + 1}/${maxRetries}):`, error);
      attempt++;
    }
  }

  console.error(`生成订单编号失败，已达到最大重试次数 ${maxRetries}`);
  return null;
}

const Exchange = sequelize.define('Exchange', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  orderNumber: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '格式化的订单编号'
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  productId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    validate: {
      min: 1
    }
  },
  unitPrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '订单创建时的商品单价快照'
  },
  totalAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '订单总金额'
  },
  priceType: {
    type: DataTypes.ENUM('ly', 'rmb'),
    allowNull: true,
    comment: '价格类型：ly(光年币价格)或rmb(人民币价格)'
  },
  paymentMethod: {
    type: DataTypes.ENUM('ly', 'rmb'),
    allowNull: false,
    comment: '支付方式：ly(光年币)或rmb(人民币)'
  },
  contactInfo: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '联系信息'
  },
  location: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '用户所在职场位置（旧字段，将被workplaceId替代）'
  },
  workplaceId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'workplaces',
      key: 'id'
    },
    comment: '关联到workplaces表的外键'
  },
  remarks: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '用户备注'
  },
  paymentProofUrl: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '支付凭证图片URL'
  },
  status: {
    type: DataTypes.ENUM('pending', 'approved', 'shipped', 'completed', 'rejected', 'cancelled'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '状态：待处理、已批准、已发货、已完成、已拒绝、已取消'
  },
  adminRemarks: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '管理员备注'
  },
  trackingNumber: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '物流单号'
  },
  trackingCompany: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '物流公司'
  },
  totalIncome: {
    type: DataTypes.VIRTUAL,
    get() {
      // 如果已有totalAmount字段，优先使用
      if (this.getDataValue('totalAmount') && this.getDataValue('totalAmount') > 0) {
        return parseFloat(this.getDataValue('totalAmount'));
      }

      // 向后兼容的计算逻辑
      const quantity = this.getDataValue('quantity') || 1;
      if (this.Product) {
        if (this.getDataValue('paymentMethod') === 'ly') {
          return this.Product.lyPrice * quantity;
        } else {
          return this.Product.rmbPrice * quantity;
        }
      }
      return 0;
    }
  }
}, {
  tableName: 'exchanges',
  timestamps: true,
  hooks: {
    beforeCreate: async (exchange, options) => {
      try {
        // 获取商品信息并保存价格快照
        const Product = sequelize.models.Product;
        if (!Product) {
          console.error('找不到Product模型，无法计算总金额');
          return;
        }

        const product = await Product.findByPk(exchange.productId);
        if (product) {
          // 保存价格快照
          if (exchange.paymentMethod === 'ly') {
            exchange.unitPrice = product.lyPrice;
            exchange.priceType = 'ly';
          } else {
            exchange.unitPrice = parseFloat(product.rmbPrice);
            exchange.priceType = 'rmb';
          }

          // 基于价格快照计算总金额
          exchange.totalAmount = exchange.unitPrice * exchange.quantity;

          console.log(`订单价格快照已保存: 单价=${exchange.unitPrice}, 数量=${exchange.quantity}, 总额=${exchange.totalAmount}`);
        }

        // 记录创建操作
        console.log(`开始创建订单，支付方式: ${exchange.paymentMethod}`);

        // 不预先生成orderNumber，等待afterCreate钩子获取正确的ID后再生成
        // 这是因为Sequelize的beforeCreate钩子中无法获取到即将分配的ID
      } catch (error) {
        console.error('处理订单创建前钩子失败:', error);
      }
    },
    afterCreate: async (exchange, options) => {
      try {
        // 直接通过数据库实例查询，避免循环依赖
        const Product = sequelize.models.Product;
        if (!Product) {
          console.error('找不到Product模型，无法更新库存');
          return;
        }

        // 使用事务和锁来安全更新库存，避免并发冲突
        const updateTransaction = options.transaction || await sequelize.transaction();
        let shouldCommit = !options.transaction; // 只有在没有外部事务时才提交

        try {
          console.log(`开始更新商品库存，商品ID: ${exchange.productId}, 兑换数量: ${exchange.quantity}`);

          // 获取商品信息并锁定行，防止并发修改
          const product = await Product.findByPk(exchange.productId, {
            lock: updateTransaction.LOCK.UPDATE,
            transaction: updateTransaction
          });

          if (product) {
            // 计算新的库存和兑换次数
            const newStock = Math.max(0, product.stock - exchange.quantity);
            const newExchangeCount = (product.exchangeCount || 0) + exchange.quantity;

            // 更新库存和兑换次数
            await product.update({
              stock: newStock,
              exchangeCount: newExchangeCount
            }, {
              transaction: updateTransaction
            });

            if (shouldCommit) {
              await updateTransaction.commit();
            }

            console.log(`商品库存更新成功: ${product.name}, 剩余库存: ${newStock}, 总兑换次数: ${newExchangeCount}`);
          } else {
            console.error(`商品不存在: ${exchange.productId}`);
            if (shouldCommit) await updateTransaction.rollback();
          }
        } catch (updateError) {
          if (shouldCommit) {
            await updateTransaction.rollback();
          }

          // 对于锁等待超时，记录错误但不影响主流程
          if (updateError.name === 'SequelizeDatabaseError' && updateError.original?.code === 'ER_LOCK_WAIT_TIMEOUT') {
            console.error('库存更新锁等待超时，但订单已创建成功。库存将在后台异步更新。');
            // 可以在这里添加异步重试逻辑
          } else {
            console.error('更新商品库存失败:', updateError);
          }
          // 不抛出错误，避免影响订单创建的主流程
        }

        // 异步生成订单编号，避免阻塞主流程
        if (!exchange.orderNumber) {
          console.log(`开始为订单 ID=${exchange.id} 异步生成订单编号`);

          // 使用 setImmediate 异步执行，避免阻塞当前事务
          setImmediate(async () => {
            try {
              const orderNumber = await generateUniqueOrderNumber(exchange.paymentMethod, exchange.createdAt);

              if (orderNumber) {
                // 使用独立的事务更新订单编号，避免与其他操作冲突
                const orderTransaction = await sequelize.transaction();

                try {
                  await sequelize.query(
                    'UPDATE exchanges SET orderNumber = ? WHERE id = ?',
                    {
                      replacements: [orderNumber, exchange.id],
                      type: sequelize.QueryTypes.UPDATE,
                      transaction: orderTransaction
                    }
                  );

                  await orderTransaction.commit();
                  console.log(`异步更新订单 ID=${exchange.id} 的订单编号成功: ${orderNumber}`);
                } catch (updateError) {
                  await orderTransaction.rollback();
                  console.error(`异步更新订单 ID=${exchange.id} 的订单编号失败:`, updateError);
                }
              } else {
                console.error(`订单 ID=${exchange.id} 订单编号生成失败`);
              }
            } catch (error) {
              console.error(`订单 ID=${exchange.id} 异步生成订单编号时出错:`, error);
            }
          });
        } else {
          console.log(`订单 ID=${exchange.id} 已有订单编号: ${exchange.orderNumber}`);
        }
      } catch (error) {
        console.error('更新商品库存或订单编号失败:', error);
      }
    }
  }
});

module.exports = Exchange;
