-- =====================================================
-- 多职场库存分配功能 - 第一阶段迁移脚本
-- 创建商品职场库存表 (product_workplace_stocks)
-- 创建时间: 2025-07-28
-- 版本: v1.0.0
-- =====================================================

-- 检查表是否已存在，如果存在则跳过创建
SET @table_exists = (
    SELECT COUNT(*) 
    FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name = 'product_workplace_stocks'
);

-- 只有在表不存在时才创建
SET @sql = IF(@table_exists = 0, 
    'CREATE TABLE product_workplace_stocks (
        id INT AUTO_INCREMENT PRIMARY KEY COMMENT ''主键ID'',
        productId INT NOT NULL COMMENT ''商品ID'',
        workplaceId INT NOT NULL COMMENT ''职场ID'',
        stock INT NOT NULL DEFAULT 0 COMMENT ''职场库存数量'',
        reservedStock INT NOT NULL DEFAULT 0 COMMENT ''预留库存（待处理订单占用）'',
        availableStock INT GENERATED ALWAYS AS (stock - reservedStock) STORED COMMENT ''可用库存（虚拟字段）'',
        minStockAlert INT DEFAULT 10 COMMENT ''最低库存告警阈值'',
        maxStockLimit INT DEFAULT NULL COMMENT ''最大库存限制'',
        lastStockUpdate DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''最后库存更新时间'',
        createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',
        updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间'',
        
        -- 索引设计
        UNIQUE KEY uk_product_workplace (productId, workplaceId) COMMENT ''商品-职场唯一索引'',
        INDEX idx_product_id (productId) COMMENT ''商品ID索引'',
        INDEX idx_workplace_id (workplaceId) COMMENT ''职场ID索引'',
        INDEX idx_stock (stock) COMMENT ''库存数量索引'',
        INDEX idx_available_stock (availableStock) COMMENT ''可用库存索引'',
        INDEX idx_last_update (lastStockUpdate) COMMENT ''更新时间索引'',
        
        -- 外键约束
        CONSTRAINT fk_pws_product FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE ON UPDATE CASCADE,
        CONSTRAINT fk_pws_workplace FOREIGN KEY (workplaceId) REFERENCES workplaces(id) ON DELETE CASCADE ON UPDATE CASCADE,
        
        -- 数据约束
        CONSTRAINT chk_stock_non_negative CHECK (stock >= 0),
        CONSTRAINT chk_reserved_stock_non_negative CHECK (reservedStock >= 0),
        CONSTRAINT chk_reserved_not_exceed_stock CHECK (reservedStock <= stock),
        CONSTRAINT chk_min_alert_positive CHECK (minStockAlert >= 0),
        CONSTRAINT chk_max_limit_positive CHECK (maxStockLimit IS NULL OR maxStockLimit > 0)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    COMMENT=''商品职场库存表 - 管理每个商品在各个职场的库存分配'';',
    'SELECT ''Table product_workplace_stocks already exists, skipping creation'' as message;'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证表创建结果
SELECT 
    CASE 
        WHEN @table_exists = 0 THEN '✅ 表 product_workplace_stocks 创建成功'
        ELSE '⚠️  表 product_workplace_stocks 已存在，跳过创建'
    END as result;

-- 显示表结构
DESCRIBE product_workplace_stocks;
