import api from './index';

/**
 * 创建兑换申请
 * @param {Object} exchangeData - 兑换信息
 * @returns {Promise<Object>} 返回创建的兑换记录
 */
export function createExchange(exchangeData) {
  return api.post('/exchanges', exchangeData);
}

/**
 * 获取当前用户的兑换记录
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页条数
 * @returns {Promise<Object>} 返回兑换记录列表
 */
export function getUserExchanges(params = {}) {
  return api.get('/exchanges/user', { params });
}

/**
 * 获取用户最近一次兑换记录
 * @returns {Promise<Object>} 返回最近的一条兑换记录
 */
export function getUserLastExchange() {
  return api.get('/exchanges/user/last');
}

/**
 * 获取兑换记录详情
 * @param {number} id - 兑换记录ID
 * @returns {Promise<Object>} 返回兑换记录详情
 */
export function getExchangeDetail(id) {
  return api.get(`/exchanges/${id}`);
}

/**
 * 取消兑换申请
 * @param {number} id - 兑换记录ID
 * @returns {Promise<Object>} 返回操作结果
 */
export function cancelExchange(id) {
  return api.put(`/exchanges/${id}/cancel`);
}

/**
 * 管理员获取所有兑换申请
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页条数
 * @param {string} [params.status] - 状态筛选
 * @param {string} [params.search] - 搜索关键词
 * @returns {Promise<Object>} 返回兑换申请列表
 */
export function getExchangeList(params = {}) {
  return api.get('/exchanges', { params });
}

/**
 * 管理员批量删除兑换申请
 * @param {number[]} ids - 要删除的兑换记录ID数组
 * @returns {Promise<Object>} 返回删除结果
 */
export function batchDeleteExchanges(ids) {
  return api.delete('/exchanges/batch', { data: { ids } });
}

/**
 * 管理员更新兑换申请状态
 * @param {number} id - 兑换记录ID
 * @param {Object} data - 更新数据
 * @param {string} data.status - 新状态
 * @param {string} [data.adminRemarks] - 管理员备注
 * @param {string} [data.trackingNumber] - 物流单号
 * @param {string} [data.trackingCompany] - 物流公司
 * @returns {Promise<Object>} 返回更新后的兑换记录
 */
export function updateExchangeStatus(id, data) {
  return api.put(`/exchanges/${id}/status`, data);
}

/**
 * 更新兑换订单的联系方式
 * @param {number} id - 兑换记录ID
 * @param {Object} data - 更新数据
 * @param {string} data.contactInfo - 新的联系方式
 * @param {string} data.location - 新的位置信息
 * @returns {Promise<Object>} 返回更新后的兑换记录
 */
export function updateContactInfo(id, data) {
  return api.put(`/exchanges/${id}/contact-info`, data);
}

// 为了保持向后兼容，添加旧函数名的别名
export const updateExchangeContactInfo = updateContactInfo;

/**
 * 管理员更新兑换订单的联系方式
 * @param {number} id - 兑换记录ID
 * @param {Object} data - 更新数据
 * @param {string} data.contactInfo - 新的联系方式
 * @param {string} data.location - 新的位置信息
 * @param {string} [data.adminRemarks] - 管理员备注
 * @returns {Promise<Object>} 返回更新后的兑换记录
 */
export function adminUpdateContactInfo(id, data) {
  return api.put(`/exchanges/${id}/admin/contact-info`, data);
}

// 为了保持向后兼容，添加旧函数名的别名
export const adminUpdateExchangeContactInfo = adminUpdateContactInfo;

/**
 * 获取最近的兑换订单
 * @param {Object} params - 查询参数
 * @param {number} params.limit - 返回数量限制，默认7条
 * @returns {Promise<Object>} 返回最近兑换订单列表
 */
export function getRecentExchanges(params = {}) {
  console.log('调用getRecentExchanges API，参数:', params);
  return api.get('/exchanges/recent', { params })
    .then(response => {
      console.log('最近订单API原始响应:', response);
      // 检查响应数据
      if (response.data && Array.isArray(response.data)) {
        console.log('最近订单数据统计:');
        console.log('订单总数:', response.data.length);
        // 统计支付方式
        const paymentStats = {
          ly: 0,
          rmb: 0,
          other: 0
        };

        // 单独输出每个订单的支付方式，便于调试
        console.log('逐个检查订单支付方式:');
        response.data.forEach((item, index) => {
          console.log(`订单[${index}] ID=${item.id}, 支付方式=${item.paymentMethod}, 类型=${typeof item.paymentMethod}`);

          if (item.paymentMethod === 'ly') {
            paymentStats.ly++;
          } else if (item.paymentMethod === 'rmb') {
            paymentStats.rmb++;
          } else {
            paymentStats.other++;
            console.warn(`发现异常支付方式: ${item.paymentMethod}`);
          }
        });

        console.log('支付方式统计:', paymentStats);
      }

      const data = response.data || response;
      return data;
    })
    .catch(error => {
      console.error('最近订单API错误:', error);
      throw error;
    });
}

/**
 * 获取职场分布统计数据
 * @returns {Promise<Object>} 返回按职场分组的兑换统计数据
 */
export function getWorkplaceExchangeStats() {
  console.log('调用职场分布统计API');
  return api.get('/exchanges/workplace-stats')
    .then(response => {
      console.log('职场分布统计API响应:', response);
      return response.data || response;
    })
    .catch(error => {
      console.error('职场分布统计API错误:', error);
      throw error;
    });
}

/**
 * 导出订单数据
 * @param {Object} params - 导出参数
 * @param {string} params.format - 导出格式，可选值：csv, excel
 * @param {string} [params.status] - 状态筛选
 * @param {string} [params.search] - 搜索关键词
 * @param {string} [params.startDate] - 开始日期
 * @param {string} [params.endDate] - 结束日期
 * @returns {Promise<Blob>} 返回文件Blob对象
 */
export function exportExchanges(params = {}) {
  return api.get('/exchanges/export', {
    params,
    responseType: 'blob'
  });
}

/**
 * 导入订单数据
 * @param {FormData} formData - 包含订单数据的CSV/Excel文件
 * @returns {Promise<Object>} 返回导入结果
 */
export function importExchanges(formData) {
  return api.post('/exchanges/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 下载订单导入模板
 * @param {string} format - 模板格式，可选值：csv, excel
 * @returns {Promise<Blob>} 返回文件Blob对象
 */
export function downloadExchangeTemplate(format = 'csv') {
  return api.get('/exchanges/template', {
    params: { format },
    responseType: 'blob'
  });
}

/**
 * 检查并重置订单表的自增ID
 * 此功能用于当删除所有订单后，确保新订单编号从001开始
 * @returns {Promise<Object>} 返回重置结果
 */
export function resetAutoIncrement() {
  return api.post('/exchanges/reset-auto-increment');
}

/**
 * 管理员调整订单价格
 * @param {number} id - 兑换记录ID
 * @param {Object} data - 价格调整数据
 * @param {number} data.totalAmount - 调整后的总价
 * @param {string} data.adminRemarks - 价格调整原因
 * @returns {Promise<Object>} 返回更新后的兑换记录
 */
export function adjustExchangePrice(id, data) {
  return api.put(`/exchanges/${id}/adjust-price`, data);
}
