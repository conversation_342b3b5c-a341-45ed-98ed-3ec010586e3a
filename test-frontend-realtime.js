/**
 * 实时前端数据流测试脚本
 * 用于检查库存转移后前端数据是否及时更新
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:3000/api';

// 获取管理员token
async function getAdminToken() {
  try {
    const loginData = {
      username: '超管',
      email: 'chao<PERSON><EMAIL>',
      password: '654321',
      userType: 'admin'
    };
    
    const response = await axios.post(`${BASE_URL}/auth/login`, loginData);
    return response.data.token;
  } catch (error) {
    console.error('❌ 获取token失败:', error.response?.data?.message || error.message);
    return null;
  }
}

// 获取商品数据
async function getProductData(token) {
  try {
    const config = {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };
    
    const response = await axios.get(`${BASE_URL}/products`, {
      ...config,
      params: {
        includeWorkplaceStocks: true,
        limit: 1000
      }
    });
    
    const nezhaProduct = response.data?.data?.find(p => p.name && p.name.includes('哪吒捏捏乐'));
    
    if (nezhaProduct && nezhaProduct.workplaceStocks) {
      const stockData = {};
      nezhaProduct.workplaceStocks.forEach(stock => {
        stockData[stock.workplaceName] = {
          stock: stock.stock,
          reservedStock: stock.reservedStock,
          availableStock: stock.availableStock || (stock.stock - stock.reservedStock),
          lastUpdate: stock.lastStockUpdate
        };
      });
      return stockData;
    }
    
    return null;
  } catch (error) {
    console.error('❌ 获取商品数据失败:', error.response?.data?.message || error.message);
    return null;
  }
}

// 执行库存转移
async function performTransfer(token, fromWorkplaceId, toWorkplaceId, quantity, reason) {
  try {
    const config = {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };
    
    const transferData = {
      productId: 15, // 哪吒捏捏乐
      fromWorkplaceId,
      toWorkplaceId,
      quantity,
      reason
    };
    
    const response = await axios.post(`${BASE_URL}/stocks/transfer`, transferData, config);
    return response.data.success;
  } catch (error) {
    console.error('❌ 库存转移失败:', error.response?.data?.message || error.message);
    return false;
  }
}

// 主测试函数
async function runRealtimeTest() {
  console.log('🚀 开始实时前端数据流测试...\n');
  
  // 1. 获取token
  const token = await getAdminToken();
  if (!token) {
    console.log('❌ 无法获取token，测试终止');
    return;
  }
  console.log('✅ 获取token成功\n');
  
  // 2. 获取转移前数据
  console.log('📊 步骤1: 获取转移前数据');
  const beforeData = await getProductData(token);
  if (!beforeData) {
    console.log('❌ 无法获取转移前数据');
    return;
  }
  
  console.log('转移前库存状态:');
  Object.entries(beforeData).forEach(([workplace, data]) => {
    console.log(`  ${workplace}: ${data.availableStock}个 (总库存: ${data.stock}, 预留: ${data.reservedStock})`);
  });
  console.log('');
  
  // 3. 执行库存转移（从北京转移1个到武汉）
  console.log('🔄 步骤2: 执行库存转移（北京 → 武汉，1个）');
  const transferSuccess = await performTransfer(token, 1, 14, 1, '实时数据流测试');
  
  if (!transferSuccess) {
    console.log('❌ 库存转移失败，测试终止');
    return;
  }
  console.log('✅ 库存转移成功\n');
  
  // 4. 立即获取转移后数据
  console.log('📊 步骤3: 立即获取转移后数据');
  const afterDataImmediate = await getProductData(token);
  
  if (afterDataImmediate) {
    console.log('转移后库存状态（立即获取）:');
    Object.entries(afterDataImmediate).forEach(([workplace, data]) => {
      console.log(`  ${workplace}: ${data.availableStock}个 (总库存: ${data.stock}, 预留: ${data.reservedStock})`);
    });
    
    // 检查数据是否已更新
    const beijingBefore = beforeData['北京']?.availableStock || 0;
    const wuhanBefore = beforeData['武汉']?.availableStock || 0;
    const beijingAfter = afterDataImmediate['北京']?.availableStock || 0;
    const wuhanAfter = afterDataImmediate['武汉']?.availableStock || 0;
    
    const beijingChange = beijingAfter - beijingBefore;
    const wuhanChange = wuhanAfter - wuhanBefore;
    
    console.log('\n📈 数据变化分析:');
    console.log(`北京: ${beijingBefore} → ${beijingAfter} (变化: ${beijingChange})`);
    console.log(`武汉: ${wuhanBefore} → ${wuhanAfter} (变化: ${wuhanChange})`);
    
    if (beijingChange === -1 && wuhanChange === 1) {
      console.log('✅ 数据更新正确！前端API返回了最新数据');
    } else {
      console.log('❌ 数据更新异常！前端API可能返回了缓存数据');
      console.log('期望：北京-1，武汉+1');
      console.log(`实际：北京${beijingChange}，武汉${wuhanChange}`);
    }
  } else {
    console.log('❌ 无法获取转移后数据');
  }
  
  // 5. 等待2秒后再次获取数据
  console.log('\n⏳ 等待2秒后再次获取数据...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  console.log('📊 步骤4: 延迟获取数据（2秒后）');
  const afterDataDelayed = await getProductData(token);
  
  if (afterDataDelayed) {
    console.log('转移后库存状态（延迟获取）:');
    Object.entries(afterDataDelayed).forEach(([workplace, data]) => {
      console.log(`  ${workplace}: ${data.availableStock}个 (总库存: ${data.stock}, 预留: ${data.reservedStock})`);
    });
    
    // 对比立即获取和延迟获取的数据
    const immediateBeijing = afterDataImmediate['北京']?.availableStock || 0;
    const delayedBeijing = afterDataDelayed['北京']?.availableStock || 0;
    const immediateWuhan = afterDataImmediate['武汉']?.availableStock || 0;
    const delayedWuhan = afterDataDelayed['武汉']?.availableStock || 0;
    
    console.log('\n🔍 立即获取 vs 延迟获取对比:');
    console.log(`北京: ${immediateBeijing} vs ${delayedBeijing} ${immediateBeijing === delayedBeijing ? '✅' : '❌'}`);
    console.log(`武汉: ${immediateWuhan} vs ${delayedWuhan} ${immediateWuhan === delayedWuhan ? '✅' : '❌'}`);
    
    if (immediateBeijing === delayedBeijing && immediateWuhan === delayedWuhan) {
      console.log('✅ 数据一致性良好');
    } else {
      console.log('❌ 数据一致性问题，可能存在缓存延迟');
    }
  }
  
  console.log('\n📋 测试完成');
  console.log('💡 如果前端页面显示的数据与API返回的数据不一致，说明问题在前端组件的数据更新机制');
}

// 运行测试
runRealtimeTest().catch(error => {
  console.error('测试执行失败:', error);
});
