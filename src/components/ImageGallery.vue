<template>
  <div class="image-gallery">
    <div class="gallery-container">
      <el-carousel
        :height="height"
        :interval="interval"
        arrow="hover"
        indicator-position="outside"
        :autoplay="autoplay"
        ref="carousel"
        @change="handleChange"
      >
        <el-carousel-item v-for="(image, index) in images" :key="index">
          <div class="image-container">
            <img
              :src="fixImageUrl(image.imageUrl || image)"
              :alt="`${title || 'Image'} ${index + 1}`"
              class="gallery-image"
              @click="handleImageClick(index)"
              @error="handleImageError($event, index)"
            />
          </div>
        </el-carousel-item>
      </el-carousel>
      
      <div v-if="showThumbnails && images.length > 1" class="thumbnails">
        <div
          v-for="(image, index) in images"
          :key="index"
          class="thumbnail"
          :class="{ active: currentIndex === index }"
          @click="setActiveImage(index)"
        >
          <img
            :src="fixImageUrl(image.imageUrl || image)"
            :alt="`${title || 'Thumbnail'} ${index + 1}`"
            @error="handleImageError($event, index)"
          />
        </div>
      </div>
    </div>

    <!-- Image preview dialog -->
    <el-dialog
      v-model="previewVisible"
      :title="title || '图片预览'"
      width="90%"
      center
      destroy-on-close
      custom-class="preview-dialog"
    >
      <div class="preview-container">
        <el-icon class="preview-arrow-left" @click="prevImage" v-if="images.length > 1">
          <ArrowLeft />
        </el-icon>
        
        <div class="preview-image-container">
          <img
            :src="fixImageUrl(currentImage)"
            :alt="`${title || 'Preview'} ${previewIndex + 1}`"
            class="preview-image"
            @error="handlePreviewImageError"
          />
        </div>
        
        <el-icon class="preview-arrow-right" @click="nextImage" v-if="images.length > 1">
          <ArrowRight />
        </el-icon>
      </div>
      
      <div class="preview-counter" v-if="images.length > 1">
        {{ previewIndex + 1 }} / {{ images.length }}
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
import { fixImageUrl, getDefaultProductImage } from '../utils/imageUtils';

const props = defineProps({
  images: {
    type: Array,
    required: true
  },
  title: {
    type: String,
    default: ''
  },
  height: {
    type: String,
    default: '400px'
  },
  interval: {
    type: Number,
    default: 5000
  },
  autoplay: {
    type: Boolean,
    default: true
  },
  showThumbnails: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['image-click']);

const carousel = ref(null);
const currentIndex = ref(0);
const previewVisible = ref(false);
const previewIndex = ref(0);

// Watch for carousel changes and update current index
const handleChange = (index) => {
  currentIndex.value = index;
};

// Set active image in the carousel
const setActiveImage = (index) => {
  if (carousel.value) {
    carousel.value.setActiveItem(index);
    currentIndex.value = index;
  }
};

// Handle image click to show preview
const handleImageClick = (index) => {
  previewIndex.value = index;
  previewVisible.value = true;
  emit('image-click', index);
};

// Computed property for the current preview image
const currentImage = computed(() => {
  if (props.images.length === 0) return '';
  const image = props.images[previewIndex.value];
  return image.imageUrl || image;
});

// Navigation methods for the preview
const nextImage = () => {
  previewIndex.value = (previewIndex.value + 1) % props.images.length;
};

const prevImage = () => {
  previewIndex.value = (previewIndex.value - 1 + props.images.length) % props.images.length;
};

// Handle image loading errors
const handleImageError = (event, index) => {
  console.log('图片加载失败，使用默认占位图:', event.target.src);
  event.target.src = getDefaultProductImage();
};

// Handle preview image loading errors
const handlePreviewImageError = (event) => {
  console.log('预览图片加载失败，使用默认占位图:', event.target.src);
  event.target.src = getDefaultProductImage();
};

// Reset current index when images change
watch(() => props.images, () => {
  currentIndex.value = 0;
  if (carousel.value) {
    carousel.value.setActiveItem(0);
  }
}, { deep: true });
</script>

<style scoped>
.image-gallery {
  width: 100%;
  position: relative;
  animation: galleryFadeIn 0.5s ease-in-out;
}

@keyframes galleryFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.gallery-container {
  position: relative;
  margin-bottom: 10px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.gallery-container:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: #f5f7fa;
  position: relative;
}

.image-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.02), rgba(0,0,0,0.1));
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-container:hover::before {
  opacity: 1;
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  cursor: pointer;
  transition: transform 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
  will-change: transform;
}

.gallery-image:hover {
  transform: scale(1.05);
}

/* 自定义轮播箭头 */
.gallery-container :deep(.el-carousel__arrow) {
  background-color: rgba(255, 255, 255, 0.8);
  color: #606266;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  transform: translateX(0);
  transition: all 0.3s ease;
}

.gallery-container :deep(.el-carousel__arrow:hover) {
  background-color: #ffffff;
  color: #409EFF;
  transform: translateX(0) scale(1.1);
}

.gallery-container :deep(.el-carousel__arrow--left) {
  left: 15px;
}

.gallery-container :deep(.el-carousel__arrow--right) {
  right: 15px;
}

/* 自定义指示器 */
.gallery-container :deep(.el-carousel__indicators) {
  transition: all 0.3s ease;
}

.gallery-container :deep(.el-carousel__indicator) {
  margin: 0 4px;
}

.gallery-container :deep(.el-carousel__indicator--horizontal .el-carousel__button) {
  width: 20px;
  height: 4px;
  border-radius: 2px;
  background-color: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.gallery-container :deep(.el-carousel__indicator.is-active .el-carousel__button) {
  background-color: #409EFF;
  width: 30px;
}

.thumbnails {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  margin-top: 15px;
}

.thumbnail {
  width: 70px;
  height: 70px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  opacity: 0.7;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(0);
}

.thumbnail:hover {
  opacity: 1;
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.thumbnail.active {
  border-color: #409EFF;
  opacity: 1;
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 6px 12px rgba(64, 158, 255, 0.3);
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.thumbnail:hover img {
  transform: scale(1.1);
}

.preview-dialog :deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.preview-dialog :deep(.el-dialog__header) {
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.preview-dialog :deep(.el-dialog__body) {
  padding: 0;
  position: relative;
}

.preview-container {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  min-height: 500px;
  max-height: 80vh;
  background-color: rgba(0, 0, 0, 0.02);
}

.preview-image-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  animation: fadeZoomIn 0.3s ease-out;
}

@keyframes fadeZoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.preview-image {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  border-radius: 4px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.preview-arrow-left,
.preview-arrow-right {
  font-size: 24px;
  color: #606266;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  padding: 12px;
  cursor: pointer;
  z-index: 10;
  margin: 0 15px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.preview-arrow-left:hover,
.preview-arrow-right:hover {
  background-color: #ffffff;
  color: #409EFF;
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.preview-counter {
  text-align: center;
  padding: 15px;
  color: #606266;
  font-size: 14px;
  background-color: #f9f9f9;
  border-top: 1px solid #ebeef5;
}

/* 响应式设计增强 */
@media (max-width: 992px) {
  .gallery-container :deep(.el-carousel__arrow) {
    width: 36px;
    height: 36px;
  }
  
  .thumbnail {
    width: 60px;
    height: 60px;
  }
  
  .preview-arrow-left,
  .preview-arrow-right {
    font-size: 20px;
    padding: 10px;
  }
}

@media (max-width: 768px) {
  .preview-container {
    min-height: 300px;
  }
  
  .thumbnail {
    width: 50px;
    height: 50px;
  }
  
  .preview-arrow-left,
  .preview-arrow-right {
    font-size: 18px;
    padding: 8px;
    margin: 0 10px;
  }
  
  .gallery-container :deep(.el-carousel__arrow) {
    width: 32px;
    height: 32px;
  }
  
  .gallery-container :deep(.el-carousel__arrow--left) {
    left: 10px;
  }
  
  .gallery-container :deep(.el-carousel__arrow--right) {
    right: 10px;
  }
  
  .gallery-container :deep(.el-carousel__indicator--horizontal .el-carousel__button) {
    width: 16px;
    height: 3px;
  }
  
  .gallery-container :deep(.el-carousel__indicator.is-active .el-carousel__button) {
    width: 24px;
  }
}

@media (max-width: 480px) {
  .thumbnails {
    gap: 6px;
  }
  
  .thumbnail {
    width: 40px;
    height: 40px;
  }
  
  .preview-container {
    min-height: 250px;
  }
  
  .preview-arrow-left,
  .preview-arrow-right {
    font-size: 16px;
    padding: 6px;
    margin: 0 5px;
  }
  
  .gallery-container :deep(.el-carousel__arrow) {
    width: 28px;
    height: 28px;
  }
}
</style> 