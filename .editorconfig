# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# JavaScript, TypeScript, Vue files
[*.{js,ts,vue}]
indent_size = 2
max_line_length = 100

# JSON files
[*.json]
indent_size = 2

# CSS, SCSS, LESS files
[*.{css,scss,less}]
indent_size = 2

# HTML files
[*.html]
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = 80

# YAML files
[*.{yml,yaml}]
indent_size = 2

# Configuration files
[*.{conf,config}]
indent_size = 4

# SQL files
[*.sql]
indent_size = 2

# Shell scripts
[*.sh]
indent_size = 2

# Package.json
[package.json]
indent_size = 2
