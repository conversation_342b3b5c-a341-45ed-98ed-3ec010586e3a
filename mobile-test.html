<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端访问测试 - 光年小卖部</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .subtitle {
            opacity: 0.8;
            font-size: 0.9em;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-title {
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 1.1em;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status.success {
            background: #4CAF50;
            color: white;
        }
        .status.error {
            background: #f44336;
            color: white;
        }
        .status.testing {
            background: #ff9800;
            color: white;
        }
        .button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            text-align: center;
            font-weight: bold;
            transition: all 0.3s ease;
            width: calc(50% - 10px);
            box-sizing: border-box;
        }
        .button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .button.primary {
            background: #4CAF50;
            border-color: #4CAF50;
        }
        .info-box {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            font-size: 0.9em;
        }
        .qr-info {
            text-align: center;
            margin-top: 20px;
            font-size: 0.8em;
            opacity: 0.8;
        }
        @media (max-width: 480px) {
            .container {
                margin: 10px;
                padding: 20px;
            }
            .button {
                width: 100%;
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">📱</div>
            <div class="title">移动端访问测试</div>
            <div class="subtitle">光年小卖部 - 手机端兼容性检测</div>
        </div>

        <div class="test-section">
            <div class="test-title">🌐 网络连接测试</div>
            <div class="test-item">
                <span>前端服务</span>
                <span class="status success" id="frontend-status">已连接</span>
            </div>
            <div class="test-item">
                <span>后端API</span>
                <span class="status testing" id="backend-status">检测中...</span>
            </div>
            <div class="test-item">
                <span>数据库连接</span>
                <span class="status testing" id="database-status">检测中...</span>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">📊 API接口测试</div>
            <div class="test-item">
                <span>商品列表</span>
                <span class="status testing" id="products-status">检测中...</span>
            </div>
            <div class="test-item">
                <span>价格范围</span>
                <span class="status testing" id="price-ranges-status">检测中...</span>
            </div>
            <div class="test-item">
                <span>分类列表</span>
                <span class="status testing" id="categories-status">检测中...</span>
            </div>
        </div>

        <div class="info-box">
            <strong>📍 当前访问地址:</strong><br>
            <span id="current-url">http://**************:5173</span><br><br>
            <strong>🔧 API服务地址:</strong><br>
            <span id="api-url">http://**************:3000/api</span>
        </div>

        <div style="text-align: center;">
            <a href="http://**************:5173" class="button primary">进入应用首页</a>
            <a href="http://**************:5173/admin/login" class="button">管理员登录</a>
        </div>

        <div class="qr-info">
            💡 提示：请确保手机和电脑在同一WiFi网络下<br>
            📱 可以将此页面添加到手机桌面进行测试
        </div>
    </div>

    <script>
        // API基础配置
        const API_BASE = 'http://**************:3000/api';
        
        // 更新状态
        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            if (element) {
                element.className = `status ${status}`;
                element.textContent = text || (status === 'success' ? '正常' : status === 'error' ? '失败' : '检测中...');
            }
        }

        // 测试API连接
        async function testAPI(url, statusId, name) {
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    timeout: 5000
                });
                
                if (response.ok) {
                    updateStatus(statusId, 'success', '正常');
                    return true;
                } else {
                    updateStatus(statusId, 'error', `错误 ${response.status}`);
                    return false;
                }
            } catch (error) {
                updateStatus(statusId, 'error', '连接失败');
                console.error(`${name} 测试失败:`, error);
                return false;
            }
        }

        // 运行所有测试
        async function runTests() {
            console.log('开始移动端兼容性测试...');
            
            // 测试后端连接
            const backendOk = await testAPI(`${API_BASE}/categories`, 'backend-status', '后端API');
            
            // 如果后端连接成功，继续测试其他接口
            if (backendOk) {
                updateStatus('database-status', 'success', '正常');
                
                // 测试具体API
                await testAPI(`${API_BASE}/products?page=1&limit=5`, 'products-status', '商品列表');
                await testAPI(`${API_BASE}/products/price-ranges`, 'price-ranges-status', '价格范围');
                await testAPI(`${API_BASE}/categories`, 'categories-status', '分类列表');
            } else {
                updateStatus('database-status', 'error', '连接失败');
                updateStatus('products-status', 'error', '无法连接');
                updateStatus('price-ranges-status', 'error', '无法连接');
                updateStatus('categories-status', 'error', '无法连接');
            }
        }

        // 页面加载完成后运行测试
        window.addEventListener('load', () => {
            // 显示当前URL信息
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('api-url').textContent = API_BASE;
            
            // 延迟1秒后开始测试，让页面完全加载
            setTimeout(runTests, 1000);
        });

        // 添加触摸友好的交互
        document.addEventListener('touchstart', function() {}, {passive: true});
    </script>
</body>
</html>
