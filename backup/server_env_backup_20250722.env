# ====================================
# 前端本地开发环境配置文件 (.env)
# ====================================
# 此文件用于前端本地开发环境，请勿在生产环境使用
# 生产环境请使用 .env.production 文件
# 对应后端配置文件：server/.env

# ====================================
# 前端开发服务器配置
# ====================================
# 前端API基础URL（指向本地后端服务）
VITE_API_URL=http://localhost:3000/api

# 应用标题
VITE_APP_TITLE=光年小卖部

# 应用基础路径
VITE_APP_BASE_URL=/

# ====================================
# 开发环境特定配置
# ====================================
# 运行环境标识
VITE_NODE_ENV=development

# 是否启用开发工具
VITE_DEV_TOOLS=true

# 是否显示调试信息
VITE_DEBUG=true

# API请求超时时间（毫秒）
VITE_API_TIMEOUT=10000

# ====================================
# 后端服务配置（用于前端调用）
# ====================================
# 后端服务器地址
VITE_SERVER_URL=http://localhost:3000

# WebSocket连接地址（如果需要）
VITE_WS_URL=ws://localhost:3000

# ====================================
# 飞书相关配置（前端使用）
# ====================================
# 飞书应用ID（前端可能需要）
VITE_FEISHU_APP_ID=cli_a66b3b2dcab8d013

# 飞书登录回调地址（前端路由）
VITE_FEISHU_CALLBACK_URL=http://localhost:5173/feishu/callback
