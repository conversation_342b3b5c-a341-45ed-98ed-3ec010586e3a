#!/bin/bash

# 生产环境移动端飞书登录修复部署脚本
# 服务器: **************
# 用户: root
# 密码: Aa@123456

echo "======================================"
echo "生产环境移动端飞书登录修复部署脚本"
echo "======================================"

# 设置变量
SERVER_IP="**************"
SERVER_USER="root"
SERVER_PASSWORD="Aa@123456"
DEPLOY_DIR="/www/wwwroot/workyy"
BRANCH="feat/reset"

echo "1. 连接到生产服务器并更新代码..."

# 使用sshpass连接服务器并执行命令
sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" << 'EOF'
    echo "进入部署目录..."
    cd /www/wwwroot/workyy
    
    echo "拉取最新代码..."
    git fetch origin
    git checkout feat/reset
    git pull origin feat/reset
    
    echo "更新服务器端环境变量..."
    cd server

    # 备份原有配置文件
    if [ -f .env ]; then
        cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
        echo "已备份原有.env文件"
    fi

    if [ -f .env.production ]; then
        cp .env.production .env.production.backup.$(date +%Y%m%d_%H%M%S)
        echo "已备份原有.env.production文件"
    fi

    # 确保使用生产环境配置文件
    echo "验证生产环境配置文件..."
    if [ -f .env.production ]; then
        echo "✅ 生产环境配置文件已存在"
    else
        echo "❌ 生产环境配置文件不存在，创建中..."
        # 创建生产环境配置文件
        cat > .env.production << 'ENVEOF'
# ====================================
# 生产环境配置文件 (server/.env.production)
# ====================================
NODE_ENV=production
PORT=3000
SERVER_URL=http://**************:3000
FRONTEND_URL=http://**************

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=feishu_mall

# JWT配置
JWT_SECRET=JO/Ssvef59AR5zFMx5m/MGMin34aMPT0KY6sIcqwowA=
JWT_EXPIRES_IN=1d
JWT_LONG_EXPIRES_IN=30d

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880

# CORS配置
CORS_ORIGIN=http://**************,http://**************:80,http://**************:3000
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS

# 飞书应用配置
FEISHU_APP_ID=cli_a66b3b2dcab8d013
FEISHU_APP_SECRET=5Fa8aatAGZ2Dv6K5VZhAWhbhjzE4lT2r
FEISHU_REDIRECT_URI=http://**************:3000/api/feishu/callback

# 飞书机器人配置
FEISHU_BOT_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/e6cff700-4172-4039-a700-43c8f43765fc

# 日志配置
LOG_LEVEL=info
ENVEOF
    fi

    echo "✅ 生产环境配置已确认"

    # 设置环境变量以使用生产配置
    export NODE_ENV=production
    echo "✅ 已设置NODE_ENV=production"

    echo "验证前端配置文件..."
    cd ..
    if [ -f .env.production ]; then
        echo "✅ 前端生产环境配置文件存在"
    else
        echo "❌ 前端生产环境配置文件不存在，请检查"
    fi

    echo "验证前后端配置一致性..."
    if command -v node >/dev/null 2>&1; then
        if [ -f scripts/validate-frontend-config.cjs ]; then
            node scripts/validate-frontend-config.cjs
            if [ $? -eq 0 ]; then
                echo "✅ 前端配置验证通过"
            else
                echo "⚠️  前端配置验证有警告，但继续部署"
            fi
        else
            echo "⚠️  前端配置验证脚本不存在，跳过验证"
        fi
    else
        echo "⚠️  Node.js不可用，跳过前端配置验证"
    fi

    cd server
    echo "安装依赖..."
    npm install --production
    
    echo "重启PM2应用..."
    pm2 restart feishu-mall-api || pm2 start server.js --name feishu-mall-api
    
    echo "检查PM2状态..."
    pm2 status
    
    echo "检查应用日志..."
    pm2 logs feishu-mall-api --lines 10
    
    echo "部署完成！"
EOF

echo ""
echo "======================================"
echo "部署完成！"
echo "======================================"
echo ""
echo "修复内容："
echo "1. ✅ 更新飞书回调处理逻辑，支持移动端直接重定向"
echo "2. ✅ 优化前端处理飞书登录回调参数"
echo "3. ✅ 更新生产环境配置（CORS、回调URL等）"
echo "4. ✅ 改进移动端登录策略和错误处理"
echo ""
echo "测试步骤："
echo "1. 使用移动端浏览器访问: http://**************"
echo "2. 点击'用飞书登录'按钮"
echo "3. 完成飞书授权后，应该能正确跳转回首页"
echo "4. 验证桌面端登录功能仍然正常"
echo ""
echo "如果遇到问题，请检查："
echo "- PM2应用状态: pm2 status"
echo "- 应用日志: pm2 logs feishu-mall-api"
echo "- 飞书开放平台回调URL配置"
echo ""
