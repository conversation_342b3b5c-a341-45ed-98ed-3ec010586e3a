#!/usr/bin/env node

/**
 * API连通性测试脚本
 * 用于验证库存管理相关API接口是否正常工作
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:3000/api';

// 测试配置
const testConfig = {
  timeout: 5000,
  headers: {
    'Content-Type': 'application/json'
  }
};

// 测试结果
let testResults = [];

/**
 * 记录测试结果
 */
function logResult(testName, success, message, data = null) {
  const result = {
    test: testName,
    success,
    message,
    data,
    timestamp: new Date().toISOString()
  };
  testResults.push(result);
  
  const status = success ? '✅' : '❌';
  console.log(`${status} ${testName}: ${message}`);
  if (data) {
    console.log(`   数据: ${JSON.stringify(data, null, 2)}`);
  }
}

/**
 * 测试基础API连通性
 */
async function testBasicConnectivity() {
  try {
    const response = await axios.get(`${BASE_URL}/workplaces`, testConfig);
    logResult('基础连通性', false, '需要认证令牌（预期行为）', { status: response.status });
  } catch (error) {
    if (error.response && error.response.status === 401) {
      logResult('基础连通性', true, 'API响应正常，需要认证（预期行为）');
    } else if (error.response && error.response.status === 404) {
      logResult('基础连通性', false, 'API路由未找到 - 404错误');
    } else {
      logResult('基础连通性', false, `连接失败: ${error.message}`);
    }
  }
}

/**
 * 测试管理员登录
 */
async function testAdminLogin() {
  try {
    const loginData = {
      username: '超管',
      email: '<EMAIL>',
      password: '654321'
    };
    
    const response = await axios.post(`${BASE_URL}/auth/login`, loginData, testConfig);
    
    if (response.data.token) {
      logResult('管理员登录', true, '登录成功', { 
        user: response.data.user?.name,
        role: response.data.user?.role 
      });
      return response.data.token;
    } else {
      logResult('管理员登录', false, '登录响应无token');
      return null;
    }
  } catch (error) {
    logResult('管理员登录', false, `登录失败: ${error.response?.data?.message || error.message}`);
    return null;
  }
}

/**
 * 测试职场列表API
 */
async function testWorkplacesAPI(token) {
  if (!token) {
    logResult('职场列表API', false, '无有效token，跳过测试');
    return;
  }
  
  try {
    const config = {
      ...testConfig,
      headers: {
        ...testConfig.headers,
        'Authorization': `Bearer ${token}`
      }
    };
    
    const response = await axios.get(`${BASE_URL}/workplaces`, config);
    
    if (response.data && Array.isArray(response.data)) {
      logResult('职场列表API', true, `成功获取职场列表，共${response.data.length}个职场`);
    } else {
      logResult('职场列表API', true, '成功调用API，但数据格式可能不同', response.data);
    }
  } catch (error) {
    logResult('职场列表API', false, `API调用失败: ${error.response?.data?.message || error.message}`);
  }
}

/**
 * 测试库存管理相关API
 */
async function testStockManagementAPIs(token) {
  if (!token) {
    logResult('库存管理API', false, '无有效token，跳过测试');
    return;
  }
  
  const config = {
    ...testConfig,
    headers: {
      ...testConfig.headers,
      'Authorization': `Bearer ${token}`
    }
  };
  
  // 测试商品列表API
  try {
    const response = await axios.get(`${BASE_URL}/products?includeWorkplaceStocks=true`, config);
    logResult('商品库存API', true, '成功获取商品库存数据');
  } catch (error) {
    logResult('商品库存API', false, `API调用失败: ${error.response?.data?.message || error.message}`);
  }
  
  // 测试库存操作日志API
  try {
    const response = await axios.get(`${BASE_URL}/stocks/operation-logs`, config);
    logResult('库存日志API', true, '成功获取库存操作日志');
  } catch (error) {
    logResult('库存日志API', false, `API调用失败: ${error.response?.data?.message || error.message}`);
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始API连通性测试...\n');
  
  // 1. 测试基础连通性
  await testBasicConnectivity();
  
  // 2. 测试管理员登录
  const token = await testAdminLogin();
  
  // 3. 测试职场列表API
  await testWorkplacesAPI(token);
  
  // 4. 测试库存管理相关API
  await testStockManagementAPIs(token);
  
  // 输出测试总结
  console.log('\n📊 测试总结:');
  const successCount = testResults.filter(r => r.success).length;
  const totalCount = testResults.length;
  console.log(`总测试数: ${totalCount}`);
  console.log(`成功: ${successCount}`);
  console.log(`失败: ${totalCount - successCount}`);
  
  if (successCount === totalCount) {
    console.log('\n🎉 所有测试通过！API接口工作正常。');
  } else {
    console.log('\n⚠️  部分测试失败，请检查相关配置。');
  }
  
  return successCount === totalCount;
}

// 运行测试
runTests().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('测试执行失败:', error);
  process.exit(1);
});

export { runTests };
