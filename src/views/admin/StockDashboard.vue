<template>
  <div class="stock-dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <i class="fas fa-chart-pie"></i>
          库存统计仪表板
        </h1>
        <p class="page-description">实时监控各职场库存状况和统计数据</p>
      </div>
      <div class="header-actions">
        <div class="auto-refresh-control">
          <el-switch
            v-model="autoRefreshEnabled"
            active-text="自动刷新"
            inactive-text="手动刷新"
            @change="handleAutoRefreshToggle"
          />
          <span class="refresh-interval" v-if="autoRefreshEnabled">
            每 {{ refreshInterval / 1000 }}s
          </span>
        </div>
        <el-button
          type="primary"
          icon="el-icon-refresh"
          @click="refreshData"
          :loading="loading"
        >
          刷新数据
        </el-button>
        <el-button
          type="success"
          icon="el-icon-download"
          @click="exportReport"
        >
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 总体统计卡片 -->
    <div class="overview-cards">
      <div class="stats-card total">
        <div class="stats-icon">
          <i class="fas fa-cubes"></i>
        </div>
        <div class="stats-content">
          <div class="stats-value">{{ overviewStats.totalStock }}</div>
          <div class="stats-label">总库存</div>
          <div class="stats-trend" :class="overviewStats.stockTrend">
            <i :class="getTrendIcon(overviewStats.stockTrend)"></i>
            {{ overviewStats.stockChange }}
          </div>
        </div>
      </div>

      <div class="stats-card available">
        <div class="stats-icon">
          <i class="fas fa-check-circle"></i>
        </div>
        <div class="stats-content">
          <div class="stats-value">{{ overviewStats.availableStock }}</div>
          <div class="stats-label">可用库存</div>
          <div class="stats-trend" :class="overviewStats.availableTrend">
            <i :class="getTrendIcon(overviewStats.availableTrend)"></i>
            {{ overviewStats.availableChange }}
          </div>
        </div>
      </div>

      <div class="stats-card reserved">
        <div class="stats-icon">
          <i class="fas fa-lock"></i>
        </div>
        <div class="stats-content">
          <div class="stats-value">{{ overviewStats.reservedStock }}</div>
          <div class="stats-label">预留库存</div>
          <div class="stats-trend" :class="overviewStats.reservedTrend">
            <i :class="getTrendIcon(overviewStats.reservedTrend)"></i>
            {{ overviewStats.reservedChange }}
          </div>
        </div>
      </div>

      <div class="stats-card alerts">
        <div class="stats-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="stats-content">
          <div class="stats-value">{{ overviewStats.alertCount }}</div>
          <div class="stats-label">库存告警</div>
          <div class="stats-trend" :class="overviewStats.alertTrend">
            <i :class="getTrendIcon(overviewStats.alertTrend)"></i>
            {{ overviewStats.alertChange }}
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="chart-row">
        <!-- 职场库存分布饼图 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3 class="chart-title">职场库存分布</h3>
            <el-select
              v-model="selectedMetric"
              size="small"
              style="width: 120px"
              @change="handleMetricChange"
            >
              <el-option label="总库存" value="total" />
              <el-option label="可用库存" value="available" />
              <el-option label="预留库存" value="reserved" />
            </el-select>
          </div>
          <div class="chart-content">
            <div v-if="loading" class="chart-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <p>加载中...</p>
            </div>
            <div v-else-if="!workplaces || workplaces.length === 0" class="chart-empty">
              <el-icon><DocumentRemove /></el-icon>
              <p>暂无职场数据</p>
            </div>
            <v-chart
              v-else
              class="chart-container"
              :option="workplaceChartOption"
              autoresize
              @click="onWorkplaceChartClick"
            />
          </div>
        </div>

        <!-- 库存趋势图 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3 class="chart-title">库存变化趋势</h3>
            <el-date-picker
              v-model="trendDateRange"
              type="daterange"
              size="small"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="updateTrendChart"
            />
          </div>
          <div class="chart-content">
            <div v-if="loading" class="chart-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <p>加载中...</p>
            </div>
            <v-chart
              v-else
              class="chart-container"
              :option="trendChartOption"
              autoresize
              @click="onTrendChartClick"
            />
          </div>
        </div>
      </div>

      <div class="chart-row">
        <!-- 商品库存排行 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3 class="chart-title">商品库存排行</h3>
            <el-radio-group
              v-model="rankingType"
              size="small"
              @change="handleRankingTypeChange"
            >
              <el-radio-button label="highest">库存最多</el-radio-button>
              <el-radio-button label="lowest">库存最少</el-radio-button>
            </el-radio-group>
          </div>
          <div class="chart-content">
            <div v-if="loading" class="chart-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <p>加载中...</p>
            </div>
            <div v-else-if="!products || products.length === 0" class="chart-empty">
              <el-icon><DocumentRemove /></el-icon>
              <p>暂无商品数据</p>
            </div>
            <v-chart
              v-else
              class="chart-container"
              :option="rankingChartOption"
              autoresize
              @click="onRankingChartClick"
            />
          </div>
        </div>
      </div>

      <!-- 商品职场库存趋势图 - 占满整行 -->
      <div class="chart-row full-width">
        <div class="chart-card large-chart">
          <div class="chart-header">
            <h3 class="chart-title">
              <i class="fas fa-chart-line"></i>
              商品职场库存趋势分析
            </h3>
            <div class="chart-controls">
              <el-select
                v-model="selectedProductForTrend"
                placeholder="选择商品"
                size="small"
                style="width: 250px; margin-right: 15px"
                filterable
                clearable
                @change="handleProductTrendChange"
              >
                <el-option
                  v-for="product in products"
                  :key="product.id"
                  :label="product.name"
                  :value="product.id"
                >
                  <div class="product-option">
                    <span class="product-name">{{ product.name }}</span>
                    <span class="product-stock">总库存: {{ product.stock }}</span>
                  </div>
                </el-option>
              </el-select>
              <el-date-picker
                v-model="productTrendDateRange"
                type="daterange"
                size="small"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 280px; margin-right: 15px"
                @change="updateProductTrendChart"
              />
              <el-button
                type="primary"
                size="small"
                icon="el-icon-refresh"
                @click="refreshProductTrendData"
                :loading="productTrendLoading"
              >
                刷新
              </el-button>
            </div>
          </div>
          <div class="chart-content large-chart-content">
            <div v-if="productTrendLoading" class="chart-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <p>正在加载商品库存趋势数据...</p>
            </div>
            <div v-else-if="!selectedProductForTrend" class="chart-empty">
              <el-icon><Select /></el-icon>
              <p>请选择要查看库存趋势的商品</p>
              <p class="empty-tip">选择商品后将显示该商品在各职场的库存变化趋势</p>
            </div>
            <div v-else-if="!productTrendData || productTrendData.length === 0" class="chart-empty">
              <el-icon><DocumentRemove /></el-icon>
              <p>暂无该商品的历史库存数据</p>
              <p class="empty-tip">请选择其他商品或调整时间范围</p>
            </div>
            <v-chart
              v-else
              class="chart-container large-chart-container"
              :option="productTrendChartOption"
              autoresize
            />
          </div>
        </div>
      </div>

      <div class="chart-row">
        <!-- 库存告警详情 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3 class="chart-title">库存告警详情</h3>
            <el-button type="text" size="small" @click="showAllAlerts">
              查看全部
            </el-button>
          </div>
          <div class="chart-content">
            <div class="alerts-list">
              <div 
                v-for="alert in topAlerts"
                :key="`${alert.productId}-${alert.workplaceId}`"
                class="alert-item"
                :class="alert.alertLevel"
              >
                <div class="alert-icon">
                  <i :class="getAlertIcon(alert.alertLevel)"></i>
                </div>
                <div class="alert-content">
                  <div class="alert-title">{{ alert.productName }}</div>
                  <div class="alert-subtitle">{{ alert.workplaceName }}</div>
                  <div class="alert-stock">
                    库存: {{ alert.currentStock }} / 告警: {{ alert.minStockAlert }}
                  </div>
                </div>
                <div class="alert-actions">
                  <el-button type="text" size="small" @click="handleAlert(alert)">
                    处理
                  </el-button>
                </div>
              </div>
              
              <div v-if="topAlerts.length === 0" class="no-alerts">
                <i class="fas fa-check-circle"></i>
                <p>暂无库存告警</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 职场详情表格 -->
    <div class="workplace-details">
      <div class="section-header">
        <h3 class="section-title">职场库存详情</h3>
        <div class="section-actions">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索职场..."
            prefix-icon="el-icon-search"
            size="small"
            style="width: 200px"
            clearable
          />
        </div>
      </div>

      <el-table
        :data="filteredWorkplaces"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="name" label="职场名称" width="150" />
        <el-table-column prop="code" label="职场代码" width="120" />
        <el-table-column label="总库存" width="100" align="center">
          <template #default="{ row }">
            <span class="stock-value">{{ row.totalStock || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="可用库存" width="100" align="center">
          <template #default="{ row }">
            <span class="stock-value available">{{ row.availableStock || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="预留库存" width="100" align="center">
          <template #default="{ row }">
            <span class="stock-value reserved">{{ row.reservedStock || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="商品种类" width="100" align="center">
          <template #default="{ row }">
            <span>{{ row.productCount || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="告警数量" width="100" align="center">
          <template #default="{ row }">
            <el-tag 
              v-if="row.alertCount > 0" 
              type="warning" 
              size="small"
            >
              {{ row.alertCount }}
            </el-tag>
            <span v-else class="text-muted">0</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="row.isActive ? 'success' : 'info'" 
              size="small"
            >
              {{ row.isActive ? '活跃' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template #default="{ row }">
            <el-button-group>
              <el-button
                type="primary"
                size="small"
                @click="viewWorkplaceDetail(row)"
              >
                详情
              </el-button>
              <el-button
                type="success"
                size="small"
                @click="manageWorkplaceStock(row)"
              >
                管理
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 库存告警对话框 -->
    <el-dialog
      v-model="showAlertsDialog"
      title="库存告警详情"
      width="800px"
    >
      <el-table
        :data="allAlerts"
        stripe
        border
        max-height="400"
      >
        <el-table-column prop="productName" label="商品名称" min-width="150" />
        <el-table-column prop="workplaceName" label="职场" width="120" />
        <el-table-column label="当前库存" width="100" align="center">
          <template #default="{ row }">
            <span :class="{ 'critical': row.currentStock === 0 }">
              {{ row.currentStock }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="minStockAlert" label="告警阈值" width="100" align="center" />
        <el-table-column label="告警级别" width="100" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="row.alertLevel === 'critical' ? 'danger' : 'warning'" 
              size="small"
            >
              {{ row.alertLevel === 'critical' ? '严重' : '警告' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template #default="{ row }">
            <el-button
              type="text"
              size="small"
              @click="handleAlert(row)"
            >
              处理
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, nextTick, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Loading, DocumentRemove, Select } from '@element-plus/icons-vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart, LineChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import {
  getStockDashboardData,
  getStockAlerts,
  getProductsWithStocks,
  getProductStockTrend
} from '@/api/stockManagement'

// 注册ECharts组件
use([
  CanvasRenderer,
  PieChart,
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
])

export default {
  name: 'StockDashboard',
  components: {
    VChart,
    Loading,
    DocumentRemove,
    Select
  },
  setup() {
    const loading = ref(false)
    const searchKeyword = ref('')
    const selectedMetric = ref('total')
    const rankingType = ref('highest')
    const trendDateRange = ref([])
    const showAlertsDialog = ref(false)
    const autoRefreshEnabled = ref(true)

    // 商品趋势图相关
    const selectedProductForTrend = ref(null)
    const productTrendDateRange = ref([])
    const productTrendLoading = ref(false)
    const productTrendData = ref([])

    // 图表引用
    const workplaceChartRef = ref(null)
    const trendChartRef = ref(null)
    const rankingChartRef = ref(null)

    // 数据
    const workplaces = ref([])
    const allAlerts = ref([])
    const products = ref([])
    const autoRefreshTimer = ref(null)
    const refreshInterval = ref(30000) // 30秒自动刷新

    const overviewStats = reactive({
      totalStock: 0,
      availableStock: 0,
      reservedStock: 0,
      alertCount: 0,
      stockTrend: 'stable',
      availableTrend: 'stable',
      reservedTrend: 'stable',
      alertTrend: 'stable',
      stockChange: '0%',
      availableChange: '0%',
      reservedChange: '0%',
      alertChange: '0%'
    })

    // 图表配置
    const workplaceChartOption = ref({
      title: { text: '职场库存分布', left: 'center' },
      graphic: { type: 'text', left: 'center', top: 'middle', style: { text: '加载中...', fontSize: 14, fill: '#999' } }
    })
    const trendChartOption = ref({
      title: { text: '库存变化趋势', left: 'center' },
      graphic: { type: 'text', left: 'center', top: 'middle', style: { text: '加载中...', fontSize: 14, fill: '#999' } }
    })
    const rankingChartOption = ref({
      title: { text: '商品库存排行', left: 'center' },
      graphic: { type: 'text', left: 'center', top: 'middle', style: { text: '加载中...', fontSize: 14, fill: '#999' } }
    })

    // 计算属性
    const filteredWorkplaces = computed(() => {
      if (!searchKeyword.value) return workplaces.value
      const keyword = searchKeyword.value.toLowerCase()
      return workplaces.value.filter(workplace => 
        workplace.name.toLowerCase().includes(keyword) ||
        workplace.code.toLowerCase().includes(keyword)
      )
    })

    const topAlerts = computed(() => {
      return allAlerts.value.slice(0, 5)
    })

    // 商品趋势图配置
    const productTrendChartOption = computed(() => {
      if (!productTrendData.value || productTrendData.value.length === 0) {
        return {
          title: { text: '暂无数据', left: 'center' },
          graphic: { type: 'text', left: 'center', top: 'middle', style: { text: '请选择商品查看趋势', fontSize: 14, fill: '#999' } }
        }
      }

      // 获取所有时间点
      const timePoints = [...new Set(productTrendData.value.map(item => item.date))].sort()

      // 获取所有职场
      const workplaceNames = [...new Set(productTrendData.value.map(item => item.workplaceName))]

      // 为每个职场创建数据系列
      const series = workplaceNames.map((workplaceName, index) => {
        const workplaceData = timePoints.map(date => {
          const dataPoint = productTrendData.value.find(item =>
            item.date === date && item.workplaceName === workplaceName
          )
          return dataPoint ? dataPoint.stock : 0
        })

        const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']

        return {
          name: workplaceName,
          type: 'line',
          data: workplaceData,
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3,
            color: colors[index % colors.length]
          },
          itemStyle: {
            color: colors[index % colors.length]
          },
          areaStyle: {
            opacity: 0.1,
            color: colors[index % colors.length]
          }
        }
      })

      const selectedProduct = products.value.find(p => p.id === selectedProductForTrend.value)
      const productName = selectedProduct?.name || '未知商品'

      return {
        title: {
          text: `${productName} - 职场库存趋势`,
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold',
            color: '#303133'
          },
          subtext: `时间范围: ${timePoints[0]} 至 ${timePoints[timePoints.length - 1]}`,
          subtextStyle: {
            fontSize: 12,
            color: '#909399'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter: function(params) {
            if (!params || params.length === 0) return ''

            let result = `<div style="font-weight: bold; margin-bottom: 8px; color: #303133;">${params[0].axisValue}</div>`

            // 按库存数量排序显示
            const sortedParams = params.sort((a, b) => b.value - a.value)

            sortedParams.forEach(param => {
              const percentage = params.length > 1 ?
                ((param.value / params.reduce((sum, p) => sum + p.value, 0)) * 100).toFixed(1) : 100

              result += `<div style="margin: 4px 0; display: flex; justify-content: space-between; align-items: center;">
                <span style="display: flex; align-items: center;">
                  <span style="display: inline-block; width: 12px; height: 12px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
                  <span style="color: #606266;">${param.seriesName}:</span>
                </span>
                <span style="font-weight: bold; color: #303133; margin-left: 10px;">${param.value.toLocaleString()} 件 (${percentage}%)</span>
              </div>`
            })

            const totalStock = params.reduce((sum, p) => sum + p.value, 0)
            if (params.length > 1) {
              result += `<div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid #eee; font-weight: bold; color: #303133;">
                总计: ${totalStock.toLocaleString()} 件
              </div>`
            }

            return result
          }
        },
        legend: {
          data: workplaceNames,
          top: 60,
          type: 'scroll',
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: timePoints.length > 10 ? '15%' : '10%',
          top: '100px',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: timePoints,
          axisLabel: {
            rotate: timePoints.length > 10 ? 45 : 0,
            fontSize: 11,
            color: '#606266',
            formatter: function(value) {
              // 如果数据点太多，只显示部分日期
              if (timePoints.length > 20) {
                const date = new Date(value)
                return `${date.getMonth() + 1}/${date.getDate()}`
              }
              return value
            }
          },
          axisLine: {
            lineStyle: {
              color: '#e4e7ed'
            }
          },
          axisTick: {
            lineStyle: {
              color: '#e4e7ed'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '库存数量',
          nameLocation: 'middle',
          nameGap: 50,
          nameTextStyle: {
            color: '#606266',
            fontSize: 12
          },
          axisLabel: {
            formatter: function(value) {
              if (value >= 1000) {
                return (value / 1000).toFixed(1) + 'k'
              }
              return value.toString()
            },
            fontSize: 11,
            color: '#606266'
          },
          axisLine: {
            lineStyle: {
              color: '#e4e7ed'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#f5f7fa',
              type: 'dashed'
            }
          }
        },
        series: series,
        dataZoom: timePoints.length > 15 ? [
          {
            type: 'inside',
            start: Math.max(0, 100 - (15 / timePoints.length * 100)),
            end: 100
          },
          {
            start: Math.max(0, 100 - (15 / timePoints.length * 100)),
            end: 100,
            height: 25,
            bottom: 15,
            textStyle: {
              fontSize: 10
            }
          }
        ] : [],
        animation: true,
        animationDuration: 1000,
        animationEasing: 'cubicOut'
      }
    })

    // 方法
    const getTrendIcon = (trend) => {
      switch (trend) {
        case 'up': return 'fas fa-arrow-up'
        case 'down': return 'fas fa-arrow-down'
        default: return 'fas fa-minus'
      }
    }

    // 格式化数字显示
    const formatNumber = (num) => {
      if (num === null || num === undefined) return '0'
      return num.toLocaleString()
    }

    // 获取告警标签类型
    const getAlertTagType = (alertCount) => {
      if (alertCount === 0) return 'success'
      if (alertCount <= 2) return 'warning'
      return 'danger'
    }

    const getAlertIcon = (level) => {
      return level === 'critical' ? 'fas fa-times-circle' : 'fas fa-exclamation-triangle'
    }

    // 计算趋势变化
    const calculateTrendChange = (prevValue, currentValue) => {
      if (prevValue === 0) {
        return {
          trend: currentValue > 0 ? 'up' : 'stable',
          percentage: currentValue > 0 ? '+100%' : '0%'
        }
      }

      const change = currentValue - prevValue
      const percentage = Math.abs((change / prevValue) * 100).toFixed(1)

      if (change > 0) {
        return {
          trend: 'up',
          percentage: `+${percentage}%`
        }
      } else if (change < 0) {
        return {
          trend: 'down',
          percentage: `-${percentage}%`
        }
      } else {
        return {
          trend: 'stable',
          percentage: '0%'
        }
      }
    }

    const loadData = async () => {
      loading.value = true
      try {
        const [dashboardRes, alertsRes, productsRes] = await Promise.all([
          getStockDashboardData(),
          getStockAlerts(),
          getProductsWithStocks({ limit: 1000 })
        ])

        console.log('📦 商品数据响应:', productsRes)



        // 保存之前的数据用于趋势计算
        const prevStats = { ...overviewStats }

        // 更新总体统计
        if (dashboardRes.data) {
          const { summaries } = dashboardRes.data
          let totalStock = 0, availableStock = 0, reservedStock = 0

          summaries.forEach(summary => {
            totalStock += summary.totalStock || 0
            availableStock += summary.availableStock || 0
            reservedStock += summary.reservedStock || 0
          })

          // 计算趋势
          const stockChange = calculateTrendChange(prevStats.totalStock, totalStock)
          const availableChange = calculateTrendChange(prevStats.availableStock, availableStock)
          const reservedChange = calculateTrendChange(prevStats.reservedStock, reservedStock)

          overviewStats.totalStock = totalStock
          overviewStats.availableStock = availableStock
          overviewStats.reservedStock = reservedStock
          overviewStats.stockTrend = stockChange.trend
          overviewStats.availableTrend = availableChange.trend
          overviewStats.reservedTrend = reservedChange.trend
          overviewStats.stockChange = stockChange.percentage
          overviewStats.availableChange = availableChange.percentage
          overviewStats.reservedChange = reservedChange.percentage
        }

        // 更新告警数据
        const prevAlertCount = overviewStats.alertCount
        allAlerts.value = alertsRes.data || []
        const currentAlertCount = allAlerts.value.length

        const alertChange = calculateTrendChange(prevAlertCount, currentAlertCount)
        overviewStats.alertCount = currentAlertCount
        overviewStats.alertTrend = alertChange.trend
        overviewStats.alertChange = alertChange.percentage

        // 更新商品数据
        // 处理不同的API响应结构
        let productData = []
        if (productsRes.data) {
          if (Array.isArray(productsRes.data)) {
            productData = productsRes.data
          } else if (productsRes.data.products) {
            productData = productsRes.data.products
          } else if (productsRes.data.data) {
            productData = productsRes.data.data
          }
        }

        products.value = productData
        console.log('📦 获取到商品数据:', products.value.length, '个商品')

        // 如果还没有选择商品，自动选择第一个商品
        if (!selectedProductForTrend.value && products.value.length > 0) {
          selectedProductForTrend.value = products.value[0].id
          console.log('🎯 自动选择第一个商品:', products.value[0].name, 'ID:', products.value[0].id)
        }

        // 更新职场数据
        if (dashboardRes.data && dashboardRes.data.summaries && dashboardRes.data.workplaces) {
          const summariesMap = new Map()
          dashboardRes.data.summaries.forEach(summary => {
            if (summary && summary.workplaceId) {
              summariesMap.set(summary.workplaceId, summary)
            }
          })

          workplaces.value = (dashboardRes.data.workplaces || []).map(workplace => {
            const summary = summariesMap.get(workplace.id) || {}
            const alertCount = allAlerts.value.filter(alert => alert.workplaceId === workplace.id).length

            return {
              ...workplace,
              totalStock: summary.totalStock || 0,
              availableStock: summary.availableStock || 0,
              reservedStock: summary.reservedStock || 0,
              productCount: summary.productCount || 0,
              alertCount: alertCount,
              status: workplace.isActive ? '活跃' : '停用'
            }
          })
        } else {
          // 如果没有仪表板数据，使用空数组
          workplaces.value = []
        }

        // 初始化图表
        await nextTick()
        initCharts()

        // 检查库存预警
        checkStockAlerts()

      } catch (error) {
        console.error('加载仪表板数据失败:', error)
        ElMessage.error('加载数据失败')
      } finally {
        loading.value = false
      }
    }

    const refreshData = () => {
      loadData()
    }

    const exportReport = () => {
      try {
        // 准备导出数据
        const reportData = {
          exportTime: new Date().toLocaleString(),
          overview: {
            totalStock: overviewStats.totalStock,
            availableStock: overviewStats.availableStock,
            reservedStock: overviewStats.reservedStock,
            alertCount: overviewStats.alertCount
          },
          workplaces: workplaces.value.map(workplace => ({
            name: workplace.name,
            code: workplace.code,
            totalStock: workplace.totalStock,
            availableStock: workplace.availableStock,
            reservedStock: workplace.reservedStock,
            alertCount: workplace.alertCount,
            status: workplace.isActive ? '活跃' : '停用'
          })),
          alerts: allAlerts.value.map(alert => ({
            productName: alert.productName,
            workplaceName: alert.workplaceName,
            currentStock: alert.currentStock,
            alertThreshold: alert.minStockAlert,
            alertLevel: alert.alertLevel === 'critical' ? '严重' : '警告'
          }))
        }

        // 生成CSV内容
        let csvContent = '库存统计报表\n'
        csvContent += `导出时间,${reportData.exportTime}\n\n`

        csvContent += '总体统计\n'
        csvContent += '指标,数值\n'
        csvContent += `总库存,${reportData.overview.totalStock}\n`
        csvContent += `可用库存,${reportData.overview.availableStock}\n`
        csvContent += `预留库存,${reportData.overview.reservedStock}\n`
        csvContent += `告警数量,${reportData.overview.alertCount}\n\n`

        csvContent += '职场库存详情\n'
        csvContent += '职场名称,职场代码,总库存,可用库存,预留库存,告警数量,状态\n'
        reportData.workplaces.forEach(workplace => {
          csvContent += `${workplace.name},${workplace.code},${workplace.totalStock},${workplace.availableStock},${workplace.reservedStock},${workplace.alertCount},${workplace.status}\n`
        })

        if (reportData.alerts.length > 0) {
          csvContent += '\n库存告警详情\n'
          csvContent += '商品名称,职场名称,当前库存,告警阈值,告警级别\n'
          reportData.alerts.forEach(alert => {
            csvContent += `${alert.productName},${alert.workplaceName},${alert.currentStock},${alert.alertThreshold},${alert.alertLevel}\n`
          })
        }

        // 下载文件
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        const url = URL.createObjectURL(blob)
        link.setAttribute('href', url)
        link.setAttribute('download', `库存统计报表_${new Date().toISOString().slice(0, 10)}.csv`)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        ElMessage.success('报表导出成功')
      } catch (error) {
        console.error('导出报表失败:', error)
        ElMessage.error('导出报表失败')
      }
    }

    const initCharts = () => {
      try {
        initWorkplaceChart()
        initTrendChart()
        initRankingChart()
      } catch (error) {
        console.error('初始化图表失败:', error)
        ElMessage.error('图表初始化失败')
      }
    }

    // 初始化职场库存分布饼图
    const initWorkplaceChart = () => {
      if (!workplaces.value || workplaces.value.length === 0) {
        workplaceChartOption.value = {}
        return
      }

      const metricKey = selectedMetric.value + 'Stock'
      const chartData = workplaces.value
        .filter(workplace => workplace.isActive && workplace[metricKey] > 0)
        .map(workplace => ({
          name: workplace.name,
          value: workplace[metricKey] || 0,
          workplaceId: workplace.id
        }))
        .sort((a, b) => b.value - a.value)

      // 如果没有数据，显示空图表
      if (chartData.length === 0) {
        workplaceChartOption.value = {
          title: {
            text: '职场库存分布',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 'normal'
            }
          },
          graphic: {
            type: 'text',
            left: 'center',
            top: 'middle',
            style: {
              text: '暂无数据',
              fontSize: 14,
              fill: '#999'
            }
          }
        }
        return
      }

      workplaceChartOption.value = {
        title: {
          text: '职场库存分布',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: chartData.map(item => item.name)
        },
        series: [
          {
            name: '库存数量',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['60%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: chartData
          }
        ]
      }
    }

    // 初始化库存趋势图
    const initTrendChart = () => {
      // 生成最近7天的模拟数据
      const dates = []
      const totalStockData = []
      const availableStockData = []
      const alertData = []

      for (let i = 6; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        dates.push(date.toLocaleDateString())

        // 模拟数据，实际应该从API获取
        totalStockData.push(Math.floor(Math.random() * 1000) + 2000)
        availableStockData.push(Math.floor(Math.random() * 800) + 1500)
        alertData.push(Math.floor(Math.random() * 20) + 10)
      }

      trendChartOption.value = {
        title: {
          text: '库存变化趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['总库存', '可用库存', '告警数量'],
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dates
        },
        yAxis: [
          {
            type: 'value',
            name: '库存数量',
            position: 'left'
          },
          {
            type: 'value',
            name: '告警数量',
            position: 'right'
          }
        ],
        series: [
          {
            name: '总库存',
            type: 'line',
            data: totalStockData,
            smooth: true,
            lineStyle: {
              color: '#409eff'
            }
          },
          {
            name: '可用库存',
            type: 'line',
            data: availableStockData,
            smooth: true,
            lineStyle: {
              color: '#67c23a'
            }
          },
          {
            name: '告警数量',
            type: 'line',
            yAxisIndex: 1,
            data: alertData,
            smooth: true,
            lineStyle: {
              color: '#f56c6c'
            }
          }
        ]
      }
    }

    // 初始化商品库存排行图
    const initRankingChart = () => {

      if (!products.value || products.value.length === 0) {
        rankingChartOption.value = {
          title: {
            text: rankingType.value === 'highest' ? '库存最多商品' : '库存最少商品',
            left: 'center'
          },
          graphic: {
            type: 'text',
            left: 'center',
            top: 'middle',
            style: {
              text: '暂无商品数据',
              fontSize: 14,
              fill: '#999'
            }
          }
        }
        return
      }

      // 计算每个商品的总库存
      const productStocks = products.value.map((product, index) => {
        // 只打印前3个商品的详细信息，避免控制台过于冗长
        if (index < 3) {
          console.log('📦 处理商品:', product.name)
          console.log('  - 基础库存 (stock):', product.stock)
          console.log('  - 职场库存 (workplaceStocks):', product.workplaceStocks)
          console.log('  - Sequelize关联 (ProductWorkplaceStocks):', product.ProductWorkplaceStocks)
        }

        // 尝试多种方式计算库存
        let totalStock = 0

        if (product.workplaceStocks && Array.isArray(product.workplaceStocks) && product.workplaceStocks.length > 0) {
          // 从职场库存计算总库存
          totalStock = product.workplaceStocks.reduce((sum, stock) => {
            // 尝试不同的字段名
            const stockValue = stock.availableStock || stock.stock || 0
            if (index < 3) {
              console.log('    职场库存:', stock.workplaceName || stock.workplace?.name, '库存:', stockValue)
            }
            return sum + stockValue
          }, 0)
        } else if (product.ProductWorkplaceStocks && Array.isArray(product.ProductWorkplaceStocks) && product.ProductWorkplaceStocks.length > 0) {
          // 处理Sequelize关联查询的结果
          totalStock = product.ProductWorkplaceStocks.reduce((sum, stock) => {
            const stockValue = stock.availableStock || stock.stock || 0
            if (index < 3) {
              console.log('    Sequelize职场库存:', stockValue)
            }
            return sum + stockValue
          }, 0)
        } else if (product.stock !== undefined && product.stock !== null) {
          // 如果没有职场库存，使用总库存
          totalStock = product.stock
          if (index < 3) {
            console.log('    使用基础库存:', totalStock)
          }
        } else if (product.totalStock !== undefined && product.totalStock !== null) {
          totalStock = product.totalStock
          if (index < 3) {
            console.log('    使用总库存字段:', totalStock)
          }
        }

        if (index < 3) {
          console.log('  📊 最终计算库存:', totalStock)
        }

        return {
          name: product.name,
          value: totalStock,
          productId: product.id
        }
      }).filter(item => item.value > 0) // 只显示有库存的商品

      console.log('📈 有库存的商品:', productStocks.length, '个')

      // 排序并取前8名
      const sortedProducts = productStocks.length > 0
        ? (rankingType.value === 'highest'
          ? productStocks.sort((a, b) => b.value - a.value).slice(0, 8)
          : productStocks.sort((a, b) => a.value - b.value).slice(0, 8))
        : []

      const productNames = sortedProducts.map(item => item.name)
      const stockData = sortedProducts.map(item => item.value)

      console.log('📊 最终图表数据:', {
        商品数量: sortedProducts.length,
        商品名称: productNames,
        库存数据: stockData
      })

      // 如果没有数据，显示空图表
      if (sortedProducts.length === 0) {
        rankingChartOption.value = {
          title: {
            text: rankingType.value === 'highest' ? '库存最多商品' : '库存最少商品',
            left: 'center'
          },
          graphic: {
            type: 'text',
            left: 'center',
            top: 'middle',
            style: {
              text: '暂无数据',
              fontSize: 14,
              fill: '#999'
            }
          }
        }
        return
      }

      rankingChartOption.value = {
        title: {
          text: rankingType.value === 'highest' ? '库存最多商品' : '库存最少商品',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'category',
          data: productNames
        },
        series: [
          {
            name: '库存数量',
            type: 'bar',
            data: stockData,
            itemStyle: {
              color: rankingType.value === 'highest' ? '#67c23a' : '#f56c6c'
            }
          }
        ]
      }

      console.log('📊 设置图表配置完成，数据长度:', stockData.length)
    }

    const updateTrendChart = () => {
      initTrendChart()
    }

    // 商品趋势图相关方法
    const handleProductTrendChange = async () => {
      if (selectedProductForTrend.value) {
        await loadProductTrendData()
      } else {
        productTrendData.value = []
      }
    }

    const updateProductTrendChart = async () => {
      if (selectedProductForTrend.value) {
        await loadProductTrendData()
      }
    }

    const refreshProductTrendData = async () => {
      if (selectedProductForTrend.value) {
        await loadProductTrendData()
      }
    }

    const loadProductTrendData = async () => {
      if (!selectedProductForTrend.value) return

      productTrendLoading.value = true
      try {
        // 设置默认时间范围（最近30天）
        const endDate = new Date()
        const startDate = new Date()
        startDate.setDate(startDate.getDate() - 30)

        const dateRange = productTrendDateRange.value && productTrendDateRange.value.length === 2
          ? productTrendDateRange.value
          : [startDate, endDate]

        const params = {
          productId: selectedProductForTrend.value,
          startDate: dateRange[0].toISOString().split('T')[0],
          endDate: dateRange[1].toISOString().split('T')[0]
        }

        console.log('🔍 查询商品库存趋势:', params)

        // 调用实际的API获取商品库存趋势数据
        const response = await getProductStockTrend(params)

        console.log('📊 API响应:', response)

        if (response.success) {
          productTrendData.value = response.data.trendData || []

          console.log('📈 设置趋势数据:', productTrendData.value)

          if (productTrendData.value.length > 0) {
            ElMessage.success(`成功加载 ${productTrendData.value.length} 条趋势数据`)
          } else {
            ElMessage.info('该时间范围内暂无库存变化记录')
          }
        } else {
          throw new Error(response.message || '获取趋势数据失败')
        }
      } catch (error) {
        console.error('加载商品趋势数据失败:', error)
        ElMessage.error(`加载商品趋势数据失败: ${error.message}`)
        productTrendData.value = []
      } finally {
        productTrendLoading.value = false
      }
    }

    const showAllAlerts = () => {
      showAlertsDialog.value = true
    }

    const handleAlert = (alert) => {
      ElMessage.info(`处理告警: ${alert.productName} - ${alert.workplaceName}`)
    }

    const viewWorkplaceDetail = (workplace) => {
      ElMessage.info(`查看职场详情: ${workplace.name}`)
    }

    const manageWorkplaceStock = (workplace) => {
      ElMessage.info(`管理职场库存: ${workplace.name}`)
    }

    // 图表点击事件处理
    const onWorkplaceChartClick = (params) => {
      if (params.data) {
        const workplace = workplaces.value.find(w => w.name === params.data.name)
        if (workplace) {
          ElMessageBox.alert(
            `
            <div style="text-align: left;">
              <p><strong>职场名称:</strong> ${workplace.name}</p>
              <p><strong>职场代码:</strong> ${workplace.code}</p>
              <p><strong>总库存:</strong> ${workplace.totalStock}</p>
              <p><strong>可用库存:</strong> ${workplace.availableStock}</p>
              <p><strong>预留库存:</strong> ${workplace.reservedStock}</p>
              <p><strong>商品种类:</strong> ${workplace.productCount}</p>
              <p><strong>告警数量:</strong> ${workplace.alertCount}</p>
              <p><strong>状态:</strong> ${workplace.isActive ? '活跃' : '停用'}</p>
            </div>
            `,
            '职场库存详情',
            {
              dangerouslyUseHTMLString: true,
              confirmButtonText: '确定'
            }
          )
        }
      }
    }

    const onTrendChartClick = (params) => {
      if (params.data) {
        ElMessage.info(`${params.seriesName}: ${params.name} - ${params.data}`)
      }
    }

    const onRankingChartClick = (params) => {
      if (params.data) {
        const product = products.value.find(p => p.name === params.name)
        if (product) {
          ElMessageBox.alert(
            `
            <div style="text-align: left;">
              <p><strong>商品名称:</strong> ${product.name}</p>
              <p><strong>商品编码:</strong> ${product.code || '无'}</p>
              <p><strong>总库存:</strong> ${params.data}</p>
              <p><strong>库存管理:</strong> ${product.stockManagementType === 'workplace' ? '职场管理' : '统一管理'}</p>
              <p><strong>状态:</strong> ${product.status === 'active' ? '上架' : '下架'}</p>
            </div>
            `,
            '商品库存详情',
            {
              dangerouslyUseHTMLString: true,
              confirmButtonText: '确定'
            }
          )
        }
      }
    }

    // 监听选择器变化
    const handleMetricChange = () => {
      initWorkplaceChart()
    }

    const handleRankingTypeChange = () => {
      initRankingChart()
    }

    // 自动刷新切换处理
    const handleAutoRefreshToggle = (enabled) => {
      if (enabled) {
        startAutoRefresh()
        ElMessage.success('已开启自动刷新')
      } else {
        stopAutoRefresh()
        ElMessage.info('已关闭自动刷新')
      }
    }

    // 自动刷新功能
    const startAutoRefresh = () => {
      if (autoRefreshTimer.value) {
        clearInterval(autoRefreshTimer.value)
      }

      autoRefreshTimer.value = setInterval(() => {
        console.log('🔄 自动刷新库存数据...')
        loadData()
      }, refreshInterval.value)
    }

    const stopAutoRefresh = () => {
      if (autoRefreshTimer.value) {
        clearInterval(autoRefreshTimer.value)
        autoRefreshTimer.value = null
      }
    }

    // 检查库存预警
    const checkStockAlerts = () => {
      const criticalAlerts = allAlerts.value.filter(alert => alert.alertLevel === 'critical')
      const warningAlerts = allAlerts.value.filter(alert => alert.alertLevel === 'warning')

      if (criticalAlerts.length > 0) {
        ElMessage({
          message: `发现 ${criticalAlerts.length} 个严重库存告警！`,
          type: 'error',
          duration: 5000,
          showClose: true
        })
      } else if (warningAlerts.length > 0) {
        ElMessage({
          message: `发现 ${warningAlerts.length} 个库存预警`,
          type: 'warning',
          duration: 3000,
          showClose: true
        })
      }
    }

    // 生命周期
    onMounted(() => {
      // 初始化商品趋势图的默认日期范围（最近30天）
      const endDate = new Date()
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - 30)
      productTrendDateRange.value = [startDate, endDate]

      loadData()
      startAutoRefresh()
    })

    onUnmounted(() => {
      stopAutoRefresh()
    })

    return {
      loading,
      searchKeyword,
      selectedMetric,
      rankingType,
      trendDateRange,
      showAlertsDialog,
      autoRefreshEnabled,
      refreshInterval,
      workplaceChartRef,
      trendChartRef,
      rankingChartRef,
      workplaces,
      allAlerts,
      products,
      overviewStats,
      filteredWorkplaces,
      topAlerts,

      // 商品趋势图相关
      selectedProductForTrend,
      productTrendDateRange,
      productTrendLoading,
      productTrendData,

      // 图表配置
      workplaceChartOption,
      trendChartOption,
      rankingChartOption,
      productTrendChartOption,

      // 方法
      getTrendIcon,
      getAlertIcon,
      formatNumber,
      getAlertTagType,
      refreshData,
      exportReport,
      updateTrendChart,
      showAllAlerts,
      handleAlert,
      viewWorkplaceDetail,
      manageWorkplaceStock,

      // 商品趋势图方法
      handleProductTrendChange,
      updateProductTrendChart,
      refreshProductTrendData,

      // 图表事件处理
      onWorkplaceChartClick,
      onTrendChartClick,
      onRankingChartClick,
      handleMetricChange,
      handleRankingTypeChange,

      // 自动刷新控制
      handleAutoRefreshToggle,
      startAutoRefresh,
      stopAutoRefresh,
      checkStockAlerts
    }
  }
}
</script>

<style scoped>
.stock-dashboard {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content .page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-content .page-title i {
  color: #409eff;
}

.page-description {
  color: #909399;
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.auto-refresh-control {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.refresh-interval {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
}

/* 总体统计卡片 */
.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stats-card {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.2s;
  position: relative;
  overflow: hidden;
}

.stats-card:hover {
  transform: translateY(-2px);
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
}

.stats-card.total::before {
  background: linear-gradient(135deg, #409eff, #67c23a);
}

.stats-card.available::before {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.stats-card.reserved::before {
  background: linear-gradient(135deg, #e6a23c, #f7ba2a);
}

.stats-card.alerts::before {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stats-card.total .stats-icon {
  background: linear-gradient(135deg, #409eff, #67c23a);
}

.stats-card.available .stats-icon {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.stats-card.reserved .stats-icon {
  background: linear-gradient(135deg, #e6a23c, #f7ba2a);
}

.stats-card.alerts .stats-icon {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.stats-content {
  flex: 1;
}

.stats-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin: 4px 0;
}

.stats-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.stats-trend.up {
  color: #67c23a;
}

.stats-trend.down {
  color: #f56c6c;
}

.stats-trend.stable {
  color: #909399;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 24px;
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-row.full-width {
  grid-template-columns: 1fr;
}

.chart-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.chart-card.large-chart {
  min-height: 500px;
}

.chart-header {
  padding: 20px 20px 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.chart-content {
  padding: 20px;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.chart-container.large-chart-container {
  height: 450px;
}

.chart-loading,
.chart-empty {
  width: 100%;
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 6px;
  color: #909399;
  font-size: 14px;
}

.chart-loading .el-icon,
.chart-empty .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.chart-loading .el-icon {
  color: #409eff;
}

.chart-empty .el-icon {
  color: #c0c4cc;
}

.chart-loading p,
.chart-empty p {
  margin: 0;
  font-size: 14px;
}

/* 告警列表 */
.alerts-list {
  max-height: 300px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.2s;
}

.alert-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.alert-item.critical {
  border-color: #f56c6c;
  background: #fef0f0;
}

.alert-item.warning {
  border-color: #e6a23c;
  background: #fdf6ec;
}

.alert-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: white;
}

.alert-item.critical .alert-icon {
  background: #f56c6c;
}

.alert-item.warning .alert-icon {
  background: #e6a23c;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.alert-subtitle {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.alert-stock {
  font-size: 12px;
  color: #606266;
}

.no-alerts {
  text-align: center;
  padding: 40px;
  color: #909399;
}

.no-alerts i {
  font-size: 48px;
  color: #67c23a;
  margin-bottom: 16px;
}

.no-alerts p {
  margin: 0;
  font-size: 14px;
}

/* 职场详情表格 */
.workplace-details {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.stock-value {
  font-weight: 600;
  color: #303133;
}

.stock-value.available {
  color: #67c23a;
}

.stock-value.reserved {
  color: #e6a23c;
}

.stock-value.zero-stock {
  color: #909399;
  font-weight: normal;
}

.product-count {
  font-weight: 500;
  color: #606266;
}

.text-muted {
  color: #909399;
}

.section-header {
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.stock-value {
  font-weight: 500;
  color: #303133;
}

.stock-value.available {
  color: #67c23a;
}

.stock-value.reserved {
  color: #e6a23c;
}

.text-muted {
  color: #c0c4cc;
}

.critical {
  color: #f56c6c;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .overview-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 1200px) {
  .chart-row {
    grid-template-columns: 1fr;
  }

  .overview-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .stock-dashboard {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .auto-refresh-control {
    justify-content: center;
  }

  .overview-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .chart-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .chart-container,
  .chart-loading,
  .chart-empty {
    height: 250px;
  }

  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .alert-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .alert-actions {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .stock-dashboard {
    padding: 8px;
  }

  .page-header {
    padding: 16px;
  }

  .stats-card {
    padding: 16px;
  }

  .stats-value {
    font-size: 24px;
  }

  .chart-content {
    padding: 12px;
  }

  .chart-container,
  .chart-loading,
  .chart-empty {
    height: 200px;
  }

  .chart-loading .el-icon,
  .chart-empty .el-icon {
    font-size: 36px;
    margin-bottom: 12px;
  }

  .workplace-details {
    overflow-x: auto;
  }
}

/* 商品趋势图样式 */
.chart-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.product-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.product-name {
  font-weight: 500;
  color: #303133;
  flex: 1;
  text-align: left;
}

.product-stock {
  font-size: 12px;
  color: #909399;
  margin-left: 10px;
}

.empty-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  text-align: center;
}

.chart-title i {
  margin-right: 8px;
  color: #409eff;
}

@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .chart-controls .el-select,
  .chart-controls .el-date-picker {
    width: 100% !important;
  }
}
</style>
