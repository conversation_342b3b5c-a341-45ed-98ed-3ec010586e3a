const { sequelize } = require('./models');
const { DataTypes } = require('sequelize');

async function checkAndFixAnnouncementsTable() {
  try {
    console.log('开始检查公告表结构...');
    
    // 检查announcements表是否存在
    const tables = await sequelize.getQueryInterface().showAllTables();
    const tableExists = tables.includes('announcements');
    
    if (!tableExists) {
      console.log('公告表不存在，正在创建...');
      await sequelize.getQueryInterface().createTable('announcements', {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true
        },
        title: {
          type: DataTypes.STRING,
          allowNull: false
        },
        content: {
          type: DataTypes.TEXT,
          allowNull: false
        },
        imageUrl: {
          type: DataTypes.STRING,
          allowNull: true
        },
        type: {
          type: DataTypes.STRING,
          allowNull: false,
          defaultValue: 'system'
        },
        status: {
          type: DataTypes.STRING,
          allowNull: false,
          defaultValue: 'draft'
        },
        createdBy: {
          type: DataTypes.INTEGER,
          allowNull: true,
          references: {
            model: 'users',
            key: 'id'
          }
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: DataTypes.NOW
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: DataTypes.NOW
        }
      });
      console.log('公告表创建成功！');
    } else {
      console.log('公告表已存在，检查结构...');
      
      // 检查imageUrl字段是否存在
      try {
        await sequelize.query('SELECT imageUrl FROM announcements LIMIT 1');
        console.log('imageUrl字段已存在');
      } catch (error) {
        console.log('imageUrl字段不存在，添加该字段...');
        await sequelize.getQueryInterface().addColumn('announcements', 'imageUrl', {
          type: DataTypes.STRING,
          allowNull: true
        });
        console.log('imageUrl字段添加成功！');
      }
    }
    
    console.log('公告表检查完成！');
  } catch (error) {
    console.error('检查和修复公告表时出错:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行检查和修复
checkAndFixAnnouncementsTable();
