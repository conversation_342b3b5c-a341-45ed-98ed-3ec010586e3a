<template>
  <div class="feishu-login-container">
    <button
      class="feishu-login-button"
      :class="{ loading, mobile: isMobile }"
      @click="handleFeishuLogin"
      :disabled="loading"
    >
      <span v-if="loading" class="loading-icon"></span>
      <svg v-else class="feishu-icon" viewBox="0 0 24 24" width="20" height="20">
        <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
      </svg>
      {{ loading ? (isMobile ? '登录中...' : '飞书登录中...') : (isMobile ? '飞书登录' : '飞书账号登录') }}
    </button>

    <!-- 错误提示 -->
    <div v-if="error" class="error-message" :class="{ mobile: isMobile }">
      {{ error }}
      <div v-if="isMobile && showMobileTip" class="mobile-tip">
        建议在浏览器中打开进行登录
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onUnmounted, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { useAuthStore } from '../stores/auth';
import {
  isMobileDevice,
  getFeishuLoginStrategy,
  addTouchFeedback,
  showMobileOptimizedMessage
} from '../utils/mobileUtils';
import api from '../api';

const props = defineProps({
  // 是否在新窗口打开（null表示自动检测）
  newWindow: {
    type: Boolean,
    default: null
  }
});

const emit = defineEmits(['login-success', 'login-error']);

const router = useRouter();
const authStore = useAuthStore();
const loading = ref(false);
const error = ref('');
const isMobile = ref(false);
const showMobileTip = ref(false);
const loginStrategy = ref({});

onMounted(() => {
  // 检测移动端并获取登录策略
  isMobile.value = isMobileDevice();
  loginStrategy.value = getFeishuLoginStrategy();

  console.log('飞书登录策略:', loginStrategy.value);
});

// 处理飞书登录
const handleFeishuLogin = async () => {
  try {
    if (loading.value) return;
    loading.value = true;

    loginStrategy.value = getFeishuLoginStrategy();

    console.log('飞书登录策略:', loginStrategy.value);

    if (loginStrategy.value.method === 'same-window') {
      // 获取飞书登录URL
      const response = await api.get('/feishu/login-url');
      console.log('飞书登录URL响应:', response);

      if (!response || !response.url) {
        throw new Error('获取飞书登录链接失败: 服务器返回数据不完整');
      }

      const loginUrl = response.url;

      // 移动端优化：显示友好的跳转提示
      if (isMobile.value) {
        console.log('移动端飞书登录：准备跳转到飞书授权页面');

        // 可选：显示加载提示
        const messageOptions = showMobileOptimizedMessage('正在跳转到飞书登录...', 'info');
        ElMessage(messageOptions);
      }

      // 直接在当前窗口打开登录链接
      console.log(`${loginStrategy.value.reason}，跳转至:`, loginUrl);
      window.location.href = loginUrl;
    } else if (loginStrategy.value.method === 'new-window') {
      // 获取飞书登录URL
      const response = await api.get('/feishu/login-url');
      console.log('飞书登录URL响应:', response);

      if (!response || !response.url) {
        throw new Error('获取飞书登录链接失败: 服务器返回数据不完整');
      }

      const loginUrl = response.url;

      // 桌面端：在新窗口中打开飞书登录
      const width = 800;
      const height = 600;
      const left = (window.screen.width - width) / 2;
      const top = (window.screen.height - height) / 2;

      const loginWindow = window.open(
        loginUrl,
        'feishu-login',
        `width=${width},height=${height},left=${left},top=${top},menubar=no,toolbar=no,location=no,status=no`
      );

      // 检查窗口是否被阻止
      if (!loginWindow || loginWindow.closed || typeof loginWindow.closed === 'undefined') {
        loading.value = false;
        throw new Error('弹出窗口被阻止，请允许弹出窗口或在设置中允许弹出窗口');
      }

      // 添加消息监听，处理飞书登录回调
      window.addEventListener('message', handleFeishuCallback);

      // 设置超时，如果15秒后用户没有完成登录，则重置loading状态
      setTimeout(() => {
        if (loading.value) {
          loading.value = false;
        }
      }, 15000);
    } else {
      throw new Error('不支持的登录策略');
    }
  } catch (err) {
    console.error('飞书登录错误:', err);
    loading.value = false;

    // 设置错误信息
    error.value = err.message || '飞书登录失败，请稍后再试';

    // 移动端特殊错误处理
    if (isMobile.value) {
      if (error.value.includes('弹出窗口被阻止') || error.value.includes('网络')) {
        showMobileTip.value = true;
      }
    }

    // 发出错误事件
    emit('login-error', error.value);

    // 使用移动端优化的消息提示
    const messageOptions = showMobileOptimizedMessage(error.value, 'error');
    ElMessage(messageOptions);
  }
};

// 处理登录回调
const handleFeishuCallback = async (event) => {
  // 验证消息来源和类型
  if (event.data && event.data.type === 'feishu-login-success') {
    console.log('收到飞书登录成功消息:', event.data);

    // 验证令牌和用户信息
    if (event.data.token && event.data.user) {
      // 设置身份验证状态
      authStore.token = event.data.token;
      authStore.user = event.data.user;

      // 存储令牌
      sessionStorage.setItem('token', event.data.token);

      // 移除消息监听器
      window.removeEventListener('message', handleFeishuCallback);

      // 发出登录成功事件
      emit('login-success', event.data.user);

      // 使用移动端优化的成功消息
      const successMessage = isMobile.value ? '登录成功！' : '飞书登录成功';
      const messageOptions = showMobileOptimizedMessage(successMessage, 'success');
      ElMessage(messageOptions);

      // 跳转到首页或指定页面
      router.push('/');
    }
  }

  loading.value = false;
};

const handleFeishuApiResponse = async (response) => {
  console.log('飞书API响应:', response);

  if (response.code === 'ACCOUNT_DISABLED') {
    loading.value = false;
    ElMessage.error('账号已被禁用，请联系管理员');
    emit('login-error', '账号已被禁用，请联系管理员');
    return;
  }

  if (!response.token || !response.user) {
    loading.value = false;
    throw new Error(response.message || '服务器返回数据不完整');
  }

  // 存储令牌和用户信息
  window.sessionStorage.setItem('token', response.token);
  window.sessionStorage.setItem('user', JSON.stringify(response.user));

  // 登录成功
  const successMessage = isMobile.value ? '登录成功！' : '飞书登录成功';
  ElMessage.success(successMessage);
  loading.value = false;

  // 触发事件
  emit('login-success', response.user);

  // 跳转到首页
  router.push('/');
};

// 飞书登录API调用
const feishuApiLogin = async (code) => {
  try {
    const response = await api.post('/feishu/login', { code });
    await handleFeishuApiResponse(response);
  } catch (err) {
    console.error('飞书登录错误:', err);
    loading.value = false;

    // 检查是否是账号禁用错误
    if (err.response && err.response.data && err.response.data.code === 'ACCOUNT_DISABLED') {
      ElMessage.error('账号已被禁用，请联系管理员');
      emit('login-error', '账号已被禁用，请联系管理员');
    } else {
      const errorMessage = err.message || '飞书登录失败，请稍后再试';
      ElMessage.error(errorMessage);
      emit('login-error', errorMessage);
    }
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('message', handleFeishuCallback);
});
</script>

<style scoped>
.feishu-login-container {
  width: 100%;
  text-align: center;
}

.feishu-login-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 14px 24px;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
  background-color: #3370ff;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(51, 112, 255, 0.2);
  min-height: 48px; /* 确保触摸友好的最小高度 */
  position: relative;
  overflow: hidden;
}

/* 移动端优化 */
.feishu-login-button.mobile {
  padding: 16px 20px;
  font-size: 18px;
  border-radius: 12px;
  min-height: 56px; /* 移动端更大的触摸区域 */
  font-weight: 600;
  letter-spacing: 0.5px;
}

.feishu-login-button:hover {
  background-color: #2a5cd8;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(51, 112, 255, 0.3);
}

/* 移动端取消hover效果，使用active效果 */
.feishu-login-button.mobile:hover {
  transform: none;
  background-color: #3370ff;
}

.feishu-login-button:active,
.feishu-login-button.mobile:active {
  transform: translateY(0);
  background-color: #1c5aa8;
  box-shadow: 0 2px 8px rgba(51, 112, 255, 0.2);
}

.feishu-login-button.loading {
  background-color: #6691ff;
  cursor: not-allowed;
}

.feishu-icon {
  margin-right: 8px;
  flex-shrink: 0;
}

.feishu-login-button.mobile .feishu-icon {
  margin-right: 10px;
  width: 22px;
  height: 22px;
}

.loading-icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-right: 10px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
  flex-shrink: 0;
}

.feishu-login-button.mobile .loading-icon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error-message {
  margin-top: 12px;
  color: #f56c6c;
  font-size: 13px;
  line-height: 1.4;
  padding: 8px 12px;
  background-color: #fef0f0;
  border-radius: 6px;
  border: 1px solid #fbc4c4;
}

.error-message.mobile {
  font-size: 14px;
  padding: 12px 16px;
  margin-top: 16px;
  border-radius: 8px;
}

.mobile-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  font-style: italic;
}

.error-message.mobile .mobile-tip {
  font-size: 13px;
  margin-top: 10px;
}

/* 确保在小屏幕上的响应式布局 */
@media (max-width: 768px) {
  .feishu-login-button {
    padding: 16px 20px;
    font-size: 17px;
    border-radius: 12px;
    min-height: 54px;
  }

  .error-message {
    font-size: 14px;
    padding: 12px 16px;
    margin-top: 16px;
  }
}

/* 高对比度模式适配 */
@media (prefers-contrast: high) {
  .feishu-login-button {
    border: 2px solid #fff;
  }
}

/* 减少动画的用户偏好适配 */
@media (prefers-reduced-motion: reduce) {
  .feishu-login-button {
    transition: none;
  }

  .loading-icon {
    animation: none;
  }
}
</style>
