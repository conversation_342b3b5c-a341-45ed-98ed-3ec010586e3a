#!/usr/bin/env node

/**
 * 数据仪表盘修复验证脚本
 * 验证token存储一致性修复是否成功
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('=== 数据仪表盘修复验证 ===\n');

// 检查项目列表
const checks = [
  {
    name: '检查Dashboard.vue中token获取修复',
    check: () => {
      const filePath = path.join(__dirname, '../src/views/admin/Dashboard.vue');
      const content = fs.readFileSync(filePath, 'utf8');

      // 检查是否已修复为sessionStorage
      const hasCorrectTokenAccess = content.includes('sessionStorage.getItem(\'token\')');

      // 检查是否没有localStorage.getItem('token')
      const noIncorrectTokenAccess = !content.includes('localStorage.getItem(\'token\')');

      return hasCorrectTokenAccess && noIncorrectTokenAccess;
    }
  },
  {
    name: '检查AnnouncementManagement.vue中token获取修复',
    check: () => {
      const filePath = path.join(__dirname, '../src/views/admin/AnnouncementManagement.vue');
      const content = fs.readFileSync(filePath, 'utf8');

      // 检查uploadHeaders中是否使用sessionStorage
      const hasCorrectUploadHeaders = content.includes('sessionStorage.getItem(\'token\')') &&
                                     content.includes('uploadHeaders');

      // 检查是否没有localStorage.getItem('token')
      const noIncorrectTokenAccess = !content.includes('localStorage.getItem(\'token\')');

      return hasCorrectUploadHeaders && noIncorrectTokenAccess;
    }
  },
  {
    name: '检查RichTextEditor.vue中token获取修复',
    check: () => {
      const filePath = path.join(__dirname, '../src/components/RichTextEditor.vue');
      const content = fs.readFileSync(filePath, 'utf8');

      // 检查图片上传中是否使用sessionStorage
      const hasCorrectImageUpload = content.includes('sessionStorage.getItem(\'token\')') &&
                                   content.includes('Authorization');

      // 检查是否没有localStorage.getItem('token')
      const noIncorrectTokenAccess = !content.includes('localStorage.getItem(\'token\')');

      return hasCorrectImageUpload && noIncorrectTokenAccess;
    }
  },
  {
    name: '检查认证系统一致性',
    check: () => {
      // 检查auth store是否使用sessionStorage
      const authStorePath = path.join(__dirname, '../src/stores/auth.js');
      const authStoreContent = fs.readFileSync(authStorePath, 'utf8');

      const authStoreUsesSession = authStoreContent.includes('sessionStorage.getItem(\'token\')') &&
                                  authStoreContent.includes('sessionStorage.setItem(\'token\'');

      // 检查dashboard API是否使用sessionStorage
      const dashboardApiPath = path.join(__dirname, '../src/api/dashboard.js');
      const dashboardApiContent = fs.readFileSync(dashboardApiPath, 'utf8');

      const dashboardApiUsesSession = dashboardApiContent.includes('sessionStorage.getItem(\'token\')');

      return authStoreUsesSession && dashboardApiUsesSession;
    }
  },
  {
    name: '检查API请求拦截器配置',
    check: () => {
      const apiIndexPath = path.join(__dirname, '../src/api/index.js');
      const apiIndexContent = fs.readFileSync(apiIndexPath, 'utf8');

      // 检查是否包含exchanges相关的认证路径
      const hasExchangesAuth = apiIndexContent.includes('/exchanges') ||
                              apiIndexContent.includes('requireAuthPaths');

      // 检查是否正确设置Authorization头
      const hasAuthHeader = apiIndexContent.includes('Authorization') &&
                           apiIndexContent.includes('Bearer');

      return hasExchangesAuth && hasAuthHeader;
    }
  }
];

// 执行检查
let passedChecks = 0;
let totalChecks = checks.length;

checks.forEach((check, index) => {
  try {
    const result = check.check();
    const status = result ? '✅ 通过' : '❌ 失败';
    console.log(`${index + 1}. ${check.name}: ${status}`);

    if (result) {
      passedChecks++;
    }
  } catch (error) {
    console.log(`${index + 1}. ${check.name}: ❌ 错误 - ${error.message}`);
  }
});

console.log(`\n=== 检查结果 ===`);
console.log(`通过: ${passedChecks}/${totalChecks}`);

if (passedChecks === totalChecks) {
  console.log('🎉 所有检查都通过了！数据仪表盘修复成功。');
  console.log('\n📋 修复内容总结:');
  console.log('• 修复了Dashboard.vue中导出功能的token获取');
  console.log('• 修复了AnnouncementManagement.vue中上传功能的token获取');
  console.log('• 修复了RichTextEditor.vue中图片上传的token获取');
  console.log('• 确保了所有组件与认证系统的token存储一致性');

  console.log('\n🔧 建议的后续测试:');
  console.log('1. 登录管理员账户');
  console.log('2. 访问数据仪表盘页面');
  console.log('3. 测试导出Excel和PDF功能');
  console.log('4. 测试公告管理的图片上传功能');
  console.log('5. 测试富文本编辑器的图片上传功能');

  process.exit(0);
} else {
  console.log('❌ 部分检查失败，请检查修复是否完整。');
  process.exit(1);
}
