<template>
  <div class="hot-product-ranking">
    <div class="ranking-header">
      <h3 class="ranking-title">
        <el-icon><trophy /></el-icon>
        热门商品排行榜
      </h3>
      <el-select
        v-model="selectedTimeRange"
        @change="loadHotProducts"
        class="time-range-selector"
        size="small"
      >
        <el-option
          v-for="option in timeRangeOptions"
          :key="option.value"
          :label="option.label"
          :value="option.value"
        />
      </el-select>
    </div>

    <div v-loading="loading" class="ranking-content">
      <div v-if="hotProducts.length > 0" class="ranking-list">
        <div
          v-for="(product, index) in hotProducts"
          :key="product.id"
          class="ranking-item"
          :class="{ 'top-three': index < 3 }"
          @click="$emit('product-click', product)"
        >
          <div class="rank-badge" :class="getRankClass(index)">
            {{ index + 1 }}
          </div>

          <div class="product-image">
            <el-image
              :src="getProductImage(product)"
              fit="cover"
              :alt="product.name"
            >
              <template #error>
                <div class="image-placeholder">
                  <img :src="getDefaultProductImage()" alt="默认商品图片" style="width: 100%; height: 100%; object-fit: cover;" />
                </div>
              </template>
            </el-image>
          </div>

          <div class="product-details">
            <h4 class="product-name">{{ product.name }}</h4>
            <div class="product-stats">
              <div class="stat-item">
                <span class="stat-label">评分:</span>
                <span class="stat-value">{{ product.hotScore }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">兑换量:</span>
                <span class="stat-value">{{ product.exchangeCount || 0 }}</span>
              </div>
            </div>
            <div class="product-tags">
              <el-tag v-if="product.isNew" type="success" size="small">新品</el-tag>
              <el-tag v-if="product.isAutoHot" type="warning" size="small">
                {{ getHotLabel(product.hotTimeRange) }}
              </el-tag>
            </div>
          </div>

          <div class="product-price">
            <div class="price-item ly-price">
              <span class="price-value">{{ product.lyPrice }}</span>
              <span class="price-unit">光年币</span>
            </div>
            <div class="price-item rmb-price">
              <span class="price-value">¥{{ product.rmbPrice }}</span>
            </div>
          </div>
        </div>
      </div>

      <el-empty v-else description="暂无热门商品数据" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Trophy } from '@element-plus/icons-vue'
import { getDefaultProductImage } from '../utils/imageUtils'
import { hotProductApi } from '../api/hotProduct'

// 定义事件
const emit = defineEmits(['product-click'])

// 响应式数据
const loading = ref(false)
const selectedTimeRange = ref('all')
const hotProducts = ref([])

// 时间范围选项
const timeRangeOptions = [
  { value: 'all', label: '累积热门' },
  { value: '30d', label: '30天热门' },
  { value: '7d', label: '7天热门' },
  { value: '1d', label: '今日热门' }
]

// 获取排名样式类
const getRankClass = (index) => {
  if (index === 0) return 'rank-first'
  if (index === 1) return 'rank-second'
  if (index === 2) return 'rank-third'
  return 'rank-normal'
}

// 获取热门标签文本
const getHotLabel = (timeRange) => {
  const labels = {
    'all': '累积热门',
    '30d': '30天热门',
    '7d': '7天热门',
    '1d': '今日热门'
  }
  return labels[timeRange] || '热门'
}

// 获取商品图片
const getProductImage = (product) => {
  if (product.ProductImages && product.ProductImages.length > 0) {
    return product.ProductImages[0].imageUrl
  }
  if (product.imageUrl) {
    return product.imageUrl
  }
  return getDefaultProductImage('80x80')
}

// 加载热门商品
const loadHotProducts = async () => {
  try {
    loading.value = true
    const response = await hotProductApi.getHotProducts(selectedTimeRange.value, 10)
    hotProducts.value = response.data.products || []
  } catch (error) {
    console.error('加载热门商品失败:', error)
    ElMessage.error('加载热门商品失败')
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  loadHotProducts()
})
</script>

<style scoped>
.hot-product-ranking {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.ranking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.ranking-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-range-selector {
  width: 120px;
}

.ranking-content {
  padding: 20px;
  min-height: 200px;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  cursor: pointer;
}

.ranking-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #409eff;
}

.top-three {
  background: linear-gradient(135deg, #fff9e6 0%, #fff2cc 100%);
  border-color: #ffd700;
}

.rank-badge {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  margin-right: 16px;
  flex-shrink: 0;
}

.rank-first {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #b8860b;
}

.rank-second {
  background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
  color: #696969;
}

.rank-third {
  background: linear-gradient(135deg, #cd7f32, #daa520);
  color: #8b4513;
}

.rank-normal {
  background: #f5f7fa;
  color: #606266;
}

.product-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 16px;
  flex-shrink: 0;
}

.product-image .el-image {
  width: 100%;
  height: 100%;
}

.image-error {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  color: #c0c4cc;
}

.product-details {
  flex: 1;
  min-width: 0;
}

.product-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #409eff;
}

.product-tags {
  display: flex;
  gap: 6px;
}

.product-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  margin-left: 16px;
  flex-shrink: 0;
}

.price-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.ly-price .price-value {
  color: #409eff;
  font-weight: 600;
}

.rmb-price .price-value {
  color: #67c23a;
  font-weight: 600;
}

.price-unit {
  font-size: 12px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ranking-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .ranking-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .product-image {
    align-self: center;
  }

  .product-price {
    align-self: stretch;
    flex-direction: row;
    justify-content: space-between;
    margin-left: 0;
  }
}
</style>
