/**
 * 前端库存数据调试工具
 * 在浏览器控制台中使用
 */

// 全局调试对象
window.StockDebugger = {
  // 检查当前页面的库存数据
  checkCurrentData() {
    console.log('🔍 检查当前页面库存数据...')
    
    // 查找哪吒捏捏乐的数据
    const tableRows = document.querySelectorAll('table tbody tr')
    console.log(`📋 找到 ${tableRows.length} 个商品行`)
    
    tableRows.forEach((row, index) => {
      const nameCell = row.querySelector('td:nth-child(2)')
      if (nameCell && nameCell.textContent.includes('哪吒捏捏乐')) {
        console.log(`✅ 找到哪吒捏捏乐，行索引: ${index}`)
        
        // 获取职场库存信息
        const stockCells = row.querySelectorAll('.workplace-stock')
        stockCells.forEach(cell => {
          console.log(`📍 职场库存: ${cell.textContent}`)
        })
        
        // 获取所有单元格内容
        const cells = row.querySelectorAll('td')
        cells.forEach((cell, cellIndex) => {
          console.log(`📄 第${cellIndex + 1}列: ${cell.textContent.trim()}`)
        })
      }
    })
  },
  
  // 强制刷新页面数据
  async forceRefresh() {
    console.log('🔄 强制刷新页面数据...')
    
    // 清除所有缓存
    if ('caches' in window) {
      const cacheNames = await caches.keys()
      for (const cacheName of cacheNames) {
        await caches.delete(cacheName)
      }
      console.log('✅ 已清除浏览器缓存')
    }
    
    // 清除 localStorage 和 sessionStorage
    localStorage.clear()
    sessionStorage.clear()
    console.log('✅ 已清除本地存储')
    
    // 建议用户刷新页面
    console.log('💡 请按 Ctrl+F5 (Windows) 或 Cmd+Shift+R (Mac) 强制刷新页面')
  },
  
  // 检查网络请求
  monitorRequests() {
    console.log('🔍 开始监控网络请求...')
    
    const originalFetch = window.fetch
    window.fetch = function(...args) {
      console.log('🌐 Fetch 请求:', args[0])
      return originalFetch.apply(this, args).then(response => {
        console.log('📥 Fetch 响应:', response.status, response.url)
        return response
      })
    }
    
    console.log('✅ 网络请求监控已启用')
  },
  
  // 检查Vue组件数据
  checkVueData() {
    console.log('🔍 检查Vue组件数据...')
    
    // 尝试找到Vue应用实例
    const app = document.querySelector('#app')
    if (app && app.__vue__) {
      console.log('✅ 找到Vue实例')
      console.log('📊 Vue数据:', app.__vue__)
    } else {
      console.log('❌ 未找到Vue实例')
    }
  },
  
  // 模拟库存转移操作
  async simulateStockTransfer() {
    console.log('🔄 模拟库存转移操作...')
    
    try {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token')
      if (!token) {
        console.error('❌ 未找到认证token')
        return
      }
      
      const transferData = {
        productId: 15, // 哪吒捏捏乐的ID
        fromWorkplaceId: 1, // 北京
        toWorkplaceId: 14, // 武汉
        quantity: 1,
        reason: '调试测试转移'
      }
      
      console.log('📤 发送转移请求:', transferData)
      
      const response = await fetch('/api/stocks/transfer', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(transferData)
      })
      
      const result = await response.json()
      console.log('📥 转移响应:', result)
      
      if (result.success) {
        console.log('✅ 转移成功，请检查页面数据是否更新')
      } else {
        console.log('❌ 转移失败:', result.message)
      }
    } catch (error) {
      console.error('❌ 转移操作失败:', error)
    }
  }
}

console.log('🛠️ 库存调试工具已加载')
console.log('使用方法:')
console.log('- StockDebugger.checkCurrentData() // 检查当前数据')
console.log('- StockDebugger.forceRefresh() // 强制刷新')
console.log('- StockDebugger.monitorRequests() // 监控请求')
console.log('- StockDebugger.checkVueData() // 检查Vue数据')
console.log('- StockDebugger.simulateStockTransfer() // 模拟转移操作')
