# 光年小卖部 - 部署与环境配置指南

本文档提供如何配置开发环境和部署生产环境的详细步骤。

## 目录

- [环境要求](#环境要求)
- [开发环境配置](#开发环境配置)
- [生产环境配置](#生产环境配置)
- [服务器部署](#服务器部署)
- [数据库配置](#数据库配置)
- [Nginx配置](#nginx配置)
- [宝塔面板部署](#宝塔面板部署)
- [常见问题](#常见问题)

## 环境要求

### 系统要求
- Node.js >= 14.0.0
- npm >= 6.0.0
- MySQL >= 5.7 或 SQLite 3 (开发环境)
- Nginx (生产环境)

### 端口要求
- 前端开发服务器: 5173
- 后端API服务器: 3000
- MySQL: 3306
- Nginx: 80/443

## 开发环境配置

### 1. 克隆项目

```bash
git clone <项目仓库URL>
cd <项目目录>
```

### 2. 安装依赖

```bash
# 安装前端依赖
npm install

# 安装后端依赖
cd server && npm install
```

### 3. 配置环境变量

在项目根目录创建 `.env.development` 文件:

```
VITE_API_URL=http://localhost:3000/api
```

在 `server` 目录下创建 `.env` 文件:

```
# 服务器配置
PORT=3000
NODE_ENV=development

# 数据库配置
DB_NAME=feishu_mall
DB_USER=root
DB_PASSWORD=password
DB_HOST=localhost
DB_PORT=3306

# JWT配置
JWT_SECRET=development_secret_key
JWT_EXPIRES_IN=1d

# 上传目录配置
UPLOAD_DIR=uploads

# CORS配置
CORS_ORIGIN=http://localhost:5173
```

### 4. 初始化数据库

```bash
cd server
npm run init-db
```

### 5. 启动开发服务器

```bash
# 启动前端开发服务器
npm run dev

# 启动后端服务器(在另一个终端)
cd server && npm run dev
```

## 生产环境配置

### 1. 前端环境变量

在项目根目录下创建 `.env.production` 文件，添加以下内容：

```
# 生产环境API配置
VITE_API_URL=http://your-production-server:3000/api
```

注意：将 `your-production-server` 替换为您的实际服务器IP或域名。

### 2. 后端环境变量

在服务器端目录下创建 `.env` 文件，添加以下内容：

```
# 服务器配置
PORT=3000
NODE_ENV=production

# 数据库配置
DB_NAME=feishu_mall
DB_USER=your_db_user
DB_PASSWORD=your_secure_password
DB_HOST=localhost
DB_PORT=3306

# JWT配置
JWT_SECRET=your_very_secure_random_string
JWT_EXPIRES_IN=1d

# 上传目录配置
UPLOAD_DIR=uploads

# CORS配置
CORS_ORIGIN=http://your-production-server
```

注意：
- 将 `your_db_user` 和 `your_secure_password` 替换为您的数据库用户名和密码
- 将 `your_very_secure_random_string` 替换为一个随机生成的安全密钥
- 将 `your-production-server` 替换为您的实际服务器IP或域名

## 服务器部署

### 1. 准备工作

确保您的服务器满足以下条件：
- 云服务器已启动并可远程访问
- 已开放必要的端口（3000端口用于API服务器，80/443端口用于前端）
- 具有服务器的SSH访问权限
- 服务器安全组/防火墙已配置，允许访问必要端口

### 2. 连接到服务器

```bash
ssh user@your-server-ip
```

### 3. 安装必要的依赖项

```bash
# 更新系统
apt update
apt upgrade -y

# 安装Node.js和npm
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt install -y nodejs

# 安装MySQL
apt install -y mysql-server

# 安装nginx
apt install -y nginx

# 安装Git
apt install -y git

# 安装PM2用于进程管理
npm install -g pm2
```

### 4. 创建应用目录

```bash
mkdir -p /var/www/exchange-mall
cd /var/www/exchange-mall
```

### 5. 部署前端

```bash
# 克隆项目代码到服务器
git clone <项目仓库URL> /var/www/exchange-mall
cd /var/www/exchange-mall

# 安装依赖并构建项目
npm install
npm run build
```

构建完成后，前端资源将生成在 `dist` 目录中。

### 6. 部署后端

```bash
# 进入服务器端目录
cd /var/www/exchange-mall/server

# 安装后端依赖
npm install

# 使用PM2启动后端服务
pm2 start server.js --name feishu-mall-api

# 配置PM2自启动
pm2 startup
pm2 save
```

## 数据库配置

### 1. 登录MySQL并创建数据库

```bash
mysql -u root -p
```

在MySQL终端中执行：

```sql
CREATE DATABASE feishu_mall CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'exchange_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON feishu_mall.* TO 'exchange_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 2. 初始化数据库

```bash
cd /var/www/exchange-mall/server
NODE_ENV=production npm run init-db
```

## Nginx配置

### 1. 创建Nginx配置文件

```bash
vim /etc/nginx/sites-available/feishu-mall
```

### 2. 添加以下配置：

```nginx
server {
    listen 80;
    server_name your-server-ip-or-domain;

    # 前端静态资源
    location / {
        root /var/www/exchange-mall/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # 上传文件目录
    location /uploads {
        alias /var/www/exchange-mall/server/uploads;
    }
}
```

### 3. 启用站点配置

```bash
ln -s /etc/nginx/sites-available/feishu-mall /etc/nginx/sites-enabled/
nginx -t
systemctl restart nginx
```

## 宝塔面板部署

如果您使用宝塔面板管理服务器，可以按照以下步骤部署：

### 1. 前端部署

1. 在本地构建项目：`npm run build`
2. 登录宝塔面板，创建站点
3. 上传`dist`目录下的所有文件到站点根目录
4. 配置伪静态规则（解决Vue路由问题）：
```
location / {
  try_files $uri $uri/ /index.html;
}
```
5. 启用SSL（如需HTTPS）
6. 开启Gzip压缩以提高性能

### 2. 后端部署

1. 上传服务端代码到服务器
2. 安装Node.js环境（通过宝塔面板软件管理）
3. 安装项目依赖：`npm install`
4. 使用PM2管理Node应用（通过宝塔PM2管理器插件）
5. 配置反向代理，将API请求转发到Node服务

## 常见问题

### 1. API连接失败

检查以下几点：
- 确保服务器上的3000端口已开放
- 检查`.env`文件中的配置是否正确
- 查看PM2日志以获取更多信息：`pm2 logs feishu-mall-api`

### 2. 前端无法加载

检查以下几点：
- Nginx配置是否正确
- 前端的`.env.production`文件是否包含正确的API URL
- 检查Nginx错误日志：`cat /var/log/nginx/error.log`

### 3. 无法上传文件

检查以下几点：
- 上传目录权限是否正确
- Nginx配置中`/uploads`位置块是否正确配置
- 服务器磁盘空间是否充足

### 4. 数据库连接错误

检查以下几点：
- 数据库用户名和密码是否正确
- MySQL服务是否运行：`systemctl status mysql`
- 数据库权限是否正确设置

## 系统维护

### 1. 更新应用代码

```bash
cd /var/www/exchange-mall
git pull
npm install
npm run build

cd /var/www/exchange-mall/server
git pull
npm install
pm2 restart feishu-mall-api
```

### 2. 查看日志

```bash
# 查看API日志
pm2 logs feishu-mall-api

# 查看Nginx访问日志
tail -f /var/log/nginx/access.log

# 查看Nginx错误日志
tail -f /var/log/nginx/error.log
```

### 3. 数据库备份

```bash
# 备份数据库
mysqldump -u root -p feishu_mall > feishu_mall_backup_$(date +%Y%m%d).sql

# 恢复数据库
mysql -u root -p feishu_mall < feishu_mall_backup.sql
```

---

文档更新时间: 2024年7月
