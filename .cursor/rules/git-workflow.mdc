---
description:
globs:
alwaysApply: false
---
# 光年小卖部Git工作流规范

## 分支管理
- `main`分支：生产环境代码，受保护分支
- `develop`分支：开发环境代码
- 功能分支：`feature/功能名称`
- 修复分支：`bugfix/问题描述`
- 发布分支：`release/版本号`

## 提交规范
提交信息应使用约定式提交格式，清晰描述变更内容：

- `feat`: 新功能
- `fix`: 修复问题
- `docs`: 文档变更
- `style`: 代码格式变更
- `refactor`: 代码重构
- `perf`: 性能优化
- `test`: 测试相关
- `chore`: 构建过程或辅助工具变动

### 示例
```
feat(product): 添加商品排序功能
fix(order): 修复订单金额计算错误
docs(api): 更新API文档
```

## 工作流程

### 新功能开发
1. 从`develop`分支创建功能分支：`git checkout -b feature/功能名称 develop`
2. 开发功能并进行测试
3. 提交代码：`git commit -m "feat: 功能描述"`
4. 将功能分支合并回`develop`分支
5. 删除功能分支

### Bug修复
1. 从`develop`或`main`分支创建修复分支：`git checkout -b bugfix/问题描述 develop`
2. 修复bug并进行测试
3. 提交代码：`git commit -m "fix: 修复描述"`
4. 将修复分支合并回原分支
5. 根据需要，将修复合并到其他分支
6. 删除修复分支

## 代码审查
- 所有合并请求必须经过代码审查
- 代码审查应关注：
  - 代码质量
  - 安全问题
  - 性能问题
  - 是否符合规范

## 发布流程
1. 从`develop`分支创建发布分支：`git checkout -b release/版本号 develop`
2. 进行最终测试和bug修复
3. 将发布分支合并到`main`分支
4. 在`main`分支上创建版本标签：`git tag -a v版本号 -m "版本描述"`
5. 将发布分支合并回`develop`分支
6. 删除发布分支

## 冲突解决
- 避免长时间在功能分支上工作而不与主分支同步
- 定期将主分支合并到功能分支：`git merge develop`
- 解决冲突时，确保不丢失他人代码，必要时与相关开发人员协商
