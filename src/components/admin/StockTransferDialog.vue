<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`库存转移 - ${product?.name || ''}`"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="transfer-content" v-if="product">
      <!-- 商品信息 -->
      <div class="product-summary">
        <div class="product-info">
          <img
            :src="fixImageUrl(product.imageUrl)"
            :alt="product.name"
            class="product-image"
          />
          <div class="product-details">
            <h3 class="product-name">{{ product.name }}</h3>
            <p class="product-category">{{ product.Category?.name || '未分类' }}</p>
          </div>
        </div>
      </div>

      <!-- 转移表单 -->
      <el-form 
        :model="formData" 
        :rules="formRules"
        ref="formRef"
        label-width="100px"
      >
        <el-form-item label="源职场" prop="fromWorkplaceId">
          <el-select
            v-model="formData.fromWorkplaceId"
            placeholder="选择源职场"
            style="width: 100%"
            @change="handleFromWorkplaceChange"
          >
            <el-option
              v-for="stock in availableFromWorkplaces"
              :key="stock.workplaceId"
              :label="`${stock.workplaceName} (可用: ${stock.availableStock})`"
              :value="stock.workplaceId"
              :disabled="stock.availableStock === 0"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="目标职场" prop="toWorkplaceId">
          <el-select
            v-model="formData.toWorkplaceId"
            placeholder="选择目标职场"
            style="width: 100%"
          >
            <el-option
              v-for="workplace in availableToWorkplaces"
              :key="workplace.id"
              :label="`${workplace.name} (${workplace.code})`"
              :value="workplace.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="转移数量" prop="quantity">
          <div class="quantity-input">
            <el-input-number
              v-model="formData.quantity"
              :min="1"
              :max="Math.max(1, maxTransferQuantity)"
              :precision="0"
              style="width: 200px"
              :disabled="maxTransferQuantity <= 0"
            />
            <span class="quantity-info">
              最大可转移: {{ maxTransferQuantity }}
            </span>
          </div>
        </el-form-item>

        <el-form-item label="转移原因" prop="reason">
          <el-input
            v-model="formData.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入库存转移原因"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <!-- 转移预览 -->
      <div class="transfer-preview" v-if="formData.fromWorkplaceId && formData.toWorkplaceId && formData.quantity">
        <h4 class="preview-title">
          <i class="fas fa-eye"></i>
          转移预览
        </h4>
        
        <div class="preview-content">
          <div class="workplace-change">
            <div class="workplace-item source">
              <div class="workplace-header">
                <span class="workplace-name">{{ fromWorkplaceName }}</span>
                <span class="workplace-code">{{ fromWorkplaceCode }}</span>
              </div>
              <div class="stock-change">
                <span class="before">{{ fromWorkplaceCurrentStock }}</span>
                <span class="arrow">→</span>
                <span class="after">{{ fromWorkplaceAfterStock }}</span>
                <span class="change decrease">-{{ formData.quantity }}</span>
              </div>
            </div>

            <div class="transfer-arrow">
              <i class="fas fa-arrow-right"></i>
            </div>

            <div class="workplace-item target">
              <div class="workplace-header">
                <span class="workplace-name">{{ toWorkplaceName }}</span>
                <span class="workplace-code">{{ toWorkplaceCode }}</span>
              </div>
              <div class="stock-change">
                <span class="before">{{ toWorkplaceCurrentStock }}</span>
                <span class="arrow">→</span>
                <span class="after">{{ toWorkplaceAfterStock }}</span>
                <span class="change increase">+{{ formData.quantity }}</span>
              </div>
            </div>
          </div>

          <div class="transfer-summary">
            <div class="summary-item">
              <span class="label">转移数量:</span>
              <span class="value">{{ formData.quantity }}</span>
            </div>
            <div class="summary-item">
              <span class="label">总库存:</span>
              <span class="value">保持不变</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速转移选项 -->
      <div class="quick-transfer" v-if="formData.fromWorkplaceId">
        <h4 class="quick-title">快速转移</h4>
        <div class="quick-buttons">
          <el-button 
            size="small" 
            @click="setQuickQuantity(1)"
            :disabled="maxTransferQuantity < 1"
          >
            转移 1 个
          </el-button>
          <el-button 
            size="small" 
            @click="setQuickQuantity(Math.floor(maxTransferQuantity / 2))"
            :disabled="maxTransferQuantity < 2"
          >
            转移一半
          </el-button>
          <el-button 
            size="small" 
            @click="setQuickQuantity(maxTransferQuantity)"
            :disabled="maxTransferQuantity < 1"
          >
            全部转移
          </el-button>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm"
          :loading="loading"
          :disabled="!canTransfer"
        >
          确认转移
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { fixImageUrl } from '@/utils/imageUtils'

export default {
  name: 'StockTransferDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    product: {
      type: Object,
      default: null
    },
    workplaces: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:modelValue', 'confirm'],
  setup(props, { emit }) {
    const dialogVisible = ref(false)
    const loading = ref(false)
    const formRef = ref(null)

    const formData = ref({
      fromWorkplaceId: null,
      toWorkplaceId: null,
      quantity: 1,
      reason: ''
    })

    const formRules = {
      fromWorkplaceId: [
        { required: true, message: '请选择源职场', trigger: 'change' }
      ],
      toWorkplaceId: [
        { required: true, message: '请选择目标职场', trigger: 'change' }
      ],
      quantity: [
        { required: true, message: '请输入转移数量', trigger: 'blur' },
        { type: 'number', min: 1, message: '转移数量必须大于0', trigger: 'blur' }
      ],
      reason: [
        { required: true, message: '请输入转移原因', trigger: 'blur' }
      ]
    }

    // 计算属性
    const availableFromWorkplaces = computed(() => {
      if (!props.product?.workplaceStocks || !Array.isArray(props.product.workplaceStocks)) return []
      return props.product.workplaceStocks.filter(stock =>
        stock && typeof stock.availableStock === 'number' && stock.availableStock > 0
      )
    })

    const availableToWorkplaces = computed(() => {
      const fromWorkplaceId = formData.value.fromWorkplaceId
      if (!Array.isArray(props.workplaces)) return []
      return props.workplaces.filter(workplace =>
        workplace && workplace.isActive && workplace.id !== fromWorkplaceId
      )
    })

    const selectedFromWorkplace = computed(() => {
      if (!formData.value.fromWorkplaceId || !props.product?.workplaceStocks || !Array.isArray(props.product.workplaceStocks)) return null
      return props.product.workplaceStocks.find(stock =>
        stock && stock.workplaceId === formData.value.fromWorkplaceId
      )
    })

    const selectedToWorkplace = computed(() => {
      if (!formData.value.toWorkplaceId || !Array.isArray(props.workplaces)) return null
      return props.workplaces.find(workplace => workplace && workplace.id === formData.value.toWorkplaceId)
    })

    const maxTransferQuantity = computed(() => {
      const stock = selectedFromWorkplace.value?.availableStock
      return typeof stock === 'number' && stock >= 0 ? stock : 0
    })

    const canTransfer = computed(() => {
      return formData.value.fromWorkplaceId && 
             formData.value.toWorkplaceId && 
             formData.value.quantity > 0 && 
             formData.value.quantity <= maxTransferQuantity.value &&
             formData.value.reason.trim()
    })

    // 预览相关计算属性
    const fromWorkplaceName = computed(() => selectedFromWorkplace.value?.workplaceName || '')
    const fromWorkplaceCode = computed(() => selectedFromWorkplace.value?.workplaceCode || '')
    const fromWorkplaceCurrentStock = computed(() => selectedFromWorkplace.value?.availableStock || 0)
    const fromWorkplaceAfterStock = computed(() => fromWorkplaceCurrentStock.value - formData.value.quantity)

    const toWorkplaceName = computed(() => selectedToWorkplace.value?.name || '')
    const toWorkplaceCode = computed(() => selectedToWorkplace.value?.code || '')
    const toWorkplaceCurrentStock = computed(() => {
      if (!props.product?.workplaceStocks || !Array.isArray(props.product.workplaceStocks) || !formData.value.toWorkplaceId) return 0
      const stock = props.product.workplaceStocks.find(s => s && s.workplaceId === formData.value.toWorkplaceId)
      return stock?.availableStock || 0
    })
    const toWorkplaceAfterStock = computed(() => toWorkplaceCurrentStock.value + formData.value.quantity)

    // 方法
    const resetForm = () => {
      formData.value = {
        fromWorkplaceId: null,
        toWorkplaceId: null,
        quantity: 1,
        reason: ''
      }
    }

    const handleFromWorkplaceChange = () => {
      // 重置目标职场和数量
      formData.value.toWorkplaceId = null
      formData.value.quantity = 1
    }

    const setQuickQuantity = (quantity) => {
      formData.value.quantity = Math.max(1, Math.min(quantity, maxTransferQuantity.value))
    }

    const handleClose = () => {
      dialogVisible.value = false
      loading.value = false
      emit('update:modelValue', false)
    }

    // 重置loading状态的方法
    const resetLoading = () => {
      loading.value = false
    }

    const handleConfirm = async () => {
      console.log('🔍 开始转移确认流程...')

      if (!formRef.value) {
        console.error('❌ 表单引用不存在')
        ElMessage.error('表单初始化失败')
        return
      }

      console.log('📋 当前表单数据:', {
        fromWorkplaceId: formData.value.fromWorkplaceId,
        toWorkplaceId: formData.value.toWorkplaceId,
        quantity: formData.value.quantity,
        reason: formData.value.reason,
        productId: props.product?.id
      })

      console.log('📊 可用职场数据:', {
        availableFromWorkplaces: availableFromWorkplaces.value.length,
        availableToWorkplaces: availableToWorkplaces.value.length,
        maxTransferQuantity: maxTransferQuantity.value
      })

      try {
        console.log('🔍 开始表单验证...')
        await formRef.value.validate()
        console.log('✅ 表单验证通过')

        // 验证数据完整性
        if (!formData.value.fromWorkplaceId || !formData.value.toWorkplaceId) {
          console.error('❌ 职场选择不完整')
          ElMessage.error('请选择源职场和目标职场')
          return
        }

        if (!formData.value.quantity || formData.value.quantity <= 0) {
          console.error('❌ 转移数量无效')
          ElMessage.error('请输入有效的转移数量')
          return
        }

        if (formData.value.quantity > maxTransferQuantity.value) {
          console.error('❌ 转移数量超出限制')
          ElMessage.error(`转移数量不能超过${maxTransferQuantity.value}`)
          return
        }

        console.log('🚀 准备发送转移请求...')
        loading.value = true

        const transferData = {
          productId: props.product.id,
          fromWorkplaceId: formData.value.fromWorkplaceId,
          toWorkplaceId: formData.value.toWorkplaceId,
          quantity: formData.value.quantity,
          reason: formData.value.reason
        }

        console.log('📤 发送转移数据:', transferData)

        // 发送转移请求
        emit('confirm', transferData)

        console.log('✅ 转移请求已发送')

        // 注意：loading状态将由父组件在操作完成后重置
      } catch (error) {
        console.error('❌ 表单验证失败:', error)

        // 显示具体的验证错误
        if (error && typeof error === 'object') {
          const errorMessages = []
          for (const field in error) {
            if (error[field] && error[field].length > 0) {
              errorMessages.push(error[field][0].message)
            }
          }
          if (errorMessages.length > 0) {
            ElMessage.error(errorMessages.join('; '))
          } else {
            ElMessage.error('表单验证失败，请检查输入信息')
          }
        } else {
          ElMessage.error('表单验证失败，请检查输入信息')
        }

        loading.value = false
      }
    }

    // 监听器
    watch(() => props.modelValue, (newVal) => {
      dialogVisible.value = newVal
      if (newVal) {
        nextTick(() => {
          resetForm()
        })
      } else {
        // 对话框关闭时重置loading状态
        loading.value = false
      }
    })

    watch(dialogVisible, (newVal) => {
      if (!newVal) {
        emit('update:modelValue', false)
      }
    })

    // 监听最大转移数量变化，确保当前数量不超过最大值
    watch(maxTransferQuantity, (newMaxQuantity) => {
      if (newMaxQuantity <= 0) {
        // 如果没有可转移的库存，重置数量为1但禁用输入
        formData.value.quantity = 1
      } else if (formData.value.quantity > newMaxQuantity) {
        // 如果当前数量超过最大值，调整为最大值
        formData.value.quantity = newMaxQuantity
      }
    })

    return {
      dialogVisible,
      loading,
      formRef,
      formData,
      formRules,
      availableFromWorkplaces,
      availableToWorkplaces,
      maxTransferQuantity,
      canTransfer,
      fromWorkplaceName,
      fromWorkplaceCode,
      fromWorkplaceCurrentStock,
      fromWorkplaceAfterStock,
      toWorkplaceName,
      toWorkplaceCode,
      toWorkplaceCurrentStock,
      toWorkplaceAfterStock,
      handleFromWorkplaceChange,
      setQuickQuantity,
      handleClose,
      handleConfirm,
      resetLoading,
      fixImageUrl
    }
  }
}
</script>

<style scoped>
.transfer-content {
  max-height: 70vh;
  overflow-y: auto;
}

/* 商品信息 */
.product-summary {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.product-image {
  width: 64px;
  height: 64px;
  border-radius: 6px;
  object-fit: cover;
  border: 1px solid #e4e7ed;
}

.product-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.product-category {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

/* 数量输入 */
.quantity-input {
  display: flex;
  align-items: center;
  gap: 12px;
}

.quantity-info {
  font-size: 12px;
  color: #909399;
}

/* 转移预览 */
.transfer-preview {
  margin: 24px 0;
  padding: 16px;
  background: #f0f9ff;
  border: 1px solid #e1f5fe;
  border-radius: 6px;
}

.preview-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-title i {
  color: #409eff;
}

.workplace-change {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.workplace-item {
  flex: 1;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.workplace-item.source {
  border-color: #f56c6c;
}

.workplace-item.target {
  border-color: #67c23a;
}

.workplace-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.workplace-name {
  font-weight: 500;
  color: #303133;
}

.workplace-code {
  font-size: 12px;
  color: #909399;
  background: #f0f2f5;
  padding: 2px 6px;
  border-radius: 3px;
}

.stock-change {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.stock-change .before,
.stock-change .after {
  font-weight: 600;
  color: #303133;
}

.stock-change .arrow {
  color: #909399;
  font-weight: bold;
}

.stock-change .change {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 500;
}

.stock-change .change.increase {
  background: #f0f9ff;
  color: #67c23a;
}

.stock-change .change.decrease {
  background: #fef0f0;
  color: #f56c6c;
}

.transfer-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  font-size: 16px;
}

.transfer-summary {
  display: flex;
  gap: 24px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-item .label {
  color: #606266;
  font-size: 14px;
}

.summary-item .value {
  font-weight: 600;
  color: #303133;
}

/* 快速转移 */
.quick-transfer {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.quick-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.quick-buttons {
  display: flex;
  gap: 8px;
}

/* 响应式 */
@media (max-width: 768px) {
  .workplace-change {
    flex-direction: column;
  }
  
  .transfer-arrow {
    transform: rotate(90deg);
  }
  
  .transfer-summary {
    flex-direction: column;
    gap: 8px;
  }
  
  .quick-buttons {
    flex-direction: column;
  }
}
</style>
