<template>
  <div class="payment-settings">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <el-icon class="title-icon"><Money /></el-icon>
            支付设置
          </h1>
          <p class="page-description">管理系统支付宝收款码配置</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 支付配置卡片 -->
    <div class="payment-config">
      <el-card class="config-card">
        <template #header>
          <div class="card-header">
            <h3>支付宝收款码配置</h3>
            <el-tag :type="paymentConfigured ? 'success' : 'warning'">
              {{ paymentConfigured ? '已配置' : '未配置' }}
            </el-tag>
          </div>
        </template>

        <div class="config-content">
          <div class="upload-section">
            <div class="upload-area">
              <el-upload
                class="qr-uploader"
                :action="uploadUrl"
                :show-file-list="false"
                :before-upload="beforeUpload"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
                accept="image/*"
                drag
              >
                <div v-if="!qrCodeUrl" class="upload-placeholder">
                  <el-icon class="upload-icon"><Plus /></el-icon>
                  <div class="upload-text">
                    <p>点击或拖拽上传收款码</p>
                    <p class="upload-hint">支持 JPG、PNG 格式，文件大小不超过 2MB</p>
                  </div>
                </div>
                <div v-else class="qr-preview">
                  <img :src="qrCodeUrl" alt="收款码" />
                  <div class="preview-overlay">
                    <el-icon><Edit /></el-icon>
                    <span>点击更换</span>
                  </div>
                </div>
              </el-upload>
            </div>

            <div class="config-form">
              <el-form :model="paymentForm" label-width="120px">
                <el-form-item label="收款账户">
                  <el-input
                    v-model="paymentForm.account"
                    placeholder="请输入支付宝账户"
                  />
                </el-form-item>
                <el-form-item label="收款人姓名">
                  <el-input
                    v-model="paymentForm.name"
                    placeholder="请输入收款人姓名"
                  />
                </el-form-item>
                <el-form-item label="备注信息">
                  <el-input
                    v-model="paymentForm.remark"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入备注信息（可选）"
                  />
                </el-form-item>
                <el-form-item label="启用状态">
                  <el-switch
                    v-model="paymentForm.enabled"
                    active-text="启用"
                    inactive-text="禁用"
                  />
                </el-form-item>
              </el-form>

              <div class="form-actions">
                <el-button type="primary" @click="savePaymentConfig" :loading="saving">
                  <el-icon><Check /></el-icon>
                  保存配置
                </el-button>
                <el-button @click="resetForm">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
                <el-button type="danger" @click="deleteConfig" v-if="paymentConfigured">
                  <el-icon><Delete /></el-icon>
                  删除配置
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 使用说明 -->
    <div class="usage-guide">
      <el-card>
        <template #header>
          <h3>使用说明</h3>
        </template>
        <div class="guide-content">
          <el-steps direction="vertical" :active="3">
            <el-step title="上传收款码" description="上传您的支付宝收款二维码图片" />
            <el-step title="填写信息" description="填写收款账户和收款人姓名等信息" />
            <el-step title="启用配置" description="开启支付功能，用户可以通过扫码支付" />
            <el-step title="完成设置" description="配置完成，系统将使用此收款码处理支付" />
          </el-steps>
        </div>
      </el-card>
    </div>

    <!-- 支付统计 -->
    <div class="payment-stats" v-if="paymentConfigured">
      <el-card>
        <template #header>
          <h3>支付统计</h3>
        </template>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ stats.totalAmount }}</div>
            <div class="stat-label">总收款金额</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.totalOrders }}</div>
            <div class="stat-label">总订单数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.todayAmount }}</div>
            <div class="stat-label">今日收款</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.todayOrders }}</div>
            <div class="stat-label">今日订单</div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Money, Refresh, Plus, Edit, Check, Delete
} from '@element-plus/icons-vue';

// 响应式数据
const loading = ref(false);
const saving = ref(false);
const paymentConfigured = ref(false);
const qrCodeUrl = ref('');
const uploadUrl = '/api/upload/qrcode';

const paymentForm = reactive({
  account: '',
  name: '',
  remark: '',
  enabled: true
});

const stats = reactive({
  totalAmount: '¥0.00',
  totalOrders: 0,
  todayAmount: '¥0.00',
  todayOrders: 0
});

// 方法
const refreshData = async () => {
  loading.value = true;
  try {
    await loadPaymentConfig();
    await loadPaymentStats();
    ElMessage.success('数据刷新成功');
  } catch (error) {
    ElMessage.error('数据刷新失败');
  } finally {
    loading.value = false;
  }
};

const loadPaymentConfig = async () => {
  try {
    // 模拟API调用
    const mockConfig = {
      account: '<EMAIL>',
      name: '张三',
      remark: '商城收款码',
      enabled: true,
      qrCodeUrl: ''
    };
    
    Object.assign(paymentForm, mockConfig);
    qrCodeUrl.value = mockConfig.qrCodeUrl;
    paymentConfigured.value = !!mockConfig.account;
  } catch (error) {
    console.error('加载支付配置失败:', error);
  }
};

const loadPaymentStats = async () => {
  try {
    // 模拟API调用
    Object.assign(stats, {
      totalAmount: '¥12,345.67',
      totalOrders: 156,
      todayAmount: '¥234.56',
      todayOrders: 8
    });
  } catch (error) {
    console.error('加载支付统计失败:', error);
  }
};

const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/');
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!');
    return false;
  }
  return true;
};

const handleUploadSuccess = (response) => {
  qrCodeUrl.value = response.url;
  ElMessage.success('收款码上传成功');
};

const handleUploadError = () => {
  ElMessage.error('收款码上传失败');
};

const savePaymentConfig = async () => {
  if (!paymentForm.account || !paymentForm.name) {
    ElMessage.warning('请填写完整的收款信息');
    return;
  }

  saving.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    paymentConfigured.value = true;
    ElMessage.success('支付配置保存成功');
  } catch (error) {
    ElMessage.error('配置保存失败');
  } finally {
    saving.value = false;
  }
};

const resetForm = () => {
  Object.assign(paymentForm, {
    account: '',
    name: '',
    remark: '',
    enabled: true
  });
  qrCodeUrl.value = '';
};

const deleteConfig = async () => {
  try {
    await ElMessageBox.confirm('确定要删除支付配置吗？', '确认删除', {
      type: 'warning'
    });
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    resetForm();
    paymentConfigured.value = false;
    ElMessage.success('配置删除成功');
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};

// 生命周期
onMounted(() => {
  loadPaymentConfig();
  loadPaymentStats();
});
</script>

<style scoped>
.payment-settings {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  color: #3b82f6;
}

.page-description {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

.payment-config {
  margin-bottom: 24px;
}

.config-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.config-content {
  padding: 0;
}

.upload-section {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 32px;
}

.qr-uploader {
  width: 100%;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  background: #f9fafb;
  transition: all 0.2s;
}

.upload-placeholder:hover {
  border-color: #3b82f6;
  background: #eff6ff;
}

.upload-icon {
  font-size: 48px;
  color: #9ca3af;
  margin-bottom: 16px;
}

.upload-text p {
  margin: 4px 0;
  color: #6b7280;
}

.upload-hint {
  font-size: 12px;
  color: #9ca3af;
}

.qr-preview {
  position: relative;
  width: 200px;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
  margin: 0 auto;
}

.qr-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity 0.2s;
}

.qr-preview:hover .preview-overlay {
  opacity: 1;
}

.form-actions {
  margin-top: 24px;
  display: flex;
  gap: 12px;
}

.usage-guide {
  margin-bottom: 24px;
}

.guide-content {
  padding: 16px 0;
}

.payment-stats .stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  color: #6b7280;
  font-size: 14px;
}

@media (max-width: 768px) {
  .upload-section {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
