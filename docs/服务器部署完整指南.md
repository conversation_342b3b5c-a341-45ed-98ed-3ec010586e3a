# 光年小卖部 - 服务器部署完整指南

本文档提供从零开始的服务器部署详细步骤，包含环境搭建、项目部署、配置优化等完整流程。

## 目录

- [服务器环境搭建](#服务器环境搭建)
- [项目部署流程](#项目部署流程)
- [配置文件修改](#配置文件修改)
- [Nginx配置](#nginx配置)
- [部署验证和测试](#部署验证和测试)
- [常见问题和解决方案](#常见问题和解决方案)

## 服务器信息

- **服务器IP**: **************
- **用户**: root
- **密码**: Aa@123456
- **部署目录**: /www/wwwroot/workyy
- **前端端口**: 80 (通过Nginx代理)
- **后端端口**: 3000
- **数据库**: MySQL (端口3306)

## 服务器环境搭建

### 1. 系统更新和基础工具安装

```bash
# 连接到服务器
ssh root@**************

# 更新系统包
yum update -y

# 安装基础工具
yum install -y wget curl git vim unzip
```

### 2. Node.js环境安装

```bash
# 安装Node.js 18.x LTS版本
curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
yum install -y nodejs

# 验证安装
node --version
npm --version

# 配置npm镜像源（可选，提高下载速度）
npm config set registry https://registry.npmmirror.com
```

### 3. MySQL数据库安装和配置

```bash
# 安装MySQL 8.0
yum install -y mysql-server

# 启动MySQL服务
systemctl start mysqld
systemctl enable mysqld

# 获取临时密码
grep 'temporary password' /var/log/mysqld.log

# 安全配置MySQL
mysql_secure_installation

# 登录MySQL并创建数据库
mysql -u root -p

# 在MySQL中执行以下命令
CREATE DATABASE feishu_mall CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'feishu_user'@'localhost' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON feishu_mall.* TO 'feishu_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 4. PM2进程管理器安装

```bash
# 全局安装PM2
npm install -g pm2

# 验证安装
pm2 --version

# 配置PM2开机自启动
pm2 startup
# 按照提示执行返回的命令
```

### 5. Nginx安装和配置

```bash
# 安装Nginx
yum install -y nginx

# 启动Nginx服务
systemctl start nginx
systemctl enable nginx

# 检查Nginx状态
systemctl status nginx
```

## 项目部署流程

### 1. 创建部署目录

```bash
# 创建项目目录
mkdir -p /www/wwwroot/workyy
cd /www/wwwroot/workyy

# 创建日志目录
mkdir -p logs
```

### 2. 从Gitee仓库克隆项目

```bash
# 克隆项目（使用feat/reset分支）
git clone -b feat/reset https://gitee.com/your-username/workyy.git .

# 验证分支
git branch -a
git status
```

### 3. 安装项目依赖

```bash
# 安装前端依赖
npm install

# 安装后端依赖（排除nodejieba以避免编译问题）
cd server
npm install --omit=optional

# 如果nodejieba已安装，手动移除
npm uninstall nodejieba

# 返回项目根目录
cd ..
```

### 4. 构建前端项目

```bash
# 构建生产版本
npm run build

# 验证构建结果
ls -la dist/
```

## 配置文件修改

### 1. 后端环境配置

创建生产环境配置文件：

```bash
cd server
cp .env.example .env.production
```

编辑 `.env.production` 文件：

```bash
vim .env.production
```

配置内容：

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=feishu_mall

# 应用配置
NODE_ENV=production
PORT=3000
SERVER_URL=https://store-api.chongyangqisi.com

# JWT配置
JWT_SECRET=your_very_secure_random_string_here
JWT_EXPIRES_IN=1h
JWT_LONG_EXPIRES_IN=30d

# 飞书应用配置
FEISHU_APP_ID=cli_a66b3b2dcab8d013
FEISHU_APP_SECRET=5Fa8aatAGZ2Dv6K5VZhAWhbhjzE4lT2r
FEISHU_REDIRECT_URI=https://store-api.chongyangqisi.com/api/feishu/callback

# 飞书机器人配置
FEISHU_BOT_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/e6cff700-4172-4039-a700-43c8f43765fc

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880

# CORS配置
CORS_ORIGIN=http://**************,http://**************:80
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS

# 日志配置
LOG_LEVEL=info
```

### 2. 前端环境配置

创建前端生产环境配置：

```bash
cd /www/wwwroot/workyy
vim .env.production
```

配置内容：

```env
# 生产环境API配置
VITE_API_URL=http://**************/api

# 应用配置
VITE_APP_TITLE=光年小卖部
VITE_APP_BASE_URL=/
VITE_NODE_ENV=production
VITE_DEV_TOOLS=false
VITE_DEBUG=false
```

### 3. 数据库初始化

```bash
cd server

# 设置环境变量
export NODE_ENV=production

# 初始化数据库
npm run init-db

# 或者运行迁移
npm run migrate
```

### 4. PM2配置和启动

创建PM2配置文件：

```bash
cd /www/wwwroot/workyy/server
vim ecosystem.config.js
```

配置内容：

```javascript
module.exports = {
  apps: [{
    name: 'feishu-mall-api',
    script: 'server.js',
    instances: 1,
    exec_mode: 'fork',
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    log_file: '../logs/combined.log',
    out_file: '../logs/out.log',
    error_file: '../logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm Z',
    restart_delay: 1000,
    max_restarts: 5,
    min_uptime: '10s',
    max_memory_restart: '1G'
  }]
};
```

启动PM2应用：

```bash
# 使用配置文件启动
pm2 start ecosystem.config.js --env production

# 或者直接启动
pm2 start server.js --name feishu-mall-api --env production

# 保存PM2配置
pm2 save

# 设置开机自启动
pm2 startup
```

## Nginx配置

### 1. 创建Nginx配置文件

```bash
# 备份默认配置
cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup

# 创建站点配置文件
vim /etc/nginx/conf.d/workyy.conf
```

### 2. 完整的Nginx配置

```nginx
# 光年小卖部 - 生产环境Nginx配置
# 服务器IP: **************
# 前端端口: 80 (通过nginx代理)
# 后端端口: 3000
# 部署目录: /www/wwwroot/workyy

server {
    listen 80;
    server_name **************;

    # 项目根目录
    root /www/wwwroot/workyy;
    index index.html index.htm;

    # 设置客户端请求体大小限制（用于文件上传）
    client_max_body_size 50M;

    # 设置超时时间
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;

    # 缓冲区设置
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;
    proxy_busy_buffers_size 8k;

    # Gzip压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml
        application/x-font-ttf
        application/vnd.ms-fontobject
        font/opentype;

    # 前端静态文件服务
    location / {
        root /www/wwwroot/workyy/dist;
        try_files $uri $uri/ /index.html;
        index index.html;

        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
        }
    }

    # API代理到后端服务
    location /api/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # CORS配置
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
        add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;

        # 处理OPTIONS预检请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }

    # 上传文件服务
    location /uploads/ {
        alias /www/wwwroot/workyy/server/uploads/;
        expires 1y;
        add_header Cache-Control "public";
        access_log off;
    }

    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # 安全配置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # 隐藏Nginx版本信息
    server_tokens off;

    # 日志配置
    access_log /www/wwwroot/workyy/logs/nginx_access.log;
    error_log /www/wwwroot/workyy/logs/nginx_error.log;

    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;

    location = /50x.html {
        root /www/wwwroot/workyy/dist;
    }
}
```

### 3. SSL证书配置（可选）

如果需要HTTPS，可以添加以下配置：

```nginx
# HTTPS配置（如果有SSL证书）
server {
    listen 443 ssl http2;
    server_name **************;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 其他配置与HTTP相同...
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name **************;
    return 301 https://$server_name$request_uri;
}
```

### 4. 测试和重启Nginx

```bash
# 测试Nginx配置
nginx -t

# 如果配置正确，重启Nginx
systemctl restart nginx

# 检查Nginx状态
systemctl status nginx
```

## 部署验证和测试

### 1. 服务启动验证

```bash
# 检查PM2应用状态
pm2 list
pm2 logs feishu-mall-api --lines 20

# 检查端口占用
netstat -tlnp | grep -E ':(80|3000|3306)'

# 检查Nginx状态
systemctl status nginx

# 检查MySQL状态
systemctl status mysqld
```

### 2. 接口连通性测试

```bash
# 测试后端API健康检查
curl http://localhost:3000/api/health

# 测试通过Nginx代理的API
curl http://**************/api/health

# 测试前端页面
curl -I http://**************/
```

### 3. 前后端数据交互测试

```bash
# 测试用户登录接口
curl -X POST http://**************/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# 测试文件上传
curl -X POST http://**************/api/upload \
  -F "file=@/path/to/test/image.jpg"
```

### 4. 数据库连接测试

```bash
# 进入MySQL检查数据库
mysql -u root -p feishu_mall

# 查看表结构
SHOW TABLES;
DESCRIBE users;
```

## 常见问题和解决方案

### 1. 端口占用问题

```bash
# 查看端口占用
netstat -tlnp | grep :3000

# 杀死占用进程
kill -9 <PID>

# 或者使用fuser
fuser -k 3000/tcp
```

### 2. 权限问题

```bash
# 设置项目目录权限
chown -R root:root /www/wwwroot/workyy
chmod -R 755 /www/wwwroot/workyy

# 设置上传目录权限
chmod -R 777 /www/wwwroot/workyy/server/uploads

# 设置日志目录权限
chmod -R 755 /www/wwwroot/workyy/logs
```

### 3. 依赖安装失败处理

```bash
# 清除npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install

# 如果nodejieba编译失败
npm uninstall nodejieba
npm install --omit=optional
```

### 4. PM2应用异常

```bash
# 查看PM2日志
pm2 logs feishu-mall-api --lines 50

# 重启应用
pm2 restart feishu-mall-api

# 删除并重新启动
pm2 delete feishu-mall-api
pm2 start server.js --name feishu-mall-api
```

### 5. Nginx配置错误

```bash
# 检查Nginx配置语法
nginx -t

# 查看Nginx错误日志
tail -f /var/log/nginx/error.log

# 重新加载配置
nginx -s reload
```

### 6. 数据库连接问题

```bash
# 检查MySQL服务状态
systemctl status mysqld

# 重启MySQL服务
systemctl restart mysqld

# 检查数据库用户权限
mysql -u root -p
SHOW GRANTS FOR 'feishu_user'@'localhost';
```

### 7. 防火墙配置

```bash
# 检查防火墙状态
firewall-cmd --state

# 开放必要端口
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=3000/tcp
firewall-cmd --reload

# 查看开放的端口
firewall-cmd --list-ports
```

## 部署后维护

### 1. 日常监控命令

```bash
# 监控PM2应用
pm2 monit

# 查看系统资源
htop
df -h
free -h

# 查看应用日志
tail -f /www/wwwroot/workyy/logs/combined.log
```

### 2. 定期维护任务

```bash
# 清理PM2日志
pm2 flush

# 清理系统日志
journalctl --vacuum-time=7d

# 更新系统包
yum update -y
```

### 3. 备份策略

```bash
# 数据库备份
mysqldump -u root -p feishu_mall > backup_$(date +%Y%m%d).sql

# 项目文件备份
tar -czf workyy_backup_$(date +%Y%m%d).tar.gz /www/wwwroot/workyy
```

## 快速重启脚本

项目提供了便捷的重启脚本：

```bash
# 完整重启（包含备份和验证）
cd /www/wwwroot/workyy
./restart-production.sh

# 快速重启（日常维护）
./restart-production-simple.sh
```

## 总结

本部署指南涵盖了从服务器环境搭建到项目完整部署的所有步骤。按照本指南操作，您可以成功部署光年小卖部项目到生产环境。

**重要提醒**：
- 在生产环境操作前请务必备份数据
- 建议在维护时间窗口进行部署操作
- 部署完成后进行全面的功能测试
- 定期监控服务器性能和应用状态

如遇到问题，请参考常见问题解决方案或查看相关日志文件进行排查。
