#!/bin/bash

# 部署重启脚本到生产服务器
# 将本地的重启脚本上传到生产服务器并设置权限

# 设置颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# 服务器配置
PROD_SERVER="**************"
PROD_USER="root"
PROJECT_DIR="/www/wwwroot/workyy"

# 脚本文件列表
SCRIPTS=(
    "restart-production.sh"
    "restart-production-simple.sh"
)

echo -e "${YELLOW}===== 部署重启脚本到生产服务器 =====${NC}"
echo -e "${BLUE}目标服务器: $PROD_SERVER${NC}"
echo -e "${BLUE}项目目录: $PROJECT_DIR${NC}"

# 检查本地脚本文件是否存在
echo -e "${BLUE}检查本地脚本文件...${NC}"
for script in "${SCRIPTS[@]}"; do
    if [ ! -f "$script" ]; then
        echo -e "${RED}错误: 本地脚本文件 $script 不存在${NC}"
        exit 1
    fi
    echo -e "${GREEN}✓ 找到脚本: $script${NC}"
done

# 检查scp命令是否可用
if ! command -v scp &> /dev/null; then
    echo -e "${RED}错误: scp命令不可用，请安装OpenSSH客户端${NC}"
    exit 1
fi

# 上传脚本文件
echo -e "${BLUE}上传脚本文件到服务器...${NC}"
for script in "${SCRIPTS[@]}"; do
    echo -e "${YELLOW}上传 $script...${NC}"
    if scp "$script" "$PROD_USER@$PROD_SERVER:$PROJECT_DIR/"; then
        echo -e "${GREEN}✓ $script 上传成功${NC}"
    else
        echo -e "${RED}✗ $script 上传失败${NC}"
        exit 1
    fi
done

# 设置脚本执行权限
echo -e "${BLUE}设置脚本执行权限...${NC}"
CHMOD_CMD="cd $PROJECT_DIR"
for script in "${SCRIPTS[@]}"; do
    CHMOD_CMD="$CHMOD_CMD && chmod +x $script"
done

if ssh "$PROD_USER@$PROD_SERVER" "$CHMOD_CMD"; then
    echo -e "${GREEN}✓ 脚本权限设置成功${NC}"
else
    echo -e "${RED}✗ 脚本权限设置失败${NC}"
    exit 1
fi

# 验证部署结果
echo -e "${BLUE}验证部署结果...${NC}"
VERIFY_CMD="cd $PROJECT_DIR && ls -la restart-production*.sh"
if ssh "$PROD_USER@$PROD_SERVER" "$VERIFY_CMD"; then
    echo -e "${GREEN}✓ 脚本部署验证成功${NC}"
else
    echo -e "${RED}✗ 脚本部署验证失败${NC}"
    exit 1
fi

# 显示使用说明
echo -e "\n${GREEN}===== 部署完成 =====${NC}"
echo -e "${BLUE}脚本已成功部署到服务器${NC}"
echo -e "\n${YELLOW}使用方法:${NC}"
echo -e "${BLUE}1. 连接到服务器:${NC}"
echo -e "   ssh $PROD_USER@$PROD_SERVER"
echo -e "\n${BLUE}2. 进入项目目录:${NC}"
echo -e "   cd $PROJECT_DIR"
echo -e "\n${BLUE}3. 执行重启脚本:${NC}"
echo -e "   ${GREEN}快速重启:${NC} ./restart-production-simple.sh"
echo -e "   ${GREEN}完整重启:${NC} ./restart-production.sh"

echo -e "\n${YELLOW}注意事项:${NC}"
echo -e "- 脚本需要在服务器上以root权限执行"
echo -e "- 建议先使用简化版脚本进行日常重启"
echo -e "- 重要更新时使用完整版脚本"
echo -e "- 详细使用说明请查看: docs/生产环境重启脚本使用说明.md"

echo -e "\n${GREEN}🎉 重启脚本部署成功完成！${NC}"
