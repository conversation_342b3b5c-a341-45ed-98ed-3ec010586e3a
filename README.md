# 洋葱学园光年小卖部商品库

[![Vue.js](https://img.shields.io/badge/Vue.js-3.3.4-4FC08D?style=flat&logo=vue.js)](https://vuejs.org/)
[![Express.js](https://img.shields.io/badge/Express.js-4.18.2-000000?style=flat&logo=express)](https://expressjs.com/)
[![MySQL](https://img.shields.io/badge/MySQL-8.0+-4479A1?style=flat&logo=mysql&logoColor=white)](https://www.mysql.com/)
[![Element Plus](https://img.shields.io/badge/Element%20Plus-2.3.8-409EFF?style=flat)](https://element-plus.org/)
[![Node.js](https://img.shields.io/badge/Node.js-14+-339933?style=flat&logo=node.js)](https://nodejs.org/)

> **企业级电商福利平台** | **最后更新**: 2025年1月 | **项目状态**: 生产就绪

## 🚀 快速开始

```bash
# 1. 克隆项目
git clone <repository-url>
cd workyy

# 2. 安装依赖
npm install
cd server && npm install && cd ..

# 3. 配置环境
cp server/.env.example server/.env
# 编辑 server/.env 配置数据库和飞书信息

# 4. 初始化数据库
cd server && npm run init-db && cd ..

# 5. 启动项目
./restart.sh
```

**默认管理员账户**:
- 用户名: 超管
- 邮箱: <EMAIL>
- 密码: 654321

**数据库配置**:
- 数据库名: feishu_mall
- 密码: password

## 📋 目录

- [快速开始](#-快速开始)
- [项目概述](#-项目概述)
- [技术架构](#-技术架构)
- [功能特性](#-功能特性)
- [项目结构](#-项目结构)
- [安装和运行](#-安装和运行)
- [数据库设计](#-数据库设计)
- [API文档](#-api文档)
- [飞书集成](#-飞书集成)
- [开发指南](#-开发指南)
- [部署指南](#-部署指南)
- [常见问题](#-常见问题)
- [贡献指南](#-贡献指南)
- [更新日志](#-更新日志)

## 🚀 项目概述

洋葱学园光年小卖部商品库是一个专为企业内部员工福利兑换设计的现代化电商平台。系统采用前后端分离架构，支持光年币（虚拟货币）和人民币双支付模式，深度集成飞书生态系统，提供完整的商品管理、订单处理、用户管理和数据分析功能。

### 🎯 核心价值
- **员工福利管理**: 为企业提供完整的员工福利商品兑换解决方案
- **双支付体系**: 支持光年币虚拟货币和人民币两种支付方式
- **飞书生态集成**: OAuth单点登录、部门信息同步、群机器人通知
- **数据驱动决策**: 实时统计分析、可视化图表、定时报告推送
- **移动端友好**: 响应式设计，完美适配PC端和移动设备

### 🏆 项目亮点
- ✅ **智能订单系统**: 基于日期的唯一编号生成（GNB-/RMB-前缀）、并发安全、完整状态流转
- ✅ **价格快照机制**: 订单创建时保存价格快照，历史订单价格不受商品调价影响
- ✅ **热门商品智能识别**: 基于兑换量的自动热门商品识别，支持多时间维度配置和实时更新
- ✅ **批量操作支持**: 商品批量调价、库存批量更新、订单批量导出
- ✅ **流式数据处理**: 大数据量导出采用流式处理，避免内存溢出
- ✅ **库存智能管理**: 自动扣减、低库存告警、变更日志追踪
- ✅ **飞书深度集成**: OAuth登录、部门同步、群通知、定时报告
- ✅ **数据可视化**: ECharts图表、实时统计、趋势分析
- ✅ **权限管理**: 基于角色的访问控制、细粒度功能权限
- ✅ **移动端优化**: 响应式布局、触控友好、离线缓存

## 🏗️ 技术架构

### 前端技术栈
- **框架**: Vue 3.3.4 - 渐进式JavaScript框架
- **构建工具**: Vite 4.4.0 - 快速的前端构建工具
- **UI组件库**: Element Plus 2.3.8 - 基于Vue 3的组件库
- **状态管理**: Pinia 3.0.2 - Vue的状态管理库
- **路由管理**: Vue Router 4.5.0 - Vue官方路由管理器
- **HTTP客户端**: Axios 1.8.4 - Promise based HTTP client
- **数据可视化**: ECharts 5.6.0 + Vue-ECharts 6.6.8
- **富文本编辑**: WangEditor 5.1.23 - 轻量级富文本编辑器
- **样式预处理**: Sass 1.63.6 - CSS预处理器
- **代码规范**: ESLint 9.28.0 - JavaScript代码检查工具

### 后端技术栈
- **运行环境**: Node.js 14+ - JavaScript运行时
- **Web框架**: Express.js 4.18.2 - 快速、极简的Web框架
- **数据库**: MySQL 8.0+ - 关系型数据库
- **ORM框架**: Sequelize 6.33.0 - Promise based ORM
- **身份验证**: JWT (jsonwebtoken 9.0.2) - JSON Web Token
- **密码加密**: bcryptjs 2.4.3 - 密码哈希库
- **文件上传**: Multer 1.4.5 - 文件上传中间件
- **定时任务**: node-cron 4.1.0 + node-schedule 2.1.1
- **数据处理**: CSV-Parser 3.2.0 + ExcelJS 4.4.0
- **日志记录**: Morgan 1.10.0 - HTTP请求日志中间件
- **API文档**: Swagger - 自动生成API文档

### 第三方集成
- **飞书开放平台**: OAuth登录、用户信息同步、群机器人通知
- **文件存储**: 本地文件系统（支持扩展云存储）
- **数据库**: MySQL（开发环境支持SQLite）

## 🌟 功能特性

### 👥 用户管理系统
- **多元化登录**: 支持传统用户名密码登录和飞书OAuth单点登录
- **角色权限**: 管理员/普通用户角色区分，基于角色的访问控制（RBAC）
- **飞书集成**: 自动同步用户部门路径、企业邮箱、头像等信息
- **账户管理**: 用户启用/禁用、密码修改、个人信息编辑
- **登录安全**: 会话管理、IP记录、最后登录时间追踪

### �️ 系统设置模块
- **通知配置管理**: 支持多种类型的飞书群通知开关和参数配置
- **智能发送时间控制**: 配置智能发送时间调度，优化用户接收体验
- **高级诊断工具**: 系统健康检查、连接测试和消息发送分析工具
- **错误边界处理**: 完善的前端错误捕获和恢复机制
- **组件生命周期优化**: 修复了组件渲染问题，确保页面稳定性

### �🛍️ 商品管理系统
- **商品CRUD**: 完整的商品创建、编辑、删除和批量操作功能
- **分类管理**: 支持商品分类创建、编辑，分类统计和排序
- **库存管理**: 实时库存追踪、自动扣减、低库存告警、库存变更日志
- **商品属性**: 热门商品、新品标记，支持商品状态管理（上架/下架）
- **🔥 热门商品智能识别系统**:
  - 基于兑换量的自动热门商品识别算法
  - 支持四个时间维度：累积/30天/7天/今日热门
  - 智能评分机制：兑换量权重 + 库存奖励
  - 管理后台实时配置：热门数量上限、权重调整、更新频率
  - 前端智能展示：区分手动/自动热门标签、排行榜组件
  - 定时自动更新：每小时自动刷新，历史数据记录
- **图片管理**: 多图片上传、预览、删除，支持图片轮播展示
- **价格体系**: 双价格模式（光年币价格 + 人民币价格）
- **价格历史管理**: 价格变更历史记录、价格快照机制、审计追踪
- **数据导入导出**: 支持CSV/Excel格式的商品数据批量导入导出

### 📦 订单兑换系统
- **智能订单**: 自动生成格式化订单编号（GNB-光年币/RMB-人民币前缀）
- **双支付模式**: 支持光年币虚拟货币兑换和人民币购买
- **价格快照机制**: 订单创建时保存价格快照，确保历史订单价格不受商品价格变更影响
- **流程管理**: 完整的订单状态流转（待处理→已批准→已发货→已完成）
- **物流跟踪**: 支持物流单号录入、物流公司选择
- **支付凭证**: 人民币支付支持上传支付凭证图片
- **职场配送**: 支持多职场配送地址管理
- **自动通知**: 订单状态变更自动推送飞书群通知
- **订单管理**: 支持订单取消、拒绝、批量操作
- **并发安全**: 使用数据库事务和行锁确保高并发场景下的数据一致性
- **异常处理**: 完善的错误处理和回滚机制，确保系统稳定性

### 📊 数据分析系统
- **实时统计**: 订单数量、销售额、用户活跃度、商品兑换量统计
- **数据可视化**: 基于ECharts的图表展示，包括趋势图、饼图、柱状图
- **定时报告**: 自动生成日报、周报、月报并推送到飞书群
- **管理仪表盘**: 核心业务数据一目了然，支持数据钻取
- **数据导出**: 支持统计数据的Excel导出功能

### 🔔 通知系统
- **飞书群通知**: 订单状态变更、库存告警、定时报告自动推送
- **站内通知**: 用户站内消息通知，支持已读/未读状态
- **邮件通知**: 支持邮件通知功能（可配置）
- **通知配置**: 灵活的通知规则配置和模板管理

### 📱 移动端适配
- **响应式设计**: 完美适配PC端、平板和手机设备
- **触控优化**: 针对移动设备的触控交互优化
- **性能优化**: 图片懒加载、分页加载、缓存策略

## 📁 项目结构

```
workyy/                           # 项目根目录
├── 📁 src/                       # 前端源码目录
│   ├── 📁 api/                   # API请求封装
│   │   ├── auth.js               # 认证相关API
│   │   ├── products.js           # 商品相关API
│   │   ├── exchanges.js          # 兑换相关API
│   │   ├── users.js              # 用户相关API
│   │   ├── statistics.js         # 统计相关API
│   │   └── index.js              # API配置和拦截器
│   ├── 📁 components/            # 通用组件
│   │   ├── ProductCard.vue       # 商品卡片组件
│   │   ├── ProductFilter.vue     # 商品筛选组件
│   │   ├── ExchangeForm.vue      # 兑换表单组件
│   │   ├── ImageGallery.vue      # 图片画廊组件
│   │   ├── StatisticsCard.vue    # 统计卡片组件
│   │   ├── HotProductRanking.vue # 热门商品排行榜
│   │   └── admin/                # 管理后台专用组件
│   ├── 📁 router/                # 路由配置
│   │   └── index.js              # 路由定义和守卫
│   ├── 📁 stores/                # Pinia状态管理
│   │   ├── auth.js               # 认证状态管理
│   │   ├── products.js           # 商品状态管理
│   │   ├── statistics.js         # 统计状态管理
│   │   └── layout.js             # 布局状态管理
│   ├── 📁 styles/                # 样式文件
│   │   ├── global.scss           # 全局样式
│   │   ├── variables.scss        # SCSS变量
│   │   └── mobile.css            # 移动端样式
│   ├── 📁 utils/                 # 工具函数
│   │   ├── format.js             # 格式化工具
│   │   ├── sessionManager.js     # 会话管理
│   │   ├── request.js            # HTTP请求工具
│   │   └── constants.js          # 常量定义
│   ├── 📁 views/                 # 页面视图
│   │   ├── Home.vue              # 商品展示首页
│   │   ├── Login.vue             # 登录页面
│   │   ├── FeishuCallback.vue    # 飞书登录回调
│   │   ├── admin/                # 管理后台页面
│   │   │   ├── AdminLayout.vue   # 管理后台布局
│   │   │   ├── Dashboard.vue     # 数据仪表盘
│   │   │   ├── ProductManagement.vue    # 商品管理
│   │   │   ├── ExchangeManagement.vue   # 订单管理
│   │   │   ├── UserManagement.vue       # 用户管理
│   │   │   └── system/           # 系统管理页面
│   │   └── user/                 # 用户中心页面
│   │       ├── Profile.vue       # 个人中心
│   │       └── Exchanges.vue     # 我的兑换
│   ├── App.vue                   # 根组件
│   └── main.js                   # 应用入口
│
├── 📁 server/                    # 后端源码目录
│   ├── 📁 config/                # 配置文件
│   │   ├── database.js           # 数据库配置
│   │   ├── swagger.js            # API文档配置
│   │   └── cors.js               # 跨域配置
│   ├── 📁 controllers/           # 业务控制器
│   │   ├── authController.js     # 认证控制器
│   │   ├── productController.js  # 商品控制器
│   │   ├── exchangeController.js # 兑换控制器
│   │   ├── userController.js     # 用户控制器
│   │   ├── statisticsController.js # 统计控制器
│   │   ├── hotProductController.js # 热门商品控制器
│   │   └── systemController.js   # 系统控制器
│   ├── 📁 middlewares/           # 中间件
│   │   ├── auth.js               # 认证中间件
│   │   ├── validation.js         # 数据验证中间件
│   │   ├── upload.js             # 文件上传中间件
│   │   └── rateLimit.js          # 限流中间件
│   ├── 📁 models/                # 数据模型（Sequelize）
│   │   ├── index.js              # 模型关联关系
│   │   ├── user.js               # 用户模型
│   │   ├── product.js            # 商品模型
│   │   ├── exchange.js           # 兑换订单模型
│   │   ├── category.js           # 商品分类模型
│   │   ├── workplace.js          # 职场模型
│   │   ├── notification.js       # 通知模型
│   │   ├── hotProductConfig.js   # 热门商品配置模型
│   │   └── productPriceHistory.js # 商品价格历史模型
│   ├── 📁 routes/                # 路由定义
│   │   ├── auth.js               # 认证路由
│   │   ├── products.js           # 商品路由
│   │   ├── exchanges.js          # 兑换路由
│   │   ├── users.js              # 用户路由
│   │   ├── statistics.js         # 统计路由
│   │   ├── hotProducts.js        # 热门商品路由
│   │   ├── feishu.js             # 飞书集成路由
│   │   └── system.js             # 系统管理路由
│   ├── 📁 services/              # 业务服务
│   │   ├── feishuService.js      # 飞书服务
│   │   ├── notificationService.js # 通知服务
│   │   ├── scheduledReportService.js # 定时报告服务
│   │   ├── hotProductService.js  # 热门商品服务
│   │   ├── statisticsService.js  # 统计服务
│   │   └── scheduledTaskService.js # 定时任务服务
│   ├── 📁 utils/                 # 工具函数
│   │   ├── dbInit.js             # 数据库初始化
│   │   ├── logger.js             # 日志工具
│   │   ├── helpers.js            # 辅助函数
│   │   └── constants.js          # 常量定义
│   ├── 📁 migrations/            # 数据库迁移文件
│   ├── 📁 uploads/               # 文件上传目录
│   │   ├── images/               # 商品图片存储
│   │   └── payments/             # 支付凭证存储
│   ├── 📁 logs/                  # 日志文件目录
│   ├── package.json              # 后端依赖配置
│   └── server.js                 # 服务器入口文件
│
├── 📁 docs/                      # 项目文档
│   ├── 开发文档.md               # 开发文档
│   ├── 部署与环境配置指南.md     # 部署指南
│   ├── 飞书集成指南.md           # 飞书集成文档
│   ├── code-standard.md          # 代码规范
│   ├── dependency-analysis.md    # 依赖分析报告
│   ├── 数据库开发工作流.md        # 数据库开发工作流
│   ├── 数据库开发规则.md          # 数据库开发规则
│   ├── 活动促销功能设计.md        # 活动促销功能设计
│   ├── 热门商品自动识别功能设计.md # 热门商品功能设计
│   ├── ORDER_MANAGEMENT_ANALYSIS_REPORT.md  # 订单管理功能深度分析报告
│   └── api/                      # API文档目录
│
├── 📁 config/                    # 项目配置文件
│   ├── nginx.conf                # Nginx生产环境配置
│   └── nginx-fix.conf            # Nginx修复配置
│
├── 📁 scripts/                   # 脚本工具
│   ├── format-code.sh            # 代码格式化脚本
│   ├── clean-dependencies.sh     # 依赖清理脚本
│   ├── deploy/                   # 部署脚本
│   └── db/                       # 数据库脚本
│
├── 📁 temp/                      # 临时文件目录
│   ├── cache/                    # 缓存目录
│   ├── tests/                    # 测试临时文件
│   └── uploads/                  # 上传临时文件
│
├── 📁 backup_docs/               # 备份文档
├── 📁 uploads/                   # 上传文件目录
├── 📁 logs/                      # 日志文件目录
├── .gitignore                    # Git忽略文件
├── .prettierrc                   # Prettier配置
├── .prettierignore               # Prettier忽略文件
├── .editorconfig                 # 编辑器配置
├── package.json                  # 前端依赖配置
├── vite.config.js                # Vite构建配置
├── eslint.config.js              # ESLint配置
├── restart.sh                    # 项目重启脚本
└── README.md                     # 项目说明文档
```

## 🚀 安装和运行

### 环境要求
- **Node.js**: 14.0.0 或更高版本
- **npm**: 6.0.0 或更高版本
- **MySQL**: 8.0 或更高版本（开发环境可使用SQLite）
- **Git**: 用于代码管理

### 快速开始

#### 1. 克隆项目
```bash
git clone <repository-url>
cd workyy
```

#### 2. 安装依赖
```bash
# 安装前端依赖
npm install

# 安装后端依赖
cd server
npm install
cd ..
```

#### 3. 环境配置
```bash
# 复制环境配置文件
cp server/.env.example server/.env

# 编辑环境配置（配置数据库连接、飞书应用信息等）
vim server/.env
```

#### 4. 数据库初始化
```bash
cd server

# 初始化数据库（创建表结构和初始数据）
npm run init-db

# 或者手动运行迁移
npm run migrate
```

#### 5. 启动开发服务器
```bash
# 启动后端服务器（端口3000）
cd server
npm run dev

# 新开终端，启动前端开发服务器（端口5173）
npm run dev
```

#### 6. 访问应用
- **前端应用**: http://localhost:5173
- **后端API**: http://localhost:3000/api
- **API文档**: http://localhost:3000/api-docs（如果启用Swagger）

### 生产环境部署
```bash
# 构建前端生产版本
npm run build

# 启动生产服务器
cd server
npm start

# 或使用PM2进程管理
pm2 start server.js --name "light-year-store"
```

## 🗄️ 数据库设计

### 核心数据表

#### 用户表 (users)
```sql
- id: 主键，自增
- username: 用户名，非空
- email: 邮箱，唯一，非空
- password: 密码哈希，非空
- role: 角色 (admin/user)
- department: 部门名称
- departmentPath: 完整部门路径
- workplace: 工作地点
- workplaceId: 工作地点ID（外键）
- mobile: 手机号码
- feishuOpenId: 飞书OpenID
- feishuUnionId: 飞书UnionID
- feishuAvatar: 飞书头像URL
- authType: 认证类型 (password/feishu)
- isActive: 账户状态
- points: 光年币数量
- lastLoginAt: 最后登录时间
- lastLoginIp: 最后登录IP
```

#### 商品表 (products)
```sql
- id: 主键，自增
- name: 商品名称，非空
- categoryId: 分类ID（外键）
- lyPrice: 光年币价格
- rmbPrice: 人民币价格
- description: 商品描述
- stock: 库存数量
- exchangeCount: 兑换次数
- isHot: 是否热门商品（手动设置）
- isNew: 是否新品
- isAutoHot: 是否自动识别的热门商品
- hotTimeRange: 热门时间维度 (all/30d/7d/1d)
- hotScore: 热门度评分
- hotRank: 热门排名
- lastHotUpdate: 最后热门状态更新时间
- status: 商品状态 (active/inactive)
- createdAt: 创建时间
- updatedAt: 更新时间
```

#### 兑换订单表 (exchanges)
```sql
- id: 主键，自增
- orderNumber: 订单编号
- userId: 用户ID（外键）
- productId: 商品ID（外键）
- quantity: 兑换数量
- unitPrice: 订单创建时的商品单价快照
- totalAmount: 订单总金额
- priceType: 价格类型 (ly/rmb)
- paymentMethod: 支付方式 (ly/rmb)
- contactInfo: 联系信息
- location: 配送地址
- workplaceId: 职场ID（外键）
- remarks: 用户备注
- paymentProofUrl: 支付凭证URL
- status: 订单状态 (pending/approved/shipped/completed/rejected/cancelled)
- adminRemarks: 管理员备注
- trackingNumber: 物流单号
- shippingCompany: 物流公司
- createdAt: 创建时间
- updatedAt: 更新时间
```

#### 商品分类表 (categories)
```sql
- id: 主键，自增
- name: 分类名称，唯一
- description: 分类描述
- sortOrder: 排序顺序
- createdAt: 创建时间
- updatedAt: 更新时间
```

#### 职场表 (workplaces)
```sql
- id: 主键，自增
- name: 职场名称，唯一
- code: 职场代码，唯一
- description: 职场描述
- isActive: 是否启用
- createdAt: 创建时间
- updatedAt: 更新时间
```

#### 商品价格历史表 (product_price_history)
```sql
- id: 主键，自增
- productId: 商品ID（外键）
- oldLyPrice: 变更前的光年币价格
- newLyPrice: 变更后的光年币价格
- oldRmbPrice: 变更前的人民币价格
- newRmbPrice: 变更后的人民币价格
- changeReason: 价格变更原因
- changedBy: 价格变更操作人（外键）
- effectiveDate: 价格生效时间
- createdAt: 创建时间
- updatedAt: 更新时间
```

### 数据关联关系
- 用户 ↔ 兑换订单：一对多关系
- 商品 ↔ 兑换订单：一对多关系
- 分类 ↔ 商品：一对多关系
- 职场 ↔ 用户：一对多关系
- 职场 ↔ 兑换订单：一对多关系
- 商品 ↔ 商品图片：一对多关系
- 商品 ↔ 价格历史：一对多关系
- 用户 ↔ 价格历史：一对多关系（价格变更操作人）

## 📡 API文档

### 认证接口
```
POST /api/auth/login          # 用户登录
POST /api/auth/register       # 用户注册
GET  /api/auth/profile        # 获取用户信息
POST /api/auth/logout         # 用户登出
POST /api/auth/refresh-token  # 刷新令牌
```

### 商品管理接口
```
GET    /api/products                    # 获取商品列表
GET    /api/products/:id                # 获取商品详情
POST   /api/products                    # 创建商品（管理员）
PUT    /api/products/:id                # 更新商品（管理员）
DELETE /api/products/:id                # 删除商品（管理员）
POST   /api/products/import             # 批量导入商品（管理员）
GET    /api/products/export             # 导出商品数据（管理员）
GET    /api/products/:id/price-history  # 获取商品价格历史（管理员）
GET    /api/products/price-history/stats # 获取价格历史统计（管理员）
```

### 热门商品管理接口
```
GET    /api/hot-products                # 获取指定时间维度的热门商品
GET    /api/hot-products/all            # 获取所有时间维度的热门商品
GET    /api/hot-products/configs        # 获取热门商品配置（管理员）
PUT    /api/hot-products/configs        # 更新热门商品配置（管理员）
POST   /api/hot-products/update         # 手动触发热门商品更新（管理员）
GET    /api/hot-products/history        # 获取热门商品历史记录（管理员）
GET    /api/hot-products/stats          # 获取热门商品统计信息（管理员）
```

### 兑换订单接口
```
GET    /api/exchanges         # 获取兑换订单列表
GET    /api/exchanges/:id     # 获取订单详情
POST   /api/exchanges         # 创建兑换申请
PUT    /api/exchanges/:id     # 更新订单状态（管理员）
DELETE /api/exchanges/:id     # 取消订单
```

### 用户管理接口
```
GET    /api/users             # 获取用户列表（管理员）
GET    /api/users/:id         # 获取用户详情（管理员）
POST   /api/users             # 创建用户（管理员）
PUT    /api/users/:id         # 更新用户信息
DELETE /api/users/:id         # 删除用户（管理员）
```

### 飞书集成接口
```
GET    /api/feishu/login      # 飞书登录授权
GET    /api/feishu/callback   # 飞书登录回调
POST   /api/feishu/notify     # 发送飞书通知
```

## 🚁 飞书集成

### 功能特性
- **OAuth单点登录**: 用户可通过飞书账号直接登录系统
- **用户信息同步**: 自动同步用户基本信息、部门信息、企业邮箱等
- **部门层级管理**: 自动获取并记录用户的完整部门路径信息
- **群机器人通知**: 订单状态变更、库存告警等自动推送到飞书群
- **定时报告推送**: 自动生成业务报告并推送到指定飞书群

### 飞书应用配置

#### 1. 创建飞书应用
在[飞书开放平台](https://open.feishu.cn/)创建企业自建应用

#### 2. 配置应用权限
需要申请以下API权限：
```
contact:department.base:readonly          # 获取部门基础信息
contact:department.organize:readonly      # 获取部门组织架构信息
contact:user.base:readonly               # 获取用户基本信息
contact:user.department:readonly         # 获取用户组织架构信息
contact:user.email:readonly              # 获取用户邮箱信息
contact:user.employee_id:readonly        # 获取用户员工ID
contact:user.location:readonly           # 获取用户工作地点信息
im:message                               # 发送消息（群机器人通知）
```

#### 3. 配置重定向URL
```
重定向URL: {YOUR_DOMAIN}/api/feishu/callback
例如: https://your-domain.com/api/feishu/callback
```

#### 4. 环境变量配置
```bash
# 飞书应用配置
FEISHU_APP_ID=your_app_id
FEISHU_APP_SECRET=your_app_secret
FEISHU_REDIRECT_URI=https://your-domain.com/api/feishu/callback

# 飞书群机器人Webhook（可选）
FEISHU_WEBHOOK_URL=your_webhook_url
```

#### 5. 数据访问范围设置
⚠️ **重要**: 在飞书开放平台设置应用可访问的数据范围
- 路径：开发者后台 → 权限管理 → 可访问的数据范围
- 建议设置为"全部部门和成员"
- 如果权限范围不足，用户部门信息可能显示为默认值

### 使用说明
1. 用户点击"飞书登录"按钮
2. 跳转到飞书授权页面
3. 用户授权后自动跳转回系统
4. 系统自动创建或更新用户信息
5. 完成登录并跳转到首页

## � 代码质量与分析

### 订单管理功能分析
项目包含了对订单管理功能模块的深度分析报告，详见 [订单管理功能深度分析报告](docs/ORDER_MANAGEMENT_ANALYSIS_REPORT.md)。

**分析内容包括:**
- **代码审查**: 控制器、模型、服务层的代码质量分析
- **架构设计**: MVC架构评估和设计模式应用分析
- **性能优化**: 数据库查询优化、缓存策略、并发处理建议
- **安全性分析**: 权限控制、数据验证、SQL注入防护评估
- **功能完整性**: 订单生命周期、支付方式、价格快照机制检查
- **改进方案**: 具体的优化建议和实施计划

**主要发现:**
- ✅ 架构设计优秀，采用清晰的MVC模式
- ✅ 并发安全性良好，使用事务和行锁机制
- ✅ 功能完整，覆盖订单完整生命周期
- ⚠️ 存在性能优化空间，建议添加缓存和查询优化
- ⚠️ 错误处理可进一步完善，建议统一异常处理机制

### 测试覆盖
项目包含多种类型的测试：
- **核心功能测试**: `tests/core_features_test.cjs`
- **并发控制测试**: `tests/concurrency_test.cjs`
- **性能测试**: `tests/performance_test.cjs`
- **价格快照测试**: `tests/price_snapshot_test.cjs`

运行测试：
```bash
# 运行核心功能测试
node tests/core_features_test.cjs

# 运行并发控制测试
node tests/concurrency_test.cjs

# 运行性能测试
node tests/performance_test.cjs
```

## �🛠️ 开发指南

### 开发环境搭建
1. **安装Node.js**: 确保安装Node.js 14+版本
2. **安装MySQL**: 安装并配置MySQL 8.0+数据库
3. **克隆项目**: `git clone <repository-url>`
4. **安装依赖**: 分别安装前端和后端依赖
5. **配置环境**: 复制并编辑`.env`配置文件
6. **初始化数据库**: 运行数据库初始化脚本

### 代码规范
- **ESLint**: 使用ESLint进行代码检查
- **命名规范**:
  - 文件名：kebab-case（如：user-management.vue）
  - 变量名：camelCase（如：userName）
  - 常量名：UPPER_SNAKE_CASE（如：API_BASE_URL）
- **注释规范**: 重要函数和复杂逻辑必须添加注释
- **Git提交**: 使用语义化提交信息

### 项目结构说明
- **前端**: 基于Vue 3 Composition API开发
- **后端**: 基于Express.js和Sequelize ORM
- **数据库**: 使用MySQL存储数据，支持迁移和种子数据
- **文件上传**: 支持本地存储，可扩展云存储

### 开发工作流
1. **创建功能分支**: `git checkout -b feature/new-feature`
2. **开发功能**: 遵循代码规范进行开发
3. **测试功能**: 确保功能正常工作
4. **提交代码**: 使用语义化提交信息
5. **创建PR**: 提交Pull Request进行代码审查

### 调试技巧
- **前端调试**: 使用Vue DevTools浏览器扩展
- **后端调试**: 使用console.log或Node.js调试器
- **数据库调试**: 查看server/logs目录下的日志文件
- **网络调试**: 使用浏览器开发者工具的Network面板

## 🚀 部署指南

### 生产环境要求
- **服务器**: Linux服务器（推荐Ubuntu 20.04+）
- **Node.js**: 14.0.0或更高版本
- **MySQL**: 8.0或更高版本
- **Nginx**: 用于反向代理和静态文件服务
- **PM2**: 用于进程管理（推荐）

### 部署步骤
1. **服务器准备**: 安装Node.js、MySQL、Nginx
2. **代码部署**: 上传代码到服务器
3. **依赖安装**: 安装生产环境依赖
4. **环境配置**: 配置生产环境变量
5. **数据库初始化**: 运行数据库迁移
6. **构建前端**: 构建生产版本前端代码
7. **配置Nginx**: 配置反向代理和静态文件服务
8. **启动服务**: 使用PM2启动后端服务
9. **SSL配置**: 配置HTTPS证书（推荐）

### 环境变量配置
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=light_year_store
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# JWT配置
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d

# 飞书配置
FEISHU_APP_ID=your_feishu_app_id
FEISHU_APP_SECRET=your_feishu_app_secret
FEISHU_REDIRECT_URI=https://your-domain.com/api/feishu/callback

# 服务器配置
NODE_ENV=production
PORT=3000
SERVER_URL=https://your-domain.com
```

### 监控和维护
- **日志监控**: 定期检查应用日志
- **性能监控**: 监控服务器资源使用情况
- **数据备份**: 定期备份数据库
- **安全更新**: 及时更新依赖包和系统补丁

## 📚 更新日志

### v1.5.0 (2025年6月) - 热门商品智能识别系统上线
- 🚀 **重大功能**: 全新热门商品自动识别系统正式上线
- ✨ **智能识别算法**:
  - 基于兑换量的自动热门商品识别机制
  - 支持四个时间维度：累积/30天/7天/今日热门
  - 智能评分算法：兑换量权重 + 库存奖励机制
  - 可配置的热门商品数量上限和最小兑换量要求
- ✨ **管理后台功能**:
  - 热门商品规则配置页面，支持实时参数调整
  - 四个时间维度的独立配置管理
  - 手动触发更新功能，支持一键刷新所有维度
  - 热门商品统计报表和历史记录查询
- ✨ **前端展示优化**:
  - 商品卡片智能热门标签显示（区分手动/自动热门）
  - 热门商品排行榜组件，支持多时间维度切换
  - 首页热门商品展示区域集成
  - 商品详情页热门度信息展示
- ✨ **自动化机制**:
  - 集成定时任务系统，每小时自动更新热门商品
  - 历史数据记录和过期数据清理机制
  - 并发安全的更新机制，防止重复执行
- 🔧 **数据库设计**:
  - 扩展products表，新增热门商品相关字段
  - 创建hot_product_configs配置表
  - 创建hot_product_history历史记录表
  - 完善索引设计，优化查询性能
- 📊 **测试验证**:
  - 功能测试验证：累积热门15个商品，30天热门10个商品
  - API接口测试通过，所有时间维度正常工作
  - 前端组件测试通过，用户体验良好
  - 定时任务测试通过，自动更新机制稳定运行

### v1.4.2 (2025年6月) - 通知统计监控数据显示修复
- 🐛 **重要修复**: 修复系统设置页面通知统计监控数据显示为0的问题
- ✨ **缓存控制优化**:
  - 后端API添加缓存控制头，防止浏览器缓存导致数据不更新
  - 前端API调用添加时间戳参数，强制获取最新数据
  - 解决304 Not Modified响应导致的数据显示问题
- ✨ **数据准确性保障**:
  - 验证数据库查询逻辑正常工作（总发送量55，成功53，失败1，成功率96%）
  - 确保统计数据格式化和计算逻辑正确
  - 修复前端数据绑定和显示逻辑
- 🔧 **用户体验改进**:
  - 通知统计卡片现在正确显示实际数据而非0
  - 提升飞书群通知管理的数据可视化效果
  - 增强管理员对通知系统运行状态的监控能力
- 📝 **测试和验证**:
  - 新增通知统计修复验证脚本
  - 确保API响应和前端显示的数据一致性
  - 添加详细的修复步骤和问题排查文档

### v1.4.1 (2025年1月) - 系统设置模块前端渲染问题修复
- 🐛 **重要修复**: 修复管理后台系统设置模块前端渲染问题
- ✨ **组件生命周期优化**:
  - 修复IntelligentSchedule.vue组件中onUnmounted钩子嵌套错误
  - 正确导入和使用Vue生命周期钩子函数
  - 优化定时器清理机制，防止内存泄漏
- ✨ **错误处理增强**:
  - 为高级功能组件添加ErrorBoundary错误边界包装
  - 增强API调用的错误处理和容错机制
  - 添加默认值设置，避免页面崩溃
- ✨ **路由稳定性提升**:
  - 增强路由错误处理机制
  - 添加路由跳转失败的捕获和处理
  - 优化组件切换时的状态管理
- 🔧 **用户体验改进**:
  - 解决"点击高级诊断后再点击其他菜单无反应变空白"的问题
  - 确保系统设置页面各功能模块间的正常切换
  - 提升页面渲染稳定性和响应速度
- 📝 **测试和验证**:
  - 新增修复验证脚本，确保问题彻底解决
  - 完善组件错误处理测试用例
  - 添加详细的修复步骤文档

### v1.4.0 (2025年6月) - 核心功能深度优化与安全加固
- 🚀 **重大优化**: 全面升级三大核心功能，提升系统稳定性和性能
- ✨ **订单编号系统优化**:
  - 实现基于日期的智能序列号生成机制
  - 添加订单编号唯一性检查和并发冲突处理
  - 完善错误处理和重试机制，提升生成成功率
  - 统一前后端编号生成逻辑，确保格式一致性
- ✨ **商品调价功能增强**:
  - 添加事务保护确保价格变更的数据一致性
  - 实现价格变更影响分析，评估对现有订单的影响
  - 新增批量价格调整功能，支持多商品同时调价
  - 完善价格变更审计追踪和历史记录管理
- ✨ **订单导出系统重构**:
  - 实现流式导出避免大数据量内存溢出问题
  - 添加导出进度跟踪和批量处理机制
  - 修复价格计算逻辑，确保使用价格快照而非当前价格
  - 完善错误处理和用户反馈机制
- 🔒 **安全性全面加固**:
  - 实现数据库事务控制和行锁机制，防止库存超卖
  - 添加全面的输入验证和SQL注入防护
  - 强化文件上传安全：类型验证、大小限制、路径安全
  - 实现XSS防护和输出转义机制
  - 增强密码安全策略和用户认证机制
  - 添加并发控制测试，验证高并发场景下的数据一致性
- 🔧 **系统稳定性提升**:
  - 新增核心功能综合测试套件
  - 完善数据库约束和字段验证
  - 优化查询性能和内存使用
  - 添加并发控制和竞态条件防护
- 📝 **文档和测试完善**:
  - 新增核心功能测试用例和验证脚本
  - 添加并发控制测试和安全测试
  - 完善API文档和功能说明
  - 添加性能优化指南和最佳实践

### v1.3.0 (2025年1月)
- ✨ **重大更新**: 新增价格快照机制，解决数据一致性问题
- ✨ 新增商品价格历史版本管理和审计追踪功能
- ✨ 订单创建时保存价格快照，历史订单价格不受商品价格变更影响
- ✨ 新增价格变更历史记录和统计分析功能
- 🔧 优化订单数据结构，增强财务数据准确性
- 🔧 完善数据库迁移脚本，支持现有数据平滑升级
- 📝 新增价格快照功能测试用例和文档

### v1.2.0 (2024年12月)
- ✨ 新增职场管理功能，支持多职场配送
- ✨ 优化飞书集成，支持工作地点信息同步
- 🐛 修复移动端兑换表单显示问题
- 🔧 优化数据库查询性能
- 📝 完善API文档和代码注释

### v1.1.0 (2024年7月)
- ✨ 新增飞书OAuth登录功能
- ✨ 新增定时报告推送功能
- ✨ 新增库存预警通知
- 🔧 优化兑换流程和状态管理
- 🔧 改进数据统计图表展示
- 🐛 修复若干已知问题

### v1.0.0 (2024年6月)
- 🎉 项目初始版本发布
- ✨ 完成基础功能开发
- ✨ 实现商品管理系统
- ✨ 实现用户管理系统
- ✨ 实现订单兑换系统
- ✨ 实现数据统计功能

## ❓ 常见问题

### 安装和配置问题

**Q: 启动时提示数据库连接失败？**
A: 请检查以下配置：
1. 确保MySQL服务已启动
2. 检查 `server/.env` 中的数据库配置
3. 确认数据库 `feishu_mall` 已创建
4. 验证数据库用户权限

**Q: 前端页面显示"服务器未连接"？**
A: 请确认：
1. 后端服务已启动（端口3000）
2. 前端服务已启动（端口5173）
3. 尝试刷新浏览器（Ctrl+F5）
4. 检查控制台错误信息

**Q: 飞书登录失败？**
A: 请检查：
1. 飞书应用配置是否正确
2. 重定向URL是否匹配
3. 应用权限是否充足
4. 网络连接是否正常

### 开发问题

**Q: 如何添加新的API接口？**
A:
1. 在 `server/routes/` 中添加路由
2. 在 `server/controllers/` 中添加控制器
3. 更新API文档
4. 添加相应的前端API调用

**Q: 如何修改数据库结构？**
A:
1. 创建新的迁移文件：`npx sequelize-cli migration:generate --name your-migration-name`
2. 编辑迁移文件
3. 运行迁移：`npm run migrate`
4. 更新对应的模型文件

**Q: 如何部署到生产环境？**
A: 参考 [部署指南](#-部署指南) 部分，或查看 `docs/production-deployment-guide.md`

## 🤝 贡献指南

我们欢迎所有形式的贡献！请遵循以下步骤：

### 开发流程

1. **Fork 项目**
   ```bash
   git clone https://github.com/your-username/workyy.git
   cd workyy
   ```

2. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **开发和测试**
   ```bash
   # 安装依赖
   npm install
   cd server && npm install && cd ..

   # 启动开发环境
   ./restart.sh

   # 运行代码检查
   npm run lint
   npm run format:check
   ```

4. **提交代码**
   ```bash
   git add .
   git commit -m "feat: 添加新功能描述"
   git push origin feature/your-feature-name
   ```

5. **创建 Pull Request**

### 代码规范

- 遵循 [代码规范](docs/code-standard.md)
- 使用语义化提交信息
- 添加必要的测试
- 更新相关文档

### 提交信息格式

```
<type>(<scope>): <description>

[optional body]

[optional footer]
```

类型说明：
- `feat`: 新功能
- `fix`: 修复问题
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具变动

### 问题报告

提交Issue时请包含：
- 问题描述
- 复现步骤
- 期望行为
- 实际行为
- 环境信息（操作系统、Node.js版本等）
- 相关截图或日志

---

## 📞 技术支持

如有技术问题或建议，请通过以下方式联系：

- **项目维护者**: 洋葱学园技术团队
- **问题反馈**: 请在项目仓库创建Issue
- **技术交流**: 欢迎参与项目讨论
- **文档问题**: 请查看 `docs/` 目录下的详细文档

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

*文档最后更新时间: 2025年1月*
