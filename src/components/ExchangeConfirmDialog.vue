<template>
  <el-dialog
    :model-value="dialogVisible"
    @update:model-value="(val) => $emit('update:dialogVisible', val)"
    title="商品兑换确认"
    :width="window.innerWidth <= 768 ? '90%' : '60%'"
    center
    :show-close="true"
    transition="dialog-fade"
    :duration="300"
  >
    <div v-if="product" class="exchange-confirm-container">
      <div class="content-wrapper">
        <div class="left-section">
          <div class="product-image">
            <el-carousel :height="window.innerWidth <= 768 ? '300px' : '350px'" :interval="3000" arrow="hover" indicator-position="outside">
              <el-carousel-item v-for="(image, imageIndex) in product.images" :key="imageIndex">
                <img :src="image" :alt="product.name" style="width: 100%; height: 100%; object-fit: cover; transition: transform 0.3s ease;" @click="$emit('preview-images', product.images, imageIndex)">
              </el-carousel-item>
            </el-carousel>
            <div class="hot-tag" v-if="product.isHot">爆款</div>
            <div class="new-tag" v-if="product.isNew">新品</div>
          </div>
        </div>
        <div class="right-section">
          <h3 class="product-name">
            <template v-if="product.name.includes('(')">
              {{ product.name.split('(')[0] }}
              <span>({{ product.name.split('(')[1] }}</span>
            </template>
            <template v-else>
              {{ product.name }}
            </template>
          </h3>
          <div class="product-info">
            <div class="ly-price">光年币：{{ product.lyPrice }}</div>
            <div class="rmb-price">人民币：{{ product.rmbPrice }}元</div>
            <div class="description">{{ product.description }}</div>
          </div>
          <div class="workplace-selection">
            <div class="selection-title">请选择兑换职场：</div>
            <el-radio-group v-model="selectedWorkplace" class="workplace-radio-group">
              <el-radio label="武汉">武汉职场</el-radio>
              <el-radio label="北京">北京职场</el-radio>
              <el-radio label="西安">西安职场</el-radio>
              <el-radio label="深圳">深圳职场</el-radio>
            </el-radio-group>
          </div>
          <div class="dialog-footer">
            <el-button type="primary" @click="handleRegister" :disabled="!selectedWorkplace">兑换登记</el-button>
            <el-button type="success" @click="$emit('confirm', selectedWorkplace)" :disabled="!selectedWorkplace">确认兑换</el-button>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { defineProps, defineEmits, onMounted, onUnmounted, ref } from 'vue'

const window = ref(globalThis.window)

onMounted(() => {
  window.value = globalThis.window
  window.value.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.value.removeEventListener('resize', handleResize)
})

const handleResize = () => {
  window.value = globalThis.window
}

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    required: true
  },
  product: {
    type: Object,
    required: true
  },
  selectedCity: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update:dialogVisible', 'confirm'])

// 用户选择的职场
const selectedWorkplace = ref('')

const handleRegister = () => {
  globalThis.window.open('https://guanghe.feishu.cn/share/base/form/shrcn1vq4bu593AjwMiqlvic79g', '_blank')
  emit('update:dialogVisible', false)
}
</script>

<style scoped>
@media screen and (max-width: 768px) {
  .content-wrapper {
    flex-direction: column;
    padding: 0;
    gap: 10px;
  }

  .left-section {
    max-width: 100%;
    width: 100%;
    margin: 0 auto;
    padding: 0;
  }
  
  :deep(.el-carousel) {
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
  }

  .right-section {
    padding: 12px;
    width: 100%;
  }

  :deep(.el-carousel) {
    height: 300px !important;
    width: 100%;
  }
  
  :deep(.el-carousel__container) {
    height: 300px !important;
  }
  
  :deep(.el-carousel__item) {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    border-radius: 8px;
  }
  
  .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .product-image {
    width: 100%;
    height: auto;
    border-radius: 8px;
    margin-bottom: 10px;
  }

  
  .product-image {
    width: 100%;
    height: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    overflow: hidden;
  }

  .product-info {
    font-size: 14px;
  }
}

.exchange-confirm-container {
  padding: 20px;
}

@media screen and (max-width: 768px) {
  .exchange-confirm-container {
    padding: 10px;
  }
}

.content-wrapper {
  display: flex;
  gap: 24px;
  min-height: 400px;
}

.left-section {
  flex: 1;
  max-width: 50%;
}

@media screen and (max-width: 768px) {
  .left-section {
    max-width: 100%;
    width: 100%;
  }
}

.right-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding: 0 20px;
}

.product-image {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  height: 100%;
}

:deep(.el-dialog) {
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(8px);
}

:deep(.el-dialog__header) {
  background: linear-gradient(135deg, #1a73e8, #34a853);
  margin: 0;
  padding: 32px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

:deep(.el-dialog__header)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
  pointer-events: none;
}

:deep(.el-dialog__title) {
  font-size: 32px;
  font-weight: 800;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  letter-spacing: 2px;
  position: relative;
  display: inline-block;
  padding: 0 24px;
}

:deep(.el-dialog__title)::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 3px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  border-radius: 2px;
}

:deep(.el-dialog__body) {
  padding: 32px;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
}

.hot-tag,
.new-tag {
  position: absolute;
  top: 10px;
  padding: 4px 8px;
  border-radius: 4px;
  color: white;
  font-size: 12px;
  z-index: 1;
}

.hot-tag {
  right: 10px;
  background-color: #ff4d4f;
}

.new-tag {
  left: 10px;
  background-color: #52c41a;
}

.product-name {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: bold;
}

.product-name span {
  color: #666;
  font-size: 14px;
}

.product-info {
  display: grid;
  grid-template-areas:
    "ly-price ly-price"
    "rmb-price rmb-price"
    "description description";
  gap: 12px;
  margin-bottom: 16px;
}

.workplace-selection {
  margin-bottom: 24px;
  border-radius: 12px;
  padding: 16px;
  background-color: #f9f9f9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.selection-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
  display: flex;
  align-items: center;
}

.selection-title::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 16px;
  background-color: #1a73e8;
  margin-right: 8px;
  border-radius: 2px;
}

.workplace-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.workplace-radio-group :deep(.el-radio) {
  margin-right: 0;
  transition: all 0.3s ease;
}

.workplace-radio-group :deep(.el-radio__input.is-checked + .el-radio__label) {
  color: #1a73e8;
  font-weight: 500;
}

@media screen and (max-width: 768px) {
  .workplace-selection {
    padding: 12px;
    margin-bottom: 16px;
  }
  
  .workplace-radio-group {
    flex-direction: column;
    gap: 0;
    width: 100%;
  }
  
  .workplace-radio-group :deep(.el-radio) {
    margin-right: 0;
    padding: 12px 0;
    width: 100%;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
  }

  .workplace-radio-group :deep(.el-radio):last-child {
    border-bottom: none;
  }

  .workplace-radio-group :deep(.el-radio__label) {
    font-size: 16px;
  }

  .workplace-radio-group :deep(.el-radio__input) {
    margin-right: 10px;
  }
}

.product-info > div {
  margin: 0;
  padding: 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
  font-weight: 600;
}

.product-info .ly-price {
  grid-area: ly-price;
  color: #ff4d4f;
  background: linear-gradient(135deg, #fff5f5, #fff1f0);
  border: 2px solid #ffccc7;
  font-size: 20px;
  text-align: center;
}

.product-info .rmb-price {
  grid-area: rmb-price;
  color: #52c41a;
  background: linear-gradient(135deg, #f9ffe6, #f6ffed);
  border: 2px solid #b7eb8f;
}



.product-info .description {
  grid-area: description;
  color: #333;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border: 2px solid #e9ecef;
  white-space: pre-wrap;
}

.product-info > div:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
}

.dialog-footer .el-button {
  width: 180px;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  color: white;
}

.dialog-footer .el-button--primary {
  background: linear-gradient(135deg, #1a73e8, #4285f4);
  box-shadow: 0 4px 12px rgba(26, 115, 232, 0.2);
}

.dialog-footer .el-button--success {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.2);
}

.dialog-footer .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}
</style>