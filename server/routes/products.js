const express = require('express');
const router = express.Router();
const productController = require('../controllers/productController');
const { authenticate } = require('../middlewares/authMiddleware');
const { isAdmin } = require('../middlewares/admin');
const multer = require('multer');
const path = require('path');

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'import-' + uniqueSuffix + ext);
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 限制10MB
  fileFilter: function (req, file, cb) {
    // 只接受csv和excel文件
    const filetypes = /csv|xlsx|xls/;
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = filetypes.test(file.mimetype);

    if (extname && mimetype) {
      return cb(null, true);
    } else {
      cb(new Error('只支持上传CSV和Excel文件'));
    }
  }
});

/**
 * @swagger
 * tags:
 *   name: Products
 *   description: 商品管理接口
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Product:
 *       type: object
 *       required:
 *         - name
 *         - price
 *         - category
 *       properties:
 *         _id:
 *           type: string
 *           description: 商品唯一ID
 *         name:
 *           type: string
 *           description: 商品名称
 *         description:
 *           type: string
 *           description: 商品描述
 *         price:
 *           type: number
 *           description: 商品价格(光年币)
 *         rmb_price:
 *           type: number
 *           description: 商品价格(人民币)
 *         stock:
 *           type: number
 *           description: 库存数量
 *         category:
 *           type: string
 *           description: 商品分类ID
 *         images:
 *           type: array
 *           items:
 *             type: string
 *           description: 商品图片URL数组
 *         status:
 *           type: string
 *           enum: [active, inactive]
 *           description: 商品状态
 *         featured:
 *           type: boolean
 *           description: 是否为特色商品
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 */

/**
 * @swagger
 * /products:
 *   get:
 *     summary: 获取商品列表
 *     tags: [Products]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: 每页数量
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *         description: 排序字段
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: 按分类筛选
 *       - in: query
 *         name: minPrice
 *         schema:
 *           type: number
 *         description: 最低价格
 *       - in: query
 *         name: maxPrice
 *         schema:
 *           type: number
 *         description: 最高价格
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *     responses:
 *       200:
 *         description: 返回商品列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 products:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Product'
 *                 totalPages:
 *                   type: integer
 *                 currentPage:
 *                   type: integer
 *                 totalItems:
 *                   type: integer
 */

// 公开路由 - 不需要认证
// 获取商品列表（支持筛选、排序和分页）
router.get('/', productController.getProducts);

/**
 * @swagger
 * /products/price-ranges:
 *   get:
 *     summary: 获取商品价格范围
 *     tags: [Products]
 *     responses:
 *       200:
 *         description: 返回价格范围
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 min:
 *                   type: number
 *                 max:
 *                   type: number
 */
// 获取商品价格范围（用于滑块初始化）
router.get('/price-ranges', productController.getProductPriceRanges);

/**
 * @swagger
 * /products/stats:
 *   get:
 *     summary: 获取商品统计数据
 *     tags: [Products]
 *     responses:
 *       200:
 *         description: 返回商品统计数据
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: number
 *                   description: 总商品数
 *                 active:
 *                   type: number
 *                   description: 上架商品数
 *                 inactive:
 *                   type: number
 *                   description: 下架商品数
 *                 lowStock:
 *                   type: number
 *                   description: 库存紧张商品数
 *                 hot:
 *                   type: number
 *                   description: 热门商品数
 *                 new:
 *                   type: number
 *                   description: 新品数
 */
// 获取商品统计数据
router.get('/stats', productController.getProductStats);

/**
 * @swagger
 * /products/popular:
 *   get:
 *     summary: 获取热门商品
 *     tags: [Products]
 *     responses:
 *       200:
 *         description: 返回热门商品列表
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Product'
 */
// 获取热门商品
router.get('/popular', productController.getPopularProducts);

/**
 * @swagger
 * /products/export:
 *   get:
 *     summary: 导出商品数据
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 导出商品数据
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 */
// 导出商品数据
router.get('/export', authenticate, isAdmin, productController.exportProducts);

/**
 * @swagger
 * /products/import:
 *   post:
 *     summary: 导入商品数据
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: CSV或Excel文件
 *     responses:
 *       200:
 *         description: 导入成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 importCount:
 *                   type: integer
 */
// 导入商品数据
router.post('/import', authenticate, isAdmin, upload.single('file'), productController.importProducts);

/**
 * @swagger
 * /products/bulk-delete:
 *   post:
 *     summary: 批量删除商品
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: 批量删除成功
 */
// 批量删除商品
router.post('/bulk-delete', authenticate, isAdmin, productController.bulkDeleteProducts);

// 批量更新商品状态
router.post('/bulk-status', authenticate, isAdmin, productController.bulkUpdateProductStatus);

// 批量更新商品库存
router.post('/bulk-stock', authenticate, isAdmin, productController.bulkUpdateProductStock);

// 批量更新商品价格
router.post('/bulk-price', authenticate, isAdmin, productController.batchUpdatePrice);

// 获取价格历史统计
router.get('/price-history/stats', authenticate, isAdmin, productController.getPriceHistoryStats);

/**
 * @swagger
 * /products/{id}:
 *   get:
 *     summary: 获取单个商品详情
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: 商品ID
 *     responses:
 *       200:
 *         description: 返回商品详情
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Product'
 *       404:
 *         description: 商品不存在
 */
// 获取单个商品详情
router.get('/:id', productController.getProductById);

/**
 * @swagger
 * /products:
 *   post:
 *     summary: 添加新商品
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Product'
 *     responses:
 *       201:
 *         description: 商品创建成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Product'
 */
// 受保护的路由 - 需要管理员权限
// 添加新商品
router.post('/', authenticate, isAdmin, productController.createProduct);

/**
 * @swagger
 * /products/{id}:
 *   put:
 *     summary: 更新商品信息
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: 商品ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Product'
 *     responses:
 *       200:
 *         description: 商品更新成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Product'
 *       404:
 *         description: 商品不存在
 */
// 更新商品信息
router.put('/:id', authenticate, isAdmin, productController.updateProduct);

/**
 * @swagger
 * /products/{id}:
 *   delete:
 *     summary: 删除商品
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: 商品ID
 *     responses:
 *       200:
 *         description: 商品删除成功
 *       404:
 *         description: 商品不存在
 */
// 删除商品
router.delete('/:id', authenticate, isAdmin, productController.deleteProduct);

/**
 * @swagger
 * /products/{id}/status:
 *   put:
 *     summary: 更新商品状态
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: 商品ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [active, inactive]
 *     responses:
 *       200:
 *         description: 状态更新成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Product'
 *       404:
 *         description: 商品不存在
 */
// 更新商品状态（上/下线）
router.put('/:id/status', authenticate, isAdmin, productController.updateProductStatus);

// 获取商品价格历史
router.get('/:id/price-history', authenticate, isAdmin, productController.getProductPriceHistory);

module.exports = router;
