const { ProductImage, Product } = require('../models');
const fs = require('fs');
const path = require('path');
const config = require('../config/config');

/**
 * 为商品添加图片
 */
exports.addProductImage = async (req, res) => {
  try {
    const { productId } = req.params;
    const { imageUrl, sortOrder = 0 } = req.body;
    
    // 验证商品是否存在
    const product = await Product.findByPk(productId);
    
    if (!product) {
      return res.status(404).json({ message: '商品不存在' });
    }
    
    // 创建商品图片
    const productImage = await ProductImage.create({
      productId,
      imageUrl,
      sortOrder
    });
    
    res.status(201).json(productImage);
  } catch (error) {
    console.error('添加商品图片失败:', error);
    res.status(500).json({ message: '添加商品图片失败', error: error.message });
  }
};

/**
 * 上传商品图片
 */
exports.uploadProductImage = async (req, res) => {
  try {
    const { productId } = req.params;
    
    // 验证商品是否存在
    const product = await Product.findByPk(productId);
    
    if (!product) {
      return res.status(404).json({ message: '商品不存在' });
    }
    
    // 文件上传由 multer 中间件处理
    if (!req.file) {
      return res.status(400).json({ message: '没有上传文件' });
    }
    
    // 构建图片URL - 确保在生产环境中使用正确的服务器地址
    let serverUrl;

    // 检查当前环境
    const isProduction = process.env.NODE_ENV === 'production';
    const isLocalDevelopment = process.env.NODE_ENV === 'development';

    if (isProduction) {
      // 生产环境：强制使用生产服务器地址
      serverUrl = process.env.SERVER_URL || 'https://store-api.chongyangqisi.com';
      console.log('生产环境：使用固定的生产服务器地址');
    } else if (isLocalDevelopment) {
      // 开发环境：使用localhost
      serverUrl = 'http://localhost:3000';
      console.log('开发环境：使用localhost地址');
    } else {
      // 其他情况：优先使用环境变量，否则从请求中获取
      serverUrl = process.env.SERVER_URL || `${req.protocol}://${req.get('host')}`;
      console.log('其他环境：使用环境变量或请求地址');
    }

    const imageUrl = `${serverUrl}/uploads/images/${req.file.filename}`;

    console.log('商品图片上传成功，环境:', process.env.NODE_ENV, 'URL路径:', imageUrl);
    
    // 创建商品图片记录
    const productImage = await ProductImage.create({
      productId,
      imageUrl,
      sortOrder: 0 // 默认排序顺序
    });
    
    res.status(201).json({
      message: '图片上传成功',
      productImage
    });
  } catch (error) {
    console.error('上传商品图片失败:', error);
    res.status(500).json({ message: '上传商品图片失败', error: error.message });
  }
};

/**
 * 获取商品的所有图片
 */
exports.getProductImages = async (req, res) => {
  try {
    const { productId } = req.params;

    // 验证商品是否存在
    const product = await Product.findByPk(productId);

    if (!product) {
      return res.status(404).json({ message: '商品不存在' });
    }

    // 获取商品图片
    const productImages = await ProductImage.findAll({
      where: { productId },
      order: [['sortOrder', 'ASC']]
    });

    // 处理图片URL，确保在生产环境中使用正确的服务器地址
    let serverUrl;

    // 检查当前环境
    const isProduction = process.env.NODE_ENV === 'production';
    const isLocalDevelopment = process.env.NODE_ENV === 'development';

    if (isProduction) {
      // 生产环境：强制使用生产服务器地址
      serverUrl = process.env.SERVER_URL || 'https://store-api.chongyangqisi.com';
      console.log('生产环境：使用固定的生产服务器地址');
    } else if (isLocalDevelopment) {
      // 开发环境：使用localhost
      serverUrl = 'http://localhost:3000';
      console.log('开发环境：使用localhost地址');
    } else {
      // 其他情况：优先使用环境变量，否则从请求中获取
      serverUrl = process.env.SERVER_URL || `${req.protocol}://${req.get('host')}`;
      console.log('其他环境：使用环境变量或请求地址');
    }

    // 处理图片URL
    const processedImages = productImages.map(image => {
      const imageData = image.toJSON();

      // 如果imageUrl是相对路径，转换为完整URL
      if (imageData.imageUrl && !imageData.imageUrl.startsWith('http')) {
        imageData.imageUrl = `${serverUrl}${imageData.imageUrl.startsWith('/') ? '' : '/'}${imageData.imageUrl}`;
      }

      return imageData;
    });

    res.json(processedImages);
  } catch (error) {
    console.error('获取商品图片失败:', error);
    res.status(500).json({ message: '获取商品图片失败', error: error.message });
  }
};

/**
 * 更新商品图片
 */
exports.updateProductImage = async (req, res) => {
  try {
    const { imageId } = req.params;
    const { sortOrder } = req.body;
    
    // 验证图片是否存在
    const productImage = await ProductImage.findByPk(imageId);
    
    if (!productImage) {
      return res.status(404).json({ message: '商品图片不存在' });
    }
    
    // 更新排序顺序
    await productImage.update({ sortOrder });
    
    res.json(productImage);
  } catch (error) {
    console.error('更新商品图片失败:', error);
    res.status(500).json({ message: '更新商品图片失败', error: error.message });
  }
};

/**
 * 删除商品图片
 */
exports.deleteProductImage = async (req, res) => {
  try {
    const { imageId } = req.params;
    
    // 验证图片是否存在
    const productImage = await ProductImage.findByPk(imageId);
    
    if (!productImage) {
      return res.status(404).json({ message: '商品图片不存在' });
    }
    
    // 从图片URL中提取文件名
    const imageUrl = productImage.imageUrl;
    const filename = imageUrl.split('/').pop();
    
    if (filename) {
      // 构建图片的完整路径
      const imagePath = path.join(__dirname, '..', 'uploads', filename);
      
      // 检查文件是否存在，并删除
      if (fs.existsSync(imagePath)) {
        fs.unlinkSync(imagePath);
      }
    }
    
    // 删除数据库记录
    await productImage.destroy();
    
    res.json({ message: '商品图片已成功删除' });
  } catch (error) {
    console.error('删除商品图片失败:', error);
    res.status(500).json({ message: '删除商品图片失败', error: error.message });
  }
}; 