# 生产环境重启脚本使用说明

## 概述

本文档介绍了光年小卖部项目的生产环境重启脚本的使用方法和注意事项。这些脚本设计为在生产服务器上直接执行，无需远程SSH连接。

## 服务器信息

- **服务器IP**: **************
- **用户名**: root
- **项目目录**: /www/wwwroot/workyy
- **PM2应用名**: workyy

## 服务架构

- **前端**: 静态文件通过Nginx在80端口提供服务
- **后端**: Node.js应用通过PM2管理，运行在3000端口
- **数据库**: MySQL本地实例，端口3306

## 脚本部署

### 1. 上传脚本到服务器

将脚本文件上传到服务器的项目根目录：

```bash
# 在本地执行
scp restart-production.sh root@**************:/www/wwwroot/workyy/
scp restart-production-simple.sh root@**************:/www/wwwroot/workyy/
```

### 2. 设置执行权限

```bash
# 在服务器上执行
ssh root@**************
cd /www/wwwroot/workyy
chmod +x restart-production.sh
chmod +x restart-production-simple.sh
```

## 脚本文件

### 1. restart-production.sh (完整版)

**功能特点**:
- 完整的重启流程，包含所有安全检查
- 自动备份当前状态
- 数据库连接检查
- 服务健康验证
- 失败时自动回滚
- 详细的日志输出

**使用方法**:
```bash
# 在服务器上执行
cd /www/wwwroot/workyy
./restart-production.sh
```

**执行流程**:
1. 检查当前服务状态
2. 备份当前配置和状态
3. 检查数据库连接
4. 安全停止服务
5. 启动服务
6. 验证服务状态
7. 显示访问信息

### 2. restart-production-simple.sh (简化版)

**功能特点**:
- 快速重启，适合日常维护
- 基本的状态检查
- 简洁的输出信息

**使用方法**:
```bash
# 在服务器上执行
cd /www/wwwroot/workyy
./restart-production-simple.sh
```

**执行流程**:
1. 检查当前状态
2. 重启PM2应用
3. 重新加载Nginx
4. 验证服务状态
5. 测试服务访问

## 前置要求

### 服务器环境

确保服务器上已安装以下工具：
- PM2 (进程管理)
- Nginx (Web服务器)
- MySQL (数据库)
- curl (HTTP测试)
- netstat (网络状态检查)

## 使用场景

### 日常重启
使用简化版脚本进行快速重启：
```bash
# 在服务器上执行
cd /www/wwwroot/workyy
./restart-production-simple.sh
```

### 重要更新或故障恢复
使用完整版脚本进行安全重启：
```bash
# 在服务器上执行
cd /www/wwwroot/workyy
./restart-production.sh
```

### 紧急情况
如果脚本执行失败，可以手动执行以下命令：
```bash
# 在服务器上执行
cd /www/wwwroot/workyy
pm2 restart workyy
systemctl reload nginx
```

## 监控和日志

### 查看PM2状态
```bash
# 在服务器上执行
pm2 list
pm2 monit
```

### 查看应用日志
```bash
# 在服务器上执行
pm2 logs workyy --lines 50
pm2 logs workyy -f  # 实时查看日志
```

### 查看Nginx日志
```bash
# 在服务器上执行
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

### 查看系统资源
```bash
# 在服务器上执行
htop
df -h
free -h
```

## 访问地址

重启完成后，可以通过以下地址访问服务：

- **前端应用**: http://**************
- **后端API**: http://**************:3000/api
- **管理后台**: http://**************/admin
- **API文档**: http://**************:3000/api/docs
- **健康检查**: http://**************:3000/api/health

## 故障排除

### 常见问题

1. **脚本权限问题**
   ```bash
   # 解决方案
   chmod +x restart-production.sh
   chmod +x restart-production-simple.sh
   ```

2. **PM2应用启动失败**
   ```bash
   # 诊断步骤
   pm2 logs workyy --lines 50
   pm2 describe workyy
   # 检查环境配置文件
   cat server/.env
   ```

3. **Nginx服务异常**
   ```bash
   # 诊断步骤
   nginx -t  # 检查配置文件语法
   systemctl status nginx
   tail -f /var/log/nginx/error.log
   ```

4. **数据库连接失败**
   ```bash
   # 诊断步骤
   systemctl status mysql
   mysql -u root -p -e "SHOW DATABASES;"
   netstat -tlnp | grep 3306
   ```

5. **端口占用问题**
   ```bash
   # 诊断步骤
   netstat -tlnp | grep -E ':(80|3000)'
   lsof -i :3000
   # 强制终止占用进程
   kill -9 <PID>
   ```

### 回滚操作

如果重启后服务异常，可以：

1. **使用备份恢复配置**
   ```bash
   # 查看备份目录
   ls -la backups/
   # 恢复环境配置
   cp backups/restart_YYYYMMDD_HHMMSS/server_env_backup server/.env
   ```

2. **手动重启服务**
   ```bash
   pm2 restart workyy
   systemctl restart nginx
   ```

3. **检查日志定位问题**
   ```bash
   pm2 logs workyy --lines 100
   tail -f /var/log/nginx/error.log
   ```

### 紧急恢复

如果服务完全无法访问：

1. **重启所有服务**
   ```bash
   pm2 restart all
   systemctl restart nginx
   systemctl restart mysql
   ```

2. **检查系统资源**
   ```bash
   df -h  # 检查磁盘空间
   free -h  # 检查内存使用
   top  # 检查CPU使用
   ```

3. **重启服务器** (最后手段)
   ```bash
   reboot
   ```

## 安全注意事项

1. **权限控制**: 确保脚本只有root用户可执行
2. **备份重要**: 重启前自动备份配置文件
3. **日志监控**: 定期检查系统和应用日志
4. **定期维护**: 建议每周执行一次完整版重启脚本

## 性能优化建议

1. **定期清理日志**
   ```bash
   pm2 flush  # 清理PM2日志
   logrotate -f /etc/logrotate.conf  # 轮转系统日志
   ```

2. **监控系统资源**
   ```bash
   pm2 monit  # 监控应用性能
   htop  # 监控系统资源
   ```

3. **数据库优化**
   ```bash
   # 定期优化数据库表
   mysql -u root -p -e "OPTIMIZE TABLE feishu_mall.*;"
   ```

## 更新记录

- 2025-07-16: 创建初始版本，改为服务器本地执行
- 基于实际生产环境配置编写
- 包含完整版和简化版两个脚本
- 移除SSH远程连接依赖
