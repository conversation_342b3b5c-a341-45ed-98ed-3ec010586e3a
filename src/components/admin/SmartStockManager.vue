<template>
  <div class="smart-stock-manager">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>

    <!-- 单职场模式 - 直接编辑库存 -->
    <div v-else-if="workplaceMode === 'single'" class="single-workplace-mode">
      <div class="mode-header">
        <el-icon class="mode-icon"><Box /></el-icon>
        <div class="mode-info">
          <h4>单职场库存管理</h4>
          <p>系统中只有一个活跃职场，您可以直接编辑商品库存</p>
        </div>
      </div>

      <div class="stock-editor">
        <el-form-item label="库存数量" :rules="stockRules">
          <div class="stock-input-group">
            <el-input-number
              v-model="localStock"
              :min="0"
              :max="999999"
              :precision="0"
              controls-position="right"
              placeholder="请输入库存数量"
              style="width: 200px;"
              @change="handleStockChange"
            />
            <div class="stock-actions">
              <el-button 
                size="small" 
                type="primary" 
                :icon="Plus" 
                @click="quickAdjust(10)"
              >
                +10
              </el-button>
              <el-button 
                size="small" 
                type="primary" 
                :icon="Plus" 
                @click="quickAdjust(50)"
              >
                +50
              </el-button>
              <el-button 
                size="small" 
                type="warning" 
                :icon="Minus" 
                @click="quickAdjust(-10)"
                :disabled="localStock < 10"
              >
                -10
              </el-button>
            </div>
          </div>
        </el-form-item>

        <div class="stock-info">
          <el-alert
            v-if="localStock < 10"
            title="库存告警"
            :description="`当前库存 ${localStock} 低于建议最低库存 10，建议及时补货`"
            type="warning"
            :closable="false"
            show-icon
          />
          <div v-else class="stock-status">
            <el-icon class="success-icon"><SuccessFilled /></el-icon>
            <span>库存充足</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 多职场模式 - 提示跳转到专门的库存管理 -->
    <div v-else-if="workplaceMode === 'multiple'" class="multiple-workplace-mode">
      <div class="mode-header">
        <el-icon class="mode-icon warning"><Setting /></el-icon>
        <div class="mode-info">
          <h4>多职场库存管理</h4>
          <p>系统中存在 {{ workplaceCount }} 个活跃职场，需要通过专门的库存管理模块进行分配</p>
        </div>
      </div>

      <div class="current-stock-display">
        <div class="stock-summary">
          <div class="total-stock">
            <span class="label">总库存：</span>
            <span class="value">{{ currentStock || 0 }}</span>
          </div>
          <div class="workplace-hint">
            <el-icon><InfoFilled /></el-icon>
            <span>库存已分配到各个职场，无法在此页面直接修改</span>
          </div>
        </div>
      </div>

      <div class="action-buttons">
        <el-button 
          type="primary" 
          :icon="Setting"
          @click="navigateToStockManagement"
        >
          前往库存管理
        </el-button>
        <el-button 
          type="info" 
          :icon="View"
          @click="showStockDistribution"
        >
          查看库存分布
        </el-button>
      </div>

      <div class="help-text">
        <el-alert
          title="为什么不能直接编辑？"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>在多职场环境下，库存需要分配到具体的职场。直接修改总库存可能导致各职场库存分配不一致，影响订单处理。</p>
            <div class="help-actions">
              <el-button
                type="primary"
                size="small"
                text
                @click="navigateToStockManagement"
              >
                立即前往库存管理 →
              </el-button>
            </div>
          </template>
        </el-alert>
      </div>
    </div>

    <!-- 无职场模式 -->
    <div v-else-if="workplaceMode === 'none'" class="no-workplace-mode">
      <div class="mode-header">
        <el-icon class="mode-icon error"><WarningFilled /></el-icon>
        <div class="mode-info">
          <h4>无可用职场</h4>
          <p>系统中暂无活跃职场，请先创建职场后再管理库存</p>
        </div>
      </div>

      <div class="action-buttons">
        <el-button 
          type="primary" 
          :icon="Plus"
          @click="navigateToWorkplaceManagement"
        >
          创建职场
        </el-button>
      </div>
    </div>

    <!-- 库存分布弹窗 -->
    <el-dialog
      v-model="stockDistributionVisible"
      title="库存分布详情"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="stockDistributionLoading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>
      <div v-else class="stock-distribution">
        <div class="distribution-summary">
          <div class="summary-item">
            <span class="label">总库存：</span>
            <span class="value">{{ stockDistribution.totalStock || 0 }}</span>
          </div>
          <div class="summary-item">
            <span class="label">可用库存：</span>
            <span class="value">{{ stockDistribution.totalAvailableStock || 0 }}</span>
          </div>
          <div class="summary-item">
            <span class="label">预留库存：</span>
            <span class="value">{{ stockDistribution.totalReservedStock || 0 }}</span>
          </div>
        </div>

        <div class="workplace-stocks">
          <div 
            v-for="ws in stockDistribution.workplaceStocks" 
            :key="ws.workplaceId"
            class="workplace-stock-item"
            :class="{ 'low-stock': ws.isLowStock }"
          >
            <div class="workplace-info">
              <span class="workplace-name">{{ ws.workplaceName }}</span>
              <span class="workplace-code">({{ ws.workplaceCode }})</span>
            </div>
            <div class="stock-details">
              <span class="stock-amount">库存: {{ ws.stock }}</span>
              <span class="available-amount">可用: {{ ws.availableStock }}</span>
              <span v-if="ws.reservedStock > 0" class="reserved-amount">预留: {{ ws.reservedStock }}</span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="stockDistributionVisible = false">关闭</el-button>
        <el-button type="primary" @click="navigateToStockManagement">前往管理</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { 
  Box, Setting, Plus, Minus, View, InfoFilled, WarningFilled, 
  SuccessFilled 
} from '@element-plus/icons-vue';
import { getActiveWorkplaceCount } from '@/api/system';
import { getProductWorkplaceStocks } from '@/api/products';

const props = defineProps({
  productId: {
    type: [String, Number],
    required: true
  },
  currentStock: {
    type: Number,
    default: 0
  },
  modelValue: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['update:modelValue', 'stock-change']);

const router = useRouter();

// 响应式数据
const loading = ref(true);
const workplaceMode = ref('single'); // 'single', 'multiple', 'none'
const workplaceCount = ref(0);
const localStock = ref(props.modelValue || props.currentStock || 0);
const stockDistributionVisible = ref(false);
const stockDistributionLoading = ref(false);
const stockDistribution = ref({});

// 库存验证规则
const stockRules = [
  { required: true, message: '请输入库存数量', trigger: 'blur' },
  { type: 'number', min: 0, max: 999999, message: '库存数量必须在0-999999之间', trigger: 'blur' }
];

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  localStock.value = newVal || 0;
});

watch(() => props.currentStock, (newVal) => {
  if (!props.modelValue) {
    localStock.value = newVal || 0;
  }
});

// 初始化
onMounted(async () => {
  await checkWorkplaceMode();
  loading.value = false;
});

// 检测职场模式
const checkWorkplaceMode = async () => {
  try {
    const response = await getActiveWorkplaceCount();
    workplaceCount.value = response.count;
    
    if (response.count === 0) {
      workplaceMode.value = 'none';
    } else if (response.count === 1) {
      workplaceMode.value = 'single';
    } else {
      workplaceMode.value = 'multiple';
    }
  } catch (error) {
    console.error('检测职场模式失败:', error);
    ElMessage.error('检测职场模式失败');
    workplaceMode.value = 'single'; // 默认为单职场模式
  }
};

// 库存变更处理
const handleStockChange = (value) => {
  emit('update:modelValue', value);
  emit('stock-change', value);
};

// 快速调整库存
const quickAdjust = (amount) => {
  const newStock = Math.max(0, localStock.value + amount);
  localStock.value = newStock;
  handleStockChange(newStock);
};

// 导航到库存管理页面
const navigateToStockManagement = () => {
  router.push('/admin/stock-management');
};

// 导航到职场管理页面
const navigateToWorkplaceManagement = () => {
  router.push('/admin/system/workplace-management');
};

// 显示库存分布
const showStockDistribution = async () => {
  stockDistributionVisible.value = true;
  stockDistributionLoading.value = true;
  
  try {
    const response = await getProductWorkplaceStocks(props.productId);
    stockDistribution.value = response.data;
  } catch (error) {
    console.error('获取库存分布失败:', error);
    ElMessage.error('获取库存分布失败');
  } finally {
    stockDistributionLoading.value = false;
  }
};
</script>

<style scoped>
.smart-stock-manager {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
}

.loading-container {
  padding: 20px;
}

.mode-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.mode-icon {
  font-size: 24px;
  margin-right: 12px;
  color: #409eff;
}

.mode-icon.warning {
  color: #e6a23c;
}

.mode-icon.error {
  color: #f56c6c;
}

.mode-info h4 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 16px;
}

.mode-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stock-editor {
  margin-top: 16px;
}

.stock-input-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stock-actions {
  display: flex;
  gap: 8px;
}

.stock-info {
  margin-top: 12px;
}

.stock-status {
  display: flex;
  align-items: center;
  color: #67c23a;
  font-size: 14px;
}

.success-icon {
  margin-right: 4px;
}

.current-stock-display {
  margin: 16px 0;
  padding: 16px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.stock-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.total-stock {
  font-size: 16px;
  font-weight: 500;
}

.total-stock .label {
  color: #606266;
}

.total-stock .value {
  color: #303133;
  font-weight: 600;
}

.workplace-hint {
  display: flex;
  align-items: center;
  color: #909399;
  font-size: 14px;
}

.workplace-hint .el-icon {
  margin-right: 4px;
}

.action-buttons {
  margin: 16px 0;
  display: flex;
  gap: 12px;
}

.help-text {
  margin-top: 16px;
}

.help-actions {
  margin-top: 8px;
}

.help-actions .el-button {
  padding: 0;
  font-size: 13px;
}

.stock-distribution {
  padding: 16px 0;
}

.distribution-summary {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 6px;
}

.summary-item {
  text-align: center;
}

.summary-item .label {
  display: block;
  color: #909399;
  font-size: 12px;
  margin-bottom: 4px;
}

.summary-item .value {
  display: block;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.workplace-stocks {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.workplace-stock-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.workplace-stock-item.low-stock {
  border-color: #e6a23c;
  background: #fef7e0;
}

.workplace-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.workplace-name {
  font-weight: 500;
  color: #303133;
}

.workplace-code {
  color: #909399;
  font-size: 12px;
}

.stock-details {
  display: flex;
  gap: 12px;
  font-size: 14px;
}

.stock-amount {
  color: #303133;
  font-weight: 500;
}

.available-amount {
  color: #67c23a;
}

.reserved-amount {
  color: #e6a23c;
}
</style>
