<template>
  <div class="stock-detail-panel">
    <div class="panel-header">
      <h3 class="panel-title">
        <i class="fas fa-chart-bar"></i>
        {{ product.name }} - 职场库存详情
      </h3>
      <div class="panel-actions">
        <el-button 
          type="primary" 
          size="small" 
          icon="el-icon-edit"
          @click="editStocks"
        >
          批量编辑
        </el-button>
        <el-button 
          type="success" 
          size="small" 
          icon="el-icon-sort"
          @click="transferStock"
        >
          库存转移
        </el-button>
      </div>
    </div>

    <div class="panel-content">
      <!-- 库存概览 -->
      <div class="stock-overview">
        <div class="overview-item">
          <div class="overview-label">总库存</div>
          <div class="overview-value total">{{ product.totalStock || product.stock || 0 }}</div>
        </div>
        <div class="overview-item">
          <div class="overview-label">可用库存</div>
          <div class="overview-value available">{{ product.totalAvailableStock || product.stock || 0 }}</div>
        </div>
        <div class="overview-item">
          <div class="overview-label">预留库存</div>
          <div class="overview-value reserved">{{ product.totalReservedStock || 0 }}</div>
        </div>
        <div class="overview-item">
          <div class="overview-label">库存管理</div>
          <div class="overview-value">
            <el-tag :type="product.stockManagementType === 'workplace' ? 'success' : 'info'" size="small">
              {{ product.stockManagementType === 'workplace' ? '职场模式' : '传统模式' }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 职场库存分布 -->
      <div class="workplace-distribution">
        <h4 class="section-title">职场库存分布</h4>
        
        <div v-if="workplaceStocks.length > 0" class="workplace-grid">
          <div 
            v-for="stock in workplaceStocks"
            :key="stock.workplaceId"
            class="workplace-card"
            :class="{ 'low-stock': stock.isLowStock, 'out-of-stock': stock.availableStock === 0 }"
          >
            <div class="workplace-header">
              <div class="workplace-info">
                <h5 class="workplace-name">{{ stock.workplaceName }}</h5>
                <span class="workplace-code">{{ stock.workplaceCode }}</span>
              </div>
              <div class="workplace-status">
                <el-tag
                  :type="getStockStatusType(stock)"
                  size="small"
                >
                  {{ getStockStatusText(stock) }}
                </el-tag>
              </div>
            </div>
            
            <div class="stock-details">
              <div class="stock-item">
                <span class="stock-label">库存:</span>
                <span class="stock-value">{{ stock.stock }}</span>
              </div>
              <div class="stock-item">
                <span class="stock-label">预留:</span>
                <span class="stock-value">{{ stock.reservedStock }}</span>
              </div>
              <div class="stock-item">
                <span class="stock-label">可用:</span>
                <span class="stock-value available">{{ stock.availableStock }}</span>
              </div>
              <div class="stock-item">
                <span class="stock-label">告警:</span>
                <span class="stock-value">{{ stock.minStockAlert }}</span>
              </div>
            </div>

            <div class="stock-actions">
              <el-button
                type="text"
                size="small"
                @click="quickEdit(stock)"
              >
                快速编辑
              </el-button>

            </div>

            <div class="last-update">
              <i class="el-icon-time"></i>
              更新: {{ formatTime(stock.lastStockUpdate) }}
            </div>
          </div>
        </div>

        <div v-else class="no-workplace-stocks">
          <div class="empty-state">
            <i class="fas fa-box-open"></i>
            <div class="stock-allocation-guide">
              <el-icon class="guide-icon"><InfoFilled /></el-icon>
              <div class="guide-content">
                <span class="guide-text">该商品尚未分配职场库存</span>
                <span class="guide-hint">请前往库存管理操作，手动编辑分配各职场库存</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 库存变化趋势 -->
      <div class="stock-trends" v-if="showTrends">
        <h4 class="section-title">库存变化趋势</h4>
        <div class="trends-chart">
          <!-- 这里可以集成图表组件 -->
          <div class="chart-placeholder">
            <i class="fas fa-chart-line"></i>
            <p>库存变化趋势图表</p>
            <small>功能开发中...</small>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速编辑对话框 -->
    <el-dialog
      v-model="showQuickEditDialog"
      title="快速编辑库存"
      width="400px"
    >
      <el-form 
        :model="quickEditForm" 
        label-width="80px"
        v-if="selectedStock"
      >
        <el-form-item label="职场">
          <span>{{ selectedStock.workplaceName }} ({{ selectedStock.workplaceCode }})</span>
        </el-form-item>
        <el-form-item label="库存数量">
          <el-input-number 
            v-model="quickEditForm.stock" 
            :min="0" 
            :max="9999"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="告警阈值">
          <el-input-number 
            v-model="quickEditForm.minStockAlert" 
            :min="0" 
            :max="999"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="操作原因">
          <el-input 
            v-model="quickEditForm.reason" 
            placeholder="请输入操作原因"
            maxlength="100"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showQuickEditDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="confirmQuickEdit"
            :loading="quickEditLoading"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'
import { updateProductWorkplaceStocks } from '@/api/stockManagement'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

export default {
  name: 'StockDetailPanel',
  components: {
    InfoFilled
  },
  props: {
    product: {
      type: Object,
      required: true
    }
  },
  emits: ['update-stock', 'transfer-stock'],
  setup(props, { emit }) {
    const showTrends = ref(false) // 暂时隐藏趋势图
    const showQuickEditDialog = ref(false)
    const quickEditLoading = ref(false)
    const selectedStock = ref(null)
    const quickEditForm = ref({
      stock: 0,
      minStockAlert: 10,
      reason: ''
    })

    const workplaceStocks = computed(() => {
      return props.product.workplaceStocks || []
    })

    const getStockStatusType = (stock) => {
      if (stock.availableStock === 0) return 'danger'
      if (stock.isLowStock) return 'warning'
      return 'success'
    }

    const getStockStatusText = (stock) => {
      if (stock.availableStock === 0) return '缺货'
      if (stock.isLowStock) return '告警'
      return '正常'
    }

    const formatTime = (time) => {
      if (!time) return '未知'
      try {
        return formatDistanceToNow(new Date(time), { 
          addSuffix: true, 
          locale: zhCN 
        })
      } catch (error) {
        return '时间格式错误'
      }
    }

    const editStocks = () => {
      emit('update-stock', props.product.id)
    }

    const transferStock = () => {
      emit('transfer-stock', props.product.id)
    }



    const quickEdit = (stock) => {
      selectedStock.value = stock
      quickEditForm.value = {
        stock: stock.stock,
        minStockAlert: stock.minStockAlert,
        reason: ''
      }
      showQuickEditDialog.value = true
    }

    const confirmQuickEdit = async () => {
      if (!selectedStock.value) return

      quickEditLoading.value = true
      try {
        const updateData = {
          stockUpdates: [{
            workplaceId: selectedStock.value.workplaceId,
            stock: quickEditForm.value.stock,
            minStockAlert: quickEditForm.value.minStockAlert
          }],
          reason: quickEditForm.value.reason || '快速编辑库存'
        }

        await updateProductWorkplaceStocks(props.product.id, updateData)
        
        ElMessage.success('库存更新成功')
        showQuickEditDialog.value = false
        emit('update-stock', props.product.id)

      } catch (error) {
        console.error('快速编辑失败:', error)
        ElMessage.error('库存更新失败')
      } finally {
        quickEditLoading.value = false
      }
    }



    return {
      showTrends,
      showQuickEditDialog,
      quickEditLoading,
      selectedStock,
      quickEditForm,
      workplaceStocks,
      getStockStatusType,
      getStockStatusText,
      formatTime,
      editStocks,
      transferStock,
      quickEdit,
      confirmQuickEdit
    }
  }
}
</script>

<style scoped>
.stock-detail-panel {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 16px 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-title i {
  color: #409eff;
}

.panel-actions {
  display: flex;
  gap: 8px;
}

/* 库存概览 */
.stock-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.overview-item {
  text-align: center;
}

.overview-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.overview-value {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.overview-value.total {
  color: #409eff;
}

.overview-value.available {
  color: #67c23a;
}

.overview-value.reserved {
  color: #e6a23c;
}

/* 职场分布 */
.workplace-distribution {
  margin-bottom: 24px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.workplace-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.workplace-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  transition: all 0.2s;
}

.workplace-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.workplace-card.low-stock {
  border-color: #e6a23c;
  background: #fdf6ec;
}

.workplace-card.out-of-stock {
  border-color: #f56c6c;
  background: #fef0f0;
}

.workplace-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.workplace-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.workplace-code {
  font-size: 12px;
  color: #909399;
  background: #f0f2f5;
  padding: 2px 6px;
  border-radius: 3px;
}

.stock-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 12px;
}

.stock-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.stock-label {
  color: #909399;
}

.stock-value {
  font-weight: 600;
  color: #303133;
}

.stock-value.available {
  color: #67c23a;
}

.stock-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.last-update {
  font-size: 11px;
  color: #c0c4cc;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 空状态 */
.no-workplace-stocks {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 40px;
}

.empty-state {
  text-align: center;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.stock-allocation-guide {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-top: 16px;
  text-align: left;
}

.guide-icon {
  color: #409eff;
  font-size: 16px;
  margin-top: 1px;
  flex-shrink: 0;
}

.guide-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.guide-text {
  color: #606266;
  font-size: 13px;
  font-weight: 500;
}

.guide-hint {
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

/* 趋势图 */
.trends-chart {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 40px;
}

.chart-placeholder {
  text-align: center;
  color: #909399;
}

.chart-placeholder i {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.chart-placeholder p {
  margin: 0 0 8px 0;
  font-size: 14px;
}

.chart-placeholder small {
  font-size: 12px;
  color: #c0c4cc;
}

/* 响应式 */
@media (max-width: 768px) {
  .stock-detail-panel {
    padding: 16px;
  }
  
  .panel-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .panel-actions {
    justify-content: flex-end;
  }
  
  .stock-overview {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .workplace-grid {
    grid-template-columns: 1fr;
  }
}
</style>
