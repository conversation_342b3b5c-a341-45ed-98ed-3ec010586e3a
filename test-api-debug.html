<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>API调试测试</h1>
    
    <div class="test-section">
        <h3>1. 测试管理员登录</h3>
        <button onclick="testLogin()">登录测试</button>
        <div id="login-result"></div>
    </div>

    <div class="test-section">
        <h3>2. 测试商品API (带职场库存)</h3>
        <button onclick="testProductsAPI()">测试商品API</button>
        <div id="products-result"></div>
    </div>

    <div class="test-section">
        <h3>3. 测试职场API</h3>
        <button onclick="testWorkplacesAPI()">测试职场API</button>
        <div id="workplaces-result"></div>
    </div>

    <div class="test-section">
        <h3>4. 测试分类API</h3>
        <button onclick="testCategoriesAPI()">测试分类API</button>
        <div id="categories-result"></div>
    </div>

    <div class="test-section">
        <h3>5. 测试活跃职场API</h3>
        <button onclick="testActiveWorkplacesAPI()">测试活跃职场API</button>
        <div id="active-workplaces-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';
        
        // 通用API调用函数
        async function apiCall(url, options = {}) {
            const token = localStorage.getItem('token');
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    ...(token && { 'Authorization': `Bearer ${token}` })
                }
            };
            
            console.log(`API调用: ${options.method || 'GET'} ${url}`);
            console.log('请求头:', { ...defaultOptions.headers, ...options.headers });
            
            const response = await fetch(url, { ...defaultOptions, ...options });
            const data = await response.json();
            
            console.log(`响应状态: ${response.status}`);
            console.log('响应数据:', data);
            
            if (!response.ok) {
                throw new Error(`API错误 (${response.status}): ${data.message || '未知错误'}`);
            }
            
            return data;
        }

        // 显示结果
        function showResult(containerId, content, isSuccess = true) {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="result ${isSuccess ? 'success' : 'error'}">${content}</div>`;
        }

        // 测试登录
        async function testLogin() {
            try {
                const data = await apiCall(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: '654321'
                    })
                });
                
                if (data.token) {
                    localStorage.setItem('token', data.token);
                    showResult('login-result', `✅ 登录成功！\nToken: ${data.token.substring(0, 50)}...\n用户信息: ${JSON.stringify(data.user, null, 2)}`, true);
                } else {
                    throw new Error('登录响应中没有token');
                }
            } catch (error) {
                showResult('login-result', `❌ 登录失败: ${error.message}`, false);
            }
        }

        // 测试商品API
        async function testProductsAPI() {
            try {
                const data = await apiCall(`${API_BASE}/products?includeWorkplaceStocks=true&page=1&limit=3`);
                
                const result = `✅ 商品API测试成功！
数据结构: 
- 返回商品数量: ${data.data?.length || 0}
- 总数: ${data.total}
- 页码: ${data.page}
- 每页数量: ${data.limit}

第一个商品示例:
${data.data?.[0] ? JSON.stringify({
    id: data.data[0].id,
    name: data.data[0].name,
    stockManagementType: data.data[0].stockManagementType,
    totalAvailableStock: data.data[0].totalAvailableStock,
    workplaceStocksCount: data.data[0].workplaceStocks?.length || 0,
    workplaceStocks: data.data[0].workplaceStocks?.slice(0, 2) // 只显示前2个
}, null, 2) : '无商品数据'}`;
                
                showResult('products-result', result, true);
            } catch (error) {
                showResult('products-result', `❌ 商品API测试失败: ${error.message}`, false);
            }
        }

        // 测试职场API
        async function testWorkplacesAPI() {
            try {
                const data = await apiCall(`${API_BASE}/workplaces`);
                
                const result = `✅ 职场API测试成功！
响应结构: ${JSON.stringify({
    success: data.success,
    message: data.message,
    dataLength: data.data?.length || 0
}, null, 2)}

职场列表:
${JSON.stringify(data.data || [], null, 2)}`;
                
                showResult('workplaces-result', result, true);
            } catch (error) {
                showResult('workplaces-result', `❌ 职场API测试失败: ${error.message}`, false);
            }
        }

        // 测试分类API
        async function testCategoriesAPI() {
            try {
                const data = await apiCall(`${API_BASE}/categories`);
                
                const result = `✅ 分类API测试成功！
分类数量: ${data.length}
分类列表: ${JSON.stringify(data.map(cat => ({ id: cat.id, name: cat.name })), null, 2)}`;
                
                showResult('categories-result', result, true);
            } catch (error) {
                showResult('categories-result', `❌ 分类API测试失败: ${error.message}`, false);
            }
        }

        // 测试活跃职场API
        async function testActiveWorkplacesAPI() {
            try {
                const data = await apiCall(`${API_BASE}/system/workplaces/active`);
                
                const result = `✅ 活跃职场API测试成功！
活跃职场数量: ${data.length}
职场列表: ${JSON.stringify(data, null, 2)}`;
                
                showResult('active-workplaces-result', result, true);
            } catch (error) {
                showResult('active-workplaces-result', `❌ 活跃职场API测试失败: ${error.message}`, false);
            }
        }

        // 页面加载时检查token状态
        window.onload = function() {
            const token = localStorage.getItem('token');
            if (token) {
                console.log('已有token，可以直接测试API');
                document.body.insertAdjacentHTML('afterbegin', 
                    '<div style="background: #d1ecf1; padding: 10px; margin-bottom: 20px; border-radius: 5px;">✅ 已检测到登录token，可以直接测试API</div>'
                );
            } else {
                document.body.insertAdjacentHTML('afterbegin', 
                    '<div style="background: #f8d7da; padding: 10px; margin-bottom: 20px; border-radius: 5px;">⚠️ 未检测到登录token，请先执行登录测试</div>'
                );
            }
        };
    </script>
</body>
</html>
