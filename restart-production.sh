#!/bin/bash

# 生产环境重启脚本 (服务器本地执行版本)
# 在服务器 ************** 的 /www/wwwroot/workyy 目录下执行
# 项目: 光年小卖部 (workyy)

# 设置颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

# 项目配置
PROJECT_DIR="/www/wwwroot/workyy"
SERVER_DIR="$PROJECT_DIR/server"
SERVER_IP="**************"

# PM2应用配置
PM2_APP_NAME="workyy"
BACKEND_PORT=3000
FRONTEND_PORT=80

# 数据库配置
DB_HOST="localhost"
DB_PORT=3306
DB_USER="root"
DB_PASSWORD="password"
DB_NAME="feishu_mall"

echo -e "${YELLOW}===== 生产环境重启脚本 =====${NC}"
echo -e "${BLUE}服务器: $SERVER_IP${NC}"
echo -e "${BLUE}项目目录: $PROJECT_DIR${NC}"
echo -e "${BLUE}PM2应用: $PM2_APP_NAME${NC}"

# 检查是否在正确的目录
if [ ! -d "$PROJECT_DIR" ]; then
    echo -e "${RED}错误: 项目目录 $PROJECT_DIR 不存在${NC}"
    exit 1
fi

cd $PROJECT_DIR

# 函数：执行命令并检查结果
execute_command() {
    local command="$1"
    local description="$2"

    echo -e "${BLUE}$description${NC}"

    eval "$command"
    local exit_code=$?

    if [ $exit_code -ne 0 ]; then
        echo -e "${RED}错误: $description 失败 (退出码: $exit_code)${NC}"
        return $exit_code
    fi

    echo -e "${GREEN}✓ $description 完成${NC}"
    return 0
}

# 函数：检查服务状态
check_service_status() {
    echo -e "${BLUE}===== 检查当前服务状态 =====${NC}"

    execute_command "pm2 list" "检查PM2应用状态"
    execute_command "systemctl status nginx --no-pager -l | head -15" "检查Nginx状态"
    execute_command "netstat -tlnp | grep -E ':(80|3000)'" "检查端口占用情况"
}

# 函数：备份当前状态
backup_current_state() {
    echo -e "${BLUE}===== 备份当前状态 =====${NC}"

    local backup_dir="backups/restart_$(date +%Y%m%d_%H%M%S)"

    execute_command "mkdir -p $backup_dir" "创建备份目录"
    execute_command "pm2 save && cp ~/.pm2/dump.pm2 $backup_dir/" "备份PM2配置"
    execute_command "cp server/.env $backup_dir/server_env_backup" "备份服务器环境配置"
    execute_command "cp .env.production $backup_dir/frontend_env_backup" "备份前端环境配置"

    echo -e "${GREEN}✓ 状态备份完成: $backup_dir${NC}"
}

# 函数：检查数据库连接
check_database() {
    echo -e "${BLUE}===== 检查数据库连接 =====${NC}"

    local db_check_cmd="mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD -e 'SELECT 1; USE $DB_NAME; SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema=\"$DB_NAME\";'"

    if execute_command "$db_check_cmd" "检查数据库连接和表结构"; then
        echo -e "${GREEN}✓ 数据库连接正常${NC}"
        return 0
    else
        echo -e "${RED}✗ 数据库连接失败，请检查数据库服务${NC}"
        return 1
    fi
}

# 函数：安全停止服务
stop_services() {
    echo -e "${BLUE}===== 安全停止服务 =====${NC}"

    # 停止PM2应用
    execute_command "pm2 stop $PM2_APP_NAME" "停止后端应用"

    # 等待进程完全停止
    execute_command "sleep 3" "等待进程停止"

    # 检查端口是否释放
    execute_command "if netstat -tlnp | grep :$BACKEND_PORT; then echo '端口$BACKEND_PORT仍被占用'; exit 1; else echo '端口$BACKEND_PORT已释放'; fi" "验证后端端口释放"

    echo -e "${GREEN}✓ 服务停止完成${NC}"
}

# 函数：启动服务
start_services() {
    echo -e "${BLUE}===== 启动服务 =====${NC}"

    # 启动后端服务
    execute_command "pm2 start $PM2_APP_NAME" "启动后端应用"

    # 等待服务启动
    execute_command "sleep 5" "等待服务启动"

    # 重新加载nginx配置
    execute_command "nginx -t && systemctl reload nginx" "重新加载Nginx配置"

    echo -e "${GREEN}✓ 服务启动完成${NC}"
}

# 函数：验证服务状态
verify_services() {
    echo -e "${BLUE}===== 验证服务状态 =====${NC}"

    # 检查PM2应用状态
    if execute_command "pm2 list | grep $PM2_APP_NAME | grep online" "验证后端应用状态"; then
        echo -e "${GREEN}✓ 后端应用运行正常${NC}"
    else
        echo -e "${RED}✗ 后端应用启动失败${NC}"
        execute_command "pm2 logs $PM2_APP_NAME --lines 20" "查看后端应用日志"
        return 1
    fi

    # 检查后端API健康状态
    if execute_command "curl -s http://localhost:$BACKEND_PORT/api/health | grep -q 'ok\\|success\\|healthy'" "检查后端API健康状态"; then
        echo -e "${GREEN}✓ 后端API响应正常${NC}"
    else
        echo -e "${YELLOW}⚠ 后端API健康检查失败，但服务可能仍在启动中${NC}"
    fi

    # 检查Nginx状态
    if execute_command "systemctl is-active nginx" "检查Nginx服务状态"; then
        echo -e "${GREEN}✓ Nginx服务运行正常${NC}"
    else
        echo -e "${RED}✗ Nginx服务异常${NC}"
        return 1
    fi

    # 检查前端访问
    if execute_command "curl -s -o /dev/null -w '%{http_code}' http://localhost:$FRONTEND_PORT | grep -q '200'" "检查前端页面访问"; then
        echo -e "${GREEN}✓ 前端页面访问正常${NC}"
    else
        echo -e "${YELLOW}⚠ 前端页面访问异常，请检查静态文件${NC}"
    fi

    return 0
}

# 函数：回滚服务
rollback_services() {
    echo -e "${YELLOW}===== 执行服务回滚 =====${NC}"

    execute_command "pm2 restart $PM2_APP_NAME" "重启后端应用"
    execute_command "systemctl restart nginx" "重启Nginx服务"

    echo -e "${YELLOW}✓ 回滚操作完成${NC}"
}

# 函数：显示服务信息
show_service_info() {
    echo -e "\n${GREEN}===== 重启完成 =====${NC}"
    echo -e "${BLUE}前端访问地址: ${NC}http://$SERVER_IP"
    echo -e "${BLUE}后端API地址: ${NC}http://$SERVER_IP:$BACKEND_PORT/api"
    echo -e "${BLUE}管理后台: ${NC}http://$SERVER_IP/admin"
    echo -e "${BLUE}API文档: ${NC}http://$SERVER_IP:$BACKEND_PORT/api/docs"

    echo -e "\n${YELLOW}===== 监控命令 =====${NC}"
    echo -e "${BLUE}查看PM2状态: ${NC}pm2 monit"
    echo -e "${BLUE}查看应用日志: ${NC}pm2 logs $PM2_APP_NAME --lines 50"
    echo -e "${BLUE}查看Nginx访问日志: ${NC}tail -f /var/log/nginx/access.log"
    echo -e "${BLUE}查看Nginx错误日志: ${NC}tail -f /var/log/nginx/error.log"
}

# 主执行流程
main() {
    echo -e "${YELLOW}开始执行生产环境重启流程...${NC}"

    # 1. 检查当前状态
    if ! check_service_status; then
        echo -e "${RED}服务状态检查失败，请手动检查服务器状态${NC}"
        exit 1
    fi

    # 2. 备份当前状态
    if ! backup_current_state; then
        echo -e "${YELLOW}备份失败，但继续执行重启流程${NC}"
    fi

    # 3. 检查数据库
    if ! check_database; then
        echo -e "${RED}数据库检查失败，终止重启流程${NC}"
        exit 1
    fi

    # 4. 停止服务
    if ! stop_services; then
        echo -e "${RED}服务停止失败，尝试回滚${NC}"
        rollback_services
        exit 1
    fi

    # 5. 启动服务
    if ! start_services; then
        echo -e "${RED}服务启动失败，尝试回滚${NC}"
        rollback_services
        exit 1
    fi

    # 6. 验证服务
    if ! verify_services; then
        echo -e "${RED}服务验证失败，尝试回滚${NC}"
        rollback_services
        exit 1
    fi

    # 7. 显示服务信息
    show_service_info

    echo -e "\n${GREEN}🎉 生产环境重启成功完成！${NC}"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
