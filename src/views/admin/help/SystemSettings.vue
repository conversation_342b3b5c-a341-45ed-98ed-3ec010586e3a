<template>
  <div class="help-doc system-settings">
    <h2>系统设置</h2>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>系统设置概述</h3>
        </div>
      </template>
      <div class="card-content">
        <p>系统设置模块提供了完整的系统配置管理功能，包括通知配置、消息模板管理、智能调度设置等。通过合理配置这些设置，可以让系统更好地服务于您的业务需求。</p>
        
        <div class="info-block">
          <h4>主要配置模块</h4>
          <ul>
            <li><strong>通知配置</strong>：管理各种类型的飞书群通知开关和参数</li>
            <li><strong>消息模板</strong>：自定义飞书群推送消息的格式和内容</li>
            <li><strong>智能调度</strong>：控制消息推送的时间和频率</li>
            <li><strong>职场管理</strong>：管理企业内部的职场和部门信息</li>
            <li><strong>高级诊断</strong>：系统状态检查和故障排查工具</li>
          </ul>
        </div>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>通知配置</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>配置项说明</h4>
        <el-table :data="notificationConfigs" style="width: 100%">
          <el-table-column prop="name" label="配置项" width="150" />
          <el-table-column prop="description" label="说明" />
          <el-table-column prop="default_value" label="默认值" width="120" />
        </el-table>
        
        <h4>配置步骤</h4>
        <ol>
          <li>进入系统设置页面</li>
          <li>在"通知配置"区域找到要配置的通知类型</li>
          <li>使用开关控制是否启用该类型通知</li>
          <li>设置Webhook地址（如果使用自定义群聊）</li>
          <li>配置推送时间（对于定时推送的通知）</li>
          <li>设置重试次数（推送失败时的重试策略）</li>
          <li>点击保存按钮应用配置</li>
        </ol>
        
        <el-alert
          title="重要：修改通知配置后会立即生效，请谨慎操作"
          type="warning"
          :closable="false"
          show-icon
          style="margin: 15px 0;"
        />
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>自定义消息模板</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>模板管理</h4>
        <p>系统支持自定义飞书群推送消息的模板，您可以：</p>
        <ul>
          <li>创建新的消息模板</li>
          <li>编辑现有模板内容</li>
          <li>设置模板变量和占位符</li>
          <li>预览模板渲染效果</li>
          <li>测试模板发送功能</li>
        </ul>
        
        <h4>模板变量</h4>
        <p>在模板中可以使用以下变量：</p>
        <ul>
          <li><code>{{date}}</code> - 当前日期</li>
          <li><code>{{time}}</code> - 当前时间</li>
          <li><code>{{totalOrders}}</code> - 总订单数</li>
          <li><code>{{totalRevenue}}</code> - 总收入</li>
          <li><code>{{topProduct}}</code> - 热门商品</li>
          <li><code>{{userName}}</code> - 用户姓名</li>
          <li><code>{{department}}</code> - 部门名称</li>
        </ul>
        
        <h4>创建模板步骤</h4>
        <ol>
          <li>进入系统设置 > 自定义消息模板</li>
          <li>点击"创建模板"按钮</li>
          <li>填写模板名称和描述</li>
          <li>选择模板类型（卡片消息或文本消息）</li>
          <li>编写模板内容，使用变量占位符</li>
          <li>添加模板变量定义</li>
          <li>预览模板效果</li>
          <li>保存模板</li>
        </ol>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>智能发送时间控制</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>时间控制功能</h4>
        <p>智能调度系统可以帮助您：</p>
        <ul>
          <li>避免在非工作时间发送消息</li>
          <li>控制消息发送频率，防止过度打扰</li>
          <li>设置节假日自动跳过规则</li>
          <li>为紧急消息设置例外规则</li>
        </ul>
        
        <h4>配置选项</h4>
        <ul>
          <li><strong>工作时间</strong>：设置允许发送消息的时间范围</li>
          <li><strong>工作日</strong>：选择哪些天可以发送消息</li>
          <li><strong>发送间隔</strong>：设置两次消息之间的最小间隔</li>
          <li><strong>节假日规则</strong>：配置节假日是否发送消息</li>
          <li><strong>紧急消息</strong>：设置哪些类型的消息可以忽略时间限制</li>
        </ul>
        
        <h4>最佳实践</h4>
        <ul>
          <li>将工作时间设置为9:00-18:00，避免打扰员工休息</li>
          <li>每日报告建议在下班前发送（如17:30）</li>
          <li>每周报告建议在周一上午发送</li>
          <li>紧急预警消息可以设置为不受时间限制</li>
        </ul>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>职场管理</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>职场信息管理</h4>
        <p>职场管理功能用于维护企业内部的组织架构信息：</p>
        <ul>
          <li>添加和编辑职场信息</li>
          <li>设置职场代码和描述</li>
          <li>管理职场状态（启用/禁用）</li>
          <li>关联用户到特定职场</li>
        </ul>
        
        <h4>操作步骤</h4>
        <ol>
          <li>进入系统设置，找到"职场管理"区域</li>
          <li>点击"添加职场"按钮</li>
          <li>填写职场名称、代码和描述</li>
          <li>设置职场状态</li>
          <li>保存职场信息</li>
          <li>在用户管理中可以为用户分配职场</li>
        </ol>
      </div>
    </el-card>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>高级诊断工具</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>诊断功能</h4>
        <p>高级诊断工具提供了系统健康检查和故障排查功能：</p>
        <ul>
          <li><strong>连接测试</strong>：测试飞书API连接状态</li>
          <li><strong>消息测试</strong>：发送测试消息到飞书群</li>
          <li><strong>数据库检查</strong>：检查数据库连接和数据完整性</li>
          <li><strong>日志查看</strong>：查看系统运行日志</li>
          <li><strong>性能监控</strong>：监控系统性能指标</li>
        </ul>
        
        <h4>使用方法</h4>
        <ol>
          <li>进入系统设置 > 高级诊断工具</li>
          <li>选择要执行的诊断项目</li>
          <li>点击"开始诊断"按钮</li>
          <li>查看诊断结果和建议</li>
          <li>根据建议进行系统优化</li>
        </ol>
        
        <el-alert
          title="建议定期使用诊断工具检查系统状态，及时发现和解决问题"
          type="info"
          :closable="false"
          show-icon
          style="margin: 15px 0;"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
// 通知配置数据
const notificationConfigs = [
  {
    name: '启用状态',
    description: '控制该类型通知是否启用',
    default_value: '启用'
  },
  {
    name: 'Webhook地址',
    description: '飞书群机器人的Webhook URL',
    default_value: '环境变量'
  },
  {
    name: '推送时间',
    description: '定时推送的具体时间',
    default_value: '19:00'
  },
  {
    name: '重试次数',
    description: '推送失败时的重试次数',
    default_value: '3次'
  },
  {
    name: '推送条件',
    description: '触发推送的条件设置',
    default_value: '默认条件'
  }
];
</script>

<style scoped>
.system-settings h2 {
  margin-bottom: 20px;
  font-weight: bold;
  color: #303133;
}

.help-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.card-content {
  line-height: 1.6;
}

.card-content h4 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-size: 16px;
  color: #303133;
}

.card-content p {
  margin-bottom: 12px;
  color: #606266;
}

.card-content ul, .card-content ol {
  padding-left: 20px;
  margin-bottom: 16px;
}

.card-content li {
  margin-bottom: 6px;
  color: #606266;
}

.info-block {
  background-color: #f0f9ff;
  border-left: 4px solid #409EFF;
  padding: 12px 16px;
  margin: 16px 0;
  border-radius: 4px;
}

code {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 2px 6px;
  font-family: monospace;
  color: #c41d7f;
}
</style>
