<template>
  <el-dialog
    :model-value="dialogVisible"
    @update:model-value="(val) => $emit('update:dialogVisible', val)"
    :title="product ? (product.name.includes('(') ? product.name.split('(')[0] + ' (' + product.name.split('(')[1] : product.name) : '商品详情'"
    :width="window.innerWidth <= 768 ? '90%' : '60%'"
    center
    :show-close="true"
    transition="dialog-fade"
    :duration="300"
    class="dialog-common"
  >
    <div v-if="product" class="product-detail-container">
      <div class="content-wrapper">
        <div class="left-section">
          <div class="product-image">
            <el-carousel height="400px" :interval="3000" arrow="hover" indicator-position="outside">
              <el-carousel-item v-for="(image, imageIndex) in product.images" :key="imageIndex">
                <img :src="fixImageUrl(image)" :alt="product.name" style="width: 100%; height: 100%; object-fit: cover; transition: transform 0.3s ease;" @click="$emit('preview-images', product.images, imageIndex)">
              </el-carousel-item>
            </el-carousel>
            <div class="hot-tag" v-if="product.isHot">爆款</div>
            <div class="new-tag" v-if="product.isNew">新品</div>
          </div>
        </div>
        <div class="right-section">
          <div class="product-info">
            <div class="ly-price">光年币：{{ product.lyPrice }}</div>
            <div class="rmb-price">人民币：{{ product.rmbPrice }}元</div>
            <div class="description">商品介绍：{{ product.description || '暂无介绍' }}</div>
          </div>
          <div class="dialog-footer">
            <el-button type="primary" @click="handleExchange(product)">申请兑换</el-button>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { defineProps, defineEmits, onMounted, onUnmounted, ref } from 'vue'
import { fixImageUrl } from '../utils/imageUtils'

const window = ref(globalThis.window)

onMounted(() => {
  window.value = globalThis.window
  window.value.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.value.removeEventListener('resize', handleResize)
})

const handleResize = () => {
  window.value = globalThis.window
}

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    required: true
  },
  product: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:dialogVisible', 'exchange', 'preview-images'])

const handleExchange = (product) => {
  emit('exchange', product)
  setTimeout(() => {
    emit('update:dialogVisible', false)
  }, 100)
}
</script>

<style scoped>
.product-detail-container {
  padding: 20px;
}

.content-wrapper {
  display: flex;
  gap: 24px;
  min-height: 400px;
}

@media screen and (max-width: 768px) {
  .content-wrapper {
    flex-direction: column;
    gap: 16px;
  }

  .product-detail-container {
    padding: 12px;
  }
}

.left-section {
  flex: 1;
  max-width: 100%;
}

@media screen and (min-width: 769px) {
  .left-section {
    max-width: 50%;
  }
}

.right-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px;
}

@media screen and (max-width: 768px) {
  .right-section {
    padding: 12px;
  }
}

.product-image {
  position: relative;
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-image img:hover {
  transform: scale(1.05);
}

.product-info {
  display: grid;
  grid-template-areas:
    "ly-price ly-price"
    "rmb-price rmb-price"
    "description description";
  gap: 12px;
  margin-bottom: 20px;
}

.product-info > div {
  margin: 0;
  padding: 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
  font-weight: 600;
}

.product-info .ly-price {
  grid-area: ly-price;
  color: #ff4d4f;
  background: linear-gradient(135deg, #fff5f5, #fff1f0);
  border: 2px solid #ffccc7;
  font-size: 20px;
  text-align: center;
}

.product-info .rmb-price {
  grid-area: rmb-price;
  color: #52c41a;
  background: linear-gradient(135deg, #f9ffe6, #f6ffed);
  border: 2px solid #b7eb8f;
}



.product-info .description {
  grid-area: description;
  color: #333;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border: 2px solid #e9ecef;
  white-space: pre-wrap;
}

.hot-tag,
.new-tag {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  color: white;
  z-index: 1;
}

.hot-tag {
  background: linear-gradient(45deg, #ff4d4f, #ff7875);
}

.new-tag {
  background: linear-gradient(45deg, #40c057, #69db7c);
}

:deep(.el-carousel__container) {
  border-radius: 16px;
  overflow: hidden;
}

:deep(.el-carousel__item) {
  border-radius: 16px;
}

:deep(.el-dialog) {
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(8px);
}

:deep(.el-dialog__header) {
  background: linear-gradient(135deg, #1a73e8, #34a853);
  margin: 0;
  padding: 32px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

:deep(.el-dialog__header)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
  pointer-events: none;
}

:deep(.el-dialog__title) {
  font-size: 32px;
  font-weight: 800;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  letter-spacing: 2px;
  position: relative;
  display: inline-block;
  padding: 0 24px;
  
  /* 响应式标题大小 */
  @media screen and (max-width: 768px) {
    font-size: 18px;
    padding: 0 10px;
    letter-spacing: 1px;
  }
  
  @media screen and (min-width: 769px) and (max-width: 1024px) {
    font-size: 24px;
    padding: 0 16px;
  }
}

:deep(.el-dialog__title)::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 3px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  border-radius: 2px;
}

:deep(.el-dialog__body) {
  padding: 32px;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
}

.dialog-footer {
  text-align: center;
  margin-top: 20px;
}

.dialog-footer .el-button {
  width: 240px;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #1a73e8, #4285f4);
  border: none;
  color: white;
  box-shadow: 0 4px 12px rgba(26, 115, 232, 0.2);
}

.dialog-footer .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(26, 115, 232, 0.3);
}
</style>