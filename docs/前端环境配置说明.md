# 前端环境配置说明

## 📋 概述

前端项目采用环境特定的配置文件管理方式，确保本地开发和生产环境配置完全分离，与后端配置保持一致。

## 🗂️ 配置文件结构

```
项目根目录/
├── .env                    # 本地开发环境配置
├── .env.production         # 生产环境配置
└── scripts/
    └── validate-frontend-config.cjs  # 前端配置验证工具
```

## 📝 配置文件详解

### 1. 本地开发环境配置 (`.env`)

**用途：** 本地开发时使用，Vite会自动加载此文件

**关键配置项：**
```env
# 前端API基础URL（指向本地后端服务）
VITE_API_URL=http://localhost:3000/api

# 应用标题
VITE_APP_TITLE=光年小卖部

# 应用基础路径
VITE_APP_BASE_URL=/

# 运行环境标识
VITE_NODE_ENV=development

# 后端服务器地址
VITE_SERVER_URL=http://localhost:3000

# 开发工具和调试
VITE_DEV_TOOLS=true
VITE_DEBUG=true
```

### 2. 生产环境配置 (`.env.production`)

**用途：** 生产环境构建时使用，`npm run build` 会自动加载此文件

**关键配置项：**
```env
# 前端API基础URL（指向生产环境后端服务）
# 选项1：通过Nginx代理访问(80端口) - 推荐
VITE_API_URL=http://**************/api
# 选项2：直接访问后端服务(3000端口)
# VITE_API_URL=http://**************:3000/api

# 应用标题
VITE_APP_TITLE=光年小卖部

# 运行环境标识
VITE_NODE_ENV=production

# 后端服务器地址
VITE_SERVER_URL=http://**************:3000

# 生产环境优化
VITE_DEV_TOOLS=false
VITE_DEBUG=false
VITE_ENABLE_GZIP=true
```

## 🔗 前后端配置对应关系

| 前端配置 | 后端配置 | 开发环境值 | 生产环境值 |
|----------|----------|------------|------------|
| `VITE_API_URL` | `SERVER_URL` + `/api` | `http://localhost:3000/api` | `http://**************/api` |
| `VITE_NODE_ENV` | `NODE_ENV` | `development` | `production` |
| `VITE_SERVER_URL` | `SERVER_URL` | `http://localhost:3000` | `http://**************:3000` |
| `VITE_FEISHU_APP_ID` | `FEISHU_APP_ID` | `cli_a66b3b2dcab8d013` | `cli_a66b3b2dcab8d013` |

## 🚀 使用方法

### 本地开发

```bash
# Vite自动加载 .env 文件
npm run dev
```

**自动加载：** `.env`

### 生产环境构建

```bash
# Vite自动加载 .env.production 文件
npm run build
```

**自动加载：** `.env.production`

### 预览生产构建

```bash
# 使用生产环境配置预览
npm run preview
```

## 🔍 配置验证

### 验证前端配置

```bash
# 验证前端配置文件完整性和一致性
node scripts/validate-frontend-config.cjs
```

### 验证前后端配置一致性

```bash
# 同时验证前后端配置
node scripts/validate-config.cjs
```

## 📊 配置项说明

### 必需配置项

| 配置项 | 说明 | 示例值 |
|--------|------|--------|
| `VITE_API_URL` | 后端API基础URL | `http://localhost:3000/api` |
| `VITE_APP_TITLE` | 应用标题 | `光年小卖部` |
| `VITE_APP_BASE_URL` | 应用基础路径 | `/` |
| `VITE_NODE_ENV` | 环境标识 | `development` / `production` |

### 可选配置项

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `VITE_DEV_TOOLS` | 是否启用开发工具 | `true` (开发) / `false` (生产) |
| `VITE_DEBUG` | 是否显示调试信息 | `true` (开发) / `false` (生产) |
| `VITE_API_TIMEOUT` | API请求超时时间 | `10000` (开发) / `15000` (生产) |
| `VITE_ENABLE_GZIP` | 是否启用Gzip压缩 | `true` (生产) |

## ⚠️ 注意事项

### 1. 配置项命名规范

- **前端配置项必须以 `VITE_` 开头**，这是Vite的要求
- 只有以 `VITE_` 开头的环境变量才会被暴露给前端代码

### 2. 敏感信息处理

- **前端配置文件中不要包含敏感信息**（如密钥、密码等）
- 前端配置会被打包到客户端代码中，任何人都可以查看
- 敏感信息应该放在后端配置中

### 3. API URL配置

生产环境有两种API URL配置方式：

**方式1：通过Nginx代理（推荐）**
```env
VITE_API_URL=http://**************/api
```
- 前端访问80端口，Nginx代理到后端3000端口
- 更好的性能和安全性

**方式2：直接访问后端**
```env
VITE_API_URL=http://**************:3000/api
```
- 前端直接访问后端3000端口
- 需要确保CORS配置正确

## 🛠️ 故障排查

### Q1: 前端无法访问后端API

**检查步骤：**
1. 确认 `VITE_API_URL` 配置正确
2. 检查后端服务是否正常运行
3. 验证CORS配置是否允许前端域名
4. 检查网络连接和防火墙设置

### Q2: 环境变量不生效

**可能原因：**
1. 配置项没有以 `VITE_` 开头
2. 修改配置后没有重启开发服务器
3. 配置文件语法错误

**解决方法：**
```bash
# 重启开发服务器
npm run dev

# 验证配置文件
node scripts/validate-frontend-config.cjs
```

### Q3: 生产环境构建失败

**检查步骤：**
1. 验证 `.env.production` 文件存在且配置正确
2. 检查配置项语法是否正确
3. 确认所有必需配置项都已设置

## 📈 最佳实践

### 1. 配置管理

- **保持同步**：前后端配置文件结构保持一致
- **定期验证**：使用验证工具检查配置正确性
- **文档更新**：配置变更时及时更新文档

### 2. 开发流程

- **本地开发**：使用 `.env` 配置
- **构建测试**：定期使用生产配置构建测试
- **部署前验证**：部署前运行配置验证工具

### 3. 安全考虑

- **敏感信息**：不在前端配置中存储敏感信息
- **环境隔离**：严格区分开发和生产环境配置
- **访问控制**：限制生产环境配置文件的访问权限

---

**总结：** 通过规范的前端环境配置管理，确保了开发和生产环境的配置一致性，提高了项目的可维护性和部署的可靠性。
