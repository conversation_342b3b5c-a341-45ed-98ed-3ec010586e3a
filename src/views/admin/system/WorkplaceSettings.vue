<template>
  <div class="workplace-settings">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <el-icon class="title-icon"><Location /></el-icon>
            职场管理
          </h1>
          <p class="page-description">管理系统中的职场信息和配置</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="showAddDialog = true">
            <el-icon><Plus /></el-icon>
            添加职场
          </el-button>
          <el-button @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 职场列表 -->
    <div class="workplace-list">
      <el-card>
        <template #header>
          <div class="list-header">
            <h3>职场列表</h3>
            <el-input
              v-model="searchKeyword"
              placeholder="搜索职场名称"
              style="width: 300px"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </template>

        <el-table
          :data="filteredWorkplaces"
          v-loading="loading"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="职场名称" min-width="200" />
          <el-table-column prop="address" label="地址" min-width="300" />
          <el-table-column prop="userCount" label="用户数量" width="120" align="center">
            <template #default="{ row }">
              <el-tag type="info">{{ row.userCount }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status === 'active' ? 'success' : 'warning'">
                {{ row.status === 'active' ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="创建时间" width="180" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="editWorkplace(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                size="small"
                :type="row.status === 'active' ? 'warning' : 'success'"
                @click="toggleStatus(row)"
              >
                {{ row.status === 'active' ? '禁用' : '启用' }}
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="deleteWorkplace(row)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 添加/编辑职场对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingWorkplace ? '编辑职场' : '添加职场'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="workplaceFormRef"
        :model="workplaceForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="职场名称" prop="name">
          <el-input
            v-model="workplaceForm.name"
            placeholder="请输入职场名称"
          />
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input
            v-model="workplaceForm.address"
            type="textarea"
            :rows="3"
            placeholder="请输入职场地址"
          />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input
            v-model="workplaceForm.phone"
            placeholder="请输入联系电话"
          />
        </el-form-item>
        <el-form-item label="负责人" prop="manager">
          <el-input
            v-model="workplaceForm.manager"
            placeholder="请输入负责人姓名"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="workplaceForm.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="workplaceForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveWorkplace" :loading="saving">
          {{ editingWorkplace ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 统计信息 -->
    <div class="stats-section">
      <el-row :gutter="24">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><OfficeBuilding /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.totalWorkplaces }}</div>
                <div class="stat-label">总职场数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon active">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.activeWorkplaces }}</div>
                <div class="stat-label">启用职场</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon users">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.totalUsers }}</div>
                <div class="stat-label">总用户数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon average">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.averageUsers }}</div>
                <div class="stat-label">平均用户数</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Location, Plus, Refresh, Search, Edit, Delete, Check,
  OfficeBuilding, User, TrendCharts
} from '@element-plus/icons-vue';

// 响应式数据
const loading = ref(false);
const saving = ref(false);
const showAddDialog = ref(false);
const editingWorkplace = ref(null);
const searchKeyword = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

const workplaces = ref([]);
const workplaceFormRef = ref();

const workplaceForm = reactive({
  name: '',
  address: '',
  phone: '',
  manager: '',
  status: 'active',
  remark: ''
});

const stats = reactive({
  totalWorkplaces: 0,
  activeWorkplaces: 0,
  totalUsers: 0,
  averageUsers: 0
});

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入职场名称', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入职场地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
};

// 计算属性
const filteredWorkplaces = computed(() => {
  if (!searchKeyword.value) return workplaces.value;
  return workplaces.value.filter(workplace =>
    workplace.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  );
});

// 方法
const refreshData = async () => {
  loading.value = true;
  try {
    await loadWorkplaces();
    await loadStats();
    ElMessage.success('数据刷新成功');
  } catch (error) {
    ElMessage.error('数据刷新失败');
  } finally {
    loading.value = false;
  }
};

const loadWorkplaces = async () => {
  try {
    // 模拟API调用
    const mockData = [
      {
        id: 1,
        name: '北京总部',
        address: '北京市朝阳区建国门外大街1号',
        phone: '13800138001',
        manager: '张三',
        userCount: 156,
        status: 'active',
        createdAt: '2024-01-15 10:30:00',
        remark: '公司总部'
      },
      {
        id: 2,
        name: '上海分公司',
        address: '上海市浦东新区陆家嘴金融中心',
        phone: '13800138002',
        manager: '李四',
        userCount: 89,
        status: 'active',
        createdAt: '2024-02-20 14:20:00',
        remark: '上海分公司'
      },
      {
        id: 3,
        name: '深圳研发中心',
        address: '深圳市南山区科技园',
        phone: '13800138003',
        manager: '王五',
        userCount: 67,
        status: 'inactive',
        createdAt: '2024-03-10 09:15:00',
        remark: '研发中心'
      }
    ];
    
    workplaces.value = mockData;
    total.value = mockData.length;
  } catch (error) {
    console.error('加载职场列表失败:', error);
  }
};

const loadStats = async () => {
  try {
    const totalWorkplaces = workplaces.value.length;
    const activeWorkplaces = workplaces.value.filter(w => w.status === 'active').length;
    const totalUsers = workplaces.value.reduce((sum, w) => sum + w.userCount, 0);
    const averageUsers = totalWorkplaces > 0 ? Math.round(totalUsers / totalWorkplaces) : 0;
    
    Object.assign(stats, {
      totalWorkplaces,
      activeWorkplaces,
      totalUsers,
      averageUsers
    });
  } catch (error) {
    console.error('加载统计数据失败:', error);
  }
};

const editWorkplace = (workplace) => {
  editingWorkplace.value = workplace;
  Object.assign(workplaceForm, workplace);
  showAddDialog.value = true;
};

const saveWorkplace = async () => {
  try {
    await workplaceFormRef.value.validate();
    
    saving.value = true;
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (editingWorkplace.value) {
      // 更新
      Object.assign(editingWorkplace.value, workplaceForm);
      ElMessage.success('职场信息更新成功');
    } else {
      // 添加
      const newWorkplace = {
        ...workplaceForm,
        id: Date.now(),
        userCount: 0,
        createdAt: new Date().toLocaleString()
      };
      workplaces.value.unshift(newWorkplace);
      ElMessage.success('职场添加成功');
    }
    
    showAddDialog.value = false;
    resetForm();
    await loadStats();
  } catch (error) {
    if (error !== false) { // 验证失败时返回false
      ElMessage.error('保存失败');
    }
  } finally {
    saving.value = false;
  }
};

const toggleStatus = async (workplace) => {
  try {
    const newStatus = workplace.status === 'active' ? 'inactive' : 'active';
    const action = newStatus === 'active' ? '启用' : '禁用';
    
    await ElMessageBox.confirm(`确定要${action}该职场吗？`, '确认操作', {
      type: 'warning'
    });
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    workplace.status = newStatus;
    await loadStats();
    ElMessage.success(`职场${action}成功`);
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败');
    }
  }
};

const deleteWorkplace = async (workplace) => {
  try {
    await ElMessageBox.confirm('确定要删除该职场吗？此操作不可恢复！', '确认删除', {
      type: 'warning'
    });
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    const index = workplaces.value.findIndex(w => w.id === workplace.id);
    if (index > -1) {
      workplaces.value.splice(index, 1);
      total.value--;
    }
    await loadStats();
    ElMessage.success('职场删除成功');
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};

const resetForm = () => {
  Object.assign(workplaceForm, {
    name: '',
    address: '',
    phone: '',
    manager: '',
    status: 'active',
    remark: ''
  });
  editingWorkplace.value = null;
  workplaceFormRef.value?.clearValidate();
};

const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
};

// 生命周期
onMounted(() => {
  loadWorkplaces().then(() => loadStats());
});
</script>

<style scoped>
.workplace-settings {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  color: #3b82f6;
}

.page-description {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

.workplace-list {
  margin-bottom: 24px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.pagination-wrapper {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.stats-section .stat-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3b82f6;
  color: white;
  font-size: 24px;
}

.stat-icon.active {
  background: #10b981;
}

.stat-icon.users {
  background: #f59e0b;
}

.stat-icon.average {
  background: #8b5cf6;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  color: #6b7280;
  font-size: 14px;
}
</style>
