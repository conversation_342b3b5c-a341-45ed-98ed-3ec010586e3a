/**
 * 前端库存数据调试脚本
 * 在浏览器控制台中运行此脚本来检查前端接收到的数据
 */

// 调试函数：检查哪吒捏捏乐的库存数据
async function debugNezhaStock() {
  console.log('🔍 开始调试哪吒捏捏乐库存数据...');
  
  try {
    // 获取当前页面的token
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    if (!token) {
      console.error('❌ 未找到认证token，请先登录');
      return;
    }
    
    console.log('✅ 找到认证token');
    
    // 调用API获取商品数据
    const response = await fetch('/api/products?includeWorkplaceStocks=true&limit=50', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      console.error('❌ API调用失败:', response.status, response.statusText);
      return;
    }
    
    const data = await response.json();
    console.log('✅ API调用成功');
    
    // 查找哪吒捏捏乐商品
    const products = data.data || data;
    const nezhaProduct = products.find(p => p.name.includes('哪吒捏捏乐'));
    
    if (!nezhaProduct) {
      console.error('❌ 未找到哪吒捏捏乐商品');
      console.log('📋 可用商品列表:', products.map(p => ({ id: p.id, name: p.name })));
      return;
    }
    
    console.log('✅ 找到哪吒捏捏乐商品');
    console.log('📦 商品详细信息:', {
      id: nezhaProduct.id,
      name: nezhaProduct.name,
      stockManagementType: nezhaProduct.stockManagementType,
      stock: nezhaProduct.stock,
      totalAvailableStock: nezhaProduct.totalAvailableStock,
      workplaceStocks: nezhaProduct.workplaceStocks
    });
    
    // 检查职场库存数据
    if (nezhaProduct.workplaceStocks && nezhaProduct.workplaceStocks.length > 0) {
      console.log('🏢 职场库存分布:');
      nezhaProduct.workplaceStocks.forEach(stock => {
        console.log(`- ${stock.workplaceName} (ID: ${stock.workplaceId}): ${stock.availableStock} (库存: ${stock.stock}, 预留: ${stock.reservedStock})`);
      });
      
      // 检查是否包含武汉职场
      const wuhanStock = nezhaProduct.workplaceStocks.find(s => s.workplaceName === '武汉');
      if (wuhanStock) {
        console.log('✅ 武汉职场数据存在:', wuhanStock);
      } else {
        console.log('❌ 武汉职场数据不存在');
      }
    } else {
      console.log('❌ 没有职场库存数据');
    }
    
    // 检查Vue组件数据（如果在Vue页面中）
    if (window.Vue && window.__VUE_DEVTOOLS_GLOBAL_HOOK__) {
      console.log('🔧 检查Vue组件数据...');
      // 这里可以添加Vue组件数据检查逻辑
    }
    
    return nezhaProduct;
    
  } catch (error) {
    console.error('❌ 调试过程中出现错误:', error);
  }
}

// 调试函数：强制刷新页面数据
async function forceRefreshStockData() {
  console.log('🔄 强制刷新库存数据...');
  
  // 清除可能的缓存
  if ('caches' in window) {
    const cacheNames = await caches.keys();
    for (const cacheName of cacheNames) {
      await caches.delete(cacheName);
    }
    console.log('✅ 清除了浏览器缓存');
  }
  
  // 如果在Vue页面中，尝试触发数据刷新
  if (window.Vue) {
    console.log('🔧 尝试触发Vue数据刷新...');
    // 可以在这里添加特定的Vue数据刷新逻辑
  }
  
  // 建议用户手动刷新页面
  console.log('💡 建议执行以下操作:');
  console.log('1. 按 Ctrl+F5 (Windows) 或 Cmd+Shift+R (Mac) 强制刷新页面');
  console.log('2. 或者运行: location.reload(true)');
}

// 调试函数：检查页面元素
function checkPageElements() {
  console.log('🔍 检查页面元素...');
  
  // 查找哪吒捏捏乐的表格行
  const tableRows = document.querySelectorAll('table tbody tr');
  console.log(`📋 找到 ${tableRows.length} 个表格行`);
  
  for (let i = 0; i < tableRows.length; i++) {
    const row = tableRows[i];
    const nameCell = row.querySelector('td:first-child');
    if (nameCell && nameCell.textContent.includes('哪吒捏捏乐')) {
      console.log('✅ 找到哪吒捏捏乐的表格行:', row);
      
      // 查找职场分布列
      const workplaceCell = row.querySelector('.workplace-stocks');
      if (workplaceCell) {
        console.log('🏢 职场分布元素:', workplaceCell);
        console.log('📄 职场分布HTML:', workplaceCell.innerHTML);
        
        const workplaceItems = workplaceCell.querySelectorAll('.workplace-stock-item');
        console.log(`📊 找到 ${workplaceItems.length} 个职场库存项`);
        
        workplaceItems.forEach((item, index) => {
          const name = item.querySelector('.workplace-name')?.textContent;
          const amount = item.querySelector('.stock-amount')?.textContent;
          console.log(`- 职场 ${index + 1}: ${name} = ${amount}`);
        });
      } else {
        console.log('❌ 未找到职场分布元素');
      }
      
      break;
    }
  }
}

// 导出调试函数到全局
window.debugNezhaStock = debugNezhaStock;
window.forceRefreshStockData = forceRefreshStockData;
window.checkPageElements = checkPageElements;

console.log('🚀 库存调试脚本已加载！');
console.log('📋 可用的调试函数:');
console.log('- debugNezhaStock(): 检查API返回的哪吒捏捏乐数据');
console.log('- forceRefreshStockData(): 强制刷新数据');
console.log('- checkPageElements(): 检查页面元素');
console.log('');
console.log('💡 使用方法: 在控制台中输入函数名并执行，例如: debugNezhaStock()');
