# 生产环境移动端飞书登录问题修复方案

## 问题描述

**现象：** 生产环境移动端用户点击"用飞书登录"后，被重定向到后端API地址（**************:3000），显示 `{"message":"Welcome to Exchange Mall API"}` 消息，无法正确跳转回前端首页。

**影响范围：** 仅影响移动端飞书登录，桌面端功能正常。

## 根因分析

### 1. 飞书回调URL配置问题
- 当前回调URL直接指向后端API：`http://**************:3000/api/feishu/callback`
- 移动端用户在飞书授权后被重定向到此API地址

### 2. 移动端登录流程问题
- 移动端使用 `same-window` 策略，直接在当前窗口跳转
- 回调处理返回HTML页面，依赖JavaScript重定向
- 移动端浏览器可能不执行或延迟执行JavaScript代码

### 3. 设备检测缺失
- 后端回调处理没有区分移动端和桌面端
- 所有设备都使用相同的HTML响应方式

## 修复方案

### 1. 后端修复 - 飞书回调处理优化

**文件：** `server/controllers/feishuController.js`

**修改内容：**
- 添加移动端设备检测
- 移动端直接服务器端重定向到前端页面
- 桌面端保持原有HTML+JavaScript处理方式

```javascript
// 检测是否为移动端请求
const userAgent = req.headers['user-agent'] || '';
const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);

// 移动端直接重定向到前端页面，携带token参数
if (isMobile) {
  const frontendUrl = process.env.NODE_ENV === 'production' 
    ? 'http://**************' 
    : 'http://localhost:5173';
  
  const redirectUrl = `${frontendUrl}/?feishu_login=success&token=${encodeURIComponent(token)}&user=${encodeURIComponent(JSON.stringify(userForToken))}`;
  
  return res.redirect(redirectUrl);
}
```

### 2. 前端修复 - 处理登录回调参数

**文件：** `src/views/Home.vue`

**修改内容：**
- 在页面加载时检测URL参数
- 处理飞书登录成功回调
- 自动存储token和用户信息

```javascript
// 处理飞书登录回调（移动端）
const urlParams = new URLSearchParams(window.location.search);
if (urlParams.get('feishu_login') === 'success') {
  const token = urlParams.get('token');
  const userStr = urlParams.get('user');
  
  if (token && userStr) {
    const user = JSON.parse(decodeURIComponent(userStr));
    
    // 存储token和用户信息
    sessionStorage.setItem('token', token);
    sessionStorage.setItem('user', JSON.stringify(user));
    
    // 更新auth store
    authStore.token = token;
    authStore.user = user;
    
    // 清理URL参数
    const cleanUrl = window.location.origin + window.location.pathname;
    window.history.replaceState({}, document.title, cleanUrl);
  }
}
```

### 3. 环境配置更新

**文件：** `server/.env`

**修改内容：**
- 更新为生产环境配置
- 添加生产环境CORS支持

```env
NODE_ENV=production
SERVER_URL=http://**************:3000
FEISHU_REDIRECT_URI=http://**************:3000/api/feishu/callback
CORS_ORIGIN=http://**************,http://**************:80,http://localhost:5173
```

### 4. 移动端登录策略优化

**文件：** `src/components/FeishuLogin.vue`

**修改内容：**
- 改进移动端用户体验
- 添加友好的跳转提示

## 部署步骤

### 自动部署（推荐）

```bash
# 执行部署脚本
./deploy-mobile-feishu-fix.sh
```

### 手动部署

1. **连接生产服务器**
```bash
ssh root@**************
# 密码: Aa@123456
```

2. **更新代码**
```bash
cd /www/wwwroot/workyy
git fetch origin
git checkout feat/reset
git pull origin feat/reset
```

3. **更新环境配置**
```bash
cd server
# 备份原配置
cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
# 更新为生产环境配置（参考上述配置内容）
```

4. **重启服务**
```bash
npm install --production
pm2 restart feishu-mall-api
pm2 status
```

## 测试验证

### 移动端测试
1. 使用手机浏览器访问：`http://**************`
2. 点击"用飞书登录"按钮
3. 完成飞书授权
4. **预期结果：** 自动跳转回应用首页，显示登录成功

### 桌面端回归测试
1. 使用桌面浏览器访问：`http://**************`
2. 点击"用飞书登录"按钮
3. 在弹出窗口中完成飞书授权
4. **预期结果：** 弹窗关闭，主页面显示登录成功

## 监控和排查

### 检查应用状态
```bash
pm2 status
pm2 logs feishu-mall-api --lines 20
```

### 检查关键日志
- 飞书回调处理日志
- 设备类型检测日志
- 重定向URL生成日志

### 常见问题排查

1. **移动端仍然显示API消息**
   - 检查User-Agent检测逻辑
   - 确认环境变量配置正确

2. **桌面端登录异常**
   - 检查HTML响应是否正常
   - 确认JavaScript执行无误

3. **CORS错误**
   - 检查CORS_ORIGIN配置
   - 确认前端域名在白名单中

## 技术要点

### 设备检测策略
- 基于User-Agent字符串检测
- 支持主流移动设备和浏览器
- 兼容性良好，误判率低

### 安全考虑
- Token通过URL参数传递（仅移动端）
- 前端立即清理URL参数
- 保持原有的安全机制

### 兼容性保证
- 桌面端逻辑完全不变
- 移动端新增处理逻辑
- 向后兼容，无破坏性变更

## 总结

本次修复通过以下方式解决了移动端飞书登录问题：

1. **服务器端重定向**：移动端直接重定向到前端页面
2. **参数传递**：通过URL参数安全传递登录信息
3. **前端处理**：自动处理回调参数并清理URL
4. **设备区分**：精确区分移动端和桌面端处理逻辑

修复后，移动端用户可以正常使用飞书登录功能，桌面端功能保持不变。
