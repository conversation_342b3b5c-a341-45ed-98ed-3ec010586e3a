---
description:
globs:
alwaysApply: false
---
# 光年小卖部代码规范

## 代码规范文档
项目的详细代码规范定义在[docs/code-standard.md](mdc:docs/code-standard.md)文件中。所有开发人员必须遵循这些规范。

## ESLint配置
- 前端ESLint配置：[eslint.config.js](mdc:eslint.config.js)
- 后端ESLint配置：[server/eslint.config.js](mdc:server/eslint.config.js)
- 前端Vue特定规则：[client/.eslintrc.js](mdc:client/.eslintrc.js)

## 编码风格

### 通用规则
- 使用UTF-8编码
- 使用LF（\n）作为行结束符
- 缩进使用2个空格
- 使用单引号`'`而非双引号`"`
- 语句末尾必须有分号
- 文件末尾应有一个空行
- 变量命名使用驼峰命名法
- 常量使用全大写，单词间用下划线分隔

### 前端规范
- 组件名称使用PascalCase格式
- 组件事件名称使用kebab-case格式
- 使用ES6+语法
- 使用`const`和`let`替代`var`
- 优先使用箭头函数
- 使用async/await处理异步

### 后端规范
- 遵循MVC架构模式
- 使用RESTful风格设计API
- 使用try-catch捕获异步错误
- 统一错误处理中间件
- API接口必须有完整的Swagger文档注释

## VSCode配置
项目包含VSCode配置，支持自动格式化和ESLint检查：[.vscode/settings.json](mdc:.vscode/settings.json)

## 代码检查命令
- 前端代码检查：`npm run lint`
- 前端代码自动修复：`npm run lint:fix`
- 后端代码检查：`cd server && npm run lint`
- 后端代码自动修复：`cd server && npm run lint:fix`
