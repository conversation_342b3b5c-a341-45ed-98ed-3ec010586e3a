---
description:
globs:
alwaysApply: false
---
# 光年小卖部文档规范

## 文档目录
项目文档存放在[docs](mdc:docs)目录中。

## 主要文档
- [README.md](mdc:docs/README.md): 文档目录和概述
- [code-standard.md](mdc:docs/code-standard.md): 代码规范

## 文档格式
- 使用Markdown格式
- 结构清晰，使用标题层级
- 包含目录和索引
- 代码示例使用代码块并指定语言

## API文档
- API文档使用Swagger自动生成
- 配置文件：[server/config/swagger.js](mdc:server/config/swagger.js)
- 访问地址：`http://localhost:3000/api-docs`

## 文档内容要求

### README
- 项目简介
- 技术栈
- 安装步骤
- 运行说明
- 目录结构
- 主要功能

### 代码规范
- 命名规范
- 注释规范
- 编码规范
- 代码结构
- 版本控制
- 测试规范

### API文档
- 接口描述
- 请求方法
- 参数说明
- 响应格式
- 错误码
- 示例请求

### 部署文档
- 环境要求
- 依赖安装
- 配置说明
- 部署步骤
- 常见问题

## 文档维护
- 代码变更时同步更新相关文档
- 定期检查文档有效性
- 将文档纳入版本控制

## 文档责任
- 每个开发人员负责维护其开发模块的文档
- 技术负责人审核文档质量
- 重要更新需通知团队成员
