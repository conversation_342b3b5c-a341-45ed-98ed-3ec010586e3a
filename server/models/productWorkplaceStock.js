const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 商品职场库存模型
 * 管理每个商品在各个职场的库存分配
 */
const ProductWorkplaceStock = sequelize.define('ProductWorkplaceStock', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },
  productId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'productId',
    comment: '商品ID'
  },
  workplaceId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'workplaceId',
    comment: '职场ID'
  },
  stock: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0
    },
    comment: '职场库存数量'
  },
  reservedStock: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    field: 'reservedStock',
    validate: {
      min: 0
    },
    comment: '预留库存（待处理订单占用）'
  },
  availableStock: {
    type: DataTypes.VIRTUAL,
    get() {
      return this.stock - this.reservedStock;
    },
    comment: '可用库存（虚拟字段）'
  },
  minStockAlert: {
    type: DataTypes.INTEGER,
    defaultValue: 10,
    field: 'minStockAlert',
    validate: {
      min: 0
    },
    comment: '最低库存告警阈值'
  },
  maxStockLimit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'maxStockLimit',
    validate: {
      min: 1
    },
    comment: '最大库存限制'
  },
  lastStockUpdate: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'lastStockUpdate',
    comment: '最后库存更新时间'
  }
}, {
  tableName: 'product_workplace_stocks',
  timestamps: true,
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  indexes: [
    {
      unique: true,
      fields: ['productId', 'workplaceId'],
      name: 'uk_product_workplace'
    },
    {
      fields: ['productId'],
      name: 'idx_product_id'
    },
    {
      fields: ['workplaceId'],
      name: 'idx_workplace_id'
    },
    {
      fields: ['stock'],
      name: 'idx_stock'
    },
    {
      fields: ['lastStockUpdate'],
      name: 'idx_last_update'
    }
  ],
  comment: '商品职场库存表 - 管理每个商品在各个职场的库存分配'
});

/**
 * 实例方法：检查是否库存不足
 */
ProductWorkplaceStock.prototype.isLowStock = function() {
  return this.availableStock <= this.minStockAlert;
};

/**
 * 实例方法：检查是否可以扣减指定数量
 */
ProductWorkplaceStock.prototype.canDeduct = function(quantity) {
  return this.availableStock >= quantity;
};

/**
 * 实例方法：更新库存并记录最后更新时间
 */
ProductWorkplaceStock.prototype.updateStock = function(newStock, transaction = null) {
  this.stock = newStock;
  this.lastStockUpdate = new Date();
  return this.save({ transaction });
};

/**
 * 类方法：获取商品的总库存（所有职场之和）
 */
ProductWorkplaceStock.getTotalStockByProduct = async function(productId, transaction = null) {
  const queryOptions = {
    where: { productId },
    attributes: [
      [sequelize.fn('SUM', sequelize.col('stock')), 'totalStock'],
      [sequelize.fn('SUM', sequelize.col('reservedStock')), 'totalReservedStock']
    ],
    raw: true
  };

  if (transaction) {
    queryOptions.transaction = transaction;
  }

  const result = await this.findAll(queryOptions);

  return {
    totalStock: parseInt(result[0].totalStock) || 0,
    totalReservedStock: parseInt(result[0].totalReservedStock) || 0,
    totalAvailableStock: (parseInt(result[0].totalStock) || 0) - (parseInt(result[0].totalReservedStock) || 0)
  };
};

/**
 * 类方法：获取职场的库存统计
 */
ProductWorkplaceStock.getWorkplaceStockSummary = async function(workplaceId) {
  const result = await this.findAll({
    where: { workplaceId },
    attributes: [
      [sequelize.fn('COUNT', sequelize.col('id')), 'productCount'],
      [sequelize.fn('SUM', sequelize.col('stock')), 'totalStock'],
      [sequelize.fn('SUM', sequelize.col('reservedStock')), 'totalReservedStock'],
      [sequelize.fn('COUNT', sequelize.literal('CASE WHEN stock <= minStockAlert THEN 1 END')), 'lowStockCount']
    ],
    raw: true
  });
  
  return {
    productCount: parseInt(result[0].productCount) || 0,
    totalStock: parseInt(result[0].totalStock) || 0,
    totalReservedStock: parseInt(result[0].totalReservedStock) || 0,
    totalAvailableStock: (parseInt(result[0].totalStock) || 0) - (parseInt(result[0].totalReservedStock) || 0),
    lowStockCount: parseInt(result[0].lowStockCount) || 0
  };
};

module.exports = ProductWorkplaceStock;
