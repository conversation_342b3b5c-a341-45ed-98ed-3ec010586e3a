# 活动促销功能数据库设计

## 1. 数据表设计

### 1.1 活动表 (promotions)

```sql
CREATE TABLE promotions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL COMMENT '活动名称',
  description TEXT COMMENT '活动描述',
  type ENUM('discount', 'full_reduction', 'limited_time', 'bundle', 'points_multiplier') NOT NULL COMMENT '活动类型',
  status ENUM('draft', 'active', 'paused', 'ended', 'cancelled') DEFAULT 'draft' COMMENT '活动状态',
  
  -- 时间设置
  start_time DATETIME NOT NULL COMMENT '活动开始时间',
  end_time DATETIME NOT NULL COMMENT '活动结束时间',
  
  -- 参与限制
  max_participants INT DEFAULT NULL COMMENT '最大参与人数，NULL表示无限制',
  max_uses_per_user INT DEFAULT 1 COMMENT '每用户最大使用次数',
  current_participants INT DEFAULT 0 COMMENT '当前参与人数',
  
  -- 活动规则配置 (JSON格式存储)
  rules JSON NOT NULL COMMENT '活动规则配置',
  
  -- 适用范围
  applicable_products JSON COMMENT '适用商品ID列表，NULL表示全部商品',
  applicable_categories JSON COMMENT '适用分类ID列表',
  
  -- 显示设置
  banner_image VARCHAR(500) COMMENT '活动横幅图片',
  priority INT DEFAULT 0 COMMENT '显示优先级，数值越大优先级越高',
  is_featured BOOLEAN DEFAULT FALSE COMMENT '是否为精选活动',
  
  -- 创建信息
  created_by INT NOT NULL COMMENT '创建者ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- 外键约束
  FOREIGN KEY (created_by) REFERENCES users(id),
  
  -- 索引
  INDEX idx_status_time (status, start_time, end_time),
  INDEX idx_type (type),
  INDEX idx_priority (priority DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动促销表';
```

### 1.2 活动参与记录表 (promotion_participations)

```sql
CREATE TABLE promotion_participations (
  id INT AUTO_INCREMENT PRIMARY KEY,
  promotion_id INT NOT NULL COMMENT '活动ID',
  user_id INT NOT NULL COMMENT '用户ID',
  exchange_id INT COMMENT '关联的订单ID',
  
  -- 参与详情
  participation_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '参与时间',
  discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额',
  original_amount DECIMAL(10,2) NOT NULL COMMENT '原始金额',
  final_amount DECIMAL(10,2) NOT NULL COMMENT '最终金额',
  
  -- 使用的规则
  applied_rule JSON COMMENT '应用的具体规则',
  
  -- 状态
  status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '参与状态',
  
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- 外键约束
  FOREIGN KEY (promotion_id) REFERENCES promotions(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (exchange_id) REFERENCES exchanges(id),
  
  -- 索引
  INDEX idx_promotion_user (promotion_id, user_id),
  INDEX idx_user_time (user_id, participation_time),
  INDEX idx_exchange (exchange_id),
  
  -- 唯一约束（防止重复参与同一活动的同一订单）
  UNIQUE KEY unique_promotion_exchange (promotion_id, exchange_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动参与记录表';
```

### 1.3 活动商品关联表 (promotion_products)

```sql
CREATE TABLE promotion_products (
  id INT AUTO_INCREMENT PRIMARY KEY,
  promotion_id INT NOT NULL COMMENT '活动ID',
  product_id INT NOT NULL COMMENT '商品ID',
  
  -- 商品特定设置
  discount_type ENUM('percentage', 'fixed_amount', 'special_price') COMMENT '折扣类型',
  discount_value DECIMAL(10,2) COMMENT '折扣值',
  special_price DECIMAL(10,2) COMMENT '活动特价',
  
  -- 库存限制
  promotion_stock INT COMMENT '活动库存限制',
  used_stock INT DEFAULT 0 COMMENT '已使用库存',
  
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- 外键约束
  FOREIGN KEY (promotion_id) REFERENCES promotions(id) ON DELETE CASCADE,
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
  
  -- 索引
  INDEX idx_promotion (promotion_id),
  INDEX idx_product (product_id),
  
  -- 唯一约束
  UNIQUE KEY unique_promotion_product (promotion_id, product_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动商品关联表';
```

### 1.4 活动统计表 (promotion_statistics)

```sql
CREATE TABLE promotion_statistics (
  id INT AUTO_INCREMENT PRIMARY KEY,
  promotion_id INT NOT NULL COMMENT '活动ID',
  
  -- 统计日期
  stat_date DATE NOT NULL COMMENT '统计日期',
  
  -- 参与数据
  daily_participants INT DEFAULT 0 COMMENT '当日参与人数',
  daily_orders INT DEFAULT 0 COMMENT '当日订单数',
  daily_revenue DECIMAL(12,2) DEFAULT 0.00 COMMENT '当日收入',
  daily_discount DECIMAL(12,2) DEFAULT 0.00 COMMENT '当日优惠金额',
  
  -- 累计数据
  total_participants INT DEFAULT 0 COMMENT '累计参与人数',
  total_orders INT DEFAULT 0 COMMENT '累计订单数',
  total_revenue DECIMAL(12,2) DEFAULT 0.00 COMMENT '累计收入',
  total_discount DECIMAL(12,2) DEFAULT 0.00 COMMENT '累计优惠金额',
  
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- 外键约束
  FOREIGN KEY (promotion_id) REFERENCES promotions(id) ON DELETE CASCADE,
  
  -- 索引
  INDEX idx_promotion_date (promotion_id, stat_date),
  
  -- 唯一约束
  UNIQUE KEY unique_promotion_date (promotion_id, stat_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动统计表';
```

## 2. 活动规则配置说明

### 2.1 规则配置JSON结构

```json
{
  "type": "discount",
  "config": {
    "discount_type": "percentage",
    "discount_value": 20,
    "min_amount": 100,
    "max_discount": 50
  }
}
```

### 2.2 不同活动类型的规则配置

#### 折扣活动 (discount)
```json
{
  "type": "discount",
  "config": {
    "discount_type": "percentage|fixed_amount",
    "discount_value": 20,
    "min_amount": 0,
    "max_discount": null
  }
}
```

#### 满减活动 (full_reduction)
```json
{
  "type": "full_reduction",
  "config": {
    "tiers": [
      {"min_amount": 100, "reduction": 10},
      {"min_amount": 200, "reduction": 25},
      {"min_amount": 500, "reduction": 80}
    ]
  }
}
```

#### 限时优惠 (limited_time)
```json
{
  "type": "limited_time",
  "config": {
    "discount_type": "percentage",
    "discount_value": 30,
    "time_slots": [
      {"start": "09:00", "end": "12:00"},
      {"start": "18:00", "end": "21:00"}
    ]
  }
}
```

#### 套餐优惠 (bundle)
```json
{
  "type": "bundle",
  "config": {
    "required_products": [1, 2, 3],
    "bundle_price": 150,
    "discount_type": "fixed_price"
  }
}
```

#### 积分倍数 (points_multiplier)
```json
{
  "type": "points_multiplier",
  "config": {
    "multiplier": 2,
    "max_points": 1000,
    "applicable_payment": "ly"
  }
}
```

## 3. 数据关联关系

- **promotions** ↔ **promotion_participations**: 一对多
- **promotions** ↔ **promotion_products**: 一对多  
- **promotions** ↔ **promotion_statistics**: 一对多
- **users** ↔ **promotion_participations**: 一对多
- **products** ↔ **promotion_products**: 一对多
- **exchanges** ↔ **promotion_participations**: 一对一

## 4. 索引优化策略

1. **查询活跃活动**: `idx_status_time`
2. **用户参与历史**: `idx_user_time`
3. **活动效果分析**: `idx_promotion_date`
4. **商品促销查询**: `idx_product`
