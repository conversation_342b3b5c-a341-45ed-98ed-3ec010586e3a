-- =====================================================
-- 光年小卖部 - 生产环境数据库初始化脚本
-- 版本: 2.0
-- 创建时间: 2025-07-17
-- 描述: 完整的数据库结构初始化，包含所有表、索引、外键和初始数据
-- =====================================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS feishu_mall
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE feishu_mall;

-- 设置会话参数
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';
SET AUTOCOMMIT = 0;
START TRANSACTION;

-- =====================================================
-- 1. 基础数据表 (按依赖关系顺序创建)
-- =====================================================

-- 1.1 职场表 (workplaces) - 被用户表和订单表引用
DROP TABLE IF EXISTS workplaces;
CREATE TABLE workplaces (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '职场ID',
    name VARCHAR(255) NOT NULL COMMENT '职场名称',
    code VARCHAR(255) NOT NULL COMMENT '职场代码',
    description VARCHAR(255) DEFAULT NULL COMMENT '职场描述',
    isActive TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用：1-启用，0-禁用',
    createdAt DATETIME NOT NULL COMMENT '创建时间',
    updatedAt DATETIME NOT NULL COMMENT '更新时间',

    UNIQUE KEY uk_workplace_name (name),
    UNIQUE KEY uk_workplace_code (code),
    INDEX idx_workplace_active (isActive),
    INDEX idx_workplace_created (createdAt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='职场信息表 - 存储各个工作地点的基本信息';

-- 1.2 用户表 (users) - 系统核心用户信息
DROP TABLE IF EXISTS users;
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(255) NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密存储）',
    role ENUM('admin', 'user') NOT NULL DEFAULT 'user' COMMENT '用户角色：admin-管理员，user-普通用户',
    email VARCHAR(255) NOT NULL COMMENT '邮箱地址',
    mobile VARCHAR(255) DEFAULT NULL COMMENT '手机号码',
    department VARCHAR(255) DEFAULT NULL COMMENT '部门名称',
    departmentPath VARCHAR(255) DEFAULT NULL COMMENT '完整部门路径，如"公司/技术部/后端组"',
    workplace VARCHAR(255) DEFAULT NULL COMMENT '职场名称（旧字段，保留兼容性）',
    workplaceId INT DEFAULT NULL COMMENT '关联职场表ID',
    authType ENUM('password', 'feishu') NOT NULL DEFAULT 'password' COMMENT '认证类型：password-密码认证，feishu-飞书认证',
    feishuOpenId VARCHAR(255) DEFAULT NULL COMMENT '飞书OpenID',
    feishuUnionId VARCHAR(255) DEFAULT NULL COMMENT '飞书UnionID',
    feishuUserId VARCHAR(255) DEFAULT NULL COMMENT '飞书UserID',
    feishuAvatar VARCHAR(255) DEFAULT NULL COMMENT '飞书头像URL',
    feishuAccessToken VARCHAR(255) DEFAULT NULL COMMENT '飞书访问令牌',
    feishuRefreshToken VARCHAR(255) DEFAULT NULL COMMENT '飞书刷新令牌',
    feishuTokenExpireTime DATETIME DEFAULT NULL COMMENT '飞书令牌过期时间',
    points INT NOT NULL DEFAULT 0 COMMENT '用户光年币数量',
    lastLoginAt DATETIME DEFAULT NULL COMMENT '最后登录时间',
    lastLoginIp VARCHAR(255) DEFAULT NULL COMMENT '最后登录IP地址',
    isActive TINYINT(1) NOT NULL DEFAULT 1 COMMENT '账户状态：1-激活，0-禁用',
    createdAt DATETIME NOT NULL COMMENT '创建时间',
    updatedAt DATETIME NOT NULL COMMENT '更新时间',

    UNIQUE KEY uk_user_email (email),
    INDEX idx_user_username (username),
    INDEX idx_user_role (role),
    INDEX idx_user_department (department),
    INDEX idx_user_workplace (workplaceId),
    INDEX idx_user_auth_type (authType),
    INDEX idx_user_feishu_open_id (feishuOpenId),
    INDEX idx_user_active (isActive),
    INDEX idx_user_created (createdAt),

    CONSTRAINT fk_user_workplace FOREIGN KEY (workplaceId) REFERENCES workplaces(id) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='用户信息表 - 存储系统用户的基本信息和认证数据';

-- 1.3 商品分类表 (categories) - 被商品表引用
DROP TABLE IF EXISTS categories;
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '分类ID',
    name VARCHAR(255) NOT NULL COMMENT '分类名称',
    description TEXT DEFAULT NULL COMMENT '分类描述',
    sortOrder INT NOT NULL DEFAULT 0 COMMENT '排序顺序，数值越小越靠前',
    createdAt DATETIME NOT NULL COMMENT '创建时间',
    updatedAt DATETIME NOT NULL COMMENT '更新时间',

    UNIQUE KEY uk_category_name (name),
    INDEX idx_category_sort (sortOrder),
    INDEX idx_category_created (createdAt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='商品分类表 - 存储商品的分类信息，用于商品归类管理';

-- 1.4 商品表 (products) - 核心商品信息
DROP TABLE IF EXISTS products;
CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '商品ID',
    name VARCHAR(255) NOT NULL COMMENT '商品名称',
    categoryId INT NOT NULL COMMENT '关联分类ID',
    lyPrice INT NOT NULL COMMENT '光年币价格',
    rmbPrice DECIMAL(10,2) NOT NULL COMMENT '人民币价格',
    description TEXT DEFAULT NULL COMMENT '商品详细描述',
    stock INT NOT NULL DEFAULT 0 COMMENT '库存数量',
    exchangeCount INT NOT NULL DEFAULT 0 COMMENT '兑换次数统计',
    isHot TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否热门商品（手动设置）：1-是，0-否',
    isNew TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否新品：1-是，0-否',
    isAutoHot TINYINT(1) NOT NULL DEFAULT 0 COMMENT '自动识别的热门商品：1-是，0-否',
    hotTimeRange VARCHAR(20) DEFAULT NULL COMMENT '热门时间维度：all-全部时间，30d-30天，7d-7天，1d-1天',
    hotScore DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '热门度评分',
    hotRank INT DEFAULT NULL COMMENT '热门排名',
    lastHotUpdate DATETIME DEFAULT NULL COMMENT '最后热门状态更新时间',
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active' COMMENT '商品状态：active-上架，inactive-下架',
    createdAt DATETIME NOT NULL COMMENT '创建时间',
    updatedAt DATETIME NOT NULL COMMENT '更新时间',

    INDEX idx_product_name (name),
    INDEX idx_product_category (categoryId),
    INDEX idx_product_ly_price (lyPrice),
    INDEX idx_product_rmb_price (rmbPrice),
    INDEX idx_product_stock (stock),
    INDEX idx_product_exchange_count (exchangeCount),
    INDEX idx_product_hot (isHot),
    INDEX idx_product_new (isNew),
    INDEX idx_product_auto_hot (isAutoHot),
    INDEX idx_product_hot_score (hotScore),
    INDEX idx_product_status (status),
    INDEX idx_product_created (createdAt),
    INDEX idx_product_hot_time_range (hotTimeRange),

    CONSTRAINT fk_product_category FOREIGN KEY (categoryId) REFERENCES categories(id) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='商品信息表 - 存储商品的基本信息、价格、库存和热门度数据';

-- 1.5 商品图片表 (product_images) - 商品图片信息
DROP TABLE IF EXISTS product_images;
CREATE TABLE product_images (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '图片ID',
    productId INT NOT NULL COMMENT '关联商品ID',
    imageUrl VARCHAR(255) NOT NULL COMMENT '图片URL地址',
    sortOrder INT NOT NULL DEFAULT 0 COMMENT '图片排序顺序',
    createdAt DATETIME NOT NULL COMMENT '创建时间',
    updatedAt DATETIME NOT NULL COMMENT '更新时间',

    INDEX idx_product_image_product (productId),
    INDEX idx_product_image_sort (sortOrder),
    INDEX idx_product_image_created (createdAt),

    CONSTRAINT fk_product_image_product FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='商品图片表 - 存储商品的图片信息，支持多图片展示';

-- 1.6 商品价格历史表 (product_price_history) - 价格变更记录
DROP TABLE IF EXISTS product_price_history;
CREATE TABLE product_price_history (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    productId INT NOT NULL COMMENT '关联商品ID',
    oldLyPrice INT DEFAULT NULL COMMENT '变更前光年币价格',
    newLyPrice INT NOT NULL COMMENT '变更后光年币价格',
    oldRmbPrice DECIMAL(10,2) DEFAULT NULL COMMENT '变更前人民币价格',
    newRmbPrice DECIMAL(10,2) NOT NULL COMMENT '变更后人民币价格',
    changeReason VARCHAR(255) DEFAULT NULL COMMENT '价格变更原因',
    changedBy INT DEFAULT NULL COMMENT '操作人用户ID',
    effectiveDate DATETIME NOT NULL COMMENT '价格生效时间',
    createdAt DATETIME NOT NULL COMMENT '创建时间',
    updatedAt DATETIME NOT NULL COMMENT '更新时间',

    INDEX idx_price_history_product (productId),
    INDEX idx_price_history_changed_by (changedBy),
    INDEX idx_price_history_effective (effectiveDate),
    INDEX idx_price_history_created (createdAt),

    CONSTRAINT fk_price_history_product FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT fk_price_history_user FOREIGN KEY (changedBy) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='商品价格历史表 - 记录商品价格的变更历史，用于价格追踪和审计';

-- =====================================================
-- 2. 订单交易相关表
-- =====================================================

-- 2.1 兑换订单表 (exchanges) - 核心订单信息
DROP TABLE IF EXISTS exchanges;
CREATE TABLE exchanges (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '订单ID',
    orderNumber VARCHAR(255) DEFAULT NULL COMMENT '格式化的订单编号',
    userId INT NOT NULL COMMENT '下单用户ID',
    productId INT NOT NULL COMMENT '商品ID',
    quantity INT NOT NULL DEFAULT 1 COMMENT '购买数量',
    unitPrice DECIMAL(10,2) NOT NULL COMMENT '订单创建时的商品单价快照',
    totalAmount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '订单总金额',
    paymentMethod ENUM('ly', 'rmb') NOT NULL COMMENT '支付方式：ly-光年币，rmb-人民币',
    priceType ENUM('ly', 'rmb') NOT NULL COMMENT '价格类型：ly-光年币价格，rmb-人民币价格',
    contactInfo VARCHAR(255) DEFAULT NULL COMMENT '联系方式',
    location VARCHAR(255) DEFAULT NULL COMMENT '收货地址',
    workplaceId INT DEFAULT NULL COMMENT '关联职场ID',
    remarks TEXT DEFAULT NULL COMMENT '用户备注',
    status ENUM('pending', 'approved', 'shipped', 'completed', 'rejected', 'cancelled') NOT NULL DEFAULT 'pending'
        COMMENT '订单状态：pending-待审核，approved-已审核，shipped-已发货，completed-已完成，rejected-已拒绝，cancelled-已取消',
    adminRemarks TEXT DEFAULT NULL COMMENT '管理员备注',
    trackingNumber VARCHAR(255) DEFAULT NULL COMMENT '快递单号',
    trackingCompany VARCHAR(255) DEFAULT NULL COMMENT '快递公司',
    paymentProofUrl VARCHAR(255) DEFAULT NULL COMMENT '付款凭证URL',
    promotion_id INT DEFAULT NULL COMMENT '促销活动ID（预留字段）',
    promotion_discount DECIMAL(10,2) DEFAULT 0.00 COMMENT '促销折扣金额',
    createdAt DATETIME NOT NULL COMMENT '创建时间',
    updatedAt DATETIME NOT NULL COMMENT '更新时间',

    INDEX idx_exchange_order_number (orderNumber),
    INDEX idx_exchange_user (userId),
    INDEX idx_exchange_product (productId),
    INDEX idx_exchange_workplace (workplaceId),
    INDEX idx_exchange_payment_method (paymentMethod),
    INDEX idx_exchange_status (status),
    INDEX idx_exchange_total_amount (totalAmount),
    INDEX idx_exchange_created (createdAt),

    CONSTRAINT fk_exchange_user FOREIGN KEY (userId) REFERENCES users(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT fk_exchange_product FOREIGN KEY (productId) REFERENCES products(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT fk_exchange_workplace FOREIGN KEY (workplaceId) REFERENCES workplaces(id) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='兑换订单表 - 存储用户的商品兑换订单信息，包含支付、物流等完整流程数据';

-- =====================================================
-- 3. 内容管理相关表
-- =====================================================

-- 3.1 公告表 (announcements) - 系统公告信息
DROP TABLE IF EXISTS announcements;
CREATE TABLE announcements (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '公告ID',
    title VARCHAR(255) NOT NULL COMMENT '公告标题',
    content LONGTEXT NOT NULL COMMENT '公告内容（Markdown格式）',
    contentHtml LONGTEXT DEFAULT NULL COMMENT '公告内容（HTML格式）',
    imageUrl VARCHAR(255) DEFAULT NULL COMMENT '公告主图URL',
    imageUrls TEXT DEFAULT NULL COMMENT '公告图片列表（JSON格式）',
    type ENUM('新品', '促销', '系统更新') NOT NULL DEFAULT '系统更新' COMMENT '公告类型',
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active' COMMENT '公告状态：active-发布，inactive-草稿',
    createdBy INT NOT NULL COMMENT '创建人用户ID',
    createdAt DATETIME NOT NULL COMMENT '创建时间',
    updatedAt DATETIME NOT NULL COMMENT '更新时间',

    INDEX idx_announcement_type (type),
    INDEX idx_announcement_status (status),
    INDEX idx_announcement_created_by (createdBy),
    INDEX idx_announcement_created (createdAt),

    CONSTRAINT fk_announcement_creator FOREIGN KEY (createdBy) REFERENCES users(id) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='公告表 - 存储系统公告信息，支持富文本内容和图片展示';

-- 3.2 用户反馈表 (feedback) - 简化版反馈
DROP TABLE IF EXISTS feedback;
CREATE TABLE feedback (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '反馈ID',
    userId INT DEFAULT NULL COMMENT '反馈用户ID',
    content TEXT NOT NULL COMMENT '反馈内容',
    createdAt DATETIME NOT NULL COMMENT '创建时间',
    updatedAt DATETIME NOT NULL COMMENT '更新时间',

    INDEX idx_feedback_user (userId),
    INDEX idx_feedback_created (createdAt),

    CONSTRAINT fk_feedback_user FOREIGN KEY (userId) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='用户反馈表（简化版） - 存储用户的简单反馈信息';

-- 3.3 用户反馈表 (feedbacks) - 完整版反馈
DROP TABLE IF EXISTS feedbacks;
CREATE TABLE feedbacks (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '反馈ID',
    title VARCHAR(255) NOT NULL COMMENT '反馈标题',
    content TEXT NOT NULL COMMENT '反馈内容',
    type ENUM('product', 'feature', 'bug', 'other') DEFAULT 'other' COMMENT '反馈类型：product-商品问题，feature-功能建议，bug-系统错误，other-其他',
    status ENUM('pending', 'processing', 'completed') DEFAULT 'pending' COMMENT '处理状态：pending-待处理，processing-处理中，completed-已完成',
    userId INT NOT NULL COMMENT '反馈用户ID',
    adminReply TEXT DEFAULT NULL COMMENT '管理员回复',
    createdAt DATETIME NOT NULL COMMENT '创建时间',
    updatedAt DATETIME NOT NULL COMMENT '更新时间',

    INDEX idx_feedbacks_type (type),
    INDEX idx_feedbacks_status (status),
    INDEX idx_feedbacks_user (userId),
    INDEX idx_feedbacks_created (createdAt),

    CONSTRAINT fk_feedbacks_user FOREIGN KEY (userId) REFERENCES users(id) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='用户反馈表（完整版） - 存储用户的详细反馈信息，支持分类和状态管理';

-- =====================================================
-- 4. 通知系统相关表
-- =====================================================

-- 4.1 通知表 (notifications) - 用户通知信息
DROP TABLE IF EXISTS notifications;
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '通知ID',
    type ENUM('exchange', 'feedback', 'stock_alert', 'product') NOT NULL COMMENT '通知类型：exchange-订单通知，feedback-反馈通知，stock_alert-库存预警，product-商品通知',
    sourceId INT NOT NULL COMMENT '关联的源数据ID（如订单ID、反馈ID等）',
    title VARCHAR(255) NOT NULL COMMENT '通知标题',
    content TEXT NOT NULL COMMENT '通知内容',
    isRead TINYINT(1) DEFAULT 0 COMMENT '是否已读：1-已读，0-未读',
    recipientId INT NOT NULL COMMENT '接收人用户ID',
    createdAt DATETIME NOT NULL COMMENT '创建时间',
    updatedAt DATETIME NOT NULL COMMENT '更新时间',

    INDEX idx_notification_type (type),
    INDEX idx_notification_recipient (recipientId),
    INDEX idx_notification_read (isRead),
    INDEX idx_notification_created (createdAt),

    CONSTRAINT fk_notification_recipient FOREIGN KEY (recipientId) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='通知表 - 存储发送给用户的各类系统通知信息';

-- 4.2 消息模板表 (message_templates) - 通知消息模板
DROP TABLE IF EXISTS message_templates;
CREATE TABLE message_templates (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '模板ID',
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    template_code VARCHAR(50) DEFAULT NULL COMMENT '模板代码',
    notification_type VARCHAR(50) NOT NULL COMMENT '通知类型',
    template_type ENUM('text', 'card', 'rich') DEFAULT 'card' COMMENT '模板类型：text-纯文本，card-卡片，rich-富文本',
    template_content TEXT NOT NULL COMMENT '模板内容（支持变量占位符）',
    variables TEXT DEFAULT NULL COMMENT '模板变量说明（JSON格式）',
    description VARCHAR(200) DEFAULT NULL COMMENT '模板描述',
    is_default TINYINT(1) DEFAULT 0 COMMENT '是否默认模板：1-是，0-否',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用：1-启用，0-禁用',
    usage_count INT DEFAULT 0 COMMENT '使用次数统计',
    created_by INT DEFAULT NULL COMMENT '创建人用户ID',
    updated_by INT DEFAULT NULL COMMENT '最后更新人用户ID',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    updated_at DATETIME NOT NULL COMMENT '更新时间',

    INDEX idx_template_name (template_name),
    INDEX idx_template_type (notification_type),
    INDEX idx_template_active (is_active),
    INDEX idx_template_created_by (created_by),
    INDEX idx_template_updated_by (updated_by),
    INDEX idx_template_created (created_at),

    CONSTRAINT fk_template_creator FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT fk_template_updater FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='消息模板表 - 存储各类通知的消息模板，支持变量替换和多种格式';

-- 4.3 发送调度表 (sending_schedules) - 通知发送调度配置
DROP TABLE IF EXISTS sending_schedules;
CREATE TABLE sending_schedules (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '调度ID',
    schedule_name VARCHAR(100) NOT NULL COMMENT '调度名称',
    notification_type VARCHAR(50) NOT NULL COMMENT '关联的通知类型',
    schedule_type ENUM('fixed', 'smart', 'conditional') DEFAULT 'fixed' COMMENT '调度类型：fixed-固定时间，smart-智能调度，conditional-条件触发',
    cron_expression VARCHAR(100) DEFAULT NULL COMMENT 'Cron表达式（用于固定时间调度）',
    time_windows JSON DEFAULT NULL COMMENT '时间窗口配置（JSON格式）',
    conditions JSON DEFAULT NULL COMMENT '触发条件配置（JSON格式）',
    priority INT DEFAULT 0 COMMENT '优先级（数值越大优先级越高）',
    enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用：1-启用，0-禁用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    UNIQUE KEY uk_notification_schedule (notification_type, schedule_type),
    INDEX idx_schedule_type (schedule_type),
    INDEX idx_schedule_enabled (enabled),
    INDEX idx_schedule_priority (priority),
    INDEX idx_schedule_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='发送调度表 - 配置通知的发送时间和调度规则';

-- 4.4 通知配置表 (notification_configs) - 通知系统配置
DROP TABLE IF EXISTS notification_configs;
CREATE TABLE notification_configs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    notification_type VARCHAR(50) NOT NULL COMMENT '通知类型',
    enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用：1-启用，0-禁用',
    webhook_url VARCHAR(500) DEFAULT NULL COMMENT 'Webhook URL地址',
    schedule_time VARCHAR(20) DEFAULT NULL COMMENT '发送时间配置',
    retry_count INT DEFAULT 3 COMMENT '重试次数',
    template_id INT DEFAULT NULL COMMENT '关联消息模板ID',
    schedule_id INT DEFAULT NULL COMMENT '关联发送调度ID',
    advanced_settings JSON DEFAULT NULL COMMENT '高级设置（JSON格式）',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    updated_at DATETIME NOT NULL COMMENT '更新时间',

    UNIQUE KEY uk_notification_type (notification_type),
    INDEX idx_config_enabled (enabled),
    INDEX idx_config_template (template_id),
    INDEX idx_config_schedule (schedule_id),
    INDEX idx_config_created (created_at),

    CONSTRAINT fk_notification_config_template FOREIGN KEY (template_id) REFERENCES message_templates(id) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT fk_notification_config_schedule FOREIGN KEY (schedule_id) REFERENCES sending_schedules(id) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='通知配置表 - 存储各类通知的配置信息，包括发送规则和模板关联';

-- 4.5 通知日志表 (notification_logs) - 通知发送日志
DROP TABLE IF EXISTS notification_logs;
CREATE TABLE notification_logs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    notification_type VARCHAR(50) NOT NULL COMMENT '通知类型',
    status ENUM('pending', 'success', 'failed') DEFAULT 'pending' COMMENT '发送状态：pending-待发送，success-成功，failed-失败',
    response_time INT DEFAULT NULL COMMENT '响应时间（毫秒）',
    error_message TEXT DEFAULT NULL COMMENT '错误信息',
    webhook_url VARCHAR(500) DEFAULT NULL COMMENT '发送的Webhook URL',
    retry_count INT DEFAULT 0 COMMENT '当前重试次数',
    max_retries INT DEFAULT 3 COMMENT '最大重试次数',
    sent_at DATETIME DEFAULT NULL COMMENT '实际发送时间',
    next_retry_at DATETIME DEFAULT NULL COMMENT '下次重试时间',
    request_payload TEXT DEFAULT NULL COMMENT '请求载荷（JSON格式）',
    response_status INT DEFAULT NULL COMMENT 'HTTP响应状态码',
    response_body TEXT DEFAULT NULL COMMENT '响应内容',
    created_by INT DEFAULT NULL COMMENT '触发人用户ID',
    trigger_source VARCHAR(50) DEFAULT NULL COMMENT '触发源',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    updated_at DATETIME NOT NULL COMMENT '更新时间',

    INDEX idx_log_type (notification_type),
    INDEX idx_log_status (status),
    INDEX idx_log_sent_at (sent_at),
    INDEX idx_log_next_retry (next_retry_at),
    INDEX idx_log_created (created_at),
    INDEX idx_log_created_by (created_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='通知日志表 - 记录所有通知的发送日志，包括成功、失败和重试信息';

-- 4.6 通知调度表 (notification_schedules) - 通知调度管理
DROP TABLE IF EXISTS notification_schedules;
CREATE TABLE notification_schedules (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '调度ID',
    notification_type VARCHAR(50) NOT NULL COMMENT '通知类型',
    schedule_name VARCHAR(100) NOT NULL COMMENT '调度名称',
    schedule_type ENUM('cron', 'interval', 'once', 'conditional') NOT NULL COMMENT '调度类型：cron-定时，interval-间隔，once-一次性，conditional-条件触发',
    schedule_config TEXT NOT NULL COMMENT '调度配置（JSON格式）',
    time_rules TEXT DEFAULT NULL COMMENT '时间规则配置',
    frequency_limit TEXT DEFAULT NULL COMMENT '频率限制配置',
    holiday_handling ENUM('skip', 'next_workday', 'ignore') DEFAULT 'skip' COMMENT '节假日处理：skip-跳过，next_workday-下个工作日，ignore-忽略',
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai' COMMENT '时区设置',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活：1-激活，0-停用',
    priority INT DEFAULT 0 COMMENT '优先级',
    last_executed_at DATETIME DEFAULT NULL COMMENT '最后执行时间',
    next_execution_at DATETIME DEFAULT NULL COMMENT '下次执行时间',
    execution_count INT DEFAULT 0 COMMENT '执行次数统计',
    created_by INT DEFAULT NULL COMMENT '创建人用户ID',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    updated_at DATETIME NOT NULL COMMENT '更新时间',

    INDEX idx_schedule_type (schedule_type),
    INDEX idx_schedule_notification_type (notification_type),
    INDEX idx_schedule_active (is_active),
    INDEX idx_schedule_next_execution (next_execution_at),
    INDEX idx_schedule_created_by (created_by),
    INDEX idx_schedule_created (created_at),

    CONSTRAINT fk_notification_schedule_creator FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='通知调度表 - 管理各类通知的调度规则和执行状态';

-- 4.7 通知诊断表 (notification_diagnostics) - 通知系统诊断
DROP TABLE IF EXISTS notification_diagnostics;
CREATE TABLE notification_diagnostics (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '诊断ID',
    diagnostic_type VARCHAR(50) NOT NULL COMMENT '诊断类型',
    status VARCHAR(20) NOT NULL COMMENT '诊断状态',
    details JSON DEFAULT NULL COMMENT '诊断详情（JSON格式）',
    error_message TEXT DEFAULT NULL COMMENT '错误信息',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    resolved_at DATETIME DEFAULT NULL COMMENT '解决时间',
    resolution TEXT DEFAULT NULL COMMENT '解决方案',

    INDEX idx_diagnostic_type (diagnostic_type),
    INDEX idx_diagnostic_status (status),
    INDEX idx_diagnostic_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='通知诊断表 - 记录通知系统的诊断信息和问题解决情况';

-- =====================================================
-- 5. 热门商品系统相关表
-- =====================================================

-- 5.1 热门商品配置表 (hot_product_configs) - 热门商品算法配置
DROP TABLE IF EXISTS hot_product_configs;
CREATE TABLE hot_product_configs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    timeRange VARCHAR(20) NOT NULL COMMENT '时间维度：all-全部时间，30d-30天，7d-7天，1d-1天',
    enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用：1-启用，0-禁用',
    maxCount INT DEFAULT 10 COMMENT '最大热门商品数量',
    minExchangeCount INT DEFAULT 1 COMMENT '最小兑换次数阈值',
    exchangeWeight DECIMAL(3,2) DEFAULT 1.00 COMMENT '兑换次数权重',
    stockWeight DECIMAL(3,2) DEFAULT 0.10 COMMENT '库存权重',
    autoUpdateEnabled TINYINT(1) DEFAULT 1 COMMENT '是否启用自动更新：1-启用，0-禁用',
    updateFrequency VARCHAR(50) DEFAULT '0 */1 * * *' COMMENT '更新频率（Cron表达式）',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    UNIQUE KEY uk_hot_config_time_range (timeRange),
    INDEX idx_hot_config_enabled (enabled),
    INDEX idx_hot_config_auto_update (autoUpdateEnabled),
    INDEX idx_hot_config_created (createdAt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='热门商品配置表 - 存储热门商品算法的配置参数和更新规则';

-- 5.2 热门商品历史表 (hot_product_history) - 热门商品历史记录
DROP TABLE IF EXISTS hot_product_history;
CREATE TABLE hot_product_history (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '历史记录ID',
    productId INT NOT NULL COMMENT '商品ID',
    timeRange VARCHAR(20) NOT NULL COMMENT '时间维度',
    hotScore DECIMAL(10,2) NOT NULL COMMENT '热门度评分',
    `rank` INT NOT NULL COMMENT '排名',
    exchangeCount INT NOT NULL COMMENT '统计期间内的兑换次数',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_hot_history_product (productId),
    INDEX idx_hot_history_time_range (timeRange),
    INDEX idx_hot_history_score (hotScore),
    INDEX idx_hot_history_rank (`rank`),
    INDEX idx_hot_history_created (createdAt),

    CONSTRAINT fk_hot_history_product FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='热门商品历史表 - 记录商品在不同时间维度下的热门度历史数据';

-- =====================================================
-- 6. 系统日志表
-- =====================================================

-- 6.1 系统日志表 (logs) - 系统操作日志
DROP TABLE IF EXISTS logs;
CREATE TABLE logs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    action VARCHAR(255) NOT NULL COMMENT '操作动作',
    entityType VARCHAR(255) NOT NULL COMMENT '实体类型（如product、user、order等）',
    entityId INT NOT NULL COMMENT '实体ID',
    oldValue TEXT DEFAULT NULL COMMENT '变更前的值（JSON格式）',
    newValue TEXT DEFAULT NULL COMMENT '变更后的值（JSON格式）',
    userId INT DEFAULT NULL COMMENT '操作用户ID',
    username VARCHAR(255) DEFAULT NULL COMMENT '操作用户名',
    ipAddress VARCHAR(255) DEFAULT NULL COMMENT '操作IP地址',
    deviceInfo TEXT DEFAULT NULL COMMENT '设备信息',
    description TEXT DEFAULT NULL COMMENT '操作描述',
    createdAt DATETIME NOT NULL COMMENT '创建时间',
    updatedAt DATETIME NOT NULL COMMENT '更新时间',

    INDEX idx_log_action (action),
    INDEX idx_log_entity_type (entityType),
    INDEX idx_log_entity_id (entityId),
    INDEX idx_log_user_id (userId),
    INDEX idx_log_username (username),
    INDEX idx_log_created (createdAt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='系统日志表 - 记录系统中所有重要操作的详细日志信息';

-- =====================================================
-- 7. Sequelize 元数据表
-- =====================================================

-- 7.1 Sequelize 迁移记录表 (SequelizeMeta) - 数据库迁移记录
DROP TABLE IF EXISTS SequelizeMeta;
CREATE TABLE SequelizeMeta (
    name VARCHAR(255) NOT NULL PRIMARY KEY COMMENT '迁移文件名'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Sequelize迁移记录表 - 记录已执行的数据库迁移文件';

-- =====================================================
-- 8. 初始数据插入
-- =====================================================

-- 8.1 插入初始职场数据
INSERT INTO workplaces (id, name, code, description, isActive, createdAt, updatedAt) VALUES
(1, '北京', 'BJ', '北京职场', 1, NOW(), NOW()),
(2, '武汉', 'WH', '武汉职场', 1, NOW(), NOW()),
(3, '长沙', 'CS', '长沙职场', 1, NOW(), NOW()),

-- 8.2 插入管理员用户
INSERT INTO users (id, username, password, role, email, department, workplaceId, authType, points, isActive, createdAt, updatedAt) VALUES
(1, '超管', '$2a$10$raMSkDhYLnXikcE9gIL0QOlwr5xJ.fsbulS4BPwO4902SLd7kw4Pa', 'admin', '<EMAIL>', '行政', 1, 'password', 0, 1, NOW(), NOW());

-- 8.3 插入初始商品分类
INSERT INTO categories (id, name, description, sortOrder, createdAt, updatedAt) VALUES
(1, '解压玩具', '帮助缓解压力的玩具', 1, NOW(), NOW()),
(2, '盲盒类', '神秘的盲盒产品', 2, NOW(), NOW()),
(3, '定制手工制品类', '独特的定制手工产品', 3, NOW(), NOW()),
(4, '杯具类', '各种杯子和饮水工具', 4, NOW(), NOW()),
(5, '日常用品类', '日常生活必需品', 5, NOW(), NOW()),
(6, '家居用品类', '提升家居体验的产品', 6, NOW(), NOW()),
(7, '多功能用品类', '具有多种功能的实用产品', 7, NOW(), NOW()),
(8, '办公用品', '办公室必备用品', 8, NOW(), NOW()),
(9, '电子产品及配件', '电子产品及相关配件', 9, NOW(), NOW()),
(10, '家居生活用品', '家居生活必需品', 10, NOW(), NOW()),
(11, '创意小摆件', '可爱的装饰小摆件', 11, NOW(), NOW());

-- 8.4 插入热门商品配置数据
INSERT INTO hot_product_configs (id, timeRange, enabled, maxCount, minExchangeCount, exchangeWeight, stockWeight, autoUpdateEnabled, updateFrequency, createdAt, updatedAt) VALUES
(1, 'all', 1, 10, 1, 1.00, 0.10, 1, '0 */1 * * *', NOW(), NOW()),
(2, '30d', 1, 10, 1, 1.00, 0.10, 1, '0 */1 * * *', NOW(), NOW()),
(3, '7d', 1, 10, 1, 1.00, 0.10, 1, '0 */1 * * *', NOW(), NOW()),
(4, '1d', 1, 10, 1, 1.00, 0.10, 1, '0 */1 * * *', NOW(), NOW());

-- 8.5 插入基础消息模板
INSERT INTO message_templates (id, template_name, template_code, notification_type, template_type, template_content, variables, description, is_default, is_active, usage_count, created_by, created_at, updated_at) VALUES
(1, '订单状态变更通知', 'ORDER_STATUS_CHANGE', 'exchange', 'card', '您的订单 {{orderNumber}} 状态已更新为：{{status}}', '{"orderNumber":"订单号","status":"订单状态","productName":"商品名称","userName":"用户名"}', '用户订单状态变更时的通知模板', 1, 1, 0, 1, NOW(), NOW()),
(2, '库存预警通知', 'STOCK_ALERT', 'stock_alert', 'card', '商品 {{productName}} 库存不足，当前库存：{{currentStock}}', '{"productName":"商品名称","currentStock":"当前库存","minStock":"最低库存"}', '商品库存不足时的预警通知模板', 1, 1, 0, 1, NOW(), NOW()),
(3, '新商品上架通知', 'NEW_PRODUCT', 'product', 'card', '新商品上架：{{productName}}，光年币价格：{{lyPrice}}，人民币价格：{{rmbPrice}}', '{"productName":"商品名称","lyPrice":"光年币价格","rmbPrice":"人民币价格","categoryName":"分类名称"}', '新商品上架时的通知模板', 1, 1, 0, 1, NOW(), NOW()),
(4, '用户反馈通知', 'USER_FEEDBACK', 'feedback', 'card', '收到新的用户反馈：{{feedbackTitle}}，反馈类型：{{feedbackType}}', '{"feedbackTitle":"反馈标题","feedbackType":"反馈类型","userName":"用户名","feedbackContent":"反馈内容"}', '用户提交反馈时的通知模板', 1, 1, 0, 1, NOW(), NOW());

-- 8.6 插入基础通知配置
INSERT INTO notification_configs (id, notification_type, enabled, webhook_url, schedule_time, retry_count, template_id, advanced_settings, created_at, updated_at) VALUES
(1, 'exchange', 1, NULL, '09:00', 3, 1, '{"immediate":true,"batch_size":50}', NOW(), NOW()),
(2, 'stock_alert', 1, NULL, '10:00', 3, 2, '{"threshold":5,"check_frequency":"hourly"}', NOW(), NOW()),
(3, 'product', 1, NULL, '14:00', 3, 3, '{"immediate":false,"batch_size":20}', NOW(), NOW()),
(4, 'feedback', 1, NULL, '16:00', 3, 4, '{"immediate":true,"priority":"high"}', NOW(), NOW());

-- =====================================================
-- 9. 恢复外键检查和提交事务
-- =====================================================

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 提交事务
COMMIT;

-- =====================================================
-- 10. 创建数据库用户和权限设置（可选）
-- =====================================================

-- 创建应用程序专用数据库用户（生产环境建议使用）
-- CREATE USER IF NOT EXISTS 'feishu_mall_user'@'localhost' IDENTIFIED BY 'your_secure_password_here';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON feishu_mall.* TO 'feishu_mall_user'@'localhost';
-- FLUSH PRIVILEGES;

-- =====================================================
-- 脚本执行完成
-- =====================================================

SELECT '数据库初始化完成！' as message;
SELECT CONCAT('共创建 ', COUNT(*), ' 个表') as table_count FROM information_schema.tables WHERE table_schema = 'feishu_mall';
SELECT 'feishu_mall 数据库已成功初始化，包含完整的表结构、索引、外键约束和初始数据。' as status;

-- 创建商品表（如果不存在）
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    originalPrice DECIMAL(10,2),
    stock INT DEFAULT 0,
    imageUrl VARCHAR(500),
    category VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active',
    sellerId INT,
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_seller (sellerId),
    FOREIGN KEY (sellerId) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建订单表（如果不存在）
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    orderNumber VARCHAR(50) UNIQUE NOT NULL,
    buyerId INT NOT NULL,
    sellerId INT NOT NULL,
    productId INT NOT NULL,
    quantity INT NOT NULL,
    totalPrice DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_buyer (buyerId),
    INDEX idx_seller (sellerId),
    INDEX idx_product (productId),
    INDEX idx_status (status),
    INDEX idx_order_number (orderNumber),
    FOREIGN KEY (buyerId) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (sellerId) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入一些示例数据（仅在表为空时）
INSERT IGNORE INTO users (username, email, authType, authId, isActive) VALUES 
('admin', '<EMAIL>', 'local', 'admin', TRUE);

-- 显示创建结果
SHOW TABLES; 