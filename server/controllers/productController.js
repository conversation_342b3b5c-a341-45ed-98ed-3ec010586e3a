const { sequelize } = require('../config/database');
const { Product, Category, ProductImage, User, Log, ProductPriceHistory, ProductWorkplaceStock, Workplace } = require('../models');
const { Op } = require('sequelize');
const path = require('path');
const fs = require('fs');
const Excel = require('exceljs');
const csv = require('csv-parser');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const { productNameCompare, sortProductsByPinyin } = require('../utils/pinyinSort');
const { createLog, logProductAction } = require('./logController');
const { checkProductStock } = require('../services/stockAlertService');
const { createNotification } = require('./notificationController');

/**
 * 获取商品价格范围
 * 用于动态设置价格筛选滑块的最小值和最大值
 */
exports.getProductPriceRanges = async (req, res) => {
  try {
    // 构建查询条件
    const whereClause = {};

    // 状态筛选 - 默认只查询上架商品的价格范围
    if (req.query.showAll !== 'true' && req.query.showAll !== true) {
      whereClause.status = 'active';
    }

    // 分类筛选
    if (req.query.category) {
      whereClause.categoryId = req.query.category;
    }

    // 搜索查询 - 商品名称
    if (req.query.search) {
      whereClause.name = {
        [Op.like]: `%${req.query.search}%`
      };
    }

    // 使用聚合函数查询价格范围
    const priceRanges = await Product.findAll({
      where: whereClause,
      attributes: [
        [sequelize.fn('MIN', sequelize.col('lyPrice')), 'minLyPrice'],
        [sequelize.fn('MAX', sequelize.col('lyPrice')), 'maxLyPrice'],
        [sequelize.fn('MIN', sequelize.col('rmbPrice')), 'minRmbPrice'],
        [sequelize.fn('MAX', sequelize.col('rmbPrice')), 'maxRmbPrice']
      ],
      raw: true
    });

    // 确保有返回结果
    if (priceRanges.length > 0) {
      const range = priceRanges[0];

      // 转换为数字类型并添加一点余量便于滑块使用
      const result = {
        minLyPrice: Math.max(0, Math.floor(parseFloat(range.minLyPrice || 0))),
        maxLyPrice: Math.ceil(parseFloat(range.maxLyPrice || 0)) + 1,
        minRmbPrice: Math.max(0, Math.floor(parseFloat(range.minRmbPrice || 0))),
        maxRmbPrice: Math.ceil(parseFloat(range.maxRmbPrice || 0)) + 1
      };

      res.json(result);
    } else {
      // 默认范围
      res.json({
        minLyPrice: 0,
        maxLyPrice: 100,
        minRmbPrice: 0,
        maxRmbPrice: 100
      });
    }
  } catch (error) {
    console.error('获取商品价格范围失败:', error);
    res.status(500).json({ message: '获取商品价格范围失败', error: error.message });
  }
};

/**
 * 获取商品列表
 * 支持分页、筛选、排序
 */
exports.getProducts = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const offset = (page - 1) * limit;

    // 构建查询条件
    const whereClause = {};

    // 状态筛选 - 默认只显示上架商品，管理员可以查看所有商品
    if (req.query.status) {
      whereClause.status = req.query.status;
    } else if (req.query.showAll !== 'true' && req.query.showAll !== true) {
      whereClause.status = 'active';
    }

    // 分类筛选
    if (req.query.category) {
      whereClause.categoryId = req.query.category;
    }

    // 多分类筛选
    if (req.query.categories) {
      const categoryIds = req.query.categories.split(',').map(id => parseInt(id));
      whereClause.categoryId = {
        [Op.in]: categoryIds
      };
    }

    // 搜索查询 - 商品名称（使用安全验证）
    if (req.query.search) {
      const { validateSearchTerm } = require('../middlewares/securityValidation');
      const searchResult = validateSearchTerm(req.query.search);

      if (!searchResult.valid) {
        return res.status(400).json({ message: searchResult.message });
      }

      if (searchResult.value) {
        whereClause.name = {
          [Op.like]: `%${searchResult.value}%`
        };
      }
    }

    // 价格范围筛选 - 光年币
    if (req.query.minLyPrice || req.query.maxLyPrice) {
      whereClause.lyPrice = {};
      if (req.query.minLyPrice) {
        whereClause.lyPrice[Op.gte] = parseInt(req.query.minLyPrice);
      }
      if (req.query.maxLyPrice) {
        whereClause.lyPrice[Op.lte] = parseInt(req.query.maxLyPrice);
      }
    }

    // 价格范围筛选 - 人民币
    if (req.query.minRmbPrice || req.query.maxRmbPrice) {
      whereClause.rmbPrice = {};
      if (req.query.minRmbPrice) {
        whereClause.rmbPrice[Op.gte] = parseFloat(req.query.minRmbPrice);
      }
      if (req.query.maxRmbPrice) {
        whereClause.rmbPrice[Op.lte] = parseFloat(req.query.maxRmbPrice);
      }
    }

    // 库存筛选
    if (req.query.inStock === 'true' || req.query.inStock === true) {
      whereClause.stock = {
        [Op.gt]: 0
      };
    }

    // 标签筛选
    if (req.query.isNew === 'true') {
      whereClause.isNew = true;
    }

    if (req.query.isHot === 'true') {
      whereClause.isHot = true;
    }

    // 日期范围筛选
    if (req.query.startDate || req.query.endDate) {
      whereClause.createdAt = {};
      if (req.query.startDate) {
        whereClause.createdAt[Op.gte] = new Date(req.query.startDate);
      }
      if (req.query.endDate) {
        // 设置结束日期为当天的23:59:59
        const endDate = new Date(req.query.endDate);
        endDate.setHours(23, 59, 59, 999);
        whereClause.createdAt[Op.lte] = endDate;
      }
    }

    // 检查是否需要自定义中文拼音排序
    const needsCustomSort = req.query.sort === 'name-asc' || req.query.sort === 'name-desc';

    // 构建排序
    let order = [];
    if (req.query.sort) {
      switch (req.query.sort) {
        case 'default':
          // 默认排序：新品和热门排在前面，然后按照光年币价格从低到高排序
          order = [
            ['isNew', 'DESC'],
            ['isHot', 'DESC'],
            ['lyPrice', 'ASC']
          ];
          break;
        case 'ly-asc':
        case 'ly-price-asc':
          order = [['lyPrice', 'ASC']];
          break;
        case 'ly-desc':
        case 'ly-price-desc':
          order = [['lyPrice', 'DESC']];
          break;
        case 'rmb-asc':
        case 'rmb-price-asc':
          order = [['rmbPrice', 'ASC']];
          break;
        case 'rmb-desc':
        case 'rmb-price-desc':
          order = [['rmbPrice', 'DESC']];
          break;
        case 'newest':
          order = [['createdAt', 'DESC']];
          break;
        case 'oldest':
          order = [['createdAt', 'ASC']];
          break;
        case 'name-asc':
        case 'name-desc':
          // 中文拼音排序，不使用数据库排序，但需要传递某种顺序以获取全部数据
          order = [['id', 'ASC']];
          break;
        case 'stock-asc':
          order = [['stock', 'ASC']];
          break;
        case 'stock-desc':
          order = [['stock', 'DESC']];
          break;
        case 'exchange-asc':
          order = [['exchangeCount', 'ASC']];
          break;
        case 'exchange-desc':
          order = [['exchangeCount', 'DESC']];
          break;
      }
    } else {
      // 默认排序：新品和热门排在前面，然后按照光年币价格从低到高排序
      order = [
        ['isNew', 'DESC'],
        ['isHot', 'DESC'],
        ['lyPrice', 'ASC']
      ];
    }

    // 构建include数组
    const includeArray = [
      {
        model: Category,
        attributes: ['id', 'name']
      },
      {
        model: ProductImage,
        attributes: ['id', 'imageUrl', 'sortOrder'],
        required: false,
        order: [['sortOrder', 'ASC']]
      }
    ];

    // 如果请求包含职场库存信息
    if (req.query.includeWorkplaceStocks === 'true') {
      includeArray.push({
        model: ProductWorkplaceStock,
        as: 'workplaceStocks',
        attributes: ['workplaceId', 'stock', 'reservedStock', 'minStockAlert', 'maxStockLimit', 'lastStockUpdate'],
        include: [
          {
            model: Workplace,
            as: 'workplace',
            attributes: ['id', 'name', 'code', 'isActive'],
            where: { isActive: true },
            required: false
          }
        ],
        required: false
      });
    }

    // 查询数据
    const { count, rows } = await Product.findAndCountAll({
      where: whereClause,
      order,
      limit: needsCustomSort ? null : limit, // 拼音排序时先获取所有数据
      offset: needsCustomSort ? null : offset,
      include: includeArray,
      distinct: true
    });

    // 处理包含图片的产品
    let productsWithImages = rows.map(product => {
      const productData = product.toJSON();

      // 确保ProductImages属性存在
      if (productData.ProductImages && productData.ProductImages.length > 0) {
        // 按排序顺序整理图片
        productData.ProductImages.sort((a, b) => a.sortOrder - b.sortOrder);

        // 提取图片URL到单独数组
        productData.images = productData.ProductImages.map(image => image.imageUrl);

        // 设置主图（第一张图片）
        productData.mainImage = productData.ProductImages[0].imageUrl;
        productData.imageUrl = productData.mainImage;
      } else {
        // 如果没有图片，设置默认图片
        productData.images = [];
        productData.imageUrl = '/images/placeholder/no-image.png';
      }

      // 处理职场库存信息
      if (req.query.includeWorkplaceStocks === 'true' && productData.workplaceStocks) {
        // 计算总库存信息
        let totalStock = 0;
        let totalReservedStock = 0;
        let totalAvailableStock = 0;

        const workplaceStocksFormatted = productData.workplaceStocks
          .filter(ws => ws.workplace && ws.workplace.isActive) // 只包含活跃职场
          .map(ws => {
            totalStock += ws.stock;
            totalReservedStock += ws.reservedStock;
            const availableStock = ws.stock - ws.reservedStock;
            totalAvailableStock += availableStock;

            return {
              workplaceId: ws.workplaceId,
              workplaceName: ws.workplace.name,
              workplaceCode: ws.workplace.code,
              stock: ws.stock,
              reservedStock: ws.reservedStock,
              availableStock: availableStock,
              minStockAlert: ws.minStockAlert,
              maxStockLimit: ws.maxStockLimit,
              isLowStock: availableStock <= ws.minStockAlert,
              lastStockUpdate: ws.lastStockUpdate
            };
          });

        // 添加职场库存汇总信息
        productData.stockManagementType = 'workplace';
        productData.totalStock = totalStock;
        productData.totalReservedStock = totalReservedStock;
        productData.totalAvailableStock = totalAvailableStock;
        productData.workplaceStocks = workplaceStocksFormatted;

        // 更新库存状态基于总可用库存
        productData.stockStatus = totalAvailableStock > 0 ? '有货' : '缺货';
      } else {
        // 传统库存模式
        productData.stockManagementType = 'traditional';
        productData.stockStatus = productData.stock > 0 ? '有货' : '缺货';
      }

      return productData;
    });

    // 如果需要自定义拼音排序
    if (needsCustomSort) {
      // 使用增强的中文拼音排序函数
      productsWithImages = sortProductsByPinyin(
        productsWithImages,
        req.query.sort === 'name-desc' ? 'desc' : 'asc'
      );

      // 手动分页
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      productsWithImages = productsWithImages.slice(startIndex, endIndex);
    }

    // 返回结果
    res.json({
      data: productsWithImages,
      total: count,
      page,
      limit,
      totalPages: Math.ceil(count / limit)
    });
  } catch (error) {
    // 检查是否是拼音相关的错误
    if (error.message && error.message.includes('pinyin')) {
      console.error('拼音排序功能出错:', error);

      // 如果是拼音相关错误，尝试使用普通数据库排序作为备选
      try {
        // 备用方案：使用数据库排序
        const order = req.query.sort === 'name-desc' ? [['name', 'DESC']] : [['name', 'ASC']];

        const { count, rows } = await Product.findAndCountAll({
          where: whereClause,
          order,
          limit,
          offset,
          include: [
            {
              model: Category,
              attributes: ['id', 'name']
            },
            {
              model: ProductImage,
              attributes: ['id', 'imageUrl', 'sortOrder'],
              required: false,
              order: [['sortOrder', 'ASC']]
            }
          ],
          distinct: true
        });

        // 处理包含图片的产品
        const productsWithImages = rows.map(product => {
          const productData = product.toJSON();

          // 确保ProductImages属性存在
          if (productData.ProductImages && productData.ProductImages.length > 0) {
            productData.ProductImages.sort((a, b) => a.sortOrder - b.sortOrder);
            productData.images = productData.ProductImages.map(image => image.imageUrl);
            productData.mainImage = productData.ProductImages[0].imageUrl;
            productData.imageUrl = productData.mainImage;
          } else {
            productData.images = [];
            productData.imageUrl = '/images/placeholder/no-image.png';
          }

          productData.stockStatus = productData.stock > 0 ? '有货' : '缺货';

          return productData;
        });

        // 返回备用排序结果
        return res.json({
          data: productsWithImages,
          total: count,
          page,
          limit,
          totalPages: Math.ceil(count / limit),
          sortFallback: true // 标记使用了备用排序
        });
      } catch (fallbackError) {
        console.error('备用排序也失败:', fallbackError);
        return res.status(500).json({ message: '获取商品列表失败，排序功能暂时不可用', error: error.message });
      }
    } else {
      // 其他类型的错误
      console.error('获取商品列表失败:', error);
      res.status(500).json({ message: '获取商品列表失败', error: error.message });
    }
  }
};

/**
 * 获取单个商品详情
 */
exports.getProductById = async (req, res) => {
  try {
    const productId = req.params.id;

    const product = await Product.findByPk(productId, {
      include: [
        {
          model: Category,
          attributes: ['id', 'name']
        },
        {
          model: ProductImage,
          attributes: ['id', 'imageUrl', 'sortOrder'],
          order: [['sortOrder', 'ASC']]
        }
      ]
    });

    if (!product) {
      return res.status(404).json({ message: '商品不存在' });
    }

    // 处理包含图片的产品
    const productData = product.toJSON();
    if (productData.ProductImages && productData.ProductImages.length > 0) {
      productData.images = productData.ProductImages.map(image => image.imageUrl);
      productData.mainImage = productData.ProductImages[0].imageUrl;
      // 如果没有设置imageUrl，使用第一张图片作为主图
      if (!productData.imageUrl) {
        productData.imageUrl = productData.mainImage;
      }
    }

    res.json(productData);
  } catch (error) {
    console.error('获取商品详情失败:', error);
    res.status(500).json({ message: '获取商品详情失败', error: error.message });
  }
};

/**
 * 创建新商品
 */
exports.createProduct = async (req, res) => {
  try {
    const { name, categoryId, lyPrice, rmbPrice, description, stock, isHot, isNew, status } = req.body;

    // 检查分类是否存在
    const category = await Category.findByPk(categoryId);
    if (!category) {
      return res.status(404).json({ message: '分类不存在' });
    }

    // 创建商品
    const product = await Product.create({
      name,
      categoryId,
      lyPrice,
      rmbPrice,
      description,
      stock: stock || 0,
      isHot: isHot || false,
      isNew: isNew || false,
      status: status || 'active'
    });

    // 记录商品创建日志
    await logProductAction({
      actionType: 'create',
      productId: product.id,
      productName: product.name,
      userId: req.user?.id,
      username: req.user?.username,
      oldValues: null,
      newValues: JSON.stringify(product)
    });

    // 给所有普通用户发送新品上架通知
    if (product.isNew && product.status === 'active') {
      try {
        // 查询所有普通用户
        const users = await User.findAll({
          where: { role: 'user' }
        });

        // 为每个用户创建通知
        for (const user of users) {
          await createNotification({
            type: 'product',
            sourceId: product.id,
            title: '新品上架通知',
            content: `新商品「${product.name}」已上架，售价${product.lyPrice}光年币/${product.rmbPrice}元，欢迎选购！`,
            recipientId: user.id
          });
        }
      } catch (error) {
        console.error('创建新品通知失败:', error);
        // 不阻止商品创建，通知失败不影响主流程
      }
    }

    // 发送飞书群新品上架通知
    if (product.status === 'active') {
      try {
        const operationalNotificationService = require('../services/operationalNotificationService');

        // 获取商品分类信息
        const productWithCategory = await Product.findByPk(product.id, {
          include: [{
            model: Category,
            attributes: ['name']
          }]
        });

        await operationalNotificationService.sendNewProductNotification(
          productWithCategory,
          req.user
        );
      } catch (notificationError) {
        console.error('发送新品上架飞书通知失败:', notificationError);
        // 不影响主流程
      }
    }

    return res.status(201).json({
      message: '商品创建成功',
      data: product
    });
  } catch (error) {
    console.error('创建商品失败:', error);
    return res.status(500).json({ message: '创建商品失败', error: error.message });
  }
};

/**
 * 更新商品信息 - 优化版本，支持事务保护
 */
exports.updateProduct = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const product = await Product.findByPk(id, { transaction });

    if (!product) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: '商品不存在'
      });
    }

    // 保存更新前的商品数据，用于日志记录
    const oldProductData = { ...product.get() };

    // 检查是否有价格变更
    const priceChanged = (req.body.lyPrice !== undefined && req.body.lyPrice !== oldProductData.lyPrice) ||
                        (req.body.rmbPrice !== undefined && req.body.rmbPrice !== oldProductData.rmbPrice);

    // 如果有价格变更，进行影响分析
    if (priceChanged) {
      const affectedOrdersCount = await analyzeProductPriceChangeImpact(id, transaction);
      console.log(`商品 ${product.name} 价格变更将影响 ${affectedOrdersCount} 个活跃订单`);
    }

    // 检查是否有库存更新，如果是职场分配模式，需要特殊处理
    const stockChanged = req.body.stock !== undefined && req.body.stock !== oldProductData.stock;

    if (stockChanged && product.stockManagementType === 'workplace') {
      // 检查当前职场数量，如果只有1个职场，允许直接修改
      const { Workplace } = require('../models');
      const workplaceCount = await Workplace.count({ transaction });

      if (workplaceCount > 1) {
        // 多职场环境下，不允许直接修改总库存
        return res.status(400).json({
          success: false,
          message: '该商品使用职场分配库存模式，请通过库存管理页面调整各职场库存'
        });
      }
      // 单职场环境下，允许直接修改，并同步到职场库存
    }

    // 执行更新，传递用户信息用于价格历史记录
    await product.update(req.body, {
      userId: req.user?.id,
      changeReason: req.body.changeReason || '商品信息更新',
      transaction
    });

    // 如果库存发生变化，在单职场环境下同步到职场库存
    if (stockChanged) {
      const { Workplace } = require('../models');
      const workplaceCount = await Workplace.count({ transaction });

      if (workplaceCount === 1) {
        // 单职场环境，同步库存到职场库存表
        console.log(`同步商品 ${product.id} 库存到职场: ${oldProductData.stock} → ${req.body.stock}`);
        await ProductWorkplaceStock.update(
          { stock: req.body.stock },
          {
            where: { productId: product.id },
            transaction
          }
        );
      }
    }

    // 检查是否需要发送库存告警
    await checkProductStock(product);

    // 使用新的日志函数记录商品更新
    if (req.user) {
      // 记录库存变更日志
      if (req.body.stock !== undefined && req.body.stock !== oldProductData.stock) {
        await logProductAction('stock_update', product, req.user, JSON.stringify({ stock: oldProductData.stock }), req);
      }

      // 记录状态变更日志
      if (req.body.status !== undefined && req.body.status !== oldProductData.status) {
        await logProductAction('product_status_change', product, req.user, JSON.stringify({ status: oldProductData.status }), req);
      }

      // 记录商品更新日志（如果有其他字段变更）
      await logProductAction('product_update', product, req.user, JSON.stringify(oldProductData), req);
    }

    await transaction.commit();

    return res.status(200).json({
      success: true,
      message: '商品更新成功',
      data: product,
      priceChangeImpact: priceChanged ? await analyzeProductPriceChangeImpact(id) : null
    });
  } catch (error) {
    await transaction.rollback();
    console.error('更新商品失败:', error);
    return res.status(500).json({
      success: false,
      message: '更新商品失败',
      error: error.message
    });
  }
};

/**
 * 分析商品价格变更的影响
 * @param {number} productId - 商品ID
 * @param {object} transaction - 数据库事务
 * @returns {Promise<number>} 受影响的订单数量
 */
async function analyzeProductPriceChangeImpact(productId, transaction = null) {
  try {
    const affectedOrders = await Exchange.count({
      where: {
        productId: productId,
        status: ['pending', 'approved', 'shipped']
      },
      transaction
    });

    return affectedOrders;
  } catch (error) {
    console.error('分析价格变更影响失败:', error);
    return 0;
  }
}

/**
 * 删除商品
 */
exports.deleteProduct = async (req, res) => {
  try {
    const productId = req.params.id;

    // 查询商品是否存在
    const product = await Product.findByPk(productId);

    if (!product) {
      return res.status(404).json({ message: '商品不存在' });
    }

    // 记录商品删除日志
    if (req.user) {
      await logProductAction('product_delete', product, req.user, JSON.stringify(product.get()), req);
    }

    // 删除商品
    await product.destroy();

    res.json({ message: '商品已成功删除' });
  } catch (error) {
    console.error('删除商品失败:', error);
    res.status(500).json({ message: '删除商品失败', error: error.message });
  }
};

/**
 * 更新商品状态
 */
exports.updateProductStatus = async (req, res) => {
  try {
    const productId = req.params.id;
    const { status } = req.body;

    // 验证状态参数
    if (!status || !['active', 'inactive'].includes(status)) {
      return res.status(400).json({ message: '无效的状态值，状态必须是 active 或 inactive' });
    }

    // 查询商品是否存在
    const product = await Product.findByPk(productId);

    if (!product) {
      return res.status(404).json({ message: '商品不存在' });
    }

    // 保存旧状态
    const oldStatus = product.status;

    // 更新商品状态
    await product.update({ status });

    // 记录状态变更日志
    if (req.user) {
      await logProductAction('product_status_change', product, req.user, JSON.stringify({ status: oldStatus }), req);
    }

    res.json(product);
  } catch (error) {
    console.error('更新商品状态失败:', error);
    res.status(500).json({ message: '更新商品状态失败', error: error.message });
  }
};

/**
 * 导入商品数据
 * 从CSV或Excel文件导入商品数据
 */
exports.importProducts = async (req, res) => {
  console.log('导入商品接口被调用');

  try {
    if (!req.file) {
      return res.status(400).json({ error: '未上传文件' });
    }

    console.log('接收到文件:', req.file.originalname, '大小:', req.file.size);

    // 获取所有分类以便映射
    const categories = await Category.findAll();
    const categoryMap = {};
    categories.forEach(category => {
      categoryMap[category.name] = category.id;
    });

    console.log(`加载了 ${categories.length} 个分类`);

    // 存储要导入的商品
    const importedProducts = [];
    const tempPath = req.file.path;

    // 根据文件类型处理
    const fileExt = path.extname(req.file.originalname).toLowerCase();

    // 处理Excel文件
    if (fileExt === '.xlsx' || fileExt === '.xls') {
      console.log('开始处理Excel文件...');

      try {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.readFile(tempPath);

        // 使用第一个工作表
        const worksheet = workbook.getWorksheet(1);

        if (!worksheet) {
          console.error('Excel文件中没有找到工作表');
          return res.status(400).json({ error: 'Excel文件格式错误：没有找到工作表' });
        }

        // 获取总行数用于日志记录
        const rowCount = worksheet.rowCount;
        const columnCount = worksheet.columnCount;
        console.log(`Excel文件有 ${rowCount} 行，${columnCount} 列`);

        // 读取表头并检查必要列
        let headerRowIndex = 1; // 通常第一行是表头
        const headerRow = worksheet.getRow(headerRowIndex);

        // 打印表头行的所有单元格值，便于调试
        const headerValues = [];
        headerRow.eachCell((cell, colNumber) => {
          if (cell && cell.value) {
            headerValues.push(`列${colNumber}: ${cell.value}`);
          }
        });
        console.log('表头行内容:', headerValues.join(', '));

        // 通过直接映射列号找到必要的列
        const headers = {};
        let foundHeaders = false;

        // 查找包含表头的行 (最多检查前3行)
        for (let i = 1; i <= Math.min(3, rowCount); i++) {
          const currentRow = worksheet.getRow(i);
          let headerCount = 0;

          currentRow.eachCell((cell, colNumber) => {
            if (cell && cell.value) {
              const value = String(cell.value).trim();
              if (value === '商品名称' || value === '分类' || value === '光年币价格' ||
                  value === '人民币价格' || value === '库存') {
                headerCount++;
                headers[value] = colNumber;
              }
            }
          });

          if (headerCount >= 2) { // 至少找到了商品名称和分类两个必要列头
            headerRowIndex = i;
            foundHeaders = true;
            break;
          }
        }

        // 如果没有找到表头，尝试使用第一行作为数据行
        if (!foundHeaders) {
          console.log('未能自动识别表头行，将按默认列顺序处理数据');

          // 使用默认列顺序: 第1列为商品名称，第2列为分类，依此类推
          headers['商品名称'] = 1;
          headers['分类'] = 2;
          headers['光年币价格'] = 3;
          headers['人民币价格'] = 4;
          headers['库存'] = 5;
          headers['累计兑换'] = 6;
          headers['描述'] = 7;
          headers['是否新品'] = 8;
          headers['是否热门'] = 9;
          headers['状态'] = 10;

          headerRowIndex = 0; // 没有表头行，从第一行开始
        }

        console.log('表头行索引:', headerRowIndex);
        console.log('列映射:', JSON.stringify(headers));

        // 数据开始行为表头行的下一行
        const dataStartRow = headerRowIndex + 1;

        // 辅助函数：获取单元格值
        function getCellValue(cell) {
          if (!cell || cell.value === undefined || cell.value === null) {
            return null;
          }

          // 对于不同类型的单元格值进行处理
          if (typeof cell.value === 'object') {
            // 富文本
            if (cell.value.richText) {
              return cell.value.richText.map(rt => rt.text).join('');
            }
            // 日期
            if (cell.value instanceof Date) {
              return cell.value.toISOString();
            }
            // 其他对象
            return String(cell.value);
          }

          // 返回普通值
          return String(cell.value).trim();
        }

        // 安全获取单元格值的辅助函数
        const getSafeValue = (row, colName) => {
          if (!headers[colName]) return null;
          return getCellValue(row.getCell(headers[colName]));
        };

        let validProductCount = 0;
        let skippedProductCount = 0;

        // 从数据开始行处理所有行
        for (let rowIndex = dataStartRow; rowIndex <= rowCount; rowIndex++) {
          const row = worksheet.getRow(rowIndex);
          try {
            // 获取名称和分类 (必要字段)
            const nameValue = getSafeValue(row, '商品名称');

            // 检查是否为空行
            if (!nameValue) {
              console.log(`跳过行 ${rowIndex}: 名称为空`);
              continue;
            }

            // 获取分类名称
            const categoryName = getSafeValue(row, '分类');
            let categoryId = null;

            // 查找对应的分类ID
            if (categoryName) {
              // 首先尝试精确匹配
              const exactCategory = categories.find(c => c.name === categoryName);
              if (exactCategory) {
                categoryId = exactCategory.id;
              } else {
                // 如果精确匹配失败，尝试模糊匹配
                const similarCategory = categories.find(c =>
                  c.name.includes(categoryName) || categoryName.includes(c.name)
                );
                if (similarCategory) {
                  categoryId = similarCategory.id;
                  console.log(`行 ${rowIndex}: 使用模糊匹配找到分类 "${categoryName}" -> "${similarCategory.name}"`);
                } else {
                  // 如果还是找不到，使用默认分类或第一个分类
                  if (categories.length > 0) {
                    categoryId = categories[0].id;
                    console.log(`行 ${rowIndex}: 未找到分类 "${categoryName}"，使用默认分类ID: ${categoryId}`);
                  } else {
                    console.log(`行 ${rowIndex}: 未找到分类 "${categoryName}"，且系统中没有分类，跳过此商品`);
                    skippedProductCount++;
                    continue;
                  }
                }
              }
            } else {
              // 如果没有指定分类，使用默认分类
              if (categories.length > 0) {
                categoryId = categories[0].id;
                console.log(`行 ${rowIndex}: 未指定分类，使用默认分类ID: ${categoryId}`);
              } else {
                console.log(`行 ${rowIndex}: 未指定分类，且系统中没有分类，跳过此商品`);
                skippedProductCount++;
                continue;
              }
            }

            // 处理数字和布尔值字段
            let lyPrice = 0;
            let rmbPrice = 0;
            let stock = 0;

            // 获取并解析光年币价格
            const lyPriceValue = getSafeValue(row, '光年币价格');
            if (lyPriceValue !== null) {
              // 尝试解析数字，去除可能的非数字字符
              const parsedValue = lyPriceValue.replace(/[^\d.]/g, '');
              lyPrice = parseFloat(parsedValue) || 0;
            }

            // 获取并解析人民币价格
            const rmbPriceValue = getSafeValue(row, '人民币价格');
            if (rmbPriceValue !== null) {
              const parsedValue = rmbPriceValue.replace(/[^\d.]/g, '');
              rmbPrice = parseFloat(parsedValue) || 0;
            }

            // 获取并解析库存
            const stockValue = getSafeValue(row, '库存');
            if (stockValue !== null) {
              const parsedValue = stockValue.replace(/[^\d]/g, '');
              stock = parseInt(parsedValue, 10) || 0;
            }

            // 获取累计兑换
            const exchangeCount = getSafeValue(row, '累计兑换');

            // 获取描述
            const description = getSafeValue(row, '描述') || '';

            // 处理布尔值
            const isNewValue = getSafeValue(row, '是否新品');
            const isHotValue = getSafeValue(row, '是否热门');

            const isNew = isNewValue === '是' || isNewValue === '新品' ||
                          isNewValue === 'true' || isNewValue === true;
            const isHot = isHotValue === '是' || isHotValue === '热门' ||
                          isHotValue === 'true' || isHotValue === true;

            // 处理状态
            let status = 'active'; // 默认为上架
            const statusValue = getSafeValue(row, '状态');
            if (statusValue) {
              status = (statusValue === '上架' || statusValue === 'active') ? 'active' : 'inactive';
            }

            // 添加到导入列表
            const product = {
              name: nameValue,
              categoryId,
              lyPrice,
              rmbPrice,
              stock,
              exchangeCount,
              description,
              isNew,
              isHot,
              status
            };

            importedProducts.push(product);
            validProductCount++;

            console.log(`处理行 ${rowIndex}: 商品 "${nameValue}", 分类ID: ${categoryId}, 价格: ${lyPrice}/${rmbPrice}, 库存: ${stock}`);
          } catch (rowError) {
            console.error(`处理行 ${rowIndex} 时出错:`, rowError);
            skippedProductCount++;
          }
        }

        console.log(`Excel处理完成: 有效商品 ${validProductCount}, 跳过 ${skippedProductCount}`);

        if (validProductCount === 0) {
          return res.status(400).json({ error: '没有找到有效的商品数据' });
        }

        // 批量创建商品
        console.log(`准备批量创建 ${importedProducts.length} 个商品`);
        try {
          const createdProducts = await Product.bulkCreate(importedProducts);
          console.log(`成功创建 ${createdProducts.length} 个商品`);

          // 删除临时文件
          try {
            fs.unlinkSync(tempPath);
            console.log('临时文件已删除');
          } catch (unlinkError) {
            console.error('删除临时文件时出错:', unlinkError);
          }

          return res.status(201).json({
            message: `成功导入 ${createdProducts.length} 个商品，跳过 ${skippedProductCount} 个无效商品`,
            importedCount: createdProducts.length,
            skippedCount: skippedProductCount
          });
        } catch (createError) {
          console.error('批量创建商品时出错:', createError);
          return res.status(500).json({ error: '导入商品时出现服务器错误', details: createError.message });
        }
      } catch (excelError) {
        console.error('Excel文件处理失败:', excelError);
        return res.status(400).json({ error: '无法处理Excel文件: ' + excelError.message });
      }
    } else if (fileExt === '.csv') {
      // CSV文件处理逻辑保持不变，但增加更宽松的验证和错误处理
      console.log('开始处理CSV文件...');

      try {
        // 创建读取流并处理CSV文件
        const results = [];

        await new Promise((resolve, reject) => {
          fs.createReadStream(tempPath)
            .pipe(csv())
            .on('data', (data) => results.push(data))
            .on('end', resolve)
            .on('error', reject);
        });

        console.log(`CSV解析完成，读取到 ${results.length} 行`);

        // 检查文件不为空
        if (results.length === 0) {
          console.error('CSV文件为空');
          return res.status(400).json({ error: '导入文件为空' });
        }

        // 处理每行数据
        let validProductCount = 0;
        let skippedProductCount = 0;

        for (let i = 0; i < results.length; i++) {
          const row = results[i];
          try {
            const name = row['商品名称'];

            // 检查名称是否存在
            if (!name) {
              console.warn(`第 ${i + 1} 行: 缺少商品名称，跳过`);
              skippedProductCount++;
              continue;
            }

            // 获取分类名称
            const categoryName = row['分类'];
            let categoryId = null;

            // 查找对应的分类ID
            if (categoryName) {
              // 首先尝试精确匹配
              const exactCategory = categories.find(c => c.name === categoryName);
              if (exactCategory) {
                categoryId = exactCategory.id;
              } else {
                // 如果精确匹配失败，尝试模糊匹配
                const similarCategory = categories.find(c =>
                  c.name.includes(categoryName) || categoryName.includes(c.name)
                );
                if (similarCategory) {
                  categoryId = similarCategory.id;
                  console.log(`CSV行 ${i + 1}: 使用模糊匹配找到分类 "${categoryName}" -> "${similarCategory.name}"`);
                } else {
                  // 如果还是找不到，使用默认分类或第一个分类
                  if (categories.length > 0) {
                    categoryId = categories[0].id;
                    console.log(`CSV行 ${i + 1}: 未找到分类 "${categoryName}"，使用默认分类ID: ${categoryId}`);
                  } else {
                    console.log(`CSV行 ${i + 1}: 未找到分类 "${categoryName}"，且系统中没有分类，跳过此商品`);
                    skippedProductCount++;
                    continue;
                  }
                }
              }
            } else {
              // 如果没有指定分类，使用默认分类
              if (categories.length > 0) {
                categoryId = categories[0].id;
                console.log(`CSV行 ${i + 1}: 未指定分类，使用默认分类ID: ${categoryId}`);
              } else {
                console.log(`CSV行 ${i + 1}: 未指定分类，且系统中没有分类，跳过此商品`);
                skippedProductCount++;
                continue;
              }
            }

            // 处理价格与库存数据
            let lyPrice = 0;
            let rmbPrice = 0;
            let stock = 0;

            // 尝试解析价格数据
            const lyPriceStr = row['光年币价格'];
            if (lyPriceStr) {
              const numericValue = lyPriceStr.replace(/[^\d.]/g, '');
              lyPrice = parseFloat(numericValue) || 0;
            }

            const rmbPriceStr = row['人民币价格'];
            if (rmbPriceStr) {
              const numericValue = rmbPriceStr.replace(/[^\d.]/g, '');
              rmbPrice = parseFloat(numericValue) || 0;
            }

            const stockStr = row['库存'];
            if (stockStr) {
              const numericValue = stockStr.replace(/[^\d]/g, '');
              stock = parseInt(numericValue, 10) || 0;
            }

            // 获取累计兑换
            const exchangeCount = row['累计兑换'] ? parseInt(row['累计兑换'], 10) || 0 : 0;

            // 获取描述
            const description = row['描述'] || '';

            // 处理布尔值
            const isNew = row['是否新品'] === '是' || row['是否新品'] === '新品' ||
                          row['是否新品'] === 'true' || row['是否新品'] === true;
            const isHot = row['是否热门'] === '是' || row['是否热门'] === '热门' ||
                          row['是否热门'] === 'true' || row['是否热门'] === true;

            // 处理状态
            let status = 'active'; // 默认为上架
            if (row['状态']) {
              status = (row['状态'] === '上架' || row['状态'] === 'active') ? 'active' : 'inactive';
            }

            // 添加到导入列表
            importedProducts.push({
              name,
              categoryId,
              lyPrice,
              rmbPrice,
              stock,
              exchangeCount,
              description,
              isNew,
              isHot,
              status
            });

            validProductCount++;
            console.log(`处理CSV行 ${i + 1}: 商品 "${name}", 分类ID: ${categoryId}, 价格: ${lyPrice}/${rmbPrice}, 库存: ${stock}`);
          } catch (rowError) {
            console.error(`处理CSV第 ${i + 1} 行时出错:`, rowError);
            skippedProductCount++;
          }
        }

        console.log(`CSV处理完成: 有效商品 ${validProductCount}, 跳过 ${skippedProductCount}`);

        if (validProductCount === 0) {
          return res.status(400).json({ error: '没有找到有效的商品数据' });
        }

        // 批量创建商品
        console.log(`准备批量创建 ${importedProducts.length} 个商品`);
        try {
          const createdProducts = await Product.bulkCreate(importedProducts);
          console.log(`成功创建 ${createdProducts.length} 个商品`);

          // 删除临时文件
          try {
            fs.unlinkSync(tempPath);
            console.log('临时文件已删除');
          } catch (unlinkError) {
            console.error('删除临时文件时出错:', unlinkError);
          }

          return res.status(201).json({
            message: `成功导入 ${createdProducts.length} 个商品，跳过 ${skippedProductCount} 个无效商品`,
            importedCount: createdProducts.length,
            skippedCount: skippedProductCount
          });
        } catch (createError) {
          console.error('批量创建商品时出错:', createError);
          return res.status(500).json({ error: '导入商品时出现服务器错误', details: createError.message });
        }
      } catch (csvError) {
        console.error('CSV文件处理失败:', csvError);
        return res.status(400).json({ error: '无法处理CSV文件: ' + csvError.message });
      }
    } else {
      console.error('不支持的文件类型:', fileExt);
      return res.status(400).json({ error: '不支持的文件类型，仅支持 .xlsx, .xls 和 .csv 文件' });
    }
  } catch (error) {
    console.error('导入商品时出现错误:', error);
    return res.status(500).json({ error: '导入商品时出现服务器错误', details: error.message });
  }
};

/**
 * 导出商品数据
 * 支持CSV和Excel格式
 */
exports.exportProducts = async (req, res) => {
  try {
    // 构建查询条件，复用getProducts方法中的逻辑
    const whereClause = {};

    // 状态筛选
    if (req.query.status) {
      whereClause.status = req.query.status;
    } else if (!req.query.showAll) {
      whereClause.status = 'active';
    }

    // 分类筛选
    if (req.query.category) {
      whereClause.categoryId = req.query.category;
    }

    // 多分类筛选
    if (req.query.categories) {
      const categoryIds = req.query.categories.split(',').map(id => parseInt(id));
      whereClause.categoryId = {
        [Op.in]: categoryIds
      };
    }

    // 搜索查询 - 商品名称
    if (req.query.search) {
      whereClause.name = {
        [Op.like]: `%${req.query.search}%`
      };
    }

    console.log('导出商品查询条件:', JSON.stringify(whereClause));

    // 查询所有符合条件的商品
    const products = await Product.findAll({
      where: whereClause,
      include: [
        {
          model: Category,
          attributes: ['id', 'name']
        }
      ],
      order: [['id', 'ASC']]
    });

    console.log(`查询到 ${products.length} 个商品用于导出`);

    const format = req.query.format || 'csv';
    const timestamp = Date.now();

    if (format === 'excel') {
      console.log('开始导出Excel格式...');
      // 创建Excel工作簿和工作表
      const workbook = new Excel.Workbook();
      const worksheet = workbook.addWorksheet('商品列表');

      // 定义列
      worksheet.columns = [
        { header: 'ID', key: 'id', width: 10 },
        { header: '商品名称', key: 'name', width: 30 },
        { header: '分类', key: 'category', width: 15 },
        { header: '光年币价格', key: 'lyPrice', width: 15 },
        { header: '人民币价格', key: 'rmbPrice', width: 15 },
        { header: '库存', key: 'stock', width: 10 },
        { header: '累计兑换', key: 'exchangeCount', width: 12 },
        { header: '描述', key: 'description', width: 40 },
        { header: '是否新品', key: 'isNew', width: 10 },
        { header: '是否热门', key: 'isHot', width: 10 },
        { header: '状态', key: 'status', width: 10 },
        { header: '创建时间', key: 'createdAt', width: 20 }
      ];

      // 添加数据
      products.forEach(product => {
        const productData = product.toJSON();
        worksheet.addRow({
          id: productData.id,
          name: productData.name,
          category: productData.Category ? productData.Category.name : '未分类',
          lyPrice: productData.lyPrice,
          rmbPrice: productData.rmbPrice,
          stock: productData.stock,
          exchangeCount: productData.exchangeCount,
          description: productData.description || '',
          isNew: productData.isNew ? '是' : '否',
          isHot: productData.isHot ? '是' : '否',
          status: productData.status === 'active' ? '上架' : '下架',
          createdAt: new Date(productData.createdAt).toLocaleString()
        });
      });

      // 设置响应头
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename=products-${timestamp}.xlsx`);

      console.log('准备写入Excel响应...');
      // 将工作簿写入响应
      await workbook.xlsx.write(res);
      console.log('Excel导出完成');
      res.end();
    } else {
      console.log('开始导出CSV格式...');
      // 确保uploads目录存在
      const uploadsDir = path.join(__dirname, '../../uploads');
      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true });
        console.log(`创建目录: ${uploadsDir}`);
      }

      // 默认导出为CSV
      const csvFilePath = path.join(uploadsDir, `products-${timestamp}.csv`);

      // 创建CSV写入器
      const csvWriter = createCsvWriter({
        path: csvFilePath,
        header: [
          { id: 'id', title: 'ID' },
          { id: 'name', title: '商品名称' },
          { id: 'category', title: '分类' },
          { id: 'lyPrice', title: '光年币价格' },
          { id: 'rmbPrice', title: '人民币价格' },
          { id: 'stock', title: '库存' },
          { id: 'exchangeCount', title: '累计兑换' },
          { id: 'description', title: '描述' },
          { id: 'isNew', title: '是否新品' },
          { id: 'isHot', title: '是否热门' },
          { id: 'status', title: '状态' },
          { id: 'createdAt', title: '创建时间' }
        ]
      });

      // 准备数据
      const records = products.map(product => {
        const productData = product.toJSON();
        return {
          id: productData.id,
          name: productData.name,
          category: productData.Category ? productData.Category.name : '未分类',
          lyPrice: productData.lyPrice,
          rmbPrice: productData.rmbPrice,
          stock: productData.stock,
          exchangeCount: productData.exchangeCount,
          description: productData.description || '',
          isNew: productData.isNew ? '是' : '否',
          isHot: productData.isHot ? '是' : '否',
          status: productData.status === 'active' ? '上架' : '下架',
          createdAt: new Date(productData.createdAt).toLocaleString()
        };
      });

      console.log(`准备写入 ${records.length} 条记录到CSV文件...`);
      // 写入CSV文件
      await csvWriter.writeRecords(records);
      console.log(`CSV文件已写入: ${csvFilePath}`);

      // 发送文件
      res.download(csvFilePath, `products-${timestamp}.csv`, (err) => {
        if (err) {
          console.error('下载CSV文件失败:', err);
        }

        // 下载后删除临时文件
        fs.unlink(csvFilePath, (unlinkErr) => {
          if (unlinkErr) {
            console.error('删除临时CSV文件失败:', unlinkErr);
          } else {
            console.log(`临时CSV文件已删除: ${csvFilePath}`);
          }
        });
      });
    }
  } catch (error) {
    console.error('导出商品数据失败:', error);
    res.status(500).json({ message: '导出商品数据失败', error: error.message });
  }
};

/**
 * 获取热门商品（按兑换数量排序）
 */
exports.getPopularProducts = async (req, res) => {
  try {
    console.log('调用热门商品API');
    const limit = parseInt(req.query.limit) || 5;

    // 查询兑换数量最多的商品
    const products = await Product.findAll({
      where: {
        status: 'active',
        exchangeCount: { [Op.gt]: 0 }
      },
      order: [['exchangeCount', 'DESC']],
      limit,
      include: [
        {
          model: Category,
          attributes: ['id', 'name'],
          required: false
        },
        {
          model: ProductImage,
          attributes: ['id', 'imageUrl'],
          required: false,
          limit: 1,
          order: [['sortOrder', 'ASC']]
        }
      ]
    });

    console.log(`找到 ${products.length} 个热门商品`);

    // 如果没有找到符合条件的记录，则返回空数组
    if (products.length === 0) {
      return res.json([]);
    }

    // 格式化数据以匹配前端需要的结构
    const formattedProducts = products.map(product => {
      const images = product.ProductImages || [];
      // 获取主图或使用占位图
      const imageUrl = images.length > 0 && images[0].imageUrl
        ? images[0].imageUrl
        : '/images/placeholder/no-image-small.png';

      return {
        id: product.id,
        name: product.name,
        sales: product.exchangeCount,
        image: imageUrl,
        category: product.Category ? product.Category.name : '未分类'
      };
    });

    return res.json(formattedProducts);
  } catch (error) {
    console.error('获取热门商品失败:', error);
    return res.status(500).json({ message: '服务器错误，请稍后重试', error: error.message });
  }
};

/**
 * 批量删除商品
 * 接收商品ID数组，验证并批量删除
 */
exports.bulkDeleteProducts = async (req, res) => {
  try {
    const { ids } = req.body;

    // 验证参数
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ message: '无效的请求，请提供有效的商品ID数组' });
    }

    console.log(`正在执行批量删除，商品ID: ${ids.join(', ')}`);

    // 先查找这些商品，以便记录日志
    const products = await Product.findAll({
      where: {
        id: {
          [Op.in]: ids
        }
      },
      attributes: ['id', 'name']
    });

    if (products.length === 0) {
      return res.status(404).json({ message: '未找到任何指定的商品' });
    }

    // 获取当前用户信息，用于记录操作日志
    const userId = req.user?.id;
    const username = req.user?.username || '系统';

    // 删除商品图片关联
    await ProductImage.destroy({
      where: {
        productId: {
          [Op.in]: ids
        }
      }
    });

    // 删除商品记录
    const result = await Product.destroy({
      where: {
        id: {
          [Op.in]: ids
        }
      }
    });

    // 记录操作日志
    for (const product of products) {
      await logProductAction({
        productId: product.id,
        productName: product.name,
        action: 'delete',
        details: '批量删除操作',
        userId,
        username
      });
    }

    // 返回结果
    res.json({
      success: true,
      message: `成功删除${result}个商品`,
      deletedCount: result,
      deletedIds: products.map(p => p.id)
    });

  } catch (error) {
    console.error('批量删除商品失败:', error);
    res.status(500).json({ message: '批量删除商品失败', error: error.message });
  }
};

/**
 * 批量更新商品状态
 * @param {Array} req.body.ids - 商品ID数组
 * @param {String} req.body.status - 目标状态 (active/inactive)
 */
exports.bulkUpdateProductStatus = async (req, res) => {
  try {
    const { ids, status } = req.body;

    // 验证参数
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ message: '请提供有效的商品ID数组' });
    }

    if (!status || !['active', 'inactive'].includes(status)) {
      return res.status(400).json({ message: '无效的状态值，状态必须是 active 或 inactive' });
    }

    // 查询商品是否存在
    const products = await Product.findAll({
      where: {
        id: {
          [Op.in]: ids
        }
      }
    });

    if (products.length === 0) {
      return res.status(404).json({ message: '未找到指定的商品' });
    }

    // 统计结果
    const result = {
      totalCount: ids.length,
      successCount: 0,
      failCount: 0,
      notFoundCount: ids.length - products.length
    };

    // 更新商品状态并记录日志
    for (const product of products) {
      try {
        // 保存旧状态
        const oldStatus = product.status;

        // 如果状态已经是目标状态，则跳过
        if (oldStatus === status) {
          continue;
        }

        // 更新商品状态
        await product.update({ status });

        // 记录状态变更日志
        if (req.user) {
          await logProductAction(
            Log.ACTIONS.PRODUCT_BULK_STATUS_CHANGE,
            product,
            req.user,
            JSON.stringify({ status: oldStatus }),
            req
          );
        }

        result.successCount++;
      } catch (error) {
        console.error(`更新商品 ID:${product.id} 状态失败:`, error);
        result.failCount++;
      }
    }

    res.json({
      message: `批量更新商品状态完成: 共${result.totalCount}个, 成功${result.successCount}个, 失败${result.failCount}个, 未找到${result.notFoundCount}个`,
      result
    });
  } catch (error) {
    console.error('批量更新商品状态失败:', error);
    res.status(500).json({ message: '批量更新商品状态失败', error: error.message });
  }
};

/**
 * 批量更新商品库存
 * @param {Array} req.body.ids - 商品ID数组
 * @param {Number} req.body.value - 库存数值
 * @param {String} req.body.operation - 操作类型 (add/subtract/set)
 */
exports.bulkUpdateProductStock = async (req, res) => {
  try {
    const { ids, value, operation } = req.body;

    // 验证参数
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ message: '请提供有效的商品ID数组' });
    }

    if (value === undefined || value === null || isNaN(parseInt(value)) || parseInt(value) < 0) {
      return res.status(400).json({ message: '请提供有效的库存数值（不小于0的整数）' });
    }

    if (!operation || !['add', 'subtract', 'set'].includes(operation)) {
      return res.status(400).json({ message: '无效的操作类型，操作必须是 add, subtract 或 set' });
    }

    const stockValue = parseInt(value);

    // 查询商品是否存在
    const products = await Product.findAll({
      where: {
        id: {
          [Op.in]: ids
        }
      }
    });

    if (products.length === 0) {
      return res.status(404).json({ message: '未找到指定的商品' });
    }

    // 统计结果
    const result = {
      totalCount: ids.length,
      successCount: 0,
      failCount: 0,
      notFoundCount: ids.length - products.length
    };

    // 检查当前职场数量
    const { Workplace } = require('../models');
    const workplaceCount = await Workplace.count();

    // 更新商品库存并记录日志
    for (const product of products) {
      try {
        // 检查库存管理模式
        if (product.stockManagementType === 'workplace' && workplaceCount > 1) {
          console.log(`商品 ID:${product.id} 使用职场分配模式且为多职场环境，跳过批量库存更新`);
          result.failCount++;
          continue;
        }

        // 保存旧库存
        const oldStock = product.stock;
        let newStock;

        // 根据操作类型计算新库存
        switch (operation) {
          case 'add':
            newStock = oldStock + stockValue;
            break;
          case 'subtract':
            newStock = Math.max(0, oldStock - stockValue); // 确保库存不小于0
            break;
          case 'set':
            newStock = stockValue;
            break;
        }

        // 如果库存没有变化，则跳过
        if (oldStock === newStock) {
          continue;
        }

        // 更新商品库存
        await product.update({ stock: newStock });

        // 如果是单职场环境，同步库存到职场库存表
        if (workplaceCount === 1) {
          await ProductWorkplaceStock.update(
            { stock: newStock },
            { where: { productId: product.id } }
          );
        }

        // 记录库存变更日志
        if (req.user) {
          await logProductAction(
            Log.ACTIONS.PRODUCT_BULK_STOCK_UPDATE,
            product,
            req.user,
            JSON.stringify({ stock: oldStock }),
            req
          );
        }

        result.successCount++;
      } catch (error) {
        console.error(`更新商品 ID:${product.id} 库存失败:`, error);
        result.failCount++;
      }
    }

    // 检查是否有职场分配模式的商品（仅在多职场环境下才需要特殊处理）
    const workplaceModeCount = workplaceCount > 1
      ? products.filter(p => p.stockManagementType === 'workplace').length
      : 0;

    let message = `批量更新商品库存完成: 共${result.totalCount}个, 成功${result.successCount}个, 失败${result.failCount}个, 未找到${result.notFoundCount}个`;

    if (workplaceModeCount > 0) {
      message += `。注意：${workplaceModeCount}个商品使用职场分配模式，需要通过库存管理页面单独调整`;
    }

    res.json({
      message,
      result,
      workplaceModeCount,
      workplaceCount
    });
  } catch (error) {
    console.error('批量更新商品库存失败:', error);
    res.status(500).json({ message: '批量更新商品库存失败', error: error.message });
  }
};

/**
 * 获取商品价格历史
 */
exports.getProductPriceHistory = async (req, res) => {
  try {
    const productId = req.params.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    // 验证商品是否存在
    const product = await Product.findByPk(productId);
    if (!product) {
      return res.status(404).json({ message: '商品不存在' });
    }

    // 查询价格历史
    const { count, rows: priceHistory } = await ProductPriceHistory.findAndCountAll({
      where: { productId },
      include: [
        {
          model: User,
          as: 'changer',
          attributes: ['id', 'username', 'email'],
          required: false
        }
      ],
      order: [['effectiveDate', 'DESC']],
      limit,
      offset
    });

    res.json({
      priceHistory,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(count / limit),
        totalItems: count,
        itemsPerPage: limit
      }
    });

  } catch (error) {
    console.error('获取商品价格历史失败:', error);
    res.status(500).json({ message: '获取商品价格历史失败', error: error.message });
  }
};

/**
 * 批量获取商品价格历史统计
 */
exports.getPriceHistoryStats = async (req, res) => {
  try {
    const { startDate, endDate, productIds } = req.query;

    let whereClause = {};

    // 时间范围过滤
    if (startDate || endDate) {
      whereClause.effectiveDate = {};
      if (startDate) {
        whereClause.effectiveDate[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereClause.effectiveDate[Op.lte] = new Date(endDate);
      }
    }

    // 商品ID过滤
    if (productIds) {
      const ids = productIds.split(',').map(id => parseInt(id)).filter(id => !isNaN(id));
      if (ids.length > 0) {
        whereClause.productId = { [Op.in]: ids };
      }
    }

    // 统计价格变更次数
    const stats = await ProductPriceHistory.findAll({
      where: whereClause,
      attributes: [
        'productId',
        [sequelize.fn('COUNT', sequelize.col('id')), 'changeCount'],
        [sequelize.fn('MAX', sequelize.col('effectiveDate')), 'lastChangeDate'],
        [sequelize.fn('MIN', sequelize.col('effectiveDate')), 'firstChangeDate']
      ],
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['id', 'name', 'lyPrice', 'rmbPrice']
        }
      ],
      group: ['productId'],
      order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']]
    });

    res.json({ stats });

  } catch (error) {
    console.error('获取价格历史统计失败:', error);
    res.status(500).json({ message: '获取价格历史统计失败', error: error.message });
  }
};

/**
 * 获取商品统计数据
 * 包括总商品数、上架商品数、下架商品数、低库存商品数、热门商品数、新品数
 */
exports.getProductStats = async (req, res) => {
  try {
    // 库存阈值常量
    const STOCK_THRESHOLD = 5;

    // 查询商品总数
    const totalProducts = await Product.count();

    // 查询上架商品数量
    const activeProducts = await Product.count({
      where: { status: 'active' }
    });

    // 查询下架商品数量
    const inactiveProducts = await Product.count({
      where: { status: 'inactive' }
    });

    // 查询低库存商品数量（上架且库存低于阈值）
    const lowStockProducts = await Product.count({
      where: {
        status: 'active',
        stock: { [Op.lt]: STOCK_THRESHOLD }
      }
    });

    // 查询热门商品数量
    const hotProducts = await Product.count({
      where: { isHot: true }
    });

    // 查询新品数量
    const newProducts = await Product.count({
      where: { isNew: true }
    });

    // 返回统计数据
    res.json({
      total: totalProducts,
      active: activeProducts,
      inactive: inactiveProducts,
      lowStock: lowStockProducts,
      hot: hotProducts,
      new: newProducts
    });
  } catch (error) {
    console.error('获取商品统计数据失败:', error);
    res.status(500).json({ message: '获取商品统计数据失败', error: error.message });
  }
};

/**
 * 批量更新商品价格
 */
exports.batchUpdatePrice = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { productIds, priceUpdates, changeReason } = req.body;

    // 验证请求参数
    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: '商品ID列表不能为空'
      });
    }

    if (!priceUpdates || typeof priceUpdates !== 'object') {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: '价格更新数据格式错误'
      });
    }

    // 查找所有指定的商品
    const products = await Product.findAll({
      where: {
        id: productIds
      },
      transaction
    });

    if (products.length === 0) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: '未找到指定的商品'
      });
    }

    const result = {
      successCount: 0,
      failCount: 0,
      totalAffectedOrders: 0,
      details: []
    };

    // 批量更新商品价格
    for (const product of products) {
      try {
        // 保存更新前的价格
        const oldLyPrice = product.lyPrice;
        const oldRmbPrice = product.rmbPrice;

        // 准备更新数据
        const updateData = {};
        if (priceUpdates.lyPrice !== undefined) {
          updateData.lyPrice = priceUpdates.lyPrice;
        }
        if (priceUpdates.rmbPrice !== undefined) {
          updateData.rmbPrice = priceUpdates.rmbPrice;
        }

        // 检查是否有价格变更
        const priceChanged = (updateData.lyPrice !== undefined && updateData.lyPrice !== oldLyPrice) ||
                            (updateData.rmbPrice !== undefined && updateData.rmbPrice !== oldRmbPrice);

        if (priceChanged) {
          // 分析价格变更影响
          const affectedOrders = await analyzeProductPriceChangeImpact(product.id, transaction);
          result.totalAffectedOrders += affectedOrders;

          // 更新商品价格
          await product.update(updateData, {
            userId: req.user?.id,
            changeReason: changeReason || '批量价格更新',
            transaction
          });

          result.details.push({
            productId: product.id,
            productName: product.name,
            oldLyPrice,
            newLyPrice: updateData.lyPrice || oldLyPrice,
            oldRmbPrice,
            newRmbPrice: updateData.rmbPrice || oldRmbPrice,
            affectedOrders
          });

          // 记录价格变更日志
          if (req.user) {
            await logProductAction(
              'product_batch_price_update',
              product,
              req.user,
              JSON.stringify({ lyPrice: oldLyPrice, rmbPrice: oldRmbPrice }),
              req
            );
          }

          result.successCount++;
        }
      } catch (error) {
        console.error(`更新商品 ID:${product.id} 价格失败:`, error);
        result.failCount++;
      }
    }

    await transaction.commit();

    return res.status(200).json({
      success: true,
      message: `批量价格更新完成，成功：${result.successCount}，失败：${result.failCount}`,
      data: result
    });

  } catch (error) {
    await transaction.rollback();
    console.error('批量更新商品价格失败:', error);
    return res.status(500).json({
      success: false,
      message: '批量更新商品价格失败',
      error: error.message
    });
  }
};
