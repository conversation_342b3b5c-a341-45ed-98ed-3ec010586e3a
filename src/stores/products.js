import { defineStore } from 'pinia'
import { getProducts, getProductById } from '../api/products'
import { ElMessage } from 'element-plus'

export const useProductStore = defineStore('products', {
  state: () => ({
    products: [],
    loading: false,
    totalItems: 0,
    currentPage: 1,
    pageSize: 50,
    selectedCategory: '',
    sortOrder: '',
    searchQuery: '',
    minLyPrice: null,
    maxLyPrice: null,
    minRmbPrice: null,
    maxRmbPrice: null,
    inStock: false,
    isNew: false,
    isHot: false,
    startDate: null,
    endDate: null,
    status: null,
    showAll: false,
    dialogVisible: false,
    currentProduct: null,
    newProductDialogVisible: false,
    newProducts: [],
    requestSequence: 0
  }),

  getters: {
    filteredProducts: (state) => {
      return state.products
    },

    categories: (state) => {
      return [...new Set(state.products.map(product => product.categoryId))]
    },
    
    // 获取当前筛选条件
    currentFilters: (state) => {
      const filters = {}
      
      if (state.searchQuery) filters.search = state.searchQuery
      if (state.selectedCategory) filters.category = state.selectedCategory
      if (state.sortOrder) filters.sort = state.sortOrder
      if (state.minLyPrice !== null) filters.minLyPrice = state.minLyPrice
      if (state.maxLyPrice !== null) filters.maxLyPrice = state.maxLyPrice
      if (state.minRmbPrice !== null) filters.minRmbPrice = state.minRmbPrice
      if (state.maxRmbPrice !== null) filters.maxRmbPrice = state.maxRmbPrice
      if (state.inStock) filters.inStock = true
      if (state.isNew) filters.isNew = true
      if (state.isHot) filters.isHot = true
      if (state.startDate) filters.startDate = state.startDate
      if (state.endDate) filters.endDate = state.endDate
      if (state.status) filters.status = state.status
      
      // 总是明确传递showAll参数，不管是true还是false
      filters.showAll = !!state.showAll
      
      return filters
    }
  },

  actions: {
    // 从API加载商品数据
    async fetchProducts() {
      const currentSequence = ++this.requestSequence
      
      this.loading = true
      try {
        // 准备查询参数
        const params = {
          page: this.currentPage,
          limit: this.pageSize,
          includeWorkplaceStocks: true, // 包含职场库存信息
          ...this.currentFilters
        }

        // 发送API请求
        const response = await getProducts(params)
        
        // 检查这是否是最新的请求，如果不是则忽略结果
        if (currentSequence !== this.requestSequence) {
          console.log('忽略过期的API响应，序列号:', currentSequence, '当前序列号:', this.requestSequence)
          return response
        }
        
        // 更新状态
        this.products = response.data
        this.totalItems = response.total
        
        return response
      } catch (error) {
        // 只有在这是最新请求时才显示错误
        if (currentSequence === this.requestSequence) {
          console.error('获取商品失败:', error)
          ElMessage.error('获取商品数据失败')
        }
        return null
      } finally {
        // 只有在这是最新请求时才设置loading为false
        if (currentSequence === this.requestSequence) {
          this.loading = false
        }
      }
    },

    // 获取单个商品详情
    async fetchProductById(id) {
      try {
        const product = await getProductById(id)
        return product
      } catch (error) {
        console.error(`获取商品(ID:${id})失败:`, error)
        ElMessage.error('获取商品详情失败')
        return null
      }
    },

    // 设置分页参数
    setPage(page) {
      this.currentPage = page
      this.fetchProducts()
    },

    // 设置每页条数
    setPageSize(size) {
      this.pageSize = size
      this.currentPage = 1 // 重置到第一页
      this.fetchProducts()
    },

    // 设置分类筛选
    setSelectedCategory(category) {
      this.selectedCategory = category
      this.currentPage = 1 // 重置到第一页
      this.fetchProducts()
    },

    // 设置排序 - 增强版本，防止状态不一致
    setSortOrder(order) {
      // 确保排序值确实改变了才触发更新
      if (this.sortOrder !== order) {
        console.log('设置排序:', this.sortOrder, '->', order)
        this.sortOrder = order
        this.fetchProducts()
      }
    },

    // 设置搜索关键词
    setSearchQuery(query) {
      this.searchQuery = query
      this.currentPage = 1 // 重置到第一页
      this.fetchProducts()
    },
    
    // 批量更新所有筛选状态 - 新增方法，确保原子性
    updateAllFilters(updates) {
      let hasChanges = false
      
      // 检查并更新搜索查询
      if (updates.search !== undefined && this.searchQuery !== updates.search) {
        this.searchQuery = updates.search
        hasChanges = true
      }
      
      // 检查并更新分类
      if (updates.category !== undefined && this.selectedCategory !== updates.category) {
        this.selectedCategory = updates.category
        hasChanges = true
      }
      
      // 检查并更新排序
      if (updates.sort !== undefined && this.sortOrder !== updates.sort) {
        this.sortOrder = updates.sort
        hasChanges = true
      }
      
      // 检查并更新其他筛选条件
      if (updates.filters) {
        const filters = updates.filters
        
        if (filters.minLyPrice !== undefined && this.minLyPrice !== filters.minLyPrice) {
          this.minLyPrice = filters.minLyPrice
          hasChanges = true
        }
        if (filters.maxLyPrice !== undefined && this.maxLyPrice !== filters.maxLyPrice) {
          this.maxLyPrice = filters.maxLyPrice
          hasChanges = true
        }
        if (filters.minRmbPrice !== undefined && this.minRmbPrice !== filters.minRmbPrice) {
          this.minRmbPrice = filters.minRmbPrice
          hasChanges = true
        }
        if (filters.maxRmbPrice !== undefined && this.maxRmbPrice !== filters.maxRmbPrice) {
          this.maxRmbPrice = filters.maxRmbPrice
          hasChanges = true
        }
        if (filters.inStock !== undefined && this.inStock !== filters.inStock) {
          this.inStock = filters.inStock
          hasChanges = true
        }
        if (filters.isNew !== undefined && this.isNew !== filters.isNew) {
          this.isNew = filters.isNew
          hasChanges = true
        }
        if (filters.isHot !== undefined && this.isHot !== filters.isHot) {
          this.isHot = filters.isHot
          hasChanges = true
        }
        if (filters.startDate !== undefined && this.startDate !== filters.startDate) {
          this.startDate = filters.startDate
          hasChanges = true
        }
        if (filters.endDate !== undefined && this.endDate !== filters.endDate) {
          this.endDate = filters.endDate
          hasChanges = true
        }
      }
      
      console.log('批量更新筛选状态:', { updates, hasChanges })
      
      // 只有在确实有变化时才重置页面并获取数据
      if (hasChanges) {
        this.currentPage = 1 // 重置到第一页
        this.fetchProducts()
      }
    },
    
    // 设置多个筛选条件 - 批量更新以确保原子性
    setFilters(filters) {
      // 记录哪些字段发生了变化
      let hasChanges = false
      
      if (filters.minLyPrice !== undefined && this.minLyPrice !== filters.minLyPrice) {
        this.minLyPrice = filters.minLyPrice
        hasChanges = true
      }
      if (filters.maxLyPrice !== undefined && this.maxLyPrice !== filters.maxLyPrice) {
        this.maxLyPrice = filters.maxLyPrice
        hasChanges = true
      }
      if (filters.minRmbPrice !== undefined && this.minRmbPrice !== filters.minRmbPrice) {
        this.minRmbPrice = filters.minRmbPrice
        hasChanges = true
      }
      if (filters.maxRmbPrice !== undefined && this.maxRmbPrice !== filters.maxRmbPrice) {
        this.maxRmbPrice = filters.maxRmbPrice
        hasChanges = true
      }
      if (filters.inStock !== undefined && this.inStock !== filters.inStock) {
        this.inStock = filters.inStock
        hasChanges = true
      }
      if (filters.isNew !== undefined && this.isNew !== filters.isNew) {
        this.isNew = filters.isNew
        hasChanges = true
      }
      if (filters.isHot !== undefined && this.isHot !== filters.isHot) {
        this.isHot = filters.isHot
        hasChanges = true
      }
      if (filters.startDate !== undefined && this.startDate !== filters.startDate) {
        this.startDate = filters.startDate
        hasChanges = true
      }
      if (filters.endDate !== undefined && this.endDate !== filters.endDate) {
        this.endDate = filters.endDate
        hasChanges = true
      }
      if (filters.status !== undefined && this.status !== filters.status) {
        this.status = filters.status
        hasChanges = true
      }
      if (filters.showAll !== undefined && this.showAll !== filters.showAll) {
        this.showAll = filters.showAll
        hasChanges = true
      }
      
      // 只有在确实有变化时才重置页面并获取数据
      if (hasChanges) {
        this.currentPage = 1 // 重置到第一页
        this.fetchProducts()
      }
    },
    
    // 重置所有筛选
    resetFilters() {
      this.selectedCategory = ''
      this.sortOrder = ''
      this.searchQuery = ''
      this.minLyPrice = null
      this.maxLyPrice = null
      this.minRmbPrice = null
      this.maxRmbPrice = null
      this.inStock = false
      this.isNew = false
      this.isHot = false
      this.startDate = null
      this.endDate = null
      this.status = null
      this.showAll = false
      
      this.currentPage = 1 // 重置到第一页
      
      // 强制重新获取数据，确保应用了新的筛选条件
      this.fetchProducts()
    },

    showExchangeDialog(product) {
      this.currentProduct = product
      this.dialogVisible = true
    },

    hideExchangeDialog() {
      this.dialogVisible = false
      this.currentProduct = null
    },

    showNewProductDialog(products) {
      this.newProducts = products
      this.newProductDialogVisible = true
    },

    hideNewProductDialog() {
      this.newProductDialogVisible = false
      this.newProducts = []
    }
  }
})