# 光年小卖部 - 部署检查清单

本文档提供完整的部署前、部署中、部署后检查清单，确保部署过程顺利进行。

## 部署前检查清单

### 1. 服务器环境检查

- [ ] **服务器基本信息确认**
  - 服务器IP: **************
  - 用户: root
  - 密码: Aa@123456
  - 部署目录: /www/wwwroot/workyy

- [ ] **网络连接检查**
  ```bash
  # 测试SSH连接
  ssh root@**************
  
  # 测试网络连通性
  ping google.com
  ```

- [ ] **系统资源检查**
  ```bash
  # 检查磁盘空间（至少5GB可用）
  df -h
  
  # 检查内存（至少2GB）
  free -h
  
  # 检查CPU
  lscpu
  ```

- [ ] **端口可用性检查**
  ```bash
  # 检查关键端口是否被占用
  netstat -tlnp | grep -E ':(80|3000|3306)'
  ```

### 2. 代码仓库检查

- [ ] **Gitee仓库访问**
  - 确认仓库地址正确
  - 确认feat/reset分支存在
  - 确认有访问权限

- [ ] **本地代码检查**
  ```bash
  # 检查当前分支
  git branch -a
  
  # 检查代码状态
  git status
  
  # 确认最新提交
  git log --oneline -5
  ```

### 3. 配置文件准备

- [ ] **环境变量配置**
  - 数据库连接信息
  - 飞书应用配置
  - JWT密钥配置
  - CORS配置

- [ ] **必要文件检查**
  - package.json (前端和后端)
  - .env.example文件
  - Nginx配置模板

## 部署中检查清单

### 1. 系统依赖安装

- [ ] **Node.js安装**
  ```bash
  node --version  # 应该是v18.x或更高
  npm --version   # 应该是9.x或更高
  ```

- [ ] **PM2安装**
  ```bash
  pm2 --version   # 确认PM2已安装
  ```

- [ ] **Nginx安装**
  ```bash
  nginx -v        # 确认Nginx已安装
  systemctl status nginx  # 确认服务状态
  ```

- [ ] **MySQL安装**
  ```bash
  mysql --version # 确认MySQL已安装
  systemctl status mysqld # 确认服务状态
  ```

### 2. 项目代码部署

- [ ] **代码克隆/更新**
  ```bash
  cd /www/wwwroot/workyy
  git branch      # 确认在feat/reset分支
  git status      # 确认代码是最新的
  ```

- [ ] **依赖安装**
  ```bash
  # 前端依赖
  npm list --depth=0
  
  # 后端依赖
  cd server && npm list --depth=0
  
  # 确认nodejieba已移除
  npm list nodejieba  # 应该显示未安装
  ```

- [ ] **前端构建**
  ```bash
  # 检查构建结果
  ls -la dist/
  ls -la dist/index.html  # 确认主文件存在
  ```

### 3. 数据库配置

- [ ] **数据库连接测试**
  ```bash
  mysql -u root -p feishu_mall -e "SHOW TABLES;"
  ```

- [ ] **数据库初始化**
  ```bash
  cd server
  npm run init-db  # 确认无错误
  ```

### 4. 服务启动

- [ ] **PM2应用启动**
  ```bash
  pm2 list         # 确认应用在运行
  pm2 logs feishu-mall-api --lines 10  # 检查启动日志
  ```

- [ ] **Nginx配置**
  ```bash
  nginx -t         # 确认配置语法正确
  systemctl status nginx  # 确认服务运行
  ```

## 部署后检查清单

### 1. 服务状态检查

- [ ] **所有服务运行状态**
  ```bash
  # PM2应用状态
  pm2 list
  
  # Nginx状态
  systemctl status nginx
  
  # MySQL状态
  systemctl status mysqld
  
  # 端口监听状态
  netstat -tlnp | grep -E ':(80|3000|3306)'
  ```

### 2. 功能测试

- [ ] **后端API测试**
  ```bash
  # 健康检查
  curl http://localhost:3000/api/health
  curl http://**************/api/health
  
  # 测试数据库连接
  curl http://**************/api/test/db
  ```

- [ ] **前端页面测试**
  ```bash
  # 首页访问
  curl -I http://**************/
  
  # 静态资源
  curl -I http://**************/assets/index.js
  ```

- [ ] **文件上传测试**
  ```bash
  # 测试上传目录权限
  ls -la /www/wwwroot/workyy/server/uploads/
  
  # 测试上传接口
  curl -X POST http://**************/api/upload \
    -F "file=@test.jpg"
  ```

### 3. 用户功能测试

- [ ] **用户注册/登录**
  - 访问前端页面
  - 测试用户注册功能
  - 测试用户登录功能
  - 验证JWT Token生成

- [ ] **商品管理**
  - 测试商品列表显示
  - 测试商品添加功能
  - 测试图片上传功能
  - 测试商品编辑功能

- [ ] **订单管理**
  - 测试订单创建
  - 测试订单状态更新
  - 测试订单查询

### 4. 性能和安全检查

- [ ] **性能检查**
  ```bash
  # 检查内存使用
  free -h
  
  # 检查CPU使用
  top
  
  # 检查磁盘使用
  df -h
  
  # 检查应用性能
  pm2 monit
  ```

- [ ] **安全检查**
  ```bash
  # 检查防火墙状态
  firewall-cmd --state
  firewall-cmd --list-ports
  
  # 检查文件权限
  ls -la /www/wwwroot/workyy/
  ls -la /www/wwwroot/workyy/server/.env.production
  ```

- [ ] **日志检查**
  ```bash
  # 应用日志
  tail -f /www/wwwroot/workyy/logs/combined.log
  
  # Nginx日志
  tail -f /www/wwwroot/workyy/logs/nginx_access.log
  tail -f /www/wwwroot/workyy/logs/nginx_error.log
  
  # 系统日志
  journalctl -u nginx -f
  journalctl -u mysqld -f
  ```

## 常见问题检查

### 1. 端口冲突

- [ ] **检查端口占用**
  ```bash
  netstat -tlnp | grep :80
  netstat -tlnp | grep :3000
  netstat -tlnp | grep :3306
  ```

- [ ] **解决端口冲突**
  ```bash
  # 查找占用进程
  lsof -i :3000
  
  # 终止进程
  kill -9 <PID>
  ```

### 2. 权限问题

- [ ] **文件权限检查**
  ```bash
  # 项目目录权限
  ls -la /www/wwwroot/
  
  # 上传目录权限
  ls -la /www/wwwroot/workyy/server/uploads/
  
  # 日志目录权限
  ls -la /www/wwwroot/workyy/logs/
  ```

- [ ] **权限修复**
  ```bash
  chown -R root:root /www/wwwroot/workyy
  chmod -R 755 /www/wwwroot/workyy
  chmod -R 777 /www/wwwroot/workyy/server/uploads
  ```

### 3. 依赖问题

- [ ] **Node.js版本兼容性**
  ```bash
  node --version
  npm --version
  ```

- [ ] **依赖安装完整性**
  ```bash
  cd /www/wwwroot/workyy
  npm list --depth=0
  
  cd server
  npm list --depth=0
  ```

### 4. 数据库连接问题

- [ ] **数据库服务状态**
  ```bash
  systemctl status mysqld
  ```

- [ ] **数据库连接测试**
  ```bash
  mysql -u root -p -e "SHOW DATABASES;"
  mysql -u root -p feishu_mall -e "SHOW TABLES;"
  ```

## 回滚计划

如果部署失败，按以下步骤回滚：

### 1. 停止新服务

```bash
pm2 stop feishu-mall-api
systemctl stop nginx
```

### 2. 恢复备份

```bash
# 恢复代码
cd /www/wwwroot/workyy
git checkout <previous-commit>

# 恢复配置文件
cp server/.env.production.backup server/.env.production
cp /etc/nginx/conf.d/workyy.conf.backup /etc/nginx/conf.d/workyy.conf
```

### 3. 重启服务

```bash
pm2 start feishu-mall-api
systemctl start nginx
```

## 部署完成确认

- [ ] **所有检查项目已完成**
- [ ] **功能测试通过**
- [ ] **性能指标正常**
- [ ] **日志无错误**
- [ ] **用户可以正常访问**

## 部署后维护

### 1. 监控设置

- [ ] **设置监控脚本**
- [ ] **配置告警通知**
- [ ] **定期备份计划**

### 2. 文档更新

- [ ] **更新部署文档**
- [ ] **记录配置变更**
- [ ] **更新运维手册**

---

**注意事项**：
1. 每个检查项目都必须完成并确认无误
2. 如果任何检查项目失败，必须解决后再继续
3. 保留所有检查过程的日志和截图
4. 部署完成后进行全面的功能测试
5. 建议在非业务高峰期进行部署操作
