# 项目结构说明

## 目录结构

- **src/**: 前端源代码
  - **api/**: API请求函数
  - **components/**: Vue组件
  - **router/**: 路由配置
  - **stores/**: Pinia状态管理
  - **utils/**: 工具函数
  - **views/**: 页面视图组件

- **server/**: 后端源代码
  - **controllers/**: 控制器
  - **models/**: 数据模型
  - **routes/**: 路由定义
  - **services/**: 服务层
  - **middlewares/**: 中间件
  - **uploads/**: 文件上传目录（统一）
  - **utils/**: 工具函数

- **scripts/**: 脚本文件
  - **deploy/**: 部署相关脚本
  - **db/**: 数据库相关脚本
  - **utils/**: 实用工具脚本

- **docs/**: 文档
  - **api/**: API文档
  - **dev/**: 开发文档
  - **deploy/**: 部署文档

- **temp/**: 临时文件目录（开发环境使用）

- **public/**: 静态资源目录

- **dist/**: 构建输出目录

## 文件说明

- **package.json**: 前端依赖配置
- **server/package.json**: 后端依赖配置
- **vite.config.js**: Vite构建配置
- **server/server.js**: 后端入口文件

## 开发指南

1. 安装依赖：
   
added 181 packages in 1s

85 packages are looking for funding
  run `npm fund` for details

up to date in 651ms

64 packages are looking for funding
  run `npm fund` for details

2. 开发环境启动：
   [1;33m===== 项目重启脚本 =====[0m
[0;34m===== 当前环境变量设置 =====[0m
NODE_ENV: development
SERVER_URL: http://localhost:3000
FEISHU_REDIRECT_URI: http://localhost:3000/api/feishu/callback
[0;34m=========================[0m
[1;33m警告: 检测到生产环境配置文件，在本地开发时将被忽略[0m
[0;34m正在检查MySQL服务状态...[0m
[0;32mMySQL服务运行正常[0m
[0;32m数据库 feishu_mall 已就绪[0m
[0;34m正在停止所有相关进程...[0m
停止后端服务器进程 (PID: 43686, 端口: 3000)
停止前端服务器进程 (PID: 43093
43721, 端口: 5173)
等待进程终止...
[0;34m清除环境缓存...[0m
[0;34m更新开发环境配置...[0m
[0;32m已更新环境变量文件[0m
[0;34m正在启动后端服务器...[0m
已启动后端服务器 (PID: 45175)
等待后端服务器启动...
[0;32m后端服务器启动成功! (PID: 45175)[0m
[0;34m验证服务器环境设置...[0m
[0;32m服务器确认运行在开发环境模式[0m
[0;34m正在测试日志系统...[0m
[0;32m日志系统运行正常[0m
[0;34m正在启动前端开发服务器...[0m
等待前端开发服务器启动...
[0;32m前端服务器运行在端口: 5173[0m

[0;32m===== 重启完成 =====[0m
[0;34m后端API: [0mhttp://localhost:3000/api
[0;34m日志管理: [0mhttp://localhost:3000/api/logs
[0;34m前端应用: [0mhttp://localhost:5173
[1;33m如果页面显示'服务器未连接'，请尝试刷新浏览器 (Ctrl+F5)[0m
[1;33m查看日志: 后端 - /Users/<USER>/Desktop/chattywork/workyy/server/server.log  前端 - /Users/<USER>/Desktop/chattywork/workyy/frontend.log[0m

[0;32m===== 环境信息 =====[0m
[0;34m当前模式: [0m本地开发环境
[0;34m后端环境: [0mdevelopment
[0;34mAPI基础URL: [0mhttp://localhost:3000
[1;33m如果支付宝收款码不显示，请尝试访问 http://localhost:5173/test-payment.html 进行测试[0m

3. 生产环境构建：
   
> exchange-mall@0.0.1 build
> vite build

vite v4.5.9 building for production...
transforming...
✓ 2440 modules transformed.
rendering chunks...
computing gzip size...
dist/index.html                                      0.45 kB │ gzip:   0.36 kB
dist/assets/CategoryManagement-8869dd4f.css          0.23 kB │ gzip:   0.17 kB
dist/assets/IntelligentSchedule-97c7fbf4.css         0.75 kB │ gzip:   0.30 kB
dist/assets/LogManagement-4f62dc8c.css               0.91 kB │ gzip:   0.37 kB
dist/assets/Exchanges-d84420d0.css                   0.93 kB │ gzip:   0.36 kB
dist/assets/NotificationsPage-de195953.css           1.33 kB │ gzip:   0.45 kB
dist/assets/AnnouncementManagement-0d027163.css      1.52 kB │ gzip:   0.55 kB
dist/assets/FeishuMessageTemplates-489bbaa6.css      1.62 kB │ gzip:   0.50 kB
dist/assets/ExchangeManagement-7dff3635.css          2.12 kB │ gzip:   0.72 kB
dist/assets/FeishuCallback-bf03a588.css              2.37 kB │ gzip:   0.72 kB
dist/assets/Dashboard-4c7550dc.css                   2.95 kB │ gzip:   0.80 kB
dist/assets/FeedbackManagement-1ced0504.css          3.00 kB │ gzip:   0.82 kB
dist/assets/ProductManagement-19db6ef8.css           3.26 kB │ gzip:   0.89 kB
dist/assets/UserManagement-26528853.css              3.42 kB │ gzip:   1.05 kB
dist/assets/Profile-a2ac7cdf.css                     4.01 kB │ gzip:   1.09 kB
dist/assets/DiagnosticTools-6fde9f96.css             4.49 kB │ gzip:   1.04 kB
dist/assets/AdminLayout-cdc412e5.css                 4.96 kB │ gzip:   1.34 kB
dist/assets/Login-e8adc2d1.css                       5.26 kB │ gzip:   1.45 kB
dist/assets/productImages-700ab230.css               5.52 kB │ gzip:   1.28 kB
dist/assets/SystemSettings-c41002f9.css             12.41 kB │ gzip:   2.44 kB
dist/assets/HelpCenter-ef655a80.css                 12.72 kB │ gzip:   1.77 kB
dist/assets/Home-86bbff40.css                       27.46 kB │ gzip:   5.27 kB
dist/assets/index-33760866.css                     343.60 kB │ gzip:  48.19 kB
dist/assets/categories-b5341ff8.js                   0.26 kB │ gzip:   0.18 kB
dist/assets/announcements-22c9e6c4.js                0.30 kB │ gzip:   0.19 kB
dist/assets/feedback-d8186641.js                     0.33 kB │ gzip:   0.18 kB
dist/assets/format-b5023182.js                       0.71 kB │ gzip:   0.43 kB
dist/assets/products-2a0cbcd8.js                     0.93 kB │ gzip:   0.53 kB
dist/assets/users-ff4a3b55.js                        1.54 kB │ gzip:   0.83 kB
dist/assets/notifications-bf7ba533.js                1.85 kB │ gzip:   0.76 kB
dist/assets/exchanges-da7211ca.js                    1.87 kB │ gzip:   0.89 kB
dist/assets/FeishuCallback-c3a4cb6c.js               2.54 kB │ gzip:   1.60 kB
dist/assets/system-127d8460.js                       2.86 kB │ gzip:   1.35 kB
dist/assets/NotificationsPage-201487ce.js            3.58 kB │ gzip:   1.74 kB
dist/assets/imageUtils-11e07857.js                   3.61 kB │ gzip:   1.41 kB
dist/assets/CategoryManagement-131a8d68.js           4.14 kB │ gzip:   1.99 kB
dist/assets/productImages-e9b81664.js                7.89 kB │ gzip:   2.78 kB
dist/assets/IntelligentSchedule-e5fc248a.js          8.44 kB │ gzip:   3.33 kB
dist/assets/Exchanges-c9b09481.js                    9.11 kB │ gzip:   3.68 kB
dist/assets/AnnouncementManagement-d7e80b21.js       9.42 kB │ gzip:   3.96 kB
dist/assets/FeedbackManagement-470b36a8.js          10.30 kB │ gzip:   4.15 kB
dist/assets/AdminLayout-868666a1.js                 11.33 kB │ gzip:   4.24 kB
dist/assets/Login-d8e08c67.js                       14.91 kB │ gzip:   6.15 kB
dist/assets/FeishuMessageTemplates-39bfc6cd.js      16.06 kB │ gzip:   5.93 kB
dist/assets/Profile-c7d901a4.js                     16.82 kB │ gzip:   5.53 kB
dist/assets/DiagnosticTools-5ca4c43b.js             19.25 kB │ gzip:   6.28 kB
dist/assets/ExchangeManagement-1180139c.js          24.05 kB │ gzip:   8.28 kB
dist/assets/ProductManagement-d2910509.js           27.85 kB │ gzip:   9.83 kB
dist/assets/UserManagement-9c77de24.js              30.55 kB │ gzip:   9.66 kB
dist/assets/LogManagement-f91e721c.js               39.70 kB │ gzip:  11.89 kB
dist/assets/SystemSettings-18b743c7.js              47.24 kB │ gzip:  15.19 kB
dist/assets/Home-e5693a77.js                        50.39 kB │ gzip:  17.70 kB
dist/assets/HelpCenter-3960144b.js                  66.88 kB │ gzip:  26.38 kB
dist/assets/Dashboard-6f82691b.js                  228.06 kB │ gzip:  76.13 kB
dist/assets/installCanvasRenderer-b9f4b165.js      545.59 kB │ gzip: 185.41 kB
dist/assets/index-b7a959a1.js                    1,143.27 kB │ gzip: 384.25 kB
✓ built in 8.43s

4. 生产环境部署：
   

## 注意事项

- 所有上传文件统一存储在 `server/uploads` 目录
- 临时文件请放在 `temp` 目录下对应子目录
- 新增脚本请放在 `scripts` 目录下对应子目录
