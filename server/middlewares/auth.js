const jwt = require('jsonwebtoken');
const { User } = require('../models');
const config = require('../config/config');

/**
 * 认证中间件 - 验证JWT令牌并获取用户信息
 */
const authenticate = async (req, res, next) => {
  try {
    console.log('开始认证流程...');
    // 从请求头获取token
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('未找到认证令牌或格式不正确:', authHeader);
      return res.status(401).json({ message: '未提供认证令牌' });
    }

    // 获取令牌
    const token = authHeader.split(' ')[1];
    console.log('收到认证令牌, 长度:', token ? token.length : 0);

    // 验证令牌
    console.log('尝试验证令牌...');
    const decoded = jwt.verify(token, config.jwt.secret);
    console.log('令牌验证成功, 用户ID:', decoded.id);

    // 查询用户
    console.log('查询用户数据...');
    const user = await User.findByPk(decoded.id);

    if (!user) {
      console.log('用户不存在, ID:', decoded.id);
      return res.status(401).json({ message: '用户不存在' });
    }

    // 检查用户是否被禁用
    if (user.isActive === false) {
      console.log('用户已被禁用, ID:', decoded.id, '用户名:', user.username);
      return res.status(403).json({
        message: '账号已被禁用，请联系管理员',
        code: 'ACCOUNT_DISABLED'
      });
    }

    console.log('认证成功, 用户:', user.username, '角色:', user.role);

    // 将用户添加到请求对象
    req.user = user;
    req.userId = user.id;  // 保持兼容性

    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ message: '令牌已过期' });
    }

    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ message: '无效的令牌' });
    }

    console.error('认证错误:', error);
    res.status(500).json({ message: '认证失败' });
  }
};

/**
 * 管理员权限验证中间件
 * 验证用户是否具有管理员权限
 */
const isAdmin = (req, res, next) => {
  if (!req.user || req.user.role !== 'admin') {
    return res.status(403).json({ message: '没有管理员权限' });
  }
  next();
};

// 确保先声明checkAuth变量
const checkAuth = authenticate;

// 导出功能
module.exports = {
  authenticate,
  isAdmin,
  checkAuth
};
