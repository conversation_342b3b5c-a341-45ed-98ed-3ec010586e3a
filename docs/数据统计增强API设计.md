# 数据统计增强API设计

## 1. 用户转化漏斗分析API

### 接口：`GET /api/dashboard/conversion-funnel`

**查询参数**：
- `period`: 时间周期 (week/month/quarter)
- `startDate`: 开始日期
- `endDate`: 结束日期

**响应格式**：
```json
{
  "success": true,
  "data": {
    "period": "month",
    "funnel": [
      {
        "stage": "访问用户",
        "count": 10000,
        "rate": 100,
        "description": "网站访问用户数"
      },
      {
        "stage": "注册用户",
        "count": 1200,
        "rate": 12,
        "description": "完成注册的用户数"
      },
      {
        "stage": "活跃用户",
        "count": 800,
        "rate": 8,
        "description": "有登录行为的用户数"
      },
      {
        "stage": "下单用户",
        "count": 300,
        "rate": 3,
        "description": "完成首次下单的用户数"
      },
      {
        "stage": "复购用户",
        "count": 120,
        "rate": 1.2,
        "description": "有多次购买行为的用户数"
      }
    ],
    "conversionRates": {
      "visitToRegister": 12.0,
      "registerToActive": 66.7,
      "activeToOrder": 37.5,
      "orderToRepeat": 40.0
    }
  }
}
```

## 2. 商品运营效率分析API

### 接口：`GET /api/dashboard/product-efficiency`

**查询参数**：
- `period`: 分析周期 (week/month/quarter)
- `categoryId`: 商品分类ID (可选)

**响应格式**：
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalProducts": 150,
      "activeProducts": 120,
      "lowStockProducts": 15,
      "outOfStockProducts": 8,
      "slowMovingProducts": 25
    },
    "turnoverRate": {
      "average": 2.5,
      "top10": [
        {
          "productId": 1,
          "productName": "商品A",
          "turnoverRate": 8.2,
          "exchangeCount": 45,
          "averageStock": 5.5
        }
      ]
    },
    "stockAlerts": [
      {
        "productId": 2,
        "productName": "商品B",
        "currentStock": 3,
        "alertType": "low_stock",
        "recommendedAction": "补货"
      }
    ],
    "slowMovingProducts": [
      {
        "productId": 3,
        "productName": "商品C",
        "daysSinceLastSale": 45,
        "currentStock": 20,
        "recommendedAction": "促销或下架"
      }
    ]
  }
}
```

## 3. 支付方式偏好分析API

### 接口：`GET /api/dashboard/payment-preference`

**查询参数**：
- `period`: 时间周期 (week/month/quarter/year)
- `groupBy`: 分组方式 (category/user/time)

**响应格式**：
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalOrders": 1000,
      "rmbOrders": 400,
      "lyOrders": 600,
      "rmbPercentage": 40.0,
      "lyPercentage": 60.0
    },
    "paymentTrend": [
      {
        "date": "2024-01-01",
        "rmbCount": 15,
        "lyCount": 25,
        "rmbAmount": 1500.00,
        "lyAmount": 2500
      }
    ],
    "categoryPreference": [
      {
        "categoryId": 1,
        "categoryName": "数码产品",
        "rmbPercentage": 70.0,
        "lyPercentage": 30.0,
        "totalOrders": 200
      }
    ],
    "userSegments": {
      "rmbOnly": 150,
      "lyOnly": 200,
      "mixed": 100,
      "preferences": {
        "highValueRmb": 80,
        "frequentLy": 120
      }
    }
  }
}
```

## 4. 用户行为洞察分析API

### 接口：`GET /api/dashboard/user-insights`

**查询参数**：
- `period`: 分析周期
- `segment`: 用户分层 (new/active/dormant/lost)

**响应格式**：
```json
{
  "success": true,
  "data": {
    "userSegmentation": {
      "newUsers": {
        "count": 120,
        "percentage": 20.0,
        "avgOrderValue": 85.50
      },
      "activeUsers": {
        "count": 300,
        "percentage": 50.0,
        "avgOrderValue": 125.30
      },
      "dormantUsers": {
        "count": 150,
        "percentage": 25.0,
        "avgOrderValue": 95.20
      },
      "lostUsers": {
        "count": 30,
        "percentage": 5.0,
        "avgOrderValue": 110.80
      }
    },
    "lifetimeValue": {
      "average": 285.60,
      "median": 180.00,
      "top10Percent": 850.30,
      "distribution": [
        {"range": "0-100", "count": 200},
        {"range": "100-300", "count": 180},
        {"range": "300-500", "count": 120},
        {"range": "500+", "count": 100}
      ]
    },
    "purchaseFrequency": {
      "average": 2.8,
      "distribution": [
        {"frequency": 1, "userCount": 250},
        {"frequency": 2, "userCount": 180},
        {"frequency": "3-5", "userCount": 120},
        {"frequency": "6+", "userCount": 50}
      ]
    }
  }
}
```

## 5. 实时运营监控API

### 接口：`GET /api/dashboard/realtime-monitor`

**响应格式**：
```json
{
  "success": true,
  "data": {
    "realtime": {
      "timestamp": "2024-01-15T10:30:00Z",
      "todayOrders": 45,
      "todayRevenue": {
        "rmb": 2850.50,
        "ly": 1200
      },
      "todayNewUsers": 12,
      "onlineUsers": 28
    },
    "alerts": [
      {
        "type": "stock_alert",
        "level": "warning",
        "message": "商品A库存不足",
        "productId": 1,
        "currentStock": 2
      },
      {
        "type": "order_anomaly",
        "level": "error",
        "message": "订单#12345支付异常",
        "orderId": 12345
      }
    ],
    "systemHealth": {
      "status": "healthy",
      "responseTime": 120,
      "errorRate": 0.02,
      "uptime": "99.9%"
    },
    "hourlyStats": [
      {
        "hour": "09:00",
        "orders": 8,
        "revenue": 680.50,
        "users": 15
      }
    ]
  }
}
```

## 数据库查询优化建议

### 1. 新增索引
```sql
-- 用户行为分析索引
CREATE INDEX idx_users_created_last_login ON users(createdAt, lastLoginAt);
CREATE INDEX idx_exchanges_user_created ON exchanges(userId, createdAt);
CREATE INDEX idx_exchanges_payment_created ON exchanges(paymentMethod, createdAt);

-- 商品运营分析索引
CREATE INDEX idx_products_stock_exchange ON products(stock, exchangeCount);
CREATE INDEX idx_exchanges_product_created ON exchanges(productId, createdAt);
```

### 2. 数据聚合表
```sql
-- 每日统计汇总表
CREATE TABLE daily_stats (
  date DATE PRIMARY KEY,
  total_orders INT DEFAULT 0,
  rmb_revenue DECIMAL(10,2) DEFAULT 0,
  ly_revenue INT DEFAULT 0,
  new_users INT DEFAULT 0,
  active_users INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户访问日志表（新增）
CREATE TABLE user_access_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT,
  ip_address VARCHAR(45),
  user_agent TEXT,
  page_url VARCHAR(500),
  access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  session_id VARCHAR(100),
  INDEX idx_user_time (user_id, access_time),
  INDEX idx_session_time (session_id, access_time)
);
```

### 3. 定时任务优化
```sql
-- 创建存储过程用于每日数据汇总
DELIMITER //
CREATE PROCEDURE UpdateDailyStats()
BEGIN
  INSERT INTO daily_stats (date, total_orders, rmb_revenue, ly_revenue, new_users, active_users)
  SELECT
    DATE(NOW()) as date,
    COUNT(*) as total_orders,
    SUM(CASE WHEN paymentMethod = 'rmb' THEN totalAmount ELSE 0 END) as rmb_revenue,
    SUM(CASE WHEN paymentMethod = 'ly' THEN totalAmount ELSE 0 END) as ly_revenue,
    (SELECT COUNT(*) FROM users WHERE DATE(createdAt) = DATE(NOW())) as new_users,
    (SELECT COUNT(DISTINCT userId) FROM user_access_logs WHERE DATE(access_time) = DATE(NOW())) as active_users
  FROM exchanges
  WHERE DATE(createdAt) = DATE(NOW())
  ON DUPLICATE KEY UPDATE
    total_orders = VALUES(total_orders),
    rmb_revenue = VALUES(rmb_revenue),
    ly_revenue = VALUES(ly_revenue),
    new_users = VALUES(new_users),
    active_users = VALUES(active_users);
END //
DELIMITER ;
```
