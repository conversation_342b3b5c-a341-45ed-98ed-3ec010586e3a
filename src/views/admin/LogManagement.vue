<template>
  <div class="log-management">
    <div class="page-header">
      <h1>日志管理</h1>
      <div class="header-actions">
        <el-button type="success" @click="exportLogs" :loading="exporting">
          <i class="el-icon-download"></i> 导出日志
        </el-button>
        <el-button type="primary" @click="refreshLogs">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>
    </div>

    <div class="filter-section">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="操作类型">
          <el-select
            v-model="searchForm.action"
            placeholder="全部操作类型"
            clearable
            multiple
            collapse-tags
            style="width: 240px;"
          >
            <el-option
              v-for="item in actionTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="实体类型">
          <el-select
            v-model="searchForm.entityType"
            placeholder="全部实体类型"
            clearable
            multiple
            collapse-tags
            style="width: 240px;"
          >
            <el-option
              v-for="item in entityTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="实体ID">
          <el-input v-model="searchForm.entityId" placeholder="实体ID" clearable></el-input>
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model="searchForm.username" placeholder="姓名" clearable></el-input>
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchLogs">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="statistics-section" v-if="showStatistics">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="stat-card">
            <div slot="header">
              <span>操作类型统计</span>
            </div>
            <div class="chart-container" ref="actionChart"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="stat-card">
            <div slot="header">
              <span>实体类型统计</span>
            </div>
            <div class="chart-container" ref="entityChart"></div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="stat-card">
            <div slot="header">
              <span>日志数量趋势</span>
            </div>
            <div class="chart-container" ref="dateChart"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="stat-card">
            <div slot="header">
              <span>用户登录统计</span>
            </div>
            <div class="chart-container" ref="userLoginChart"></div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="20" v-if="statistics.productActions && statistics.productActions.length > 0">
        <el-col :span="24">
          <el-card class="stat-card">
            <div slot="header">
              <span>商品操作统计</span>
            </div>
            <div class="chart-container" ref="productActionChart"></div>
          </el-card>
        </el-col>
      </el-row>
      <el-divider></el-divider>
    </div>

    <div class="logs-table">
      <el-table
        v-loading="loading"
        :data="logs"
        stripe
        border
        style="width: 100%">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="action" label="操作类型" width="150">
          <template #default="scope">
            <el-tag :type="getActionTagType(scope.row.action)">
              {{ getActionName(scope.row.action) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="entityType" label="实体类型" width="120">
          <template #default="scope">
            <el-tag>{{ getEntityTypeName(scope.row.entityType) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="entityId" label="实体ID" width="100"></el-table-column>
        <el-table-column prop="username" label="操作用户" width="120"></el-table-column>
        <el-table-column prop="description" label="操作描述" show-overflow-tooltip>
          <template #default="scope">
            <div class="description-cell">{{ scope.row.description }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="操作时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button size="small" type="primary" @click="viewLogDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </div>

    <!-- 日志详情对话框 -->
    <el-dialog
      title="日志详情"
      v-model="detailDialogVisible"
      width="60%">
      <div v-if="selectedLog" class="log-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="ID">{{ selectedLog.id }}</el-descriptions-item>
          <el-descriptions-item label="操作类型">
            <el-tag :type="getActionTagType(selectedLog.action)">
              {{ getActionName(selectedLog.action) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="实体类型">
            <el-tag>{{ getEntityTypeName(selectedLog.entityType) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="实体ID">{{ selectedLog.entityId }}</el-descriptions-item>
          <el-descriptions-item label="操作用户">{{ selectedLog.username }}</el-descriptions-item>
          <el-descriptions-item label="IP地址">{{ selectedLog.ipAddress }}</el-descriptions-item>
          <el-descriptions-item label="操作描述">
            <div class="detail-description">{{ selectedLog.description }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="操作时间">{{ formatDate(selectedLog.createdAt) }}</el-descriptions-item>
          <el-descriptions-item label="变更前数据" v-if="selectedLog.oldValue">
            <pre class="json-display">{{ formatJson(selectedLog.oldValue) }}</pre>
          </el-descriptions-item>
          <el-descriptions-item label="变更后数据" v-if="selectedLog.newValue">
            <pre class="json-display">{{ formatJson(selectedLog.newValue) }}</pre>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import api from '../../api';
import { format } from 'date-fns';
import * as echarts from 'echarts/core';
import {
  BarChart,
  LineChart,
  PieChart
} from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import axios from 'axios';
import { useAuthStore } from '../../stores/auth';

// 注册 ECharts 组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  BarChart,
  LineChart,
  PieChart,
  CanvasRenderer
]);

export default {
  name: 'LogManagement',
  setup() {
    // 在setup中获取authStore，组件加载时即可使用
    const authStore = useAuthStore();

    // 确保有用户信息
    if (authStore.token && !authStore.user) {
      authStore.getProfile().catch(error => {
        console.error('获取用户资料失败:', error);
      });
    }

    return { authStore };
  },
  data() {
    return {
      logs: [],
      loading: false,
      exporting: false,
      currentPage: 1,
      pageSize: 20,
      total: 0,
      searchForm: {
        action: [],
        entityType: [],
        entityId: '',
        username: ''
      },
      dateRange: [],
      actionTypes: [
        { value: 'user_login', label: '用户登录' },
        { value: 'user_logout', label: '用户退出' },
        { value: 'user_register', label: '用户注册' },
        { value: 'user_update', label: '用户更新' },
        { value: 'user_delete', label: '用户删除' },
        { value: 'user_password_change', label: '用户密码变更' },
        { value: 'product_create', label: '商品创建' },
        { value: 'product_update', label: '商品更新' },
        { value: 'product_delete', label: '商品删除' },
        { value: 'product_status_change', label: '商品状态变更' },
        { value: 'stock_update', label: '库存变更' },
        { value: 'exchange_create', label: '创建兑换' },
        { value: 'exchange_status_update', label: '兑换状态变更' },
        { value: 'exchange_cancel', label: '取消兑换' },
        { value: 'file_upload', label: '文件上传' },
        { value: 'file_delete', label: '文件删除' },
        { value: 'data_import', label: '数据导入' },
        { value: 'data_export', label: '数据导出' },
        { value: 'system_logs_cleanup', label: '日志清理' }
      ],
      entityTypes: [
        { value: 'user', label: '用户' },
        { value: 'product', label: '商品' },
        { value: 'exchange', label: '兑换' },
        { value: 'stock', label: '库存' },
        { value: 'file', label: '文件' },
        { value: 'system', label: '系统' }
      ],
      showStatistics: true,
      detailDialogVisible: false,
      selectedLog: null,
      charts: {
        action: null,
        entity: null,
        date: null,
        userLogin: null,
        productAction: null
      },
      statistics: {
        byAction: [],
        byEntityType: [],
        byDate: [],
        userLogins: [],
        productActions: []
      }
    };
  },
  async created() {
    // 确保用户信息加载完成
    if (this.authStore.token && !this.authStore.user) {
      try {
        await this.authStore.getProfile();
        console.log('用户信息加载完成:', this.authStore.user);
      } catch (error) {
        console.error('获取用户资料失败:', error);
      }
    }

    // 记录当前认证状态和角色
    console.log('认证状态:', this.authStore.isAuthenticated);
    console.log('管理员角色:', this.authStore.isAdmin);

    // 获取日志数据
    this.fetchLogs();
    this.fetchStatistics();
  },
  mounted() {
    // 图表初始化需要在DOM加载后进行
    this.$nextTick(() => {
      if (this.showStatistics) {
        this.initCharts();
      }
    });
  },
  computed: {
    isAdmin() {
      return this.authStore.isAdmin;
    }
  },
  methods: {
    // 处理API错误的通用方法
    handleApiError(error, defaultMessage = '操作失败') {
      console.error('API错误:', error);
      let errorMessage = defaultMessage;

      if (error.message) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      this.$message.error(errorMessage);
      return errorMessage;
    },

    async fetchLogs() {
      this.loading = true;
      try {
        const params = {
          page: this.currentPage,
          limit: this.pageSize,
          entityId: this.searchForm.entityId,
          username: this.searchForm.username,
          // Join arrays into comma-separated strings for backend
          action: this.searchForm.action.length > 0 ? this.searchForm.action.join(',') : undefined,
          entityType: this.searchForm.entityType.length > 0 ? this.searchForm.entityType.join(',') : undefined
        };

        // 添加日期范围参数
        if (this.dateRange && this.dateRange.length === 2) {
          params.startDate = this.dateRange[0];
          params.endDate = this.dateRange[1];
        }

        console.log('获取日志列表, 参数:', params);

        // 使用直接的axios实例以便记录更详细的信息
        // 从sessionStorage直接获取token，确保获取到最新的token
        const token = sessionStorage.getItem('token');
        if (!token) {
          throw new Error('未找到认证令牌，请重新登录');
        }

        const response = await axios({
          method: 'get',
          url: `${import.meta.env.VITE_API_URL || 'http://localhost:3000/api'}/logs`,
          params,
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        console.log('日志API响应状态:', response.status);

        if (response.status === 200 && response.data && response.data.success) {
          this.logs = response.data.data;
          this.total = response.data.pagination ? response.data.pagination.total : (this.logs.length || 0);
          console.log(`成功获取${this.logs.length}条日志记录`);
        } else {
          this.logs = [];
          this.total = 0;
          console.warn('未收到预期格式的日志数据:', response.data);
        }
      } catch (error) {
        console.error('获取日志失败:', error);

        if (error.response) {
          console.error('响应状态:', error.response.status);
          console.error('响应数据:', error.response.data);
        }

        this.$message.error(error.response?.data?.message || error.message || '获取日志失败');
        this.logs = [];
        this.total = 0;
      } finally {
        this.loading = false;
      }
    },

    async fetchStatistics() {
      try {
        console.log('开始获取统计数据...');

        const response = await axios({
          method: 'get',
          url: `${import.meta.env.VITE_API_URL || 'http://localhost:3000/api'}/logs/stats`,
          headers: {
            'Authorization': `Bearer ${this.authStore.token}`
          }
        });

        console.log('统计API响应状态:', response.status);
        console.log('收到的原始统计数据:', JSON.stringify(response.data)); // 打印原始数据

        if (response.status === 200 && response.data && response.data.success) {
          // 强制更新 statistics 对象以确保响应性
          this.statistics = { ...response.data.data };
          console.log('统计数据获取成功');
          console.log('处理后的商品操作统计数据:', this.statistics.productActions);
        } else {
          console.warn('未收到预期格式的统计数据:', response.data);
          this.statistics = {byDate: [], byAction: [], byEntityType: [], userLogins: [], productActions: []};
        }

        // 数据加载后更新图表
        this.$nextTick(() => {
          if (this.showStatistics) {
            // 移动图表更新/初始化逻辑到这里
            this.updateCharts();
          }
        });
      } catch (error) {
        console.error('获取日志统计数据失败:', error);

        if (error.response) {
          console.error('响应状态:', error.response.status);
          console.error('响应数据:', error.response.data);
        }

        this.$message.error(error.response?.data?.message || error.message || '获取统计数据失败');
        // 设置默认空数据，避免图表出错
        this.statistics = {byDate: [], byAction: [], byEntityType: [], userLogins: [], productActions: []};
         // 清空图表
        this.$nextTick(() => this.clearCharts());
      }
    },

    initCharts() {
      // 保留基本的引用获取，但实际初始化移到 updateCharts
      if (this.$refs.actionChart && !this.charts.action) {
        this.charts.action = echarts.init(this.$refs.actionChart);
      }
      if (this.$refs.entityChart && !this.charts.entity) {
        this.charts.entity = echarts.init(this.$refs.entityChart);
      }
      if (this.$refs.dateChart && !this.charts.date) {
        this.charts.date = echarts.init(this.$refs.dateChart);
      }
      if (this.$refs.userLoginChart && !this.charts.userLogin) {
        this.charts.userLogin = echarts.init(this.$refs.userLoginChart);
      }
      // productActionChart 的初始化现在也依赖于 updateCharts 中的条件

      // 窗口调整大小时重新渲染图表
      window.addEventListener('resize', this.resizeCharts);
    },

    updateCharts() {
      // 确保 chart 实例存在
      this.initCharts();

      // 更新操作类型图表
      if (this.charts.action && this.statistics.byAction) {
        const actionData = this.statistics.byAction.map(item => ({
          name: this.getActionName(item.action),
          value: item.count
        }));

        this.charts.action.setOption({
          title: {
            text: '操作类型分布',
            left: 'center'
          },
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b} : {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 'left',
            data: actionData.map(item => item.name)
          },
          series: [
            {
              name: '操作类型',
              type: 'pie',
              radius: '55%',
              center: ['50%', '60%'],
              data: actionData,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        });
      }

      // 更新实体类型图表
      if (this.charts.entity && this.statistics.byEntityType) {
        // 获取所有定义的实体类型及其名称
        const allEntityTypes = this.entityTypes.map(et => ({
          value: et.value,
          name: et.label
        }));

        // 创建一个映射，用于存储从后端获取的统计数据
        const statsMap = new Map(this.statistics.byEntityType.map(item => [item.entityType, item.count]));

        // 准备图表数据，确保包含所有类型，没有数据的类型计数为0
        const entityData = allEntityTypes.map(et => ({
          name: et.name,
          value: statsMap.get(et.value) || 0
        }));

        // 过滤掉值为0的数据项，避免图表显示过多0值扇区（可选）
        // const filteredEntityData = entityData.filter(item => item.value > 0);

        this.charts.entity.setOption({
          title: {
            text: '实体类型分布',
            left: 'center'
          },
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b} : {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 'left',
            // 使用处理后的数据生成图例
            data: entityData.map(item => item.name)
          },
          series: [
            {
              name: '实体类型',
              type: 'pie',
              radius: '55%',
              center: ['50%', '60%'],
              // 使用处理后的数据渲染图表
              data: entityData,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        });
      }

      // 更新日期趋势图表
      if (this.charts.date && this.statistics.byDate) {
        const dateData = this.statistics.byDate.map(item => ({
          date: item.date,
          count: item.count
        }));

        this.charts.date.setOption({
          title: {
            text: '日志数量趋势',
            left: 'center'
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          xAxis: {
            type: 'category',
            data: dateData.map(item => item.date),
            axisLabel: {
              rotate: 45
            }
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '日志数量',
              type: 'line',
              data: dateData.map(item => item.count),
              markPoint: {
                data: [
                  { type: 'max', name: '最大值' },
                  { type: 'min', name: '最小值' }
                ]
              }
            }
          ]
        });
      }

      // 更新用户登录统计图表
      if (this.charts.userLogin && this.statistics.userLogins && this.statistics.userLogins.length > 0) {
        const userLoginData = this.statistics.userLogins.map(item => ({
          name: item.username || '未知用户',
          value: item.loginCount,
          lastLogin: item.lastLoginTime ? this.formatDate(item.lastLoginTime) : '未知'
        }));

        this.charts.userLogin.setOption({
          title: {
            text: '用户登录次数统计',
            left: 'center'
          },
          tooltip: {
            trigger: 'axis',
            formatter: function(params) {
              const item = params[0];
              return `${item.name}: ${item.value} 次<br/>最后登录: ${userLoginData[item.dataIndex].lastLogin}`;
            }
          },
          xAxis: {
            type: 'category',
            data: userLoginData.map(item => item.name),
            axisLabel: {
              rotate: 45,
              interval: 0
            }
          },
          yAxis: {
            type: 'value',
            name: '登录次数'
          },
          series: [
            {
              name: '登录次数',
              type: 'bar',
              data: userLoginData.map(item => item.value),
              itemStyle: {
                color: '#91cc75'
              }
            }
          ]
        });
      } else if (this.charts.userLogin) {
          this.charts.userLogin.clear(); // 清空图表如果无数据
      }

      // 更新商品操作统计图表 - 在这里初始化如果需要
      if (this.statistics.productActions && this.statistics.productActions.length > 0) {
        if (this.$refs.productActionChart && !this.charts.productAction) {
          this.charts.productAction = echarts.init(this.$refs.productActionChart);
        }
        if(this.charts.productAction){
          const productActionData = this.statistics.productActions.map(item => ({
            name: this.getActionName(item.action),
            value: item.count
          }));

          this.charts.productAction.setOption({
            title: {
              text: '商品操作类型统计',
              left: 'center'
            },
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b} : {c} ({d}%)'
            },
            legend: {
              orient: 'horizontal',
              bottom: 'bottom',
              data: productActionData.map(item => item.name)
            },
            series: [
              {
                name: '操作类型',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                itemStyle: {
                  borderRadius: 10,
                  borderColor: '#fff',
                  borderWidth: 2
                },
                label: {
                  show: false,
                  position: 'center'
                },
                emphasis: {
                  label: {
                    show: true,
                    fontSize: '16',
                    fontWeight: 'bold'
                  }
                },
                labelLine: {
                  show: false
                },
                data: productActionData
              }
            ]
          });
        }
      } else if (this.charts.productAction) {
         this.charts.productAction.clear(); // 清空图表如果无数据
      }
    },

    resizeCharts() {
      if (this.charts.action) this.charts.action.resize();
      if (this.charts.entity) this.charts.entity.resize();
      if (this.charts.date) this.charts.date.resize();
      if (this.charts.userLogin) this.charts.userLogin.resize();
      if (this.charts.productAction) this.charts.productAction.resize();
    },

    getActionName(action) {
      const actionMap = {
        'user_login': '用户登录',
        'user_logout': '用户退出',
        'user_register': '用户注册',
        'user_update': '用户更新',
        'user_delete': '用户删除',
        'user_password_change': '用户密码变更',
        'product_create': '商品创建',
        'product_update': '商品更新',
        'product_delete': '商品删除',
        'product_status_change': '商品状态变更',
        'stock_update': '库存变更',
        'exchange_create': '创建兑换',
        'exchange_status_update': '兑换状态变更',
        'exchange_cancel': '取消兑换',
        'file_upload': '文件上传',
        'file_delete': '文件删除',
        'data_import': '数据导入',
        'data_export': '数据导出',
        'system_logs_cleanup': '日志清理'
      };
      return actionMap[action] || action;
    },

    getEntityTypeName(entityType) {
      const entityMap = {
        'user': '用户',
        'product': '商品',
        'exchange': '兑换',
        'stock': '库存',
        'file': '文件',
        'system': '系统'
      };
      return entityMap[entityType] || entityType;
    },

    getActionTagType(action) {
      const typeMap = {
        'user_login': 'success',
        'user_logout': 'info',
        'user_register': 'primary',
        'user_update': 'warning',
        'user_delete': 'danger',
        'user_password_change': 'warning',
        'product_create': 'primary',
        'product_update': 'success',
        'product_delete': 'danger',
        'product_status_change': 'warning',
        'stock_update': 'warning',
        'exchange_create': 'primary',
        'exchange_status_update': 'info',
        'exchange_cancel': 'info',
        'file_upload': 'primary',
        'file_delete': 'danger',
        'data_import': 'primary',
        'data_export': 'primary',
        'system_logs_cleanup': 'info'
      };
      return typeMap[action] || '';
    },

    formatDate(dateString) {
      if (!dateString) return '';
      return format(new Date(dateString), 'yyyy-MM-dd HH:mm:ss');
    },

    formatJson(jsonString) {
      try {
        const obj = JSON.parse(jsonString);
        return JSON.stringify(obj, null, 2);
      } catch (e) {
        return jsonString;
      }
    },

    handleSizeChange(size) {
      this.pageSize = size;
      this.fetchLogs();
    },

    handleCurrentChange(page) {
      this.currentPage = page;
      this.fetchLogs();
    },

    searchLogs() {
      this.currentPage = 1;
      this.fetchLogs();
    },

    resetSearch() {
      this.searchForm = {
        action: [],
        entityType: [],
        entityId: '',
        username: ''
      };
      this.dateRange = [];
      this.currentPage = 1;
      this.fetchLogs();
    },

    refreshLogs() {
      this.fetchLogs();
      this.fetchStatistics();
    },

    viewLogDetail(log) {
      this.selectedLog = log;
      this.detailDialogVisible = true;
    },

    async exportLogs() {
      if (!this.isAdmin) {
        this.$message.error('导出日志需要管理员权限');
        return;
      }

      this.exporting = true;
      try {
        // 构建导出参数，与查询参数保持一致
        const params = {
          ...this.searchForm
        };

        // 添加日期范围参数
        if (this.dateRange && this.dateRange.length === 2) {
          params.startDate = this.dateRange[0];
          params.endDate = this.dateRange[1];
        }

        // 发起导出请求，使用直接的axios实例
        console.log('准备导出日志，token:', this.authStore.token?.substring(0, 10) + '...');

        const response = await axios({
          method: 'get',
          url: `${import.meta.env.VITE_API_URL || 'http://localhost:3000/api'}/logs/export`,
          params,
          responseType: 'blob',
          headers: {
            'Authorization': `Bearer ${this.authStore.token}`
          }
        });

        if (response.status !== 200) {
          throw new Error(`服务器返回状态码: ${response.status}`);
        }

        // 获取文件名
        const contentDisposition = response.headers['content-disposition'];
        let filename = 'logs_export.csv';

        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename=(.+)/);
          if (filenameMatch && filenameMatch.length > 1) {
            filename = filenameMatch[1];
          }
        }

        // 创建Blob链接并触发下载
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', filename);
        document.body.appendChild(link);
        link.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(link);

        this.$message.success('日志导出成功');
      } catch (error) {
        console.error('导出日志失败:', error);
        let errorMsg = '导出日志失败';

        // 尝试解析错误响应
        if (error.response && error.response.data) {
          try {
            // 对于blob响应，需要读取内容
            const blob = error.response.data;
            const text = await blob.text();
            try {
              const data = JSON.parse(text);
              errorMsg = data.message || errorMsg;
            } catch (e) {
              errorMsg = text || errorMsg;
            }
          } catch (e) {
            console.error('解析错误响应失败:', e);
          }
        }

        this.$message.error(errorMsg);
      } finally {
        this.exporting = false;
      }
    },

    clearCharts() {
      // 清空所有图表的方法
      Object.values(this.charts).forEach(chart => {
        if (chart) {
          chart.clear();
        }
      });
    }
  },
  beforeDestroy() {
    // 卸载图表，避免内存泄漏
    window.removeEventListener('resize', this.resizeCharts);
    if (this.charts.action) this.charts.action.dispose();
    if (this.charts.entity) this.charts.entity.dispose();
    if (this.charts.date) this.charts.date.dispose();
    if (this.charts.userLogin) this.charts.userLogin.dispose();
    if (this.charts.productAction) this.charts.productAction.dispose();
  }
};
</script>

<style scoped>
.log-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-section {
  margin-bottom: 20px;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.statistics-section {
  margin-bottom: 25px;
}

.stat-card {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
}

.logs-table {
  margin-top: 20px;
}

.description-cell {
  line-height: 1.5;
  padding: 2px 0;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.json-display {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  max-height: 300px;
  overflow: auto;
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
}

.detail-description {
  max-height: 200px;
  overflow: auto;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
