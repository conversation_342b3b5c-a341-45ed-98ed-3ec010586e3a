-- 数据库回退验证脚本：验证回退到版本c3fa2fb的状态
-- 执行时间：2025-07-15

-- 1. 检查不应该存在的活动促销表
SELECT 'promotions表检查' as check_item,
       CASE WHEN COUNT(*) = 0 THEN '✅ 已删除' ELSE '❌ 仍存在' END as status
FROM information_schema.tables 
WHERE table_schema = 'feishu_mall' AND table_name = 'promotions';

SELECT 'promotion_participations表检查' as check_item,
       CASE WHEN COUNT(*) = 0 THEN '✅ 已删除' ELSE '❌ 仍存在' END as status
FROM information_schema.tables 
WHERE table_schema = 'feishu_mall' AND table_name = 'promotion_participations';

SELECT 'promotion_products表检查' as check_item,
       CASE WHEN COUNT(*) = 0 THEN '✅ 已删除' ELSE '❌ 仍存在' END as status
FROM information_schema.tables 
WHERE table_schema = 'feishu_mall' AND table_name = 'promotion_products';

SELECT 'promotion_statistics表检查' as check_item,
       CASE WHEN COUNT(*) = 0 THEN '✅ 已删除' ELSE '❌ 仍存在' END as status
FROM information_schema.tables 
WHERE table_schema = 'feishu_mall' AND table_name = 'promotion_statistics';

-- 2. 检查应该存在的热门商品表
SELECT 'hot_product_configs表检查' as check_item,
       CASE WHEN COUNT(*) > 0 THEN '✅ 存在' ELSE '❌ 不存在' END as status
FROM information_schema.tables 
WHERE table_schema = 'feishu_mall' AND table_name = 'hot_product_configs';

SELECT 'hot_product_history表检查' as check_item,
       CASE WHEN COUNT(*) > 0 THEN '✅ 存在' ELSE '❌ 不存在' END as status
FROM information_schema.tables 
WHERE table_schema = 'feishu_mall' AND table_name = 'hot_product_history';

-- 3. 检查products表中不应该存在的活动促销字段
SELECT 'products.has_promotion字段检查' as check_item,
       CASE WHEN COUNT(*) = 0 THEN '✅ 已删除' ELSE '❌ 仍存在' END as status
FROM information_schema.columns 
WHERE table_schema = 'feishu_mall' 
  AND table_name = 'products' 
  AND column_name = 'has_promotion';

SELECT 'products.promotion_price字段检查' as check_item,
       CASE WHEN COUNT(*) = 0 THEN '✅ 已删除' ELSE '❌ 仍存在' END as status
FROM information_schema.columns 
WHERE table_schema = 'feishu_mall' 
  AND table_name = 'products' 
  AND column_name = 'promotion_price';

-- 4. 检查products表中应该存在的热门商品字段
SELECT 'products热门商品字段完整性检查' as check_item,
       CASE WHEN COUNT(*) = 5 THEN '✅ 完整(5/5)' 
            ELSE CONCAT('❌ 不完整(', COUNT(*), '/5)') END as status
FROM information_schema.columns 
WHERE table_schema = 'feishu_mall' 
  AND table_name = 'products' 
  AND column_name IN ('isAutoHot', 'hotTimeRange', 'hotScore', 'hotRank', 'lastHotUpdate');

-- 5. 检查热门商品配置数据
SELECT 'hot_product_configs配置数据检查' as check_item,
       CASE WHEN COUNT(*) >= 4 THEN CONCAT('✅ 配置完整(', COUNT(*), '条)') 
            ELSE CONCAT('❌ 配置不完整(', COUNT(*), '条)') END as status
FROM hot_product_configs;

-- 6. 显示当前所有表
SELECT '=== 当前数据库表列表 ===' as info;
SHOW TABLES;

-- 7. 显示products表结构
SELECT '=== products表结构 ===' as info;
DESCRIBE products;
