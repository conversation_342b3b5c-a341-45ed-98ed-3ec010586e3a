# 光年小卖部 - 商城平台开发文档

## 1. 项目概述

光年小卖部是一个基于Vue 3和Express构建的B2C电商平台，采用前后端分离架构设计。系统支持商品管理、公告发布、用户管理、内容管理、福利兑换等核心功能，目标是为企业提供完整的电子商务和福利兑换解决方案。

## 2. 技术栈

### 2.1 前端技术
- **核心框架**: Vue 3.3.4
- **UI组件库**: Element Plus 2.3.8
- **状态管理**: Pinia 3.0.2
- **路由管理**: Vue Router 4.5.0
- **富文本编辑**:
  - WangEditor 5.1.23 (主要使用)
  - TipTap 2.11.7 (备选方案)
- **HTTP客户端**: Axios 1.8.4
- **可视化图表**: ECharts 5.6.0 / Vue-ECharts 6.6.8
- **日期处理**: date-fns 4.1.0
- **中文分词**: nodejieba 2.5.2, pinyin 4.0.0-alpha.2
- **唯一标识**: uuid 11.1.0
- **构建工具**: Vite 4.4.0

### 2.2 后端技术
- **服务框架**: Express 4.18.2
- **数据库**:
  - MySQL 3.14.1 (生产环境)
  - SQLite 5.1.7 (开发环境)
- **ORM框架**: Sequelize 6.33.0
- **身份验证**: JWT (jsonwebtoken 9.0.2)
- **文件处理**:
  - Multer 1.4.5-lts.1
  - express-fileupload 1.5.1
- **数据导出**:
  - ExcelJS 4.4.0
  - csv-parser 3.2.0
  - csv-writer 1.6.0
  - fast-csv 5.0.2
  - json2csv 6.0.0-alpha.2
  - pdfkit 0.17.1
- **压缩工具**: archiver 7.0.1
- **密码加密**: bcryptjs 2.4.3
- **日期处理**: moment 2.30.1
- **日志记录**: Morgan 1.10.0
- **进程管理**: PM2 6.0.5
- **数据验证**: express-validator 7.0.1

## 3. 系统架构

### 3.1 项目结构
```
项目根目录/
├── src/                   # 前端源码
│   ├── api/               # API请求封装
│   ├── components/        # 通用组件
│   │   └── admin/         # 管理后台组件
│   ├── router/            # 路由配置
│   ├── stores/            # 状态管理
│   ├── styles/            # 全局样式
│   ├── utils/             # 工具函数
│   ├── views/             # 页面视图
│   │   ├── admin/         # 管理后台视图
│   │   └── user/          # 用户中心页面
│   ├── App.vue            # 根组件
│   └── main.js            # 应用入口
│
├── server/                # 后端源码
│   ├── config/            # 配置文件
│   ├── controllers/       # 业务控制器
│   ├── middlewares/       # 中间件
│   ├── models/            # 数据模型
│   ├── routes/            # 路由定义
│   ├── services/          # 业务服务
│   ├── utils/             # 工具函数
│   ├── migrations/        # 数据库迁移文件
│   ├── seeders/           # 数据库种子文件
│   ├── uploads/           # 文件上传目录
│   │   └── images/        # 图片存储目录
│   ├── scripts/           # 脚本工具
│   ├── logs/              # 日志目录
│   └── server.js          # 服务入口
```

### 3.2 数据库架构

系统的核心数据模型包括：

1. **users表** - 用户信息
   - id (主键)
   - username (用户名)
   - email (邮箱，唯一)
   - password (加密存储)
   - role (角色：admin/user)
   - department (部门)
   - workplace (职场位置，旧字段)
   - workplaceId (关联职场表的外键)
   - points (光年币数量)
   - avatar (头像URL)
   - lastLoginAt (最后登录时间)
   - lastLoginIp (最后登录IP)
   - createdAt
   - updatedAt

2. **products表** - 商品信息
   - id (主键)
   - name (商品名称)
   - categoryId (外键关联categories表)
   - lyPrice (光年币价格)
   - rmbPrice (人民币价格)
   - description (商品描述)
   - stock (库存)
   - exchangeCount (兑换次数)
   - isHot (是否热门)
   - isNew (是否新品)
   - status (状态：active/inactive)
   - createdAt
   - updatedAt

3. **product_images表** - 商品图片
   - id (主键)
   - productId (外键关联products表)
   - imageUrl (图片URL)
   - sortOrder (排序顺序)
   - createdAt
   - updatedAt

4. **categories表** - 商品分类
   - id (主键)
   - name (类别名称，唯一)
   - description (类别描述)
   - sortOrder (排序顺序)
   - createdAt
   - updatedAt

5. **exchanges表** - 兑换记录
   - id (主键)
   - orderNumber (格式化的订单编号)
   - userId (关联用户)
   - productId (关联商品)
   - quantity (数量)
   - totalAmount (订单总金额)
   - paymentMethod (支付方式：ly/rmb)
   - contactInfo (联系信息)
   - location (兑换地点，旧字段)
   - workplaceId (关联职场表的外键)
   - remarks (用户备注)
   - paymentProofUrl (支付凭证图片URL)
   - status (状态：pending/approved/shipped/completed/rejected/cancelled)
   - adminRemarks (管理员备注)
   - trackingNumber (物流单号)
   - trackingCompany (物流公司)
   - createdAt
   - updatedAt

6. **feedback表** - 用户反馈
   - id (主键)
   - title (反馈标题)
   - content (反馈内容)
   - type (反馈类型：product/feature/bug/other)
   - status (状态：pending/processing/completed)
   - userId (关联用户)
   - adminReply (管理员回复)
   - createdAt
   - updatedAt

7. **announcements表** - 系统公告
   - id (主键)
   - title (公告标题)
   - content (公告内容)
   - contentHtml (HTML格式内容)
   - imageUrl (单图片URL)
   - imageUrls (多图片URL，JSON数组格式)
   - type (公告类型：新品/促销/系统更新)
   - status (状态：active/inactive)
   - createdBy (创建者ID)
   - createdAt
   - updatedAt

8. **logs表** - 系统日志
   - id (主键)
   - action (操作类型)
   - entityType (实体类型)
   - entityId (相关实体ID)
   - oldValue (操作前的值，JSON格式)
   - newValue (操作后的值，JSON格式)
   - userId (操作用户ID)
   - username (操作用户名)
   - ipAddress (IP地址)
   - deviceInfo (设备信息)
   - description (操作描述)
   - createdAt
   - updatedAt

9. **notifications表** - 通知消息
   - id (主键)
   - type (通知类型：exchange/feedback/stock_alert/product)
   - sourceId (关联的数据ID)
   - title (通知标题)
   - content (通知内容)
   - isRead (是否已读)
   - recipientId (接收者ID)
   - createdAt
   - updatedAt

10. **workplaces表** - 职场信息
    - id (主键)
    - name (职场名称，唯一)
    - code (职场代码，唯一)
    - description (职场描述)
    - isActive (是否激活)
    - createdAt
    - updatedAt

### 3.3 数据库关系图

主要表之间的关系如下：

- **用户(User)与反馈(Feedback)**: 一对多关系，一个用户可以提交多条反馈
- **用户(User)与公告(Announcement)**: 一对多关系，一个用户(管理员)可以创建多条公告
- **用户(User)与兑换记录(Exchange)**: 一对多关系，一个用户可以有多条兑换记录
- **用户(User)与通知(Notification)**: 一对多关系，一个用户可以接收多条通知
- **用户(User)与职场(Workplace)**: 多对一关系，多个用户可以属于同一个职场
- **分类(Category)与商品(Product)**: 一对多关系，一个分类可以包含多个商品
- **商品(Product)与商品图片(ProductImage)**: 一对多关系，一个商品可以有多张图片
- **商品(Product)与兑换记录(Exchange)**: 一对多关系，一个商品可以有多条兑换记录
- **职场(Workplace)与兑换记录(Exchange)**: 一对多关系，一个职场可以有多条兑换记录

## 4. 已完成功能

### 4.1 用户认证系统
- ✅ **用户注册与登录**
  - 实现基于JWT的认证机制，支持记住登录状态
  - 使用bcryptjs进行密码加密存储，增强安全性
  - 支持管理员创建用户和用户自注册两种方式
  - 实现邮箱格式验证和唯一性检查

- ✅ **JWT认证与鉴权**
  - 使用jsonwebtoken库实现令牌生成和验证
  - 支持令牌过期时间配置（短期/长期）
  - 实现令牌刷新机制，提升用户体验
  - 在请求头中传递令牌，保持RESTful风格

- ✅ **权限控制中间件**
  - 实现基于角色的访问控制(RBAC)
  - 区分管理员和普通用户权限
  - 使用中间件拦截未授权请求
  - 支持API级别的权限控制

- ✅ **用户管理（CRUD操作）**
  - 完整的用户信息管理功能
  - 支持用户信息编辑和密码修改
  - 实现用户状态管理（启用/禁用）
  - 用户操作日志记录

- ✅ **用户导入导出**
  - 支持CSV/Excel格式批量导入用户
  - 实现用户数据导出功能
  - 导入时进行数据验证和错误处理
  - 支持导入模板下载

### 4.2 商品管理系统
- ✅ **商品基础信息管理（CRUD操作）**
  - 完整的商品信息管理功能
  - 支持商品详情编辑和批量操作
  - 实现商品搜索和高级筛选
  - 商品操作日志记录

- ✅ **商品分类管理**
  - 分类的创建、编辑和删除
  - 支持分类排序和描述
  - 分类与商品的关联管理
  - 分类统计信息（商品数量、销量等）

- ✅ **商品图片上传与管理**
  - 支持多图片上传和预览
  - 图片排序和删除功能
  - 图片懒加载优化
  - 图片URL自动修复（解决跨环境问题）

- ✅ **商品批量导入导出（CSV/Excel）**
  - 支持多种格式的数据导入导出
  - 导入时进行数据验证和错误处理
  - 支持导入模板下载
  - 导出时支持筛选条件

- ✅ **商品状态管理（上架/下架）**
  - 商品状态切换功能
  - 新品和热门商品标记
  - 库存管理和预警
  - 状态变更日志记录

### 4.3 内容管理系统
- ✅ **公告发布与管理**
  - 公告的创建、编辑和删除
  - 支持公告类型分类
  - 公告排序和状态管理
  - 公告操作日志记录

- ✅ **富文本编辑器集成**
  - 集成WangEditor富文本编辑器
  - 支持图片上传和粘贴
  - 支持多种格式的文本编辑
  - HTML内容安全过滤

- ✅ **图片上传服务**
  - 支持多种图片格式上传
  - 图片大小和类型验证
  - 图片存储路径管理
  - 支持图片预览和删除

- ✅ **公告状态管理（发布/草稿）**
  - 公告状态切换功能
  - 草稿保存和编辑
  - 公告发布时间控制
  - 状态变更日志记录

- ✅ **HTML内容渲染**
  - 安全的HTML内容渲染
  - 支持图片和链接展示
  - 移动端适配优化
  - 图片URL自动修复

### 4.4 反馈系统
- ✅ **用户反馈提交**
  - 用户反馈表单提交
  - 支持反馈类型分类
  - 反馈内容验证
  - 用户身份关联

- ✅ **反馈管理与回复**
  - 管理员查看和回复反馈
  - 反馈列表筛选和排序
  - 反馈详情查看
  - 反馈操作日志记录

- ✅ **反馈状态跟踪**
  - 反馈状态管理（待处理/处理中/已完成）
  - 状态变更通知
  - 反馈处理时间统计
  - 反馈满意度评价

### 4.5 兑换与福利系统
- ✅ **兑换申请流程**
  - 支持光年币和人民币两种支付方式
  - 兑换申请表单验证
  - 支付凭证上传功能
  - 兑换订单号自动生成

- ✅ **兑换状态管理**
  - 完整的状态流转（待处理→已批准→已发货→已完成）
  - 支持订单取消和拒绝
  - 状态变更通知
  - 状态变更日志记录

- ✅ **库存自动扣减**
  - 兑换时自动扣减库存
  - 库存不足时拦截兑换
  - 库存预警机制
  - 库存变更日志记录

- ✅ **兑换记录查询**
  - 用户查看个人兑换历史
  - 管理员查看所有兑换记录
  - 支持多条件筛选和排序
  - 兑换详情查看

### 4.6 前台展示
- ✅ **商品列表与详情展示**
  - 响应式商品列表布局
  - 商品卡片设计和动画效果
  - 商品详情弹窗展示
  - 商品图片轮播和预览

- ✅ **商品筛选与搜索**
  - 多条件组合筛选
  - 价格区间筛选
  - 分类筛选
  - 关键词搜索

- ✅ **公告展示**
  - 首页公告轮播
  - 公告弹窗展示
  - 公告已读状态记录
  - 公告详情查看

### 4.7 数据统计与可视化
- ✅ **数据看板基础功能**
  - 核心数据指标展示
  - 数据卡片布局
  - 数据刷新机制
  - 管理员权限控制

- ✅ **销售趋势图表**
  - 使用ECharts实现销售趋势图
  - 支持时间范围选择
  - 数据动态更新
  - 多维度数据对比

- ✅ **商品分类占比图表**
  - 分类销售占比饼图
  - 分类商品数量统计
  - 交互式图表操作
  - 数据标签展示

- ✅ **用户活跃度分析**
  - 用户活跃度热力图
  - 新增用户统计
  - 用户行为分析
  - 时间段对比

- ✅ **地区销售分布**
  - 职场销售分布统计
  - 地区销售排行
  - 销售热点分析
  - 数据导出功能

### 4.8 系统管理功能
- ✅ **日志管理**
  - 系统操作日志记录
  - 用户登录日志
  - 数据变更日志
  - 日志查询和导出

- ✅ **通知系统**
  - 系统通知推送
  - 用户消息中心
  - 通知已读状态管理
  - 通知类型分类

- ✅ **职场管理**
  - 职场信息管理
  - 职场与用户关联
  - 职场与订单关联
  - 职场数据统计

## 5. 进行中的功能开发

### 5.1 数据看板优化 (进度：70%)
- 🔄 **ECharts组件加载优化**
  - 解决图表初始化延迟问题
  - 优化图表组件的生命周期管理
  - 实现图表自适应容器大小
  - 添加图表加载状态指示器

- 🔄 **数据动态更新机制**
  - 实现数据自动刷新功能
  - 优化数据请求策略，减少服务器负载
  - 添加数据更新时间显示
  - 支持用户手动刷新数据

- 🔄 **多维度数据分析**
  - 增加时间维度分析（日/周/月/季/年）
  - 添加商品维度分析（分类/热度/新品）
  - 实现用户维度分析（部门/职场/活跃度）
  - 支持数据钻取功能

- 🔄 **数据统计算法优化**
  - 改进销售趋势计算方法
  - 优化库存周转率计算
  - 完善用户活跃度评估模型
  - 增加预测分析功能

### 5.2 富文本编辑器增强 (进度：85%)
- 🔄 **图片处理功能完善**
  - 优化图片上传与粘贴体验
  - 支持图片大小调整和裁剪
  - 实现图片拖拽排序
  - 添加图片描述和替代文本

- 🔄 **内容安全过滤**
  - 实现HTML内容安全过滤
  - 防止XSS攻击
  - 限制危险标签和属性
  - 添加内容审核机制

- 🔄 **编辑器UI/UX优化**
  - 改进编辑器工具栏布局
  - 优化移动端编辑体验
  - 添加快捷键支持
  - 实现编辑历史记录和撤销功能

- ✅ **多媒体内容支持**
  - 支持多图片上传和管理
  - 实现视频嵌入功能
  - 添加表格编辑功能
  - 支持文件附件上传

### 5.3 系统性能优化 (进度：60%)
- 🔄 **数据库优化**
  - 优化数据表结构和关系
  - 添加必要的索引提升查询性能
  - 实现数据分页和懒加载
  - 优化大数据量查询

- 🔄 **前端性能优化**
  - 组件懒加载和代码分割
  - 静态资源缓存策略
  - 图片懒加载和优化
  - 减少不必要的重渲染

- 🔄 **API响应优化**
  - 实现API响应缓存
  - 优化请求/响应数据结构
  - 添加API限流保护
  - 实现批量操作接口

### 5.4 移动端适配 (进度：40%)
- 🔄 **响应式布局优化**
  - 完善移动端视图适配
  - 优化触摸交互体验
  - 改进移动端表单设计
  - 实现移动端特定功能

- 🔄 **移动端性能优化**
  - 减少移动端资源加载
  - 优化移动网络下的体验
  - 实现移动端离线功能
  - 添加移动端手势操作

## 6. 待开发功能

### 6.1 高级订单系统 (计划：2024年Q3)
- ❌ **订单高级管理**
  - 批量订单处理功能
  - 订单优先级管理
  - 订单关联和拆分
  - 订单模板和快速创建

- ❌ **订单审批流程**
  - 多级审批流程配置
  - 审批规则和条件设置
  - 审批通知和提醒
  - 审批历史记录

- ❌ **订单报表和分析**
  - 自定义订单报表
  - 订单趋势分析
  - 订单完成率统计
  - 订单异常分析

### 6.2 集成支付系统 (计划：2024年Q4)
- ❌ **第三方支付接口**
  - 微信支付集成
  - 支付宝支付集成
  - 企业内部支付系统集成
  - 支付安全加密

- ❌ **支付流程管理**
  - 支付状态实时更新
  - 支付超时处理
  - 支付失败重试
  - 支付凭证自动验证

- ❌ **财务对账系统**
  - 自动对账功能
  - 财务报表生成
  - 退款和冲正处理
  - 财务数据导出

### 6.3 用户中心增强 (计划：2024年Q3)
- ❌ **个人信息管理**
  - 用户资料完善
  - 头像上传和编辑
  - 账户安全设置
  - 隐私设置

- ❌ **收货地址管理**
  - 多地址保存
  - 默认地址设置
  - 地址验证和格式化
  - 地址选择器优化

- ❌ **用户偏好设置**
  - 界面主题切换
  - 通知偏好设置
  - 语言偏好
  - 显示设置

### 6.4 系统安全与监控 (计划：2024年Q4)
- ❌ **安全加固**
  - API安全审计
  - 数据加密传输
  - CSRF/XSS防护增强
  - 敏感操作二次验证

- ❌ **系统监控**
  - 实时性能监控
  - 异常行为检测
  - 资源使用监控
  - 自动告警机制

- ❌ **备份与恢复**
  - 自动备份策略
  - 数据恢复机制
  - 灾难恢复计划
  - 数据迁移工具

## 7. API接口文档

系统主要API接口遵循RESTful设计风格，所有接口均返回JSON格式数据。

### 7.1 用户认证

#### 用户登录
- **请求**: `POST /api/auth/login`
- **权限**: 公开
- **请求体**:
  ```json
  {
    "username": "用户名",
    "email": "<EMAIL>",
    "password": "密码",
    "rememberMe": true
  }
  ```
- **响应**:
  ```json
  {
    "message": "登录成功",
    "user": {
      "id": 1,
      "username": "用户名",
      "email": "<EMAIL>",
      "role": "admin",
      "department": "技术部"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
  ```

#### 用户注册
- **请求**: `POST /api/auth/register`
- **权限**: 仅管理员
- **请求体**:
  ```json
  {
    "username": "新用户名",
    "email": "<EMAIL>",
    "password": "密码",
    "role": "user",
    "department": "市场部",
    "workplaceId": 1
  }
  ```
- **响应**:
  ```json
  {
    "message": "用户创建成功",
    "user": {
      "id": 2,
      "username": "新用户名",
      "email": "<EMAIL>",
      "role": "user"
    }
  }
  ```

#### 获取用户资料
- **请求**: `GET /api/auth/profile`
- **权限**: 已登录用户
- **响应**:
  ```json
  {
    "user": {
      "id": 1,
      "username": "用户名",
      "email": "<EMAIL>",
      "role": "admin",
      "department": "技术部",
      "workplace": "北京总部",
      "points": 1000,
      "createdAt": "2023-01-01T00:00:00.000Z"
    }
  }
  ```

### 7.2 用户管理

#### 获取用户列表
- **请求**: `GET /api/users?page=1&limit=10&search=关键词`
- **权限**: 管理员
- **响应**:
  ```json
  {
    "data": [
      {
        "id": 1,
        "username": "用户名",
        "email": "<EMAIL>",
        "role": "admin",
        "department": "技术部",
        "workplace": "北京总部"
      }
    ],
    "total": 100,
    "page": 1,
    "totalPages": 10
  }
  ```

#### 获取用户详情
- **请求**: `GET /api/users/:id`
- **权限**: 管理员或本人
- **响应**:
  ```json
  {
    "id": 1,
    "username": "用户名",
    "email": "<EMAIL>",
    "role": "admin",
    "department": "技术部",
    "workplace": "北京总部",
    "points": 1000,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
  ```

#### 添加用户
- **请求**: `POST /api/users`
- **权限**: 管理员
- **请求体**: 同注册接口
- **响应**: 同注册接口

#### 更新用户信息
- **请求**: `PUT /api/users/:id`
- **权限**: 管理员或本人
- **请求体**:
  ```json
  {
    "username": "更新的用户名",
    "department": "新部门",
    "workplaceId": 2
  }
  ```
- **响应**:
  ```json
  {
    "message": "用户信息更新成功",
    "user": {
      "id": 1,
      "username": "更新的用户名",
      "department": "新部门"
    }
  }
  ```

#### 删除用户
- **请求**: `DELETE /api/users/:id`
- **权限**: 管理员
- **响应**:
  ```json
  {
    "message": "用户删除成功"
  }
  ```

### 7.3 商品管理

#### 获取商品列表
- **请求**: `GET /api/products?page=1&limit=10&categoryId=1&search=关键词&minLyPrice=100&maxLyPrice=500&isHot=true&isNew=true&status=active`
- **权限**: 公开
- **响应**:
  ```json
  {
    "data": [
      {
        "id": 1,
        "name": "商品名称",
        "categoryId": 1,
        "category": {
          "id": 1,
          "name": "分类名称"
        },
        "lyPrice": 200,
        "rmbPrice": 20.00,
        "description": "商品描述",
        "stock": 100,
        "exchangeCount": 50,
        "isHot": true,
        "isNew": true,
        "status": "active",
        "images": [
          {
            "id": 1,
            "imageUrl": "http://example.com/image1.jpg",
            "sortOrder": 0
          }
        ]
      }
    ],
    "total": 100,
    "page": 1,
    "totalPages": 10
  }
  ```

#### 获取单个商品详情
- **请求**: `GET /api/products/:id`
- **权限**: 公开
- **响应**:
  ```json
  {
    "id": 1,
    "name": "商品名称",
    "categoryId": 1,
    "category": {
      "id": 1,
      "name": "分类名称"
    },
    "lyPrice": 200,
    "rmbPrice": 20.00,
    "description": "商品详细描述",
    "stock": 100,
    "exchangeCount": 50,
    "isHot": true,
    "isNew": true,
    "status": "active",
    "images": [
      {
        "id": 1,
        "imageUrl": "http://example.com/image1.jpg",
        "sortOrder": 0
      }
    ],
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
  ```

#### 添加新商品
- **请求**: `POST /api/products`
- **权限**: 管理员
- **请求体**:
  ```json
  {
    "name": "新商品",
    "categoryId": 1,
    "lyPrice": 300,
    "rmbPrice": 30.00,
    "description": "新商品描述",
    "stock": 50,
    "isHot": false,
    "isNew": true,
    "status": "active"
  }
  ```
- **响应**:
  ```json
  {
    "message": "商品创建成功",
    "product": {
      "id": 2,
      "name": "新商品",
      "categoryId": 1,
      "lyPrice": 300,
      "rmbPrice": 30.00
    }
  }
  ```

#### 更新商品信息
- **请求**: `PUT /api/products/:id`
- **权限**: 管理员
- **请求体**:
  ```json
  {
    "name": "更新的商品名称",
    "lyPrice": 350,
    "stock": 60
  }
  ```
- **响应**:
  ```json
  {
    "message": "商品更新成功",
    "product": {
      "id": 1,
      "name": "更新的商品名称",
      "lyPrice": 350,
      "stock": 60
    }
  }
  ```

#### 删除商品
- **请求**: `DELETE /api/products/:id`
- **权限**: 管理员
- **响应**:
  ```json
  {
    "message": "商品删除成功"
  }
  ```

#### 更新商品状态
- **请求**: `PUT /api/products/:id/status`
- **权限**: 管理员
- **请求体**:
  ```json
  {
    "status": "inactive"
  }
  ```
- **响应**:
  ```json
  {
    "message": "商品状态更新成功",
    "status": "inactive"
  }
  ```

### 7.4 分类管理

#### 获取所有分类
- **请求**: `GET /api/categories`
- **权限**: 公开
- **响应**:
  ```json
  [
    {
      "id": 1,
      "name": "分类名称",
      "description": "分类描述",
      "sortOrder": 0,
      "productCount": 10
    }
  ]
  ```

#### 获取单个分类详情
- **请求**: `GET /api/categories/:id`
- **权限**: 公开
- **响应**:
  ```json
  {
    "id": 1,
    "name": "分类名称",
    "description": "分类详细描述",
    "sortOrder": 0,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z",
    "products": [
      {
        "id": 1,
        "name": "商品名称"
      }
    ]
  }
  ```

#### 添加新分类
- **请求**: `POST /api/categories`
- **权限**: 管理员
- **请求体**:
  ```json
  {
    "name": "新分类",
    "description": "新分类描述",
    "sortOrder": 1
  }
  ```
- **响应**:
  ```json
  {
    "id": 2,
    "name": "新分类",
    "description": "新分类描述",
    "sortOrder": 1
  }
  ```

#### 更新分类信息
- **请求**: `PUT /api/categories/:id`
- **权限**: 管理员
- **请求体**:
  ```json
  {
    "name": "更新的分类名称",
    "sortOrder": 2
  }
  ```
- **响应**:
  ```json
  {
    "message": "分类更新成功",
    "category": {
      "id": 1,
      "name": "更新的分类名称",
      "sortOrder": 2
    }
  }
  ```

#### 删除分类
- **请求**: `DELETE /api/categories/:id`
- **权限**: 管理员
- **响应**:
  ```json
  {
    "message": "分类删除成功"
  }
  ```

### 7.5 公告管理

#### 获取公告列表
- **请求**: `GET /api/announcements?page=1&limit=10&type=系统更新&status=active`
- **权限**: 公开
- **响应**:
  ```json
  {
    "data": [
      {
        "id": 1,
        "title": "公告标题",
        "content": "公告内容",
        "contentHtml": "<p>公告HTML内容</p>",
        "type": "系统更新",
        "status": "active",
        "createdBy": 1,
        "creator": {
          "id": 1,
          "username": "管理员"
        },
        "createdAt": "2023-01-01T00:00:00.000Z"
      }
    ],
    "total": 20,
    "page": 1,
    "totalPages": 2
  }
  ```

#### 获取单个公告详情
- **请求**: `GET /api/announcements/:id`
- **权限**: 公开
- **响应**:
  ```json
  {
    "id": 1,
    "title": "公告标题",
    "content": "公告详细内容",
    "contentHtml": "<p>公告HTML详细内容</p>",
    "imageUrl": "http://example.com/image.jpg",
    "imageUrls": ["http://example.com/image1.jpg", "http://example.com/image2.jpg"],
    "type": "系统更新",
    "status": "active",
    "createdBy": 1,
    "creator": {
      "id": 1,
      "username": "管理员"
    },
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
  ```

#### 发布新公告
- **请求**: `POST /api/announcements`
- **权限**: 管理员
- **请求体**:
  ```json
  {
    "title": "新公告",
    "content": "新公告内容",
    "contentHtml": "<p>新公告HTML内容</p>",
    "type": "新品",
    "status": "active",
    "imageUrls": ["http://example.com/image1.jpg"]
  }
  ```
- **响应**:
  ```json
  {
    "message": "公告发布成功",
    "announcement": {
      "id": 2,
      "title": "新公告",
      "type": "新品",
      "status": "active"
    }
  }
  ```

#### 更新公告信息
- **请求**: `PUT /api/announcements/:id`
- **权限**: 管理员
- **请求体**:
  ```json
  {
    "title": "更新的公告标题",
    "contentHtml": "<p>更新的公告HTML内容</p>"
  }
  ```
- **响应**:
  ```json
  {
    "message": "公告更新成功",
    "announcement": {
      "id": 1,
      "title": "更新的公告标题"
    }
  }
  ```

#### 更新公告状态
- **请求**: `PUT /api/announcements/:id/status`
- **权限**: 管理员
- **请求体**:
  ```json
  {
    "status": "inactive"
  }
  ```
- **响应**:
  ```json
  {
    "message": "公告状态更新成功",
    "status": "inactive"
  }
  ```

#### 删除公告
- **请求**: `DELETE /api/announcements/:id`
- **权限**: 管理员
- **响应**:
  ```json
  {
    "message": "公告删除成功"
  }
  ```

### 7.6 兑换管理

#### 创建新的兑换订单
- **请求**: `POST /api/exchanges`
- **权限**: 已登录用户
- **请求体**:
  ```json
  {
    "productId": 1,
    "quantity": 2,
    "paymentMethod": "ly",
    "contactInfo": "联系方式",
    "workplaceId": 1,
    "remarks": "备注信息",
    "paymentProofUrl": "http://example.com/payment.jpg"
  }
  ```
- **响应**:
  ```json
  {
    "message": "兑换申请已提交",
    "data": {
      "id": 1,
      "orderNumber": "EX20240701001",
      "userId": 1,
      "productId": 1,
      "quantity": 2,
      "paymentMethod": "ly",
      "status": "pending",
      "product": {
        "id": 1,
        "name": "商品名称",
        "lyPrice": 200,
        "rmbPrice": 20.00
      }
    }
  }
  ```

#### 获取用户的兑换历史
- **请求**: `GET /api/exchanges/user?page=1&limit=10&status=pending`
- **权限**: 已登录用户
- **响应**:
  ```json
  {
    "data": [
      {
        "id": 1,
        "orderNumber": "EX20240701001",
        "productId": 1,
        "product": {
          "id": 1,
          "name": "商品名称",
          "lyPrice": 200,
          "rmbPrice": 20.00
        },
        "quantity": 2,
        "paymentMethod": "ly",
        "status": "pending",
        "createdAt": "2023-01-01T00:00:00.000Z"
      }
    ],
    "total": 5,
    "page": 1,
    "totalPages": 1
  }
  ```

#### 获取单个兑换详情
- **请求**: `GET /api/exchanges/:id`
- **权限**: 管理员或订单所有者
- **响应**:
  ```json
  {
    "id": 1,
    "orderNumber": "EX20240701001",
    "userId": 1,
    "user": {
      "id": 1,
      "username": "用户名",
      "email": "<EMAIL>"
    },
    "productId": 1,
    "product": {
      "id": 1,
      "name": "商品名称",
      "lyPrice": 200,
      "rmbPrice": 20.00
    },
    "quantity": 2,
    "totalAmount": 400,
    "paymentMethod": "ly",
    "contactInfo": "联系方式",
    "workplaceId": 1,
    "workplace": {
      "id": 1,
      "name": "北京总部"
    },
    "remarks": "备注信息",
    "paymentProofUrl": "http://example.com/payment.jpg",
    "status": "pending",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
  ```

#### 更新兑换状态
- **请求**: `PUT /api/exchanges/:id/status`
- **权限**: 管理员
- **请求体**:
  ```json
  {
    "status": "approved",
    "adminRemarks": "管理员备注"
  }
  ```
- **响应**:
  ```json
  {
    "message": "兑换状态更新成功",
    "exchange": {
      "id": 1,
      "status": "approved",
      "adminRemarks": "管理员备注"
    }
  }
  ```

### 7.7 反馈管理

#### 提交反馈
- **请求**: `POST /api/feedback`
- **权限**: 已登录用户
- **请求体**:
  ```json
  {
    "title": "反馈标题",
    "content": "反馈内容",
    "type": "feature"
  }
  ```
- **响应**:
  ```json
  {
    "message": "反馈提交成功",
    "feedback": {
      "id": 1,
      "title": "反馈标题",
      "type": "feature",
      "status": "pending"
    }
  }
  ```

#### 获取反馈列表
- **请求**: `GET /api/feedback?page=1&limit=10&status=pending&type=feature`
- **权限**: 管理员
- **响应**:
  ```json
  {
    "data": [
      {
        "id": 1,
        "title": "反馈标题",
        "content": "反馈内容",
        "type": "feature",
        "status": "pending",
        "userId": 1,
        "user": {
          "id": 1,
          "username": "用户名"
        },
        "createdAt": "2023-01-01T00:00:00.000Z"
      }
    ],
    "total": 5,
    "page": 1,
    "totalPages": 1
  }
  ```

#### 回复反馈
- **请求**: `PUT /api/feedback/:id`
- **权限**: 管理员
- **请求体**:
  ```json
  {
    "adminReply": "管理员回复内容",
    "status": "completed"
  }
  ```
- **响应**:
  ```json
  {
    "message": "反馈回复成功",
    "feedback": {
      "id": 1,
      "adminReply": "管理员回复内容",
      "status": "completed"
    }
  }
  ```

#### 删除反馈
- **请求**: `DELETE /api/feedback/:id`
- **权限**: 管理员
- **响应**:
  ```json
  {
    "message": "反馈删除成功"
  }
  ```

### 7.8 文件上传

#### 上传图片
- **请求**: `POST /api/upload/image`
- **权限**: 已登录用户
- **请求体**: `multipart/form-data` 格式，包含 `file` 字段
- **响应**:
  ```json
  {
    "message": "图片上传成功",
    "url": "http://example.com/uploads/images/image.jpg"
  }
  ```

#### 粘贴上传图片
- **请求**: `POST /api/upload/paste-image`
- **权限**: 已登录用户
- **请求体**: `multipart/form-data` 格式，包含 `file` 字段
- **响应**:
  ```json
  {
    "message": "图片上传成功",
    "url": "http://example.com/uploads/images/paste-image.jpg"
  }
  ```

#### 删除文件
- **请求**: `DELETE /api/upload/files/:filename`
- **权限**: 已登录用户
- **响应**:
  ```json
  {
    "message": "文件删除成功"
  }
  ```

### 7.9 系统管理

#### 获取系统日志
- **请求**: `GET /api/logs?page=1&limit=10&action=user_login&entityType=user`
- **权限**: 管理员
- **响应**:
  ```json
  {
    "data": [
      {
        "id": 1,
        "action": "user_login",
        "entityType": "user",
        "entityId": 1,
        "userId": 1,
        "username": "用户名",
        "ipAddress": "127.0.0.1",
        "description": "用户登录",
        "createdAt": "2023-01-01T00:00:00.000Z"
      }
    ],
    "total": 100,
    "page": 1,
    "totalPages": 10
  }
  ```

#### 获取通知
- **请求**: `GET /api/notifications?isRead=false`
- **权限**: 已登录用户
- **响应**:
  ```json
  {
    "data": [
      {
        "id": 1,
        "type": "exchange",
        "sourceId": 1,
        "title": "兑换状态更新",
        "content": "您的兑换申请已审核通过",
        "isRead": false,
        "createdAt": "2023-01-01T00:00:00.000Z"
      }
    ],
    "total": 5,
    "unreadCount": 3
  }
  ```

#### 标记通知为已读
- **请求**: `PUT /api/notifications/:id/read`
- **权限**: 通知接收者
- **响应**:
  ```json
  {
    "message": "通知已标记为已读"
  }
  ```

#### 获取职场列表
- **请求**: `GET /api/system/workplaces`
- **权限**: 管理员
- **响应**:
  ```json
  {
    "data": [
      {
        "id": 1,
        "name": "北京总部",
        "code": "BJ",
        "description": "北京总部办公室",
        "isActive": true
      }
    ]
  }
  ```

## 8. 已知问题与解决方案

### 8.1 图片URL处理问题
- **问题描述**: 在不同环境（开发/生产）之间切换时，图片URL路径不一致导致图片无法正确显示
- **解决方案**:
  - 实现了`fixImageUrl`工具函数，自动处理图片URL路径
  - 在前端组件中统一使用此函数处理图片URL
  - 支持多种URL格式的自动修复和转换
  - 相关代码位于`src/utils/imageUtils.js`

### 8.2 富文本编辑器图片上传问题
- **问题描述**: WangEditor富文本编辑器的图片上传和粘贴功能在某些情况下失效
- **解决方案**:
  - 重写了编辑器的图片上传处理逻辑
  - 实现了自定义上传函数`customUpload`
  - 添加了更完善的错误处理和日志记录
  - 相关代码位于`src/components/RichTextEditor.vue`

### 8.3 认证中间件错误
- **问题描述**: 在某些API请求中，认证中间件无法正确验证JWT令牌
- **解决方案**:
  - 优化了JWT验证逻辑，增加了错误处理
  - 实现了令牌刷新机制
  - 添加了详细的日志记录，便于问题排查
  - 相关代码位于`server/middlewares/auth.js`

### 8.4 数据库字段缺失问题
- **问题描述**: 部分表缺少必要字段，如公告表缺少`contentHtml`字段
- **解决方案**:
  - 创建了数据库迁移脚本，添加缺失字段
  - 实现了数据同步逻辑，确保现有数据的完整性
  - 相关代码位于`server/migrations/`目录

### 8.5 静态资源访问问题
- **问题描述**: 上传的图片和文件在某些路径下无法正确访问
- **解决方案**:
  - 配置了多个静态文件路径，确保不同路径下都能访问
  - 优化了文件上传路径的处理逻辑
  - 添加了详细的路径日志，便于问题排查
  - 相关代码位于`server/server.js`

## 9. 开发计划

### 9.1 短期计划 (2024年Q3)
1. **完成进行中功能**:
   - 数据看板优化 - 完善图表组件和数据统计算法
   - 富文本编辑器增强 - 完成内容安全过滤和编辑器UI优化
   - 系统性能优化 - 实现数据库索引优化和API响应缓存
   - 移动端适配 - 完善响应式布局和触摸交互体验

2. **用户体验改进**:
   - 实现批量操作功能（批量导入/导出/删除）
   - 优化表单验证和错误提示
   - 改进页面加载状态和过渡动画
   - 完善通知系统和消息提醒

3. **系统稳定性提升**:
   - 完善错误处理和日志记录
   - 实现自动化测试用例
   - 优化异常情况下的用户体验
   - 增强数据验证和安全检查

### 9.2 中期计划 (2024年Q4)
1. **高级订单系统开发**:
   - 实现订单高级管理功能
   - 开发订单审批流程
   - 设计订单报表和分析功能
   - 完善订单状态流转和通知

2. **用户中心增强**:
   - 完善个人信息管理
   - 实现收货地址管理
   - 开发用户偏好设置
   - 优化用户数据展示

3. **系统安全与监控**:
   - 实现API安全审计
   - 开发实时性能监控
   - 设计自动备份与恢复机制
   - 增强敏感操作的安全控制

### 9.3 长期计划 (2025年Q1-Q2)
1. **集成支付系统**:
   - 对接第三方支付接口
   - 实现支付流程管理
   - 开发财务对账系统
   - 设计退款和冲正处理

2. **高级数据分析**:
   - 实现数据挖掘和预测分析
   - 开发自定义报表生成器
   - 设计多维度数据可视化
   - 实现数据导出和共享功能

3. **系统扩展与集成**:
   - 开发API网关和服务集成
   - 实现多语言支持
   - 设计移动应用集成
   - 开发第三方系统对接接口

---

文档更新时间: 2024年7月10日
