#!/usr/bin/env node

/**
 * 库存数据一致性修复脚本
 * 修复商品库存管理类型与职场库存记录不一致的问题
 */

import { sequelize } from './server/config/database.js';
import { Product, ProductWorkplaceStock, Workplace } from './server/models/index.js';

/**
 * 检查数据不一致的商品
 */
async function checkDataInconsistency() {
  console.log('🔍 检查数据不一致的商品...');
  
  const inconsistentProducts = await sequelize.query(`
    SELECT 
      p.id, 
      p.name, 
      p.stockManagementType, 
      COUNT(pws.id) as workplace_stock_count 
    FROM products p 
    LEFT JOIN product_workplace_stocks pws ON p.id = pws.productId 
    WHERE p.status = 'active' 
    GROUP BY p.id 
    HAVING (p.stockManagementType = 'single' AND workplace_stock_count > 0) 
       OR (p.stockManagementType = 'workplace' AND workplace_stock_count = 0)
  `, { type: sequelize.QueryTypes.SELECT });
  
  console.log(`发现 ${inconsistentProducts.length} 个数据不一致的商品:`);
  inconsistentProducts.forEach(product => {
    console.log(`- ID: ${product.id}, 名称: ${product.name}, 类型: ${product.stockManagementType}, 职场库存记录数: ${product.workplace_stock_count}`);
  });
  
  return inconsistentProducts;
}

/**
 * 修复数据不一致问题
 */
async function fixDataInconsistency() {
  const transaction = await sequelize.transaction();
  
  try {
    console.log('🔧 开始修复数据不一致问题...');
    
    // 1. 获取所有活跃的职场
    const activeWorkplaces = await Workplace.findAll({
      where: { isActive: true },
      transaction
    });
    
    console.log(`找到 ${activeWorkplaces.length} 个活跃职场:`, activeWorkplaces.map(w => w.name).join(', '));
    
    // 2. 处理 stockManagementType = 'single' 但有职场库存记录的商品
    const singleTypeWithWorkplaceStocks = await sequelize.query(`
      SELECT DISTINCT p.id, p.name
      FROM products p 
      JOIN product_workplace_stocks pws ON p.id = pws.productId 
      WHERE p.stockManagementType = 'single' AND p.status = 'active'
    `, { type: sequelize.QueryTypes.SELECT, transaction });
    
    console.log(`\n处理 ${singleTypeWithWorkplaceStocks.length} 个 single 类型但有职场库存的商品...`);
    
    for (const product of singleTypeWithWorkplaceStocks) {
      console.log(`处理商品: ${product.name} (ID: ${product.id})`);
      
      // 获取该商品的职场库存记录
      const workplaceStocks = await ProductWorkplaceStock.findAll({
        where: { productId: product.id },
        transaction
      });
      
      // 计算总库存
      const totalStock = workplaceStocks.reduce((sum, ws) => sum + ws.stock, 0);
      
      // 更新商品为 workplace 类型，并设置总库存
      await Product.update({
        stockManagementType: 'workplace',
        stock: totalStock
      }, {
        where: { id: product.id },
        transaction
      });
      
      console.log(`  - 更新为 workplace 类型，总库存: ${totalStock}`);
      
      // 确保所有活跃职场都有库存记录
      for (const workplace of activeWorkplaces) {
        const existingStock = workplaceStocks.find(ws => ws.workplaceId === workplace.id);
        if (!existingStock) {
          await ProductWorkplaceStock.create({
            productId: product.id,
            workplaceId: workplace.id,
            stock: 0,
            reservedStock: 0,
            minStockAlert: 10
          }, { transaction });
          console.log(`  - 为职场 ${workplace.name} 创建库存记录`);
        }
      }
    }
    
    // 3. 处理 stockManagementType = 'workplace' 但没有职场库存记录的商品
    const workplaceTypeWithoutStocks = await sequelize.query(`
      SELECT p.id, p.name, p.stock
      FROM products p 
      LEFT JOIN product_workplace_stocks pws ON p.id = pws.productId 
      WHERE p.stockManagementType = 'workplace' AND p.status = 'active'
      GROUP BY p.id 
      HAVING COUNT(pws.id) = 0
    `, { type: sequelize.QueryTypes.SELECT, transaction });
    
    console.log(`\n处理 ${workplaceTypeWithoutStocks.length} 个 workplace 类型但没有职场库存的商品...`);
    
    for (const product of workplaceTypeWithoutStocks) {
      console.log(`处理商品: ${product.name} (ID: ${product.id})`);
      
      // 为所有活跃职场创建库存记录，将总库存分配给第一个职场
      for (let i = 0; i < activeWorkplaces.length; i++) {
        const workplace = activeWorkplaces[i];
        const stockToAssign = i === 0 ? product.stock : 0; // 第一个职场分配全部库存
        
        await ProductWorkplaceStock.create({
          productId: product.id,
          workplaceId: workplace.id,
          stock: stockToAssign,
          reservedStock: 0,
          minStockAlert: 10
        }, { transaction });
        
        console.log(`  - 为职场 ${workplace.name} 创建库存记录，库存: ${stockToAssign}`);
      }
    }
    
    await transaction.commit();
    console.log('\n✅ 数据一致性修复完成！');
    
    return {
      fixedSingleType: singleTypeWithWorkplaceStocks.length,
      fixedWorkplaceType: workplaceTypeWithoutStocks.length
    };
    
  } catch (error) {
    await transaction.rollback();
    console.error('❌ 修复过程中出现错误:', error);
    throw error;
  }
}

/**
 * 验证修复结果
 */
async function verifyFix() {
  console.log('\n🔍 验证修复结果...');
  
  const inconsistentProducts = await checkDataInconsistency();
  
  if (inconsistentProducts.length === 0) {
    console.log('✅ 所有数据已保持一致！');
    return true;
  } else {
    console.log('❌ 仍有数据不一致的商品，需要进一步检查');
    return false;
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('🚀 开始库存数据一致性修复...\n');
    
    // 1. 检查当前数据状态
    await checkDataInconsistency();
    
    // 2. 修复数据不一致问题
    const result = await fixDataInconsistency();
    
    // 3. 验证修复结果
    const isFixed = await verifyFix();
    
    console.log('\n📊 修复总结:');
    console.log(`- 修复的 single 类型商品: ${result.fixedSingleType} 个`);
    console.log(`- 修复的 workplace 类型商品: ${result.fixedWorkplaceType} 个`);
    console.log(`- 修复状态: ${isFixed ? '成功' : '需要进一步检查'}`);
    
    process.exit(isFixed ? 0 : 1);
    
  } catch (error) {
    console.error('❌ 修复脚本执行失败:', error);
    process.exit(1);
  }
}

// 运行修复脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { checkDataInconsistency, fixDataInconsistency, verifyFix };
