# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_database_password
DB_NAME=feishu_mall

# 应用配置
NODE_ENV=development
PORT=3000
SERVER_URL=http://localhost:3000

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=1h
JWT_LONG_EXPIRES_IN=30d

# 飞书应用配置
FEISHU_APP_ID=your_feishu_app_id
FEISHU_APP_SECRET=your_feishu_app_secret
# 开发环境回调地址
FEISHU_REDIRECT_URI=http://localhost:3000/api/feishu/callback
# 生产环境回调地址（示例）
# FEISHU_REDIRECT_URI=https://store-api.chongyangqisi.com/api/feishu/callback

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880

# CORS配置
CORS_ORIGIN=http://localhost:5173,http://localhost:5174,http://localhost:8080,http://127.0.0.1:5173
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS

# 日志配置
LOG_LEVEL=info






# ====================================
# 生产环境配置文件 (server/.env.production)
# ====================================
# 此文件用于生产环境部署，请勿在本地开发环境使用
# 本地开发请使用 server/.env 文件

# ====================================
# 应用基础配置
# ====================================
# 运行环境：development（开发）/ production（生产）
NODE_ENV=production

# 服务器端口号
PORT=3000

# 服务器完整URL（生产环境服务器地址）
SERVER_URL=https://store-api.chongyangqisi.com

# 前端URL配置（用于重定向等）
FRONTEND_URL=https://store.chongyangqisi.com

# ====================================
# 数据库配置
# ====================================
# MySQL数据库连接配置（生产环境）
DB_HOST="127.0.0.1"
DB_PORT=3306
DB_USER=feishu_user
DB_PASSWORD=United_fei
DB_NAME=feishu_mall

# ====================================
# JWT令牌配置
# ====================================
# JWT密钥（生产环境专用，已生成安全密钥）
JWT_SECRET=JO/Ssvef59AR5zFMx5m/MGMin34aMPT0KY6sIcqwowA=
# 短期令牌过期时间（普通登录）
JWT_EXPIRES_IN=1d
# 长期令牌过期时间（记住我功能）
JWT_LONG_EXPIRES_IN=30d

# ====================================
# 文件上传配置
# ====================================
# 文件上传目录
UPLOAD_DIR=uploads
# 最大文件大小（字节）5MB
MAX_FILE_SIZE=5242880

# ====================================
# CORS跨域配置
# ====================================
# 允许的前端域名（生产环境域名，使用HTTPS）
CORS_ORIGIN=https://store.chongyangqisi.com,https://store-api.chongyangqisi.com
# 允许的HTTP方法
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS

# ====================================
# 飞书应用配置
# ====================================
# 飞书应用ID（已申请手机号等权限）
FEISHU_APP_ID=cli_a66b3b2dcab8d013
# 飞书应用密钥
FEISHU_APP_SECRET=5Fa8aatAGZ2Dv6K5VZhAWhbhjzE4lT2r
# 飞书OAuth回调地址（生产环境，注意：使用3000端口）
FEISHU_REDIRECT_URI=https://store-api.chongyangqisi.com/api/feishu/callback

# ====================================
# 飞书机器人配置
# ====================================
# 飞书机器人Webhook地址
FEISHU_BOT_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/e6cff700-4172-4039-a700-43c8f43765fc

# ====================================
# 日志配置
# ====================================
# 日志级别：debug / info / warn / error（生产环境建议使用info）
LOG_LEVEL=info
