-- 创建支付偏好分析测试数据
-- 用于验证支付偏好分析功能的完整性

USE feishu_mall;

-- 备份当前数据（可选）
-- CREATE TABLE exchanges_backup AS SELECT * FROM exchanges;

-- 插入人民币支付订单测试数据
INSERT INTO exchanges (
    userId, productId, quantity, unitPrice, totalAmount,
    paymentMethod, priceType, status, contactInfo,
    createdAt, updatedAt
) VALUES
-- 用户219（超管）的人民币订单
(219, 3, 2, 15.00, 30.00, 'rmb', 'rmb', 'completed', '{"name":"超管","mobile":"19876235769","address":"北京市朝阳区"}', '2025-07-10 10:30:00', '2025-07-10 10:30:00'),
(219, 5, 1, 19.00, 19.00, 'rmb', 'rmb', 'completed', '{"name":"超管","mobile":"19876235769","address":"北京市朝阳区"}', '2025-07-08 14:20:00', '2025-07-08 14:20:00'),
(219, 7, 3, 19.00, 57.00, 'rmb', 'rmb', 'approved', '{"name":"超管","mobile":"19876235769","address":"北京市朝阳区"}', '2025-07-12 09:15:00', '2025-07-12 09:15:00'),

-- 用户1的混合支付订单（不同订单使用不同支付方式）
(1, 4, 1, 15.00, 15.00, 'rmb', 'rmb', 'completed', '{"name":"admin","mobile":"13800138001","address":"上海市浦东新区"}', '2025-07-05 16:45:00', '2025-07-05 16:45:00'),
(1, 6, 2, 20.00, 40.00, 'rmb', 'rmb', 'shipped', '{"name":"admin","mobile":"13800138001","address":"上海市浦东新区"}', '2025-07-09 11:30:00', '2025-07-09 11:30:00'),

-- 用户59的混合支付订单
(59, 8, 1, 20.00, 20.00, 'rmb', 'rmb', 'completed', '{"name":"秦昭","mobile":"13800138002","address":"广州市天河区"}', '2025-07-06 13:20:00', '2025-07-06 13:20:00'),

-- 用户226的人民币订单
(226, 9, 2, 19.00, 38.00, 'rmb', 'rmb', 'completed', '{"name":"刘小娟","mobile":"13800138003","address":"深圳市南山区"}', '2025-07-11 15:10:00', '2025-07-11 15:10:00'),
(226, 10, 1, 15.00, 15.00, 'rmb', 'rmb', 'approved', '{"name":"刘小娟","mobile":"13800138003","address":"深圳市南山区"}', '2025-07-13 10:25:00', '2025-07-13 10:25:00'),

-- 用户227的人民币订单
(227, 11, 3, 15.00, 45.00, 'rmb', 'rmb', 'completed', '{"name":"曹芹","mobile":"13800138004","address":"杭州市西湖区"}', '2025-07-07 12:40:00', '2025-07-07 12:40:00'),

-- 用户228的人民币订单
(228, 12, 1, 9.90, 9.90, 'rmb', 'rmb', 'completed', '{"name":"常喻","mobile":"13800138005","address":"南京市鼓楼区"}', '2025-07-14 14:55:00', '2025-07-14 14:55:00'),

-- 为已有光年币用户添加人民币订单，创建混合支付用户
-- 用户219已经有光年币订单，现在添加人民币订单
(219, 4, 1, 15.00, 15.00, 'rmb', 'rmb', 'completed', '{"name":"超管","mobile":"19876235769","address":"成都市锦江区"}', '2025-07-09 09:30:00', '2025-07-09 09:30:00'),

-- 添加更多不同分类的人民币订单
(229, 5, 1, 19.00, 19.00, 'rmb', 'rmb', 'completed', '{"name":"陈恋恋","mobile":"13800138006","address":"重庆市渝北区"}', '2025-07-08 10:15:00', '2025-07-08 10:15:00'),
(230, 6, 2, 20.00, 40.00, 'rmb', 'rmb', 'approved', '{"name":"陈凌云","mobile":"13800138007","address":"天津市和平区"}', '2025-07-12 15:30:00', '2025-07-12 15:30:00'),

-- 生活用品分类
(231, 7, 3, 19.00, 57.00, 'rmb', 'rmb', 'completed', '{"name":"陈霞","mobile":"13800138008","address":"青岛市市南区"}', '2025-07-09 13:25:00', '2025-07-09 13:25:00'),
(232, 8, 1, 20.00, 20.00, 'rmb', 'rmb', 'shipped', '{"name":"陈彦芳","mobile":"13800138009","address":"大连市中山区"}', '2025-07-10 17:40:00', '2025-07-10 17:40:00');

-- 验证插入的数据
SELECT
    '=== 支付方式统计 ===' as info;

SELECT
    paymentMethod,
    COUNT(*) as order_count,
    SUM(totalAmount) as total_amount,
    AVG(totalAmount) as avg_amount
FROM exchanges
WHERE status IN ('approved', 'shipped', 'completed')
GROUP BY paymentMethod;

SELECT
    '=== 用户支付方式分布 ===' as info;

SELECT
    user_payment_summary.payment_types,
    COUNT(*) as user_count
FROM (
    SELECT
        userId,
        CASE
            WHEN COUNT(DISTINCT paymentMethod) = 1 AND MAX(paymentMethod) = 'rmb' THEN 'rmb_only'
            WHEN COUNT(DISTINCT paymentMethod) = 1 AND MAX(paymentMethod) = 'ly' THEN 'ly_only'
            WHEN COUNT(DISTINCT paymentMethod) > 1 THEN 'mixed'
        END as payment_types
    FROM exchanges
    WHERE status IN ('approved', 'shipped', 'completed')
    GROUP BY userId
) as user_payment_summary
GROUP BY user_payment_summary.payment_types;

SELECT
    '=== 最近7天订单分布 ===' as info;

SELECT
    DATE(createdAt) as order_date,
    paymentMethod,
    COUNT(*) as order_count,
    SUM(totalAmount) as daily_amount
FROM exchanges
WHERE status IN ('approved', 'shipped', 'completed')
    AND createdAt >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(createdAt), paymentMethod
ORDER BY order_date DESC, paymentMethod;

SELECT
    '=== 测试数据创建完成 ===' as status;
