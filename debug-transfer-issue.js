/**
 * 库存转移问题调试脚本
 * 在浏览器控制台中运行此脚本来排查转移失败的原因
 */

// 调试函数：检查前端环境
function checkFrontendEnvironment() {
  console.log('🔍 检查前端环境...');

  // 检查认证token
  const token = localStorage.getItem('token') || sessionStorage.getItem('token');
  console.log('认证token:', token ? '✅ 存在' : '❌ 不存在');

  // 检查网络连接
  console.log('网络状态:', navigator.onLine ? '✅ 在线' : '❌ 离线');

  // 检查Vue应用
  const app = document.querySelector('#app').__vue__;
  console.log('Vue应用:', app ? '✅ 正常' : '❌ 异常');

  // 检查Element Plus组件
  const hasElementPlus = !!window.ElementPlus || !!document.querySelector('.el-button');
  console.log('Element Plus:', hasElementPlus ? '✅ 已加载' : '❌ 未加载');

  // 检查控制台错误
  console.log('💡 请检查浏览器控制台是否有红色JavaScript错误');

  return { hasToken: !!token, isOnline: navigator.onLine, hasVue: !!app, hasElementPlus };
}

// 调试函数：手动执行库存转移
async function manualTransferTest() {
  console.log('🧪 手动执行库存转移测试...');
  
  const token = localStorage.getItem('token') || sessionStorage.getItem('token');
  if (!token) {
    console.error('❌ 未找到认证token');
    return;
  }
  
  const transferData = {
    productId: 15, // 哪吒捏捏乐的ID
    fromWorkplaceId: 1, // 北京
    toWorkplaceId: 14, // 武汉
    quantity: 1,
    reason: '手动调试测试'
  };
  
  console.log('📤 发送转移请求:', transferData);
  
  try {
    const response = await fetch('/api/stocks/transfer', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(transferData)
    });
    
    console.log('📥 响应状态:', response.status, response.statusText);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ 请求失败:', errorText);
      return;
    }
    
    const result = await response.json();
    console.log('✅ 转移成功:', result);
    
    // 等待一下然后检查数据
    setTimeout(async () => {
      await checkStockAfterTransfer();
    }, 1000);
    
  } catch (error) {
    console.error('❌ 网络错误:', error);
  }
}

// 调试函数：检查转移后的库存状态
async function checkStockAfterTransfer() {
  console.log('🔍 检查转移后的库存状态...');
  
  const token = localStorage.getItem('token') || sessionStorage.getItem('token');
  
  try {
    const response = await fetch('/api/products?includeWorkplaceStocks=true&limit=50', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const data = await response.json();
    const nezha = (data.data || data).find(p => p.name.includes('哪吒捏捏乐'));
    
    if (nezha && nezha.workplaceStocks) {
      console.log('📦 最新库存状态:');
      nezha.workplaceStocks.forEach(stock => {
        console.log(`- ${stock.workplaceName}: ${stock.availableStock}`);
      });
    }
  } catch (error) {
    console.error('❌ 获取库存状态失败:', error);
  }
}

// 调试函数：监听网络请求
function monitorNetworkRequests() {
  console.log('🔍 开始监听网络请求...');
  
  // 拦截fetch请求
  const originalFetch = window.fetch;
  window.fetch = function(...args) {
    const [url, options] = args;
    
    if (url.includes('/stocks/transfer')) {
      console.log('🌐 检测到库存转移请求:', {
        url,
        method: options?.method,
        body: options?.body,
        headers: options?.headers
      });
    }
    
    return originalFetch.apply(this, args).then(response => {
      if (url.includes('/stocks/transfer')) {
        console.log('📥 库存转移响应:', {
          status: response.status,
          statusText: response.statusText,
          ok: response.ok
        });
      }
      return response;
    }).catch(error => {
      if (url.includes('/stocks/transfer')) {
        console.error('❌ 库存转移请求失败:', error);
      }
      throw error;
    });
  };
  
  console.log('✅ 网络请求监听已启用');
}

// 调试函数：检查Vue组件状态
function checkVueComponentState() {
  console.log('🔍 检查Vue组件状态...');
  
  // 查找库存管理页面的Vue实例
  const app = document.querySelector('#app').__vue__;
  if (app) {
    console.log('✅ 找到Vue应用实例');
    
    // 这里可以添加更多Vue组件状态检查
    console.log('💡 建议在Vue DevTools中检查组件状态');
  } else {
    console.log('❌ 未找到Vue应用实例');
  }
}

// 调试函数：检查表单验证
function checkFormValidation() {
  console.log('🔍 检查表单验证...');

  // 查找转移对话框
  const dialog = document.querySelector('.el-dialog');
  if (dialog && !dialog.style.display.includes('none')) {
    console.log('✅ 找到转移对话框');

    // 检查表单字段
    const selects = dialog.querySelectorAll('.el-select');
    const numberInput = dialog.querySelector('input[type="number"]');
    const textarea = dialog.querySelector('textarea');
    const confirmBtn = dialog.querySelector('.el-button--primary');

    console.log('表单字段状态:', {
      源职场选择器: selects[0] ? '存在' : '不存在',
      目标职场选择器: selects[1] ? '存在' : '不存在',
      数量输入框: numberInput ? `值: ${numberInput.value}` : '不存在',
      原因输入框: textarea ? `值: ${textarea.value}` : '不存在',
      确认按钮: confirmBtn ? (confirmBtn.disabled ? '禁用' : '可用') : '不存在'
    });

    // 检查按钮点击事件
    if (confirmBtn) {
      console.log('🔍 测试确认按钮点击事件...');
      const clickEvent = new Event('click', { bubbles: true });
      confirmBtn.addEventListener('click', () => {
        console.log('✅ 确认按钮点击事件被触发');
      }, { once: true });
    }
  } else {
    console.log('❌ 未找到打开的转移对话框');
    console.log('💡 请先打开转移对话框再运行此检查');
  }
}

// 导出调试函数到全局
window.checkFrontendEnvironment = checkFrontendEnvironment;
window.manualTransferTest = manualTransferTest;
window.checkStockAfterTransfer = checkStockAfterTransfer;
window.monitorNetworkRequests = monitorNetworkRequests;
window.checkVueComponentState = checkVueComponentState;
window.checkFormValidation = checkFormValidation;

console.log('🚀 库存转移调试脚本已加载！');
console.log('📋 可用的调试函数:');
console.log('- checkFrontendEnvironment(): 检查前端环境');
console.log('- manualTransferTest(): 手动执行转移测试');
console.log('- checkStockAfterTransfer(): 检查库存状态');
console.log('- monitorNetworkRequests(): 监听网络请求');
console.log('- checkVueComponentState(): 检查Vue组件状态');
console.log('- checkFormValidation(): 检查表单验证');
console.log('');
console.log('💡 建议执行顺序:');
console.log('1. checkFrontendEnvironment()');
console.log('2. monitorNetworkRequests()');
console.log('3. 然后尝试执行转移操作');
console.log('4. 或者直接运行 manualTransferTest()');
