/**
 * 测试商品编辑功能
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

async function testEditProduct() {
  console.log('🧪 测试商品编辑功能...\n');

  try {
    // 1. 获取一个商品进行测试
    console.log('1️⃣ 获取测试商品...');
    const response = await axios.get(`${API_BASE}/products?page=1&limit=1`);
    
    if (!response.data.products || response.data.products.length === 0) {
      console.log('❌ 没有找到商品');
      return;
    }

    const product = response.data.products[0];
    console.log(`✅ 测试商品: ${product.name} (ID: ${product.id})`);
    console.log(`   当前库存: ${product.stock}`);
    console.log(`   库存管理模式: ${product.stockManagementType}`);

    // 2. 尝试更新库存
    const originalStock = product.stock;
    const newStock = originalStock + 1;

    console.log(`\n2️⃣ 尝试更新库存: ${originalStock} → ${newStock}`);

    // 模拟登录获取token（这里需要实际的管理员token）
    // 为了测试，我们直接调用API
    const updateResponse = await axios.put(
      `${API_BASE}/products/${product.id}`,
      {
        name: product.name,
        categoryId: product.categoryId,
        lyPrice: product.lyPrice,
        rmbPrice: product.rmbPrice,
        description: product.description,
        stock: newStock,
        isNew: product.isNew,
        isHot: product.isHot,
        status: product.status
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('✅ 更新请求发送成功');
    console.log('响应:', updateResponse.data);

    // 3. 验证更新结果
    console.log('\n3️⃣ 验证更新结果...');
    const verifyResponse = await axios.get(`${API_BASE}/products/${product.id}`);
    const updatedProduct = verifyResponse.data;

    console.log(`商品库存: ${updatedProduct.stock}`);
    
    if (updatedProduct.stock === newStock) {
      console.log('✅ 商品库存更新成功');
    } else {
      console.log(`❌ 商品库存更新失败，期望: ${newStock}, 实际: ${updatedProduct.stock}`);
    }

    // 4. 检查职场库存是否同步
    console.log('\n4️⃣ 检查职场库存同步...');
    // 这里需要查询数据库来验证职场库存
    
    console.log('\n🎉 测试完成');

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

// 运行测试
testEditProduct();
