#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

echo -e "${YELLOW}===== 生产环境部署启动脚本 =====${NC}"

# 项目路径
PROJECT_DIR="/www/wwwroot"
SERVER_DIR="$PROJECT_DIR/server"
LOGS_DIR="$PROJECT_DIR/logs"

# 创建日志目录
mkdir -p $LOGS_DIR

# 记录时间
DEPLOY_TIME=$(date "+%Y-%m-%d %H:%M:%S")
echo -e "${BLUE}部署时间: $DEPLOY_TIME${NC}"

# 检查PM2是否安装
if ! command -v pm2 &> /dev/null; then
  echo -e "${RED}错误: PM2未安装，请先安装PM2: npm install -g pm2${NC}"
  exit 1
fi

# 检查生产环境配置文件
if [ ! -f "$PROJECT_DIR/.env.production" ]; then
  echo -e "${RED}错误: 前端生产环境配置文件不存在 ($PROJECT_DIR/.env.production)${NC}"
  exit 1
fi

if [ ! -f "$SERVER_DIR/.env.production" ]; then
  echo -e "${RED}错误: 后端生产环境配置文件不存在 ($SERVER_DIR/.env.production)${NC}"
  exit 1
fi

# 切换到项目目录
cd $PROJECT_DIR
echo -e "${BLUE}当前工作目录: $(pwd)${NC}"

# 复制生产环境配置
echo -e "${BLUE}正在应用生产环境配置...${NC}"
cp "$PROJECT_DIR/.env.production" "$PROJECT_DIR/.env"
cp "$SERVER_DIR/.env.production" "$SERVER_DIR/.env"
echo -e "${GREEN}生产环境配置已应用${NC}"

# 安装依赖并构建前端
echo -e "${BLUE}正在安装前端依赖...${NC}"
# 安装所有依赖，包括开发依赖，因为构建需要vite
npm install >> "$LOGS_DIR/deploy.log" 2>&1
if [ $? -ne 0 ]; then
  echo -e "${RED}前端依赖安装失败，请查看日志: $LOGS_DIR/deploy.log${NC}"
  exit 1
fi
echo -e "${GREEN}前端依赖安装完成${NC}"

# 确保vite命令可用
echo -e "${BLUE}检查vite命令是否可用...${NC}"
if ! command -v ./node_modules/.bin/vite &> /dev/null; then
  echo -e "${YELLOW}本地vite命令不可用，尝试全局安装vite...${NC}"
  npm install -g vite >> "$LOGS_DIR/deploy.log" 2>&1
  if [ $? -ne 0 ]; then
    echo -e "${RED}vite安装失败，请查看日志: $LOGS_DIR/deploy.log${NC}"
    exit 1
  fi
fi

echo -e "${BLUE}正在构建前端项目...${NC}"
# 使用npx确保可以找到本地安装的vite
npx vite build >> "$LOGS_DIR/deploy.log" 2>&1
if [ $? -ne 0 ]; then
  echo -e "${RED}前端构建失败，请查看日志: $LOGS_DIR/deploy.log${NC}"
  exit 1
fi
echo -e "${GREEN}前端构建完成${NC}"

# 安装后端依赖
echo -e "${BLUE}正在安装后端依赖...${NC}"
cd $SERVER_DIR
# 服务器端可以只安装生产依赖
npm install --omit=dev >> "$LOGS_DIR/deploy.log" 2>&1
if [ $? -ne 0 ]; then
  echo -e "${RED}后端依赖安装失败，请查看日志: $LOGS_DIR/deploy.log${NC}"
  exit 1
fi
echo -e "${GREEN}后端依赖安装完成${NC}"

# 检查数据库配置
echo -e "${BLUE}正在检查数据库配置...${NC}"
source .env
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-3306}
DB_USER=${DB_USER:-"exchange_user"}
DB_PASSWORD=${DB_PASSWORD:-"your_secure_password"}
DB_NAME=${DB_NAME:-"feishu_mall"}

if command -v mysql &> /dev/null; then
  # 尝试连接到MySQL
  if mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p"$DB_PASSWORD" -e "SELECT 1" &>/dev/null; then
    echo -e "${GREEN}MySQL连接成功${NC}"

    # 检查数据库是否存在
    if mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p"$DB_PASSWORD" -e "USE $DB_NAME" &>/dev/null; then
      echo -e "${GREEN}数据库 $DB_NAME 已就绪${NC}"
    else
      echo -e "${RED}数据库 $DB_NAME 不存在，请先创建数据库${NC}"
      echo -e "${YELLOW}可使用以下命令创建:${NC}"
      echo -e "CREATE DATABASE $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
      exit 1
    fi
  else
    echo -e "${RED}无法连接到MySQL服务器，请检查数据库配置${NC}"
    exit 1
  fi
else
  echo -e "${YELLOW}警告: MySQL命令行工具未安装，跳过数据库检查${NC}"
  echo -e "${YELLOW}请确保数据库已正确配置${NC}"
fi

# 确保上传目录存在并设置权限
UPLOAD_DIR=${UPLOAD_DIR:-"uploads"}
if [[ "$UPLOAD_DIR" != /* ]]; then
  UPLOAD_DIR="$SERVER_DIR/$UPLOAD_DIR"
fi

echo -e "${BLUE}正在配置上传目录: $UPLOAD_DIR${NC}"
mkdir -p "$UPLOAD_DIR"
chmod 755 "$UPLOAD_DIR"
echo -e "${GREEN}上传目录已配置${NC}"

# 使用PM2启动后端应用
echo -e "${BLUE}使用PM2启动后端应用...${NC}"
cd $SERVER_DIR

# 检查是否已有PM2进程在运行
if pm2 list | grep -q "exchange-mall-api"; then
  echo -e "${YELLOW}发现正在运行的应用实例，正在重启...${NC}"
  pm2 restart exchange-mall-api
else
  # 启动新的PM2进程
  pm2 start server.js --name exchange-mall-api --log "$LOGS_DIR/app.log" --time
fi

# 设置PM2开机自启动
echo -e "${BLUE}配置PM2开机自启动...${NC}"
pm2 save
if command -v systemctl &> /dev/null; then
  # 系统使用systemd
  pm2 startup
  echo -e "${GREEN}PM2开机自启动已配置${NC}"
else
  echo -e "${YELLOW}警告: 无法配置PM2开机自启动，请手动设置${NC}"
fi

# 等待后端服务启动
echo -e "${BLUE}等待后端服务启动...${NC}"
for i in {1..10}; do
  if curl -s http://localhost:3000/api/health &>/dev/null; then
    echo -e "${GREEN}后端服务已成功启动${NC}"
    break
  fi

  if [ $i -eq 10 ]; then
    echo -e "${YELLOW}警告: 后端服务可能未正常启动，请检查日志: $LOGS_DIR/app.log${NC}"
  fi

  sleep 1
done

# 输出部署信息
echo -e "\n${GREEN}===== 部署完成 =====${NC}"
echo -e "${GREEN}前端目录: $PROJECT_DIR/dist${NC}"
echo -e "${GREEN}后端目录: $SERVER_DIR${NC}"
echo -e "${GREEN}服务状态：${NC}"
pm2 list | grep exchange-mall-api

echo -e "\n${YELLOW}注意事项:${NC}"
echo -e "1. 请确保已正确配置Nginx指向 $PROJECT_DIR/dist 目录"
echo -e "2. 后端API地址: http://localhost:3000/api"
echo -e "3. 日志文件位置: $LOGS_DIR/app.log"
echo -e "4. 如需查看实时日志，请运行: pm2 logs exchange-mall-api"
echo -e "5. 重启应用命令: pm2 restart exchange-mall-api"

echo -e "\n${BLUE}部署完成时间: $(date "+%Y-%m-%d %H:%M:%S")${NC}"