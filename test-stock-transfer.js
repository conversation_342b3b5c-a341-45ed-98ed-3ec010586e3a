#!/usr/bin/env node

/**
 * 库存转移功能测试脚本
 * 测试"哪吒捏捏乐"商品的库存转移功能
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:3000/api';

// 测试配置
const testConfig = {
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
};

/**
 * 管理员登录获取token
 */
async function adminLogin() {
  try {
    const loginData = {
      username: '超管',
      email: '<EMAIL>',
      password: '654321'
    };
    
    const response = await axios.post(`${BASE_URL}/auth/login`, loginData, testConfig);
    
    if (response.data.token) {
      console.log('✅ 管理员登录成功');
      return response.data.token;
    } else {
      console.log('❌ 登录失败：无token');
      return null;
    }
  } catch (error) {
    console.log('❌ 登录失败:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 获取"哪吒捏捏乐"商品的当前库存状态
 */
async function getNezhaProductStock(token) {
  try {
    const config = {
      ...testConfig,
      headers: {
        ...testConfig.headers,
        'Authorization': `Bearer ${token}`
      }
    };
    
    const response = await axios.get(`${BASE_URL}/products?includeWorkplaceStocks=true&limit=50`, config);
    const products = response.data?.data || response.data;
    
    const nezhaProduct = products.find(p => p.name.includes('哪吒捏捏乐'));
    
    if (nezhaProduct) {
      console.log('\n📦 哪吒捏捏乐当前库存状态:');
      console.log(`- 商品ID: ${nezhaProduct.id}`);
      console.log(`- 总库存: ${nezhaProduct.stock}`);
      console.log(`- 总可用库存: ${nezhaProduct.totalAvailableStock}`);
      
      if (nezhaProduct.workplaceStocks && nezhaProduct.workplaceStocks.length > 0) {
        console.log('- 职场库存分布:');
        nezhaProduct.workplaceStocks.forEach(stock => {
          console.log(`  * ${stock.workplaceName} (ID: ${stock.workplaceId}): ${stock.availableStock} (库存: ${stock.stock}, 预留: ${stock.reservedStock})`);
        });
      }
      
      return nezhaProduct;
    } else {
      console.log('❌ 未找到"哪吒捏捏乐"商品');
      return null;
    }
  } catch (error) {
    console.log('❌ 获取商品库存失败:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 执行库存转移
 */
async function performStockTransfer(token, productId, fromWorkplaceId, toWorkplaceId, quantity) {
  try {
    const config = {
      ...testConfig,
      headers: {
        ...testConfig.headers,
        'Authorization': `Bearer ${token}`
      }
    };
    
    const transferData = {
      productId,
      fromWorkplaceId,
      toWorkplaceId,
      quantity,
      reason: '测试库存转移功能'
    };
    
    console.log(`\n🔄 执行库存转移: ${quantity}个商品从职场${fromWorkplaceId}转移到职场${toWorkplaceId}...`);
    
    const response = await axios.post(`${BASE_URL}/stocks/transfer`, transferData, config);
    
    if (response.data.success) {
      console.log('✅ 库存转移成功');
      return true;
    } else {
      console.log('❌ 库存转移失败:', response.data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ 库存转移失败:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runStockTransferTest() {
  console.log('🚀 开始库存转移功能测试...\n');
  
  // 1. 管理员登录
  const token = await adminLogin();
  if (!token) {
    console.log('❌ 无法获取认证token，测试终止');
    return;
  }
  
  // 2. 获取"哪吒捏捏乐"商品的当前库存状态
  console.log('\n📋 步骤1: 获取转移前的库存状态');
  const productBefore = await getNezhaProductStock(token);
  if (!productBefore) {
    console.log('❌ 无法获取商品信息，测试终止');
    return;
  }
  
  // 3. 检查是否有足够的库存进行转移
  const beijingStock = productBefore.workplaceStocks.find(s => s.workplaceName === '北京');
  const wuhanStock = productBefore.workplaceStocks.find(s => s.workplaceName === '武汉');
  
  if (!beijingStock || !wuhanStock) {
    console.log('❌ 缺少北京或武汉职场的库存记录，测试终止');
    return;
  }
  
  if (beijingStock.availableStock < 3) {
    console.log('❌ 北京职场库存不足3个，无法进行转移测试');
    return;
  }
  
  // 4. 执行库存转移：从北京转移3个到武汉
  console.log('\n📋 步骤2: 执行库存转移');
  const transferSuccess = await performStockTransfer(
    token,
    productBefore.id,
    beijingStock.workplaceId,
    wuhanStock.workplaceId,
    3
  );
  
  if (!transferSuccess) {
    console.log('❌ 库存转移失败，测试终止');
    return;
  }
  
  // 5. 等待一下，然后获取转移后的库存状态
  console.log('\n📋 步骤3: 验证转移后的库存状态');
  await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
  
  const productAfter = await getNezhaProductStock(token);
  if (!productAfter) {
    console.log('❌ 无法获取转移后的商品信息');
    return;
  }
  
  // 6. 验证转移结果
  const beijingStockAfter = productAfter.workplaceStocks.find(s => s.workplaceName === '北京');
  const wuhanStockAfter = productAfter.workplaceStocks.find(s => s.workplaceName === '武汉');
  
  console.log('\n📊 转移结果验证:');
  console.log(`- 北京职场: ${beijingStock.availableStock} → ${beijingStockAfter.availableStock} (预期: ${beijingStock.availableStock - 3})`);
  console.log(`- 武汉职场: ${wuhanStock.availableStock} → ${wuhanStockAfter.availableStock} (预期: ${wuhanStock.availableStock + 3})`);
  
  const beijingCorrect = beijingStockAfter.availableStock === (beijingStock.availableStock - 3);
  const wuhanCorrect = wuhanStockAfter.availableStock === (wuhanStock.availableStock + 3);
  
  if (beijingCorrect && wuhanCorrect) {
    console.log('✅ 库存转移验证成功！数据更新正确。');
  } else {
    console.log('❌ 库存转移验证失败！数据更新不正确。');
    if (!beijingCorrect) {
      console.log(`  - 北京职场库存更新错误`);
    }
    if (!wuhanCorrect) {
      console.log(`  - 武汉职场库存更新错误`);
    }
  }
  
  console.log('\n📋 测试完成');
}

// 运行测试
runStockTransferTest().catch(error => {
  console.error('测试执行失败:', error);
  process.exit(1);
});
