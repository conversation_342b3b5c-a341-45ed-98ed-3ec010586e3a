const express = require('express');
const router = express.Router();

// 延迟加载控制器和中间件，避免循环依赖
let StockManagementController;
let authenticate, isAdmin;

// 延迟初始化函数
const initializeModules = () => {
  if (!StockManagementController) {
    try {
      StockManagementController = require('../controllers/stockManagementController');
      const authMiddleware = require('../middlewares/auth');
      authenticate = authMiddleware.authenticate;
      isAdmin = authMiddleware.isAdmin;
    } catch (error) {
      console.error('库存管理路由模块初始化失败:', error);
      throw error;
    }
  }
};

// 测试路由
router.get('/test-stock-api', (req, res) => {
  res.json({
    success: true,
    message: '库存管理API测试成功',
    timestamp: new Date().toISOString()
  });
});

// 临时测试路由（无认证）
router.get('/test-products/:id/workplace-stocks', (req, res) => {
  try {
    initializeModules();
    StockManagementController.getProductWorkplaceStocks(req, res);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

router.get('/test-workplaces/:id/stock-summary', (req, res) => {
  try {
    initializeModules();
    StockManagementController.getWorkplaceStockSummary(req, res);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

router.put('/test-products/:id/workplace-stocks', (req, res) => {
  try {
    initializeModules();
    StockManagementController.updateProductWorkplaceStocks(req, res);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

router.post('/test-stocks/transfer', (req, res) => {
  try {
    initializeModules();
    StockManagementController.transferStock(req, res);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

router.get('/test-stocks/operation-logs', (req, res) => {
  try {
    initializeModules();
    StockManagementController.getStockOperationLogs(req, res);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * 获取指定商品的职场库存分布
 * GET /api/products/:id/workplace-stocks
 */
router.get('/products/:id/workplace-stocks', (req, res) => {
  try {
    initializeModules();
    authenticate(req, res, () => {
      StockManagementController.getProductWorkplaceStocks(req, res);
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * 批量更新商品在各职场的库存
 * PUT /api/products/:id/workplace-stocks
 */
router.put('/products/:id/workplace-stocks', (req, res) => {
  try {
    initializeModules();
    authenticate(req, res, () => {
      isAdmin(req, res, () => {
        StockManagementController.updateProductWorkplaceStocks(req, res);
      });
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * 职场间库存转移
 * POST /api/stocks/transfer
 */
router.post('/stocks/transfer', (req, res) => {
  try {
    initializeModules();
    authenticate(req, res, () => {
      isAdmin(req, res, () => {
        StockManagementController.transferStock(req, res);
      });
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * 获取所有职场列表
 * GET /api/workplaces
 */
router.get('/workplaces', (req, res) => {
  try {
    initializeModules();
    authenticate(req, res, () => {
      StockManagementController.getWorkplaces(req, res);
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * 获取职场库存统计
 * GET /api/workplaces/:id/stock-summary
 */
router.get('/workplaces/:id/stock-summary', (req, res) => {
  try {
    initializeModules();
    authenticate(req, res, () => {
      StockManagementController.getWorkplaceStockSummary(req, res);
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * 获取库存操作日志
 * GET /api/stocks/operation-logs
 */
router.get('/stocks/operation-logs', (req, res) => {
  try {
    initializeModules();
    authenticate(req, res, () => {
      isAdmin(req, res, () => {
        StockManagementController.getStockOperationLogs(req, res);
      });
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * 保存操作记录
 * POST /api/stocks/operation-records
 */
router.post('/stocks/operation-records', (req, res) => {
  try {
    initializeModules();
    authenticate(req, res, () => {
      StockManagementController.saveOperationRecord(req, res);
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * 获取商品库存趋势数据
 * GET /api/stocks/product-trend
 */
router.get('/stocks/product-trend', (req, res) => {
  try {
    initializeModules();
    authenticate(req, res, () => {
      StockManagementController.getProductStockTrend(req, res);
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// 检查单个商品库存一致性
router.get('/products/:productId/consistency-check', (req, res) => {
  try {
    initializeModules();
    StockManagementController.checkStockConsistency(req, res);
  } catch (error) {
    console.error('检查库存一致性路由错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// 批量检查所有商品库存一致性
router.get('/consistency-check-all', (req, res) => {
  try {
    initializeModules();
    StockManagementController.checkAllStockConsistency(req, res);
  } catch (error) {
    console.error('批量检查库存一致性路由错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;


