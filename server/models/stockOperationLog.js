const { DataTypes, Op } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 库存操作日志模型
 * 记录所有库存变更操作的详细日志
 */
const StockOperationLog = sequelize.define('StockOperationLog', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },
  productId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'productId',
    comment: '商品ID'
  },
  workplaceId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'workplaceId',
    comment: '职场ID（NULL表示总库存操作）'
  },
  operationType: {
    type: DataTypes.ENUM(
      'add',              // 增加库存
      'subtract',         // 减少库存
      'set',              // 设置库存
      'transfer',         // 转移库存
      'sync',             // 同步库存
      'exchange_deduct',  // 兑换扣减
      'exchange_restore', // 兑换恢复
      'migration'         // 数据迁移
    ),
    allowNull: false,
    field: 'operationType',
    comment: '操作类型'
  },
  beforeStock: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'beforeStock',
    comment: '操作前库存'
  },
  afterStock: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'afterStock',
    comment: '操作后库存'
  },
  changeAmount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'changeAmount',
    comment: '变更数量'
  },
  relatedOrderId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'relatedOrderId',
    comment: '关联订单ID（兑换扣减时）'
  },
  operatorId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'operatorId',
    comment: '操作员ID'
  },
  operatorName: {
    type: DataTypes.STRING(50),
    allowNull: true,
    field: 'operatorName',
    comment: '操作员姓名'
  },
  reason: {
    type: DataTypes.STRING(200),
    allowNull: true,
    comment: '操作原因'
  },
  batchId: {
    type: DataTypes.STRING(50),
    allowNull: true,
    field: 'batchId',
    comment: '批次ID（批量操作时）'
  },
  ipAddress: {
    type: DataTypes.STRING(45),
    allowNull: true,
    field: 'ipAddress',
    comment: '操作IP地址'
  },
  userAgent: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'userAgent',
    comment: '用户代理信息'
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '扩展元数据'
  }
}, {
  tableName: 'stock_operation_logs',
  timestamps: true,
  createdAt: 'createdAt',
  updatedAt: false, // 日志表不需要updatedAt
  indexes: [
    {
      fields: ['productId'],
      name: 'idx_product_id'
    },
    {
      fields: ['workplaceId'],
      name: 'idx_workplace_id'
    },
    {
      fields: ['operationType'],
      name: 'idx_operation_type'
    },
    {
      fields: ['relatedOrderId'],
      name: 'idx_related_order'
    },
    {
      fields: ['operatorId'],
      name: 'idx_operator'
    },
    {
      fields: ['batchId'],
      name: 'idx_batch_id'
    },
    {
      fields: ['createdAt'],
      name: 'idx_created_at'
    },
    {
      fields: ['productId', 'workplaceId', 'createdAt'],
      name: 'idx_product_workplace_time'
    },
    {
      fields: ['operationType', 'createdAt'],
      name: 'idx_operation_time'
    }
  ],
  comment: '库存操作日志表 - 记录所有库存变更操作的详细日志'
});

/**
 * 类方法：创建库存操作日志
 */
StockOperationLog.createLog = async function(logData, transaction = null) {
  const {
    productId,
    workplaceId = null,
    operationType,
    beforeStock,
    afterStock,
    changeAmount,
    relatedOrderId = null,
    operatorId = null,
    operatorName = null,
    reason = null,
    batchId = null,
    ipAddress = null,
    userAgent = null,
    metadata = null
  } = logData;

  return await this.create({
    productId,
    workplaceId,
    operationType,
    beforeStock,
    afterStock,
    changeAmount,
    relatedOrderId,
    operatorId,
    operatorName,
    reason,
    batchId,
    ipAddress,
    userAgent,
    metadata
  }, { transaction });
};

/**
 * 类方法：获取商品的库存操作历史
 */
StockOperationLog.getProductHistory = async function(productId, options = {}) {
  const {
    workplaceId = null,
    operationType = null,
    productName = null,
    limit = 50,
    offset = 0,
    startDate = null,
    endDate = null
  } = options;

  const where = {};
  const include = [
    {
      model: sequelize.models.Product,
      as: 'product',
      attributes: ['id', 'name'],
      required: false
    },
    {
      model: sequelize.models.Workplace,
      as: 'workplace',
      attributes: ['id', 'name', 'code'],
      required: false
    },
    {
      model: sequelize.models.User,
      as: 'operator',
      attributes: ['id', 'username'],
      required: false
    }
  ];

  // 如果指定了productId，则添加到查询条件
  if (productId !== null && productId !== undefined) {
    where.productId = productId;
  }

  // 如果指定了商品名称，则通过关联查询
  if (productName) {
    include[0].where = {
      name: {
        [Op.like]: `%${productName}%`
      }
    };
    include[0].required = true; // 必须有匹配的商品
  }

  if (workplaceId !== null && workplaceId !== undefined) {
    where.workplaceId = workplaceId;
  }

  if (operationType) {
    where.operationType = operationType;
  }

  if (startDate || endDate) {
    where.createdAt = {};
    if (startDate) where.createdAt[Op.gte] = startDate;
    if (endDate) where.createdAt[Op.lte] = endDate;
  }

  try {
    return await this.findAndCountAll({
      where,
      order: [['createdAt', 'DESC']],
      limit,
      offset,
      include
    });
  } catch (error) {
    console.error('获取库存操作历史失败:', error);
    // 如果关联查询失败，尝试不使用关联查询
    return await this.findAndCountAll({
      where,
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });
  }
};

/**
 * 类方法：获取操作统计
 */
StockOperationLog.getOperationStats = async function(options = {}) {
  const {
    startDate = null,
    endDate = null,
    workplaceId = null,
    operationType = null
  } = options;

  const where = {};

  if (workplaceId !== null) {
    where.workplaceId = workplaceId;
  }

  if (operationType) {
    where.operationType = operationType;
  }

  if (startDate || endDate) {
    where.createdAt = {};
    if (startDate) where.createdAt[Op.gte] = startDate;
    if (endDate) where.createdAt[Op.lte] = endDate;
  }

  return await this.findAll({
    where,
    attributes: [
      'operationType',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      [sequelize.fn('SUM', sequelize.col('changeAmount')), 'totalChange']
    ],
    group: ['operationType'],
    raw: true
  });
};

/**
 * 类方法：获取商品库存趋势数据
 */
StockOperationLog.getProductStockTrend = async function(productId, options = {}) {
  const {
    startDate = null,
    endDate = null,
    workplaceId = null
  } = options;

  const where = {
    productId: productId
  };

  if (workplaceId !== null) {
    where.workplaceId = workplaceId;
  }

  if (startDate || endDate) {
    where.createdAt = {};
    if (startDate) where.createdAt[Op.gte] = startDate + ' 00:00:00';
    if (endDate) where.createdAt[Op.lte] = endDate + ' 23:59:59';
  }

  console.log('🔍 查询条件:', where);

  try {
    // 获取操作日志，按时间排序
    const logs = await this.findAll({
      where,
      order: [['createdAt', 'ASC']],
      attributes: [
        'id',
        'workplaceId',
        'operationType',
        'beforeStock',
        'afterStock',
        'changeAmount',
        'createdAt'
      ]
    });

    // 获取所有相关的职场信息
    const workplaceIds = [...new Set(logs.map(log => log.workplaceId).filter(id => id))];
    const workplaces = {};

    if (workplaceIds.length > 0) {
      const Workplace = sequelize.models.Workplace;
      const workplaceList = await Workplace.findAll({
        where: {
          id: workplaceIds
        },
        attributes: ['id', 'name', 'code']
      });

      workplaceList.forEach(wp => {
        workplaces[wp.id] = wp;
      });
    }

    // 按日期和职场分组处理数据
    const trendMap = new Map();

    logs.forEach(log => {
      const date = log.createdAt.toISOString().split('T')[0]; // 获取日期部分
      const workplaceId = log.workplaceId || 0; // 0表示全局库存
      const workplace = workplaces[log.workplaceId];
      const workplaceName = workplace?.name || (log.workplaceId ? `职场${log.workplaceId}` : '全局');

      const key = `${date}_${workplaceId}`;

      if (!trendMap.has(key)) {
        trendMap.set(key, {
          date,
          workplaceId,
          workplaceName,
          stock: log.afterStock || 0,
          operations: []
        });
      } else {
        // 如果同一天有多个操作，取最后一个操作的库存值
        const existing = trendMap.get(key);
        if (log.createdAt > new Date(existing.lastUpdate || 0)) {
          existing.stock = log.afterStock || 0;
          existing.lastUpdate = log.createdAt;
        }
      }

      // 记录操作详情
      trendMap.get(key).operations.push({
        operationType: log.operationType,
        changeAmount: log.changeAmount,
        beforeStock: log.beforeStock,
        afterStock: log.afterStock,
        time: log.createdAt
      });
    });

    // 转换为数组并排序
    const trendData = Array.from(trendMap.values())
      .map(item => ({
        date: item.date,
        workplaceId: item.workplaceId,
        workplaceName: item.workplaceName,
        stock: item.stock,
        operationCount: item.operations.length
      }))
      .sort((a, b) => {
        if (a.date !== b.date) {
          return a.date.localeCompare(b.date);
        }
        return a.workplaceId - b.workplaceId;
      });

    return trendData;

  } catch (error) {
    console.error('获取商品库存趋势失败:', error);
    return [];
  }
};

// 设置模型关联
StockOperationLog.associate = function(models) {
  // 关联商品
  StockOperationLog.belongsTo(models.Product, {
    foreignKey: 'productId',
    as: 'product'
  });

  // 关联职场
  StockOperationLog.belongsTo(models.Workplace, {
    foreignKey: 'workplaceId',
    as: 'workplace'
  });

  // 关联操作员
  StockOperationLog.belongsTo(models.User, {
    foreignKey: 'operatorId',
    as: 'operator'
  });
};

module.exports = StockOperationLog;
