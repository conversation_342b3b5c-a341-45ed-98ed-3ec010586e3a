<template>
  <el-dialog
    v-model="dialogVisible"
    title="批量库存编辑"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="batch-edit-content">
      <!-- 选中商品列表 -->
      <div class="selected-products">
        <h4 class="section-title">
          <i class="fas fa-list"></i>
          选中商品 ({{ products.length }})
        </h4>
        <div class="products-grid">
          <div 
            v-for="product in products"
            :key="product.id"
            class="product-item"
          >
            <img
              :src="fixImageUrl(product.imageUrl)"
              :alt="product.name"
              class="product-image"
            />
            <div class="product-info">
              <div class="product-name">{{ product.name }}</div>
              <div class="current-stock">当前: {{ product.totalStock || product.stock || 0 }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 批量操作选项 -->
      <div class="batch-operations">
        <h4 class="section-title">
          <i class="fas fa-cogs"></i>
          批量操作
        </h4>
        
        <el-form 
          :model="formData" 
          :rules="formRules"
          ref="formRef"
          label-width="120px"
        >
          <el-form-item label="操作类型" prop="operationType">
            <el-radio-group v-model="formData.operationType">
              <el-radio label="set">设置为指定值</el-radio>
              <el-radio label="add">增加指定数量</el-radio>
              <el-radio label="subtract">减少指定数量</el-radio>
              <el-radio label="multiply">按比例调整</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item 
            :label="getValueLabel()" 
            prop="value"
          >
            <el-input-number
              v-model="formData.value"
              :min="getMinValue()"
              :max="getMaxValue()"
              :precision="formData.operationType === 'multiply' ? 2 : 0"
              :step="formData.operationType === 'multiply' ? 0.1 : 1"
              style="width: 200px"
            />
            <span class="value-hint">{{ getValueHint() }}</span>
          </el-form-item>

          <el-form-item label="应用范围" prop="applyScope">
            <el-radio-group v-model="formData.applyScope">
              <el-radio label="total">总库存</el-radio>
              <el-radio label="workplace">职场库存</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item 
            label="目标职场" 
            prop="targetWorkplaceId"
            v-if="formData.applyScope === 'workplace'"
          >
            <el-select
              v-model="formData.targetWorkplaceId"
              placeholder="选择目标职场"
              style="width: 300px"
            >
              <el-option
                v-for="workplace in workplaces"
                :key="workplace.id"
                :label="`${workplace.name} (${workplace.code})`"
                :value="workplace.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="操作原因" prop="reason">
            <el-input
              v-model="formData.reason"
              type="textarea"
              :rows="3"
              placeholder="请输入批量操作原因"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作预览 -->
      <div class="operation-preview" v-if="previewData.length > 0">
        <h4 class="section-title">
          <i class="fas fa-eye"></i>
          操作预览
        </h4>
        <div class="preview-table">
          <el-table
            :data="previewData"
            size="small"
            max-height="200"
          >
            <el-table-column prop="productName" label="商品名称" width="200" />
            <el-table-column prop="currentStock" label="当前库存" width="100" align="center" />
            <el-table-column prop="newStock" label="新库存" width="100" align="center">
              <template #default="{ row }">
                <span :class="{ 'stock-increase': row.newStock > row.currentStock, 'stock-decrease': row.newStock < row.currentStock }">
                  {{ row.newStock }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="change" label="变化" width="100" align="center">
              <template #default="{ row }">
                <span :class="{ 'change-positive': row.change > 0, 'change-negative': row.change < 0 }">
                  {{ row.change > 0 ? '+' : '' }}{{ row.change }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="previewChanges" :disabled="!canPreview">
          预览变化
        </el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm"
          :loading="loading"
          :disabled="!canConfirm"
        >
          确认执行
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { fixImageUrl } from '@/utils/imageUtils'

export default {
  name: 'BatchStockEditDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    products: {
      type: Array,
      default: () => []
    },
    workplaces: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:modelValue', 'confirm'],
  setup(props, { emit }) {
    const dialogVisible = ref(false)
    const loading = ref(false)
    const formRef = ref(null)
    const previewData = ref([])

    const formData = ref({
      operationType: 'add',
      value: 1,
      applyScope: 'total',
      targetWorkplaceId: null,
      reason: ''
    })

    const formRules = {
      operationType: [
        { required: true, message: '请选择操作类型', trigger: 'change' }
      ],
      value: [
        { required: true, message: '请输入数值', trigger: 'blur' }
      ],
      reason: [
        { required: true, message: '请输入操作原因', trigger: 'blur' }
      ]
    }

    // 计算属性
    const canPreview = computed(() => {
      return formData.value.operationType && 
             formData.value.value !== null && 
             formData.value.value !== undefined &&
             (formData.value.applyScope === 'total' || formData.value.targetWorkplaceId)
    })

    const canConfirm = computed(() => {
      return canPreview.value && 
             formData.value.reason.trim() && 
             previewData.value.length > 0
    })

    // 方法
    const getValueLabel = () => {
      switch (formData.value.operationType) {
        case 'set': return '设置数值'
        case 'add': return '增加数量'
        case 'subtract': return '减少数量'
        case 'multiply': return '调整比例'
        default: return '数值'
      }
    }

    const getMinValue = () => {
      switch (formData.value.operationType) {
        case 'set': return 0
        case 'add': return 1
        case 'subtract': return 1
        case 'multiply': return 0.1
        default: return 0
      }
    }

    const getMaxValue = () => {
      switch (formData.value.operationType) {
        case 'multiply': return 10
        default: return 9999
      }
    }

    const getValueHint = () => {
      switch (formData.value.operationType) {
        case 'set': return '将库存设置为此数值'
        case 'add': return '在当前库存基础上增加'
        case 'subtract': return '从当前库存中减少'
        case 'multiply': return '当前库存乘以此比例'
        default: return ''
      }
    }

    const previewChanges = () => {
      if (!canPreview.value) return

      previewData.value = props.products.map(product => {
        const currentStock = product.totalStock || product.stock || 0
        let newStock = currentStock

        switch (formData.value.operationType) {
          case 'set':
            newStock = formData.value.value
            break
          case 'add':
            newStock = currentStock + formData.value.value
            break
          case 'subtract':
            newStock = Math.max(0, currentStock - formData.value.value)
            break
          case 'multiply':
            newStock = Math.round(currentStock * formData.value.value)
            break
        }

        return {
          productId: product.id,
          productName: product.name,
          currentStock,
          newStock,
          change: newStock - currentStock
        }
      })

      ElMessage.success('预览生成成功')
    }

    const handleClose = () => {
      dialogVisible.value = false
      emit('update:modelValue', false)
      resetForm()
    }

    const resetForm = () => {
      formData.value = {
        operationType: 'add',
        value: 1,
        applyScope: 'total',
        targetWorkplaceId: null,
        reason: ''
      }
      previewData.value = []
    }

    const handleConfirm = async () => {
      if (!formRef.value) return

      try {
        await formRef.value.validate()
        
        if (previewData.value.length === 0) {
          ElMessage.warning('请先预览变化')
          return
        }

        loading.value = true
        emit('confirm', {
          operationType: formData.value.operationType,
          value: formData.value.value,
          applyScope: formData.value.applyScope,
          targetWorkplaceId: formData.value.targetWorkplaceId,
          reason: formData.value.reason,
          previewData: previewData.value
        })
      } catch (error) {
        console.error('表单验证失败:', error)
      } finally {
        loading.value = false
      }
    }

    // 监听器
    watch(() => props.modelValue, (newVal) => {
      dialogVisible.value = newVal
      if (newVal) {
        nextTick(() => {
          resetForm()
        })
      }
    })

    watch(dialogVisible, (newVal) => {
      if (!newVal) {
        emit('update:modelValue', false)
      }
    })

    return {
      dialogVisible,
      loading,
      formRef,
      formData,
      formRules,
      previewData,
      canPreview,
      canConfirm,
      getValueLabel,
      getMinValue,
      getMaxValue,
      getValueHint,
      previewChanges,
      handleClose,
      handleConfirm,
      fixImageUrl
    }
  }
}
</script>

<style scoped>
.batch-edit-content {
  max-height: 70vh;
  overflow-y: auto;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title i {
  color: #409eff;
}

/* 选中商品列表 */
.selected-products {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.product-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.product-image {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  object-fit: cover;
}

.product-info {
  flex: 1;
  min-width: 0;
}

.product-name {
  font-size: 12px;
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.current-stock {
  font-size: 11px;
  color: #909399;
}

/* 批量操作 */
.batch-operations {
  margin-bottom: 24px;
}

.value-hint {
  margin-left: 12px;
  font-size: 12px;
  color: #909399;
}

/* 操作预览 */
.operation-preview {
  margin-bottom: 16px;
}

.preview-table {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.stock-increase {
  color: #67c23a;
  font-weight: 600;
}

.stock-decrease {
  color: #f56c6c;
  font-weight: 600;
}

.change-positive {
  color: #67c23a;
  font-weight: 600;
}

.change-negative {
  color: #f56c6c;
  font-weight: 600;
}

/* 响应式 */
@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: 1fr;
  }

  .product-item {
    flex-direction: column;
    text-align: center;
  }
}
</style>
