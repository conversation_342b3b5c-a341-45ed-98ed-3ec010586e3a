# 环境配置管理文档

## 概述

本项目采用标准的环境配置管理体系，根据不同的运行环境使用对应的配置文件，确保配置隔离和安全性。

## 环境配置文件结构

### 标准配置文件

| 文件名 | 环境 | 用途 | NODE_ENV |
|--------|------|------|----------|
| `.env.development` | 开发环境 | 本地开发和调试 | development |
| `.env.test` | 测试环境 | 测试服务器部署 | test |
| `.env.production` | 生产环境 | 正式生产环境 | production |
| `.env.example` | 示例文件 | 配置模板参考 | - |

### 配置文件加载逻辑

环境配置加载器 (`config/envLoader.js`) 根据 `NODE_ENV` 环境变量自动选择对应的配置文件：

```javascript
// 开发环境 (默认)
NODE_ENV=development → .env.development

// 测试环境
NODE_ENV=test → .env.test

// 生产环境
NODE_ENV=production → .env.production
```

## 关键配置项说明

### 1. 数据库配置
```env
DB_HOST=localhost          # 数据库主机
DB_PORT=3306              # 数据库端口
DB_USER=root              # 数据库用户名
DB_PASSWORD=password      # 数据库密码
DB_NAME=feishu_mall       # 数据库名称
```

### 2. 应用配置
```env
NODE_ENV=development      # 运行环境
PORT=3000                # 服务器端口
SERVER_URL=http://localhost:3000  # 服务器URL
```

### 3. CORS跨域配置

#### 开发环境 (.env.development)
```env
# 支持本地开发 + 移动设备测试
CORS_ORIGIN=http://localhost:5173,http://localhost:5174,http://localhost:8080,http://127.0.0.1:5173,http://**************:5173
```

#### 测试环境 (.env.test)
```env
# 支持测试服务器 + 本地调试
CORS_ORIGIN=http://**************,http://**************:80,http://**************:3000,http://localhost:5173,http://127.0.0.1:5173
```

#### 生产环境 (.env.production)
```env
# 支持新域名 + 旧服务器兼容
CORS_ORIGIN=https://store.chongyangqisi.com,https://store-api.chongyangqisi.com,http://**************,http://**************:80,http://**************:3000
```

### 4. JWT配置
```env
JWT_SECRET=your_secret_key        # JWT密钥
JWT_EXPIRES_IN=1h                # 短期令牌过期时间
JWT_LONG_EXPIRES_IN=30d          # 长期令牌过期时间（记住我）
```

### 5. 飞书配置
```env
FEISHU_APP_ID=cli_xxx            # 飞书应用ID
FEISHU_APP_SECRET=xxx            # 飞书应用密钥
FEISHU_REDIRECT_URI=xxx          # 飞书回调地址
FEISHU_BOT_WEBHOOK_URL=xxx       # 飞书机器人Webhook
```

## 使用指南

### 本地开发

1. 确保使用 `.env.development` 配置文件
2. 设置环境变量：`NODE_ENV=development`
3. 启动服务：`npm start` 或 `node server.js`

### 移动设备测试

1. 确保 `.env.development` 中包含本机IP地址
2. 启动前端服务器：`npm run dev -- --host 0.0.0.0`
3. 手机访问：`http://[本机IP]:5173`

### 测试环境部署

1. 设置环境变量：`NODE_ENV=test`
2. 确保 `.env.test` 配置正确
3. 部署到测试服务器

### 生产环境部署

1. 设置环境变量：`NODE_ENV=production`
2. 确保 `.env.production` 配置正确
3. 部署到生产服务器

## 安全注意事项

### 1. 敏感信息保护
- 所有 `.env.*` 文件都应该在 `.gitignore` 中
- 生产环境密钥必须使用强密码
- 定期更换JWT密钥和数据库密码

### 2. CORS配置
- 生产环境只允许信任的域名
- 避免使用通配符 `*`
- 定期审查允许的域名列表

### 3. 配置验证
- 启动时自动验证关键配置项
- 缺少必要配置时拒绝启动
- 记录配置加载状态

## 故障排除

### 配置文件不存在
```
❌ 配置文件不存在: /path/to/.env.xxx
```
**解决方案：** 检查文件路径和文件名，确保配置文件存在

### CORS错误
```
Access to XMLHttpRequest blocked by CORS policy
```
**解决方案：** 检查CORS_ORIGIN配置，确保包含前端访问地址

### 环境变量未加载
```
JWT_SECRET: 未设置
```
**解决方案：** 检查配置文件格式，确保没有语法错误

## 维护建议

1. **定期备份配置文件**
2. **文档化配置变更**
3. **测试配置文件切换**
4. **监控配置加载状态**
5. **定期安全审查**
