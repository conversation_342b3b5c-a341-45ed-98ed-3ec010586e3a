'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('product_price_history', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      productId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'products',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      oldLyPrice: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '变更前的光年币价格'
      },
      newLyPrice: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '变更后的光年币价格'
      },
      oldRmbPrice: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
        comment: '变更前的人民币价格'
      },
      newRmbPrice: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        comment: '变更后的人民币价格'
      },
      changeReason: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: '价格变更原因'
      },
      changedBy: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: '价格变更操作人'
      },
      effectiveDate: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        comment: '价格生效时间'
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // 添加索引
    await queryInterface.addIndex('product_price_history', ['productId']);
    await queryInterface.addIndex('product_price_history', ['effectiveDate']);
    await queryInterface.addIndex('product_price_history', ['changedBy']);

    // 为现有商品创建初始价格历史记录
    console.log('正在为现有商品创建初始价格历史记录...');
    
    await queryInterface.sequelize.query(`
      INSERT INTO product_price_history (productId, newLyPrice, newRmbPrice, changeReason, effectiveDate, createdAt, updatedAt)
      SELECT 
        id as productId,
        lyPrice as newLyPrice,
        rmbPrice as newRmbPrice,
        '系统初始化' as changeReason,
        createdAt as effectiveDate,
        NOW() as createdAt,
        NOW() as updatedAt
      FROM products
    `);
    
    console.log('初始价格历史记录创建完成');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('product_price_history');
  }
};
