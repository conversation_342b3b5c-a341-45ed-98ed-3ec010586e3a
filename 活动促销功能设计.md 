# 光年小卖部 - 活动促销功能设计方案

## 📋 项目现状分析

### 技术架构
- **前端**: Vue 3 + Element Plus + Pinia + Vue Router
- **后端**: Node.js + Express + Sequelize + MySQL
- **数据库**: MySQL (feishu_mall)
- **认证**: JWT + 飞书OAuth集成

### 现有核心模型
- **Product**: 商品模型（支持光年币/人民币双价格）
- **User**: 用户模型（包含光年币系统）
- **Exchange**: 订单模型（支持双支付方式）
- **Category**: 分类模型
- **ProductPriceHistory**: 价格历史模型

### 现有促销功能
- 商品标签：`isHot`（热门）、`isNew`（新品）
- 价格历史追踪功能
- 基础商品筛选和展示

---

## 🎯 活动促销模块设计

### 1. 数据库设计

#### 1.1 活动表 (promotions)
```sql
CREATE TABLE promotions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL COMMENT '活动名称',
  description TEXT COMMENT '活动描述',
  type ENUM('flash_sale', 'discount', 'bundle', 'newbie', 'points_multiply') NOT NULL COMMENT '活动类型',
  status ENUM('draft', 'active', 'paused', 'ended') DEFAULT 'draft' COMMENT '活动状态',
  startTime DATETIME NOT NULL COMMENT '活动开始时间',
  endTime DATETIME NOT NULL COMMENT '活动结束时间',
  priority INT DEFAULT 0 COMMENT '活动优先级（数字越大优先级越高）',

  -- 活动规则配置（JSON格式存储）
  rules JSON COMMENT '活动规则配置',

  -- 参与限制
  maxParticipants INT DEFAULT NULL COMMENT '最大参与人数',
  currentParticipants INT DEFAULT 0 COMMENT '当前参与人数',
  userLimitType ENUM('none', 'per_user', 'per_day') DEFAULT 'none' COMMENT '用户限制类型',
  userLimitValue INT DEFAULT NULL COMMENT '用户限制数量',

  -- 新用户限制
  newUserDays INT DEFAULT NULL COMMENT '新用户定义天数',

  -- 统计信息
  viewCount INT DEFAULT 0 COMMENT '浏览次数',
  participantCount INT DEFAULT 0 COMMENT '参与次数',
  successCount INT DEFAULT 0 COMMENT '成功次数',

  createdBy INT COMMENT '创建人ID',
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_type (type),
  INDEX idx_status (status),
  INDEX idx_time (startTime, endTime),
  FOREIGN KEY (createdBy) REFERENCES users(id)
);
```

#### 1.2 活动商品关联表 (promotion_products)
```sql
CREATE TABLE promotion_products (
  id INT AUTO_INCREMENT PRIMARY KEY,
  promotionId INT NOT NULL,
  productId INT NOT NULL,

  -- 促销价格
  promotionLyPrice INT COMMENT '促销光年币价格',
  promotionRmbPrice DECIMAL(10,2) COMMENT '促销人民币价格',
  discountType ENUM('fixed', 'percentage') COMMENT '折扣类型',
  discountValue DECIMAL(10,2) COMMENT '折扣值',

  -- 库存限制
  promotionStock INT COMMENT '促销库存',
  soldCount INT DEFAULT 0 COMMENT '已售数量',

  -- 排序
  sortOrder INT DEFAULT 0,

  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  UNIQUE KEY uk_promotion_product (promotionId, productId),
  FOREIGN KEY (promotionId) REFERENCES promotions(id) ON DELETE CASCADE,
  FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE
);
```

#### 1.3 活动参与记录表 (promotion_participations)
```sql
CREATE TABLE promotion_participations (
  id INT AUTO_INCREMENT PRIMARY KEY,
  promotionId INT NOT NULL,
  userId INT NOT NULL,
  productId INT COMMENT '参与的商品ID（如果是商品级活动）',
  exchangeId INT COMMENT '关联的订单ID',

  participationType ENUM('view', 'participate', 'success') NOT NULL COMMENT '参与类型',
  participationData JSON COMMENT '参与数据',

  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,

  INDEX idx_promotion_user (promotionId, userId),
  INDEX idx_user_promotion (userId, promotionId),
  FOREIGN KEY (promotionId) REFERENCES promotions(id) ON DELETE CASCADE,
  FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (productId) REFERENCES products(id) ON DELETE SET NULL,
  FOREIGN KEY (exchangeId) REFERENCES exchanges(id) ON DELETE SET NULL
);
```

#### 1.4 套餐商品表 (bundle_products)
```sql
CREATE TABLE bundle_products (
  id INT AUTO_INCREMENT PRIMARY KEY,
  promotionId INT NOT NULL,
  productId INT NOT NULL,
  quantity INT DEFAULT 1 COMMENT '套餐中商品数量',
  sortOrder INT DEFAULT 0,

  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (promotionId) REFERENCES promotions(id) ON DELETE CASCADE,
  FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE
);
```

### 2. 活动类型详细设计

#### 2.1 限时秒杀 (flash_sale)
**规则配置示例**:
```json
{
  "flashSale": {
    "limitPerUser": 2,
    "limitPerDay": 1,
    "showCountdown": true,
    "previewTime": 300
  }
}
```

#### 2.2 满额优惠 (discount)
**规则配置示例**:
```json
{
  "discount": {
    "type": "threshold",
    "rules": [
      {"minAmount": 200, "discountAmount": 50, "currency": "ly"},
      {"minAmount": 500, "discountAmount": 150, "currency": "ly"}
    ],
    "maxDiscount": 300
  }
}
```

#### 2.3 组合套餐 (bundle)
**规则配置示例**:
```json
{
  "bundle": {
    "bundleLyPrice": 60,
    "bundleRmbPrice": 45.00,
    "originalLyPrice": 80,
    "originalRmbPrice": 60.00,
    "limitPerUser": 1
  }
}
```

#### 2.4 新人专享 (newbie)
**规则配置示例**:
```json
{
  "newbie": {
    "newUserDays": 7,
    "discountType": "percentage",
    "discountValue": 20,
    "limitPerUser": 3
  }
}
```

#### 2.5 积分倍数 (points_multiply)
**规则配置示例**:
```json
{
  "pointsMultiply": {
    "multiplier": 2,
    "maxPoints": 1000,
    "applicableCategories": [1, 2, 3]
  }
}
```

---

## 🔧 后端实现

### 3. Sequelize模型定义

#### 3.1 Promotion模型
```javascript
// server/models/promotion.js
const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Promotion = sequelize.define('Promotion', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 255]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  type: {
    type: DataTypes.ENUM('flash_sale', 'discount', 'bundle', 'newbie', 'points_multiply'),
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('draft', 'active', 'paused', 'ended'),
    allowNull: false,
    defaultValue: 'draft'
  },
  startTime: {
    type: DataTypes.DATE,
    allowNull: false,
    validate: {
      isDate: true,
      isAfter: new Date().toISOString()
    }
  },
  endTime: {
    type: DataTypes.DATE,
    allowNull: false,
    validate: {
      isDate: true,
      isAfterStartTime(value) {
        if (value <= this.startTime) {
          throw new Error('结束时间必须晚于开始时间');
        }
      }
    }
  },
  priority: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  rules: {
    type: DataTypes.JSON,
    allowNull: true
  },
  maxParticipants: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1
    }
  },
  currentParticipants: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  userLimitType: {
    type: DataTypes.ENUM('none', 'per_user', 'per_day'),
    allowNull: false,
    defaultValue: 'none'
  },
  userLimitValue: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  newUserDays: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  viewCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  participantCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  successCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  createdBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'promotions',
  timestamps: true,
  hooks: {
    beforeCreate: (promotion) => {
      // 验证活动规则
      if (promotion.type && promotion.rules) {
        validatePromotionRules(promotion.type, promotion.rules);
      }
    },
    beforeUpdate: (promotion) => {
      // 验证活动规则
      if (promotion.type && promotion.rules) {
        validatePromotionRules(promotion.type, promotion.rules);
      }
    }
  }
});

// 验证活动规则的辅助函数
function validatePromotionRules(type, rules) {
  const validators = {
    flash_sale: (rules) => {
      if (!rules.flashSale) throw new Error('秒杀活动缺少flashSale配置');
      if (rules.flashSale.limitPerUser && rules.flashSale.limitPerUser < 1) {
        throw new Error('每用户限购数量必须大于0');
      }
    },
    discount: (rules) => {
      if (!rules.discount) throw new Error('优惠活动缺少discount配置');
      if (!rules.discount.rules || !Array.isArray(rules.discount.rules)) {
        throw new Error('优惠规则必须是数组');
      }
    },
    bundle: (rules) => {
      if (!rules.bundle) throw new Error('套餐活动缺少bundle配置');
      if (!rules.bundle.bundleLyPrice && !rules.bundle.bundleRmbPrice) {
        throw new Error('套餐必须设置至少一种价格');
      }
    },
    newbie: (rules) => {
      if (!rules.newbie) throw new Error('新人活动缺少newbie配置');
      if (!rules.newbie.newUserDays || rules.newbie.newUserDays < 1) {
        throw new Error('新用户天数必须大于0');
      }
    },
    points_multiply: (rules) => {
      if (!rules.pointsMultiply) throw new Error('积分倍数活动缺少pointsMultiply配置');
      if (!rules.pointsMultiply.multiplier || rules.pointsMultiply.multiplier < 1) {
        throw new Error('积分倍数必须大于等于1');
      }
    }
  };

  if (validators[type]) {
    validators[type](rules);
  }
}

module.exports = Promotion;
```

#### 3.2 PromotionProduct模型
```javascript
// server/models/promotionProduct.js
const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const PromotionProduct = sequelize.define('PromotionProduct', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  promotionId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'promotions',
      key: 'id'
    }
  },
  productId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  promotionLyPrice: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 0
    }
  },
  promotionRmbPrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    }
  },
  discountType: {
    type: DataTypes.ENUM('fixed', 'percentage'),
    allowNull: true
  },
  discountValue: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  promotionStock: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 0
    }
  },
  soldCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  sortOrder: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  }
}, {
  tableName: 'promotion_products',
  timestamps: true,
  indexes: [
    {
      unique: true,
      fields: ['promotionId', 'productId']
    }
  ]
});

module.exports = PromotionProduct;
```

#### 3.3 PromotionParticipation模型
```javascript
// server/models/promotionParticipation.js
const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const PromotionParticipation = sequelize.define('PromotionParticipation', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  promotionId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'promotions',
      key: 'id'
    }
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  productId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  exchangeId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'exchanges',
      key: 'id'
    }
  },
  participationType: {
    type: DataTypes.ENUM('view', 'participate', 'success'),
    allowNull: false
  },
  participationData: {
    type: DataTypes.JSON,
    allowNull: true
  }
}, {
  tableName: 'promotion_participations',
  timestamps: true,
  updatedAt: false,
  indexes: [
    {
      fields: ['promotionId', 'userId']
    },
    {
      fields: ['userId', 'promotionId']
    }
  ]
});

module.exports = PromotionParticipation;
```

#### 3.4 BundleProduct模型
```javascript
// server/models/bundleProduct.js
const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const BundleProduct = sequelize.define('BundleProduct', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  promotionId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'promotions',
      key: 'id'
    }
  },
  productId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    validate: {
      min: 1
    }
  },
  sortOrder: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  }
}, {
  tableName: 'bundle_products',
  timestamps: true,
  updatedAt: false
});

module.exports = BundleProduct;
```

### 4. 模型关联关系

#### 4.1 更新models/index.js
```javascript
// 在server/models/index.js中添加新模型和关联关系

const Promotion = require('./promotion');
const PromotionProduct = require('./promotionProduct');
const PromotionParticipation = require('./promotionParticipation');
const BundleProduct = require('./bundleProduct');

// 活动与用户的关系：多对多（通过参与记录）
Promotion.belongsToMany(User, {
  through: PromotionParticipation,
  foreignKey: 'promotionId',
  otherKey: 'userId',
  as: 'participants'
});
User.belongsToMany(Promotion, {
  through: PromotionParticipation,
  foreignKey: 'userId',
  otherKey: 'promotionId',
  as: 'participatedPromotions'
});

// 活动与商品的关系：多对多（通过活动商品表）
Promotion.belongsToMany(Product, {
  through: PromotionProduct,
  foreignKey: 'promotionId',
  otherKey: 'productId',
  as: 'products'
});
Product.belongsToMany(Promotion, {
  through: PromotionProduct,
  foreignKey: 'productId',
  otherKey: 'promotionId',
  as: 'promotions'
});

// 活动与套餐商品的关系：一对多
Promotion.hasMany(BundleProduct, { foreignKey: 'promotionId', as: 'bundleProducts' });
BundleProduct.belongsTo(Promotion, { foreignKey: 'promotionId' });

// 商品与套餐的关系：一对多
Product.hasMany(BundleProduct, { foreignKey: 'productId' });
BundleProduct.belongsTo(Product, { foreignKey: 'productId' });

// 活动参与记录的关联
Promotion.hasMany(PromotionParticipation, { foreignKey: 'promotionId' });
PromotionParticipation.belongsTo(Promotion, { foreignKey: 'promotionId' });

User.hasMany(PromotionParticipation, { foreignKey: 'userId' });
PromotionParticipation.belongsTo(User, { foreignKey: 'userId' });

Product.hasMany(PromotionParticipation, { foreignKey: 'productId' });
PromotionParticipation.belongsTo(Product, { foreignKey: 'productId' });

Exchange.hasMany(PromotionParticipation, { foreignKey: 'exchangeId' });
PromotionParticipation.belongsTo(Exchange, { foreignKey: 'exchangeId' });

// 活动创建者关联
User.hasMany(Promotion, { foreignKey: 'createdBy', as: 'createdPromotions' });
Promotion.belongsTo(User, { foreignKey: 'createdBy', as: 'creator' });

module.exports = {
  // ... 现有模型
  Promotion,
  PromotionProduct,
  PromotionParticipation,
  BundleProduct
};
```

### 5. 控制器实现

#### 5.1 活动控制器
```javascript
// server/controllers/promotionController.js
const {
  Promotion,
  PromotionProduct,
  PromotionParticipation,
  BundleProduct,
  Product,
  User,
  Exchange
} = require('../models');
const { Op } = require('sequelize');
const PromotionService = require('../services/promotionService');

/**
 * 获取活动列表
 */
exports.getPromotions = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      type,
      status,
      search,
      startDate,
      endDate
    } = req.query;

    const offset = (page - 1) * limit;
    const where = {};

    // 筛选条件
    if (type) where.type = type;
    if (status) where.status = status;
    if (search) {
      where[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }
    if (startDate && endDate) {
      where.startTime = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    }

    const { count, rows } = await Promotion.findAndCountAll({
      where,
      include: [
        {
          model: Product,
          as: 'products',
          through: { attributes: ['promotionLyPrice', 'promotionRmbPrice', 'soldCount'] }
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ],
      order: [['priority', 'DESC'], ['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset
    });

    res.json({
      success: true,
      data: rows,
      total: count,
      page: parseInt(page),
      limit: parseInt(limit)
    });
  } catch (error) {
    console.error('获取活动列表失败:', error);
    res.status(500).json({ success: false, message: '获取活动列表失败', error: error.message });
  }
};

/**
 * 获取活动详情
 */
exports.getPromotionById = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    const promotion = await Promotion.findByPk(id, {
      include: [
        {
          model: Product,
          as: 'products',
          through: {
            attributes: ['promotionLyPrice', 'promotionRmbPrice', 'promotionStock', 'soldCount']
          },
          include: [
            { model: ProductImage, as: 'images' },
            { model: Category }
          ]
        },
        {
          model: BundleProduct,
          as: 'bundleProducts',
          include: [{ model: Product }]
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ]
    });

    if (!promotion) {
      return res.status(404).json({ success: false, message: '活动不存在' });
    }

    // 记录浏览
    if (userId) {
      await PromotionService.recordParticipation(id, userId, 'view');
    }

    // 检查用户参与状态
    let userParticipation = null;
    if (userId) {
      userParticipation = await PromotionService.getUserParticipationStatus(id, userId);
    }

    res.json({
      success: true,
      data: {
        ...promotion.toJSON(),
        userParticipation
      }
    });
  } catch (error) {
    console.error('获取活动详情失败:', error);
    res.status(500).json({ success: false, message: '获取活动详情失败', error: error.message });
  }
};

/**
 * 创建活动
 */
exports.createPromotion = async (req, res) => {
  try {
    const userId = req.user.id;
    const promotionData = {
      ...req.body,
      createdBy: userId
    };

    const promotion = await PromotionService.createPromotion(promotionData);

    res.status(201).json({
      success: true,
      data: promotion,
      message: '活动创建成功'
    });
  } catch (error) {
    console.error('创建活动失败:', error);
    res.status(500).json({ success: false, message: '创建活动失败', error: error.message });
  }
};

/**
 * 更新活动
 */
exports.updatePromotion = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const promotion = await PromotionService.updatePromotion(id, updateData);

    res.json({
      success: true,
      data: promotion,
      message: '活动更新成功'
    });
  } catch (error) {
    console.error('更新活动失败:', error);
    res.status(500).json({ success: false, message: '更新活动失败', error: error.message });
  }
};

/**
 * 删除活动
 */
exports.deletePromotion = async (req, res) => {
  try {
    const { id } = req.params;

    await PromotionService.deletePromotion(id);

    res.json({
      success: true,
      message: '活动删除成功'
    });
  } catch (error) {
    console.error('删除活动失败:', error);
    res.status(500).json({ success: false, message: '删除活动失败', error: error.message });
  }
};

/**
 * 参与活动
 */
exports.participatePromotion = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const { productId, quantity = 1 } = req.body;

    const result = await PromotionService.participatePromotion(id, userId, productId, quantity);

    res.json({
      success: true,
      data: result,
      message: '参与活动成功'
    });
  } catch (error) {
    console.error('参与活动失败:', error);
    res.status(400).json({ success: false, message: error.message });
  }
};

/**
 * 获取活动统计
 */
exports.getPromotionStats = async (req, res) => {
  try {
    const { id } = req.params;

    const stats = await PromotionService.getPromotionStats(id);

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('获取活动统计失败:', error);
    res.status(500).json({ success: false, message: '获取活动统计失败', error: error.message });
  }
};
```

### 6. 服务层实现

#### 6.1 活动服务
```javascript
// server/services/promotionService.js
const {
  Promotion,
  PromotionProduct,
  PromotionParticipation,
  BundleProduct,
  Product,
  User,
  Exchange,
  sequelize
} = require('../models');
const { Op } = require('sequelize');

class PromotionService {
  /**
   * 创建活动
   */
  static async createPromotion(promotionData) {
    const transaction = await sequelize.transaction();

    try {
      const { products, bundleProducts, ...mainData } = promotionData;

      // 创建主活动
      const promotion = await Promotion.create(mainData, { transaction });

      // 添加活动商品
      if (products && products.length > 0) {
        const promotionProducts = products.map(product => ({
          promotionId: promotion.id,
          ...product
        }));
        await PromotionProduct.bulkCreate(promotionProducts, { transaction });
      }

      // 添加套餐商品（如果是套餐活动）
      if (bundleProducts && bundleProducts.length > 0) {
        const bundleItems = bundleProducts.map(item => ({
          promotionId: promotion.id,
          ...item
        }));
        await BundleProduct.bulkCreate(bundleItems, { transaction });
      }

      await transaction.commit();
      return promotion;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 更新活动
   */
  static async updatePromotion(promotionId, updateData) {
    const transaction = await sequelize.transaction();

    try {
      const { products, bundleProducts, ...mainData } = updateData;

      // 更新主活动
      await Promotion.update(mainData, {
        where: { id: promotionId },
        transaction
      });

      // 更新活动商品
      if (products !== undefined) {
        // 删除现有关联
        await PromotionProduct.destroy({
          where: { promotionId },
          transaction
        });

        // 添加新关联
        if (products.length > 0) {
          const promotionProducts = products.map(product => ({
            promotionId,
            ...product
          }));
          await PromotionProduct.bulkCreate(promotionProducts, { transaction });
        }
      }

      // 更新套餐商品
      if (bundleProducts !== undefined) {
        await BundleProduct.destroy({
          where: { promotionId },
          transaction
        });

        if (bundleProducts.length > 0) {
          const bundleItems = bundleProducts.map(item => ({
            promotionId,
            ...item
          }));
          await BundleProduct.bulkCreate(bundleItems, { transaction });
        }
      }

      await transaction.commit();

      // 返回更新后的活动
      return await Promotion.findByPk(promotionId, {
        include: [
          { model: Product, as: 'products' },
          { model: BundleProduct, as: 'bundleProducts' }
        ]
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 删除活动
   */
  static async deletePromotion(promotionId) {
    const promotion = await Promotion.findByPk(promotionId);
    if (!promotion) {
      throw new Error('活动不存在');
    }

    if (promotion.status === 'active') {
      throw new Error('无法删除进行中的活动');
    }

    await promotion.destroy();
  }

  /**
   * 记录用户参与
   */
  static async recordParticipation(promotionId, userId, type, data = {}) {
    try {
      await PromotionParticipation.create({
        promotionId,
        userId,
        participationType: type,
        participationData: data,
        productId: data.productId || null,
        exchangeId: data.exchangeId || null
      });

      // 更新活动统计
      if (type === 'view') {
        await Promotion.increment('viewCount', { where: { id: promotionId } });
      } else if (type === 'participate') {
        await Promotion.increment('participantCount', { where: { id: promotionId } });
      } else if (type === 'success') {
        await Promotion.increment('successCount', { where: { id: promotionId } });
      }
    } catch (error) {
      console.error('记录参与失败:', error);
    }
  }

  /**
   * 获取用户参与状态
   */
  static async getUserParticipationStatus(promotionId, userId) {
    const participations = await PromotionParticipation.findAll({
      where: { promotionId, userId },
      order: [['createdAt', 'DESC']]
    });

    return {
      hasViewed: participations.some(p => p.participationType === 'view'),
      hasParticipated: participations.some(p => p.participationType === 'participate'),
      hasSucceeded: participations.some(p => p.participationType === 'success'),
      participationCount: participations.filter(p => p.participationType === 'participate').length,
      successCount: participations.filter(p => p.participationType === 'success').length,
      lastParticipation: participations[0]
    };
  }

  /**
   * 参与活动
   */
  static async participatePromotion(promotionId, userId, productId, quantity = 1) {
    const transaction = await sequelize.transaction();

    try {
      // 获取活动信息
      const promotion = await Promotion.findByPk(promotionId, {
        include: [
          { model: Product, as: 'products' },
          { model: BundleProduct, as: 'bundleProducts' }
        ],
        transaction
      });

      if (!promotion) {
        throw new Error('活动不存在');
      }

      // 检查活动状态
      if (promotion.status !== 'active') {
        throw new Error('活动未开始或已结束');
      }

      // 检查活动时间
      const now = new Date();
      if (now < promotion.startTime || now > promotion.endTime) {
        throw new Error('不在活动时间范围内');
      }

      // 检查用户资格
      await this.checkUserEligibility(promotion, userId, transaction);

      // 检查参与限制
      await this.checkParticipationLimits(promotion, userId, quantity, transaction);

      // 根据活动类型处理参与逻辑
      const result = await this.processParticipation(promotion, userId, productId, quantity, transaction);

      await transaction.commit();
      return result;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 检查用户资格
   */
  static async checkUserEligibility(promotion, userId, transaction) {
    const user = await User.findByPk(userId, { transaction });

    if (!user) {
      throw new Error('用户不存在');
    }

    // 新用户检查
    if (promotion.type === 'newbie' && promotion.newUserDays) {
      const daysSinceRegistration = Math.floor(
        (new Date() - user.createdAt) / (1000 * 60 * 60 * 24)
      );

      if (daysSinceRegistration > promotion.newUserDays) {
        throw new Error('仅限新用户参与');
      }
    }
  }

  /**
   * 检查参与限制
   */
  static async checkParticipationLimits(promotion, userId, quantity, transaction) {
    // 检查总参与人数限制
    if (promotion.maxParticipants && promotion.currentParticipants >= promotion.maxParticipants) {
      throw new Error('活动参与人数已满');
    }

    // 检查用户参与限制
    if (promotion.userLimitType !== 'none' && promotion.userLimitValue) {
      const whereCondition = {
        promotionId: promotion.id,
        userId,
        participationType: 'participate'
      };

      if (promotion.userLimitType === 'per_day') {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        whereCondition.createdAt = {
          [Op.between]: [today, tomorrow]
        };
      }

      const participationCount = await PromotionParticipation.count({
        where: whereCondition,
        transaction
      });

      if (participationCount + quantity > promotion.userLimitValue) {
        const limitText = promotion.userLimitType === 'per_day' ? '每日' : '总计';
        throw new Error(`超出${limitText}参与限制（${promotion.userLimitValue}次）`);
      }
    }
  }

  /**
   * 处理参与逻辑
   */
  static async processParticipation(promotion, userId, productId, quantity, transaction) {
    switch (promotion.type) {
      case 'flash_sale':
        return await this.processFlashSale(promotion, userId, productId, quantity, transaction);
      case 'discount':
        return await this.processDiscount(promotion, userId, productId, quantity, transaction);
      case 'bundle':
        return await this.processBundle(promotion, userId, quantity, transaction);
      case 'newbie':
        return await this.processNewbie(promotion, userId, productId, quantity, transaction);
      case 'points_multiply':
        return await this.processPointsMultiply(promotion, userId, productId, quantity, transaction);
      default:
        throw new Error('不支持的活动类型');
    }
  }

  /**
   * 处理秒杀活动
   */
  static async processFlashSale(promotion, userId, productId, quantity, transaction) {
    const promotionProduct = await PromotionProduct.findOne({
      where: { promotionId: promotion.id, productId },
      transaction
    });

    if (!promotionProduct) {
      throw new Error('商品未参与此活动');
    }

    // 检查促销库存
    if (promotionProduct.promotionStock !== null) {
      if (promotionProduct.soldCount + quantity > promotionProduct.promotionStock) {
        throw new Error('促销库存不足');
      }
    }

    // 更新销售数量
    await PromotionProduct.update(
      { soldCount: promotionProduct.soldCount + quantity },
      { where: { id: promotionProduct.id }, transaction }
    );

    // 记录参与
    await this.recordParticipation(promotion.id, userId, 'participate', {
      productId,
      quantity,
      promotionPrice: promotionProduct.promotionLyPrice || promotionProduct.promotionRmbPrice
    });

    return {
      type: 'flash_sale',
      productId,
      quantity,
      promotionPrice: {
        ly: promotionProduct.promotionLyPrice,
        rmb: promotionProduct.promotionRmbPrice
      }
    };
  }

  /**
   * 获取活动统计
   */
  static async getPromotionStats(promotionId) {
    const promotion = await Promotion.findByPk(promotionId);
    if (!promotion) {
      throw new Error('活动不存在');
    }

    // 基础统计
    const stats = {
      viewCount: promotion.viewCount,
      participantCount: promotion.participantCount,
      successCount: promotion.successCount,
      conversionRate: promotion.viewCount > 0 ?
        (promotion.participantCount / promotion.viewCount * 100).toFixed(2) : 0,
      successRate: promotion.participantCount > 0 ?
        (promotion.successCount / promotion.participantCount * 100).toFixed(2) : 0
    };

    // 商品统计
    const productStats = await PromotionProduct.findAll({
      where: { promotionId },
      include: [{ model: Product, attributes: ['name'] }]
    });

    stats.products = productStats.map(pp => ({
      productId: pp.productId,
      productName: pp.Product.name,
      soldCount: pp.soldCount,
      promotionStock: pp.promotionStock,
      sellRate: pp.promotionStock ?
        (pp.soldCount / pp.promotionStock * 100).toFixed(2) : null
    }));

    // 时间分布统计
    const participationsByHour = await PromotionParticipation.findAll({
      where: { promotionId },
      attributes: [
        [sequelize.fn('HOUR', sequelize.col('createdAt')), 'hour'],
        [sequelize.fn('COUNT', '*'), 'count']
      ],
      group: [sequelize.fn('HOUR', sequelize.col('createdAt'))],
      order: [[sequelize.fn('HOUR', sequelize.col('createdAt')), 'ASC']]
    });

    stats.hourlyDistribution = participationsByHour;

    return stats;
  }
}

module.exports = PromotionService;
```

---

## 🎨 前端实现

### 7. 前端组件设计

#### 7.1 活动卡片组件
```vue
<!-- src/components/PromotionCard.vue -->
<template>
  <div class="promotion-card" :class="[`promotion-${promotion.type}`, `status-${promotion.status}`]">
    <div class="promotion-header">
      <div class="promotion-type-badge">
        <el-tag :type="getTypeColor(promotion.type)" size="small">
          {{ getTypeName(promotion.type) }}
        </el-tag>
      </div>
      <div class="promotion-status">
        <el-tag :type="getStatusColor(promotion.status)" size="small">
          {{ getStatusName(promotion.status) }}
        </el-tag>
      </div>
    </div>

    <div class="promotion-content">
      <h3 class="promotion-title">{{ promotion.name }}</h3>
      <p class="promotion-description">{{ promotion.description }}</p>

      <!-- 时间信息 -->
      <div class="promotion-time">
        <div class="time-item">
          <el-icon><Clock /></el-icon>
          <span>{{ formatTime(promotion.startTime) }} - {{ formatTime(promotion.endTime) }}</span>
        </div>
        <div v-if="isActive" class="countdown">
          <el-icon><Timer /></el-icon>
          <span>剩余: {{ countdown }}</span>
        </div>
      </div>

      <!-- 商品预览 -->
      <div v-if="promotion.products && promotion.products.length > 0" class="promotion-products">
        <div class="product-preview">
          <div
            v-for="(product, index) in promotion.products.slice(0, 3)"
            :key="product.id"
            class="product-item"
          >
            <el-image
              :src="getProductImage(product)"
              fit="cover"
              class="product-image"
            />
            <div class="product-info">
              <div class="product-name">{{ product.name }}</div>
              <div class="product-price">
                <span v-if="getPromotionPrice(product, 'ly')" class="ly-price">
                  {{ getPromotionPrice(product, 'ly') }}光年币
                </span>
                <span v-if="getPromotionPrice(product, 'rmb')" class="rmb-price">
                  ¥{{ getPromotionPrice(product, 'rmb') }}
                </span>
              </div>
            </div>
          </div>
          <div v-if="promotion.products.length > 3" class="more-products">
            +{{ promotion.products.length - 3 }}
          </div>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="promotion-stats">
        <div class="stat-item">
          <span class="stat-label">浏览</span>
          <span class="stat-value">{{ promotion.viewCount || 0 }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">参与</span>
          <span class="stat-value">{{ promotion.participantCount || 0 }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">成功</span>
          <span class="stat-value">{{ promotion.successCount || 0 }}</span>
        </div>
      </div>
    </div>

    <div class="promotion-actions">
      <el-button
        v-if="isActive"
        type="primary"
        @click="handleParticipate"
        :disabled="!canParticipate"
      >
        {{ getActionText() }}
      </el-button>
      <el-button
        v-else-if="promotion.status === 'draft'"
        type="info"
        @click="handleEdit"
      >
        编辑
      </el-button>
      <el-button type="default" @click="handleViewDetail">
        查看详情
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { Clock, Timer } from '@element-plus/icons-vue';
import { formatDistanceToNow, format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

const props = defineProps({
  promotion: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['participate', 'edit', 'view-detail']);

// 倒计时
const countdown = ref('');
const countdownTimer = ref(null);

// 计算属性
const isActive = computed(() => {
  const now = new Date();
  return props.promotion.status === 'active' &&
         now >= new Date(props.promotion.startTime) &&
         now <= new Date(props.promotion.endTime);
});

const canParticipate = computed(() => {
  if (!isActive.value) return false;

  // 检查参与人数限制
  if (props.promotion.maxParticipants &&
      props.promotion.currentParticipants >= props.promotion.maxParticipants) {
    return false;
  }

  return true;
});

// 方法
const getTypeColor = (type) => {
  const colors = {
    flash_sale: 'danger',
    discount: 'warning',
    bundle: 'success',
    newbie: 'info',
    points_multiply: 'primary'
  };
  return colors[type] || 'default';
};

const getTypeName = (type) => {
  const names = {
    flash_sale: '限时秒杀',
    discount: '满额优惠',
    bundle: '组合套餐',
    newbie: '新人专享',
    points_multiply: '积分倍数'
  };
  return names[type] || type;
};

const getStatusColor = (status) => {
  const colors = {
    draft: 'info',
    active: 'success',
    paused: 'warning',
    ended: 'danger'
  };
  return colors[status] || 'default';
};

const getStatusName = (status) => {
  const names = {
    draft: '草稿',
    active: '进行中',
    paused: '已暂停',
    ended: '已结束'
  };
  return names[status] || status;
};

const formatTime = (time) => {
  return format(new Date(time), 'MM-dd HH:mm');
};

const getProductImage = (product) => {
  return product.images && product.images.length > 0 ?
    product.images[0].imageUrl : '/images/default-product.png';
};

const getPromotionPrice = (product, currency) => {
  const promotionProduct = product.PromotionProduct;
  if (!promotionProduct) return null;

  if (currency === 'ly') {
    return promotionProduct.promotionLyPrice;
  } else {
    return promotionProduct.promotionRmbPrice;
  }
};

const getActionText = () => {
  const typeTexts = {
    flash_sale: '立即抢购',
    discount: '立即参与',
    bundle: '购买套餐',
    newbie: '新人专享',
    points_multiply: '参与活动'
  };
  return typeTexts[props.promotion.type] || '参与活动';
};

const updateCountdown = () => {
  if (!isActive.value) return;

  const now = new Date();
  const endTime = new Date(props.promotion.endTime);

  if (now >= endTime) {
    countdown.value = '已结束';
    if (countdownTimer.value) {
      clearInterval(countdownTimer.value);
    }
    return;
  }

  countdown.value = formatDistanceToNow(endTime, {
    locale: zhCN,
    addSuffix: false
  });
};

const handleParticipate = () => {
  emit('participate', props.promotion);
};

const handleEdit = () => {
  emit('edit', props.promotion);
};

const handleViewDetail = () => {
  emit('view-detail', props.promotion);
};

// 生命周期
onMounted(() => {
  updateCountdown();
  if (isActive.value) {
    countdownTimer.value = setInterval(updateCountdown, 60000); // 每分钟更新
  }
});

onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
  }
});
</script>

<style scoped>
.promotion-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  background: white;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.promotion-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.promotion-card.status-active {
  border-left: 4px solid #67c23a;
}

.promotion-card.status-ended {
  opacity: 0.7;
}

.promotion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.promotion-content {
  margin-bottom: 20px;
}

.promotion-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #303133;
}

.promotion-description {
  color: #606266;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.promotion-time {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-size: 14px;
  color: #909399;
}

.time-item, .countdown {
  display: flex;
  align-items: center;
  gap: 4px;
}

.countdown {
  color: #f56c6c;
  font-weight: 500;
}

.promotion-products {
  margin-bottom: 16px;
}

.product-preview {
  display: flex;
  gap: 12px;
  align-items: center;
}

.product-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 6px;
  flex: 1;
  min-width: 0;
}

.product-image {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  min-width: 0;
}

.product-name {
  font-size: 12px;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-price {
  font-size: 11px;
  color: #f56c6c;
  font-weight: 500;
}

.more-products {
  color: #909399;
  font-size: 12px;
  white-space: nowrap;
}

.promotion-stats {
  display: flex;
  gap: 20px;
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.promotion-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 活动类型特殊样式 */
.promotion-flash_sale {
  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
}

.promotion-bundle {
  background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);
}

.promotion-newbie {
  background: linear-gradient(135deg, #fefce8 0%, #ffffff 100%);
}
</style>
```

#### 7.2 活动列表页面
```vue
<!-- src/views/PromotionList.vue -->
<template>
  <div class="promotion-list-container">
    <div class="page-header">
      <h1>活动促销</h1>
      <el-button
        v-if="isAdmin"
        type="primary"
        @click="handleCreatePromotion"
        :icon="Plus"
      >
        创建活动
      </el-button>
    </div>

    <!-- 筛选器 -->
    <div class="filter-section">
      <el-form :model="filters" inline>
        <el-form-item label="活动类型">
          <el-select v-model="filters.type" placeholder="全部类型" clearable>
            <el-option label="限时秒杀" value="flash_sale" />
            <el-option label="满额优惠" value="discount" />
            <el-option label="组合套餐" value="bundle" />
            <el-option label="新人专享" value="newbie" />
            <el-option label="积分倍数" value="points_multiply" />
          </el-select>
        </el-form-item>

        <el-form-item label="活动状态">
          <el-select v-model="filters.status" placeholder="全部状态" clearable>
            <el-option label="草稿" value="draft" />
            <el-option label="进行中" value="active" />
            <el-option label="已暂停" value="paused" />
            <el-option label="已结束" value="ended" />
          </el-select>
        </el-form-item>

        <el-form-item label="搜索">
          <el-input
            v-model="filters.search"
            placeholder="搜索活动名称或描述"
            clearable
            @keyup.enter="loadPromotions"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="loadPromotions">搜索</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 活动列表 -->
    <div class="promotion-list" v-loading="loading">
      <div v-if="promotions.length === 0 && !loading" class="empty-state">
        <el-empty description="暂无活动" />
      </div>

      <div v-else class="promotion-grid">
        <PromotionCard
          v-for="promotion in promotions"
          :key="promotion.id"
          :promotion="promotion"
          @participate="handleParticipate"
          @edit="handleEdit"
          @view-detail="handleViewDetail"
        />
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper" v-if="total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        background
      />
    </div>

    <!-- 活动详情对话框 -->
    <PromotionDetailDialog
      v-model:visible="detailDialogVisible"
      :promotion="selectedPromotion"
      @participate="handleParticipate"
    />

    <!-- 活动创建/编辑对话框 -->
    <PromotionFormDialog
      v-model:visible="formDialogVisible"
      :promotion="editingPromotion"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import { usePromotionStore } from '../stores/promotion';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import PromotionCard from '../components/PromotionCard.vue';
import PromotionDetailDialog from '../components/PromotionDetailDialog.vue';
import PromotionFormDialog from '../components/PromotionFormDialog.vue';

const router = useRouter();
const authStore = useAuthStore();
const promotionStore = usePromotionStore();

// 响应式数据
const loading = ref(false);
const promotions = ref([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(20);

const filters = reactive({
  type: '',
  status: '',
  search: ''
});

const detailDialogVisible = ref(false);
const formDialogVisible = ref(false);
const selectedPromotion = ref(null);
const editingPromotion = ref(null);

// 计算属性
const isAdmin = computed(() => authStore.user?.role === 'admin');

// 方法
const loadPromotions = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      ...filters
    };

    const response = await promotionStore.getPromotions(params);
    promotions.value = response.data;
    total.value = response.total;
  } catch (error) {
    ElMessage.error('加载活动列表失败');
  } finally {
    loading.value = false;
  }
};

const resetFilters = () => {
  Object.assign(filters, {
    type: '',
    status: '',
    search: ''
  });
  currentPage.value = 1;
  loadPromotions();
};

const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
  loadPromotions();
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
  loadPromotions();
};

const handleCreatePromotion = () => {
  editingPromotion.value = null;
  formDialogVisible.value = true;
};

const handleEdit = (promotion) => {
  editingPromotion.value = promotion;
  formDialogVisible.value = true;
};

const handleViewDetail = (promotion) => {
  selectedPromotion.value = promotion;
  detailDialogVisible.value = true;
};

const handleParticipate = async (promotion) => {
  if (!authStore.isAuthenticated) {
    ElMessage.warning('请先登录');
    router.push('/login');
    return;
  }

  try {
    // 根据活动类型跳转到不同的参与页面
    switch (promotion.type) {
      case 'flash_sale':
        router.push(`/promotion/${promotion.id}/flash-sale`);
        break;
      case 'bundle':
        router.push(`/promotion/${promotion.id}/bundle`);
        break;
      default:
        router.push(`/promotion/${promotion.id}`);
    }
  } catch (error) {
    ElMessage.error('参与活动失败');
  }
};

const handleFormSuccess = () => {
  formDialogVisible.value = false;
  loadPromotions();
  ElMessage.success('操作成功');
};

// 生命周期
onMounted(() => {
  loadPromotions();
});
</script>

<style scoped>
.promotion-list-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  color: #303133;
}

.filter-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.promotion-list {
  min-height: 400px;
}

.promotion-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

@media (max-width: 768px) {
  .promotion-grid {
    grid-template-columns: 1fr;
  }

  .filter-section .el-form {
    flex-direction: column;
  }

  .filter-section .el-form-item {
    margin-right: 0;
    margin-bottom: 16px;
  }
}
</style>
```

---

## 📋 实施计划

### 8. 开发阶段规划

#### 阶段一：数据库和后端基础 (1-2周)
1. **数据库迁移文件创建**
   - 创建promotions表迁移
   - 创建promotion_products表迁移
   - 创建promotion_participations表迁移
   - 创建bundle_products表迁移

2. **Sequelize模型开发**
   - 创建所有促销相关模型
   - 配置模型关联关系
   - 添加数据验证和钩子函数

3. **基础API开发**
   - 活动CRUD接口
   - 活动参与接口
   - 活动统计接口

#### 阶段二：核心功能实现 (2-3周)
1. **服务层开发**
   - PromotionService核心逻辑
   - 各种活动类型的处理逻辑
   - 参与限制和资格检查

2. **控制器完善**
   - 完整的错误处理
   - 参数验证
   - 权限控制

3. **前端基础组件**
   - PromotionCard组件
   - 基础的活动列表页面

#### 阶段三：前端界面开发 (2-3周)
1. **管理后台界面**
   - 活动创建/编辑表单
   - 活动管理列表
   - 活动统计页面

2. **用户端界面**
   - 活动展示页面
   - 活动参与流程
   - 个人参与记录

3. **移动端适配**
   - 响应式设计优化
   - 移动端交互优化

#### 阶段四：高级功能和优化 (1-2周)
1. **高级功能**
   - 活动预告功能
   - 活动分享功能
   - 活动推荐算法

2. **性能优化**
   - 数据库查询优化
   - 缓存策略
   - 前端性能优化

3. **测试和部署**
   - 单元测试
   - 集成测试
   - 生产环境部署

### 9. 技术要点

#### 9.1 数据一致性
- 使用数据库事务确保活动参与的原子性
- 乐观锁处理并发参与问题
- 定时任务处理活动状态更新

#### 9.2 性能优化
- 活动列表使用分页和索引优化
- 热门活动数据缓存
- 活动统计数据异步计算

#### 9.3 安全考虑
- 活动参与权限验证
- 防刷机制
- 数据输入验证和过滤

#### 9.4 扩展性设计
- 插件化的活动类型系统
- 可配置的活动规则引擎
- 支持自定义活动模板

### 10. 测试策略

#### 10.1 单元测试
- 模型验证测试
- 服务层逻辑测试
- 工具函数测试

#### 10.2 集成测试
- API接口测试
- 数据库操作测试
- 第三方服务集成测试

#### 10.3 端到端测试
- 完整的活动创建流程测试
- 用户参与活动流程测试
- 活动状态变更测试

#### 10.4 性能测试
- 高并发参与测试
- 大数据量查询测试
- 系统负载测试

---

## 📊 总结与展望

### 11. 设计亮点

#### 11.1 架构优势
- **模块化设计**: 清晰的分层架构，便于维护和扩展
- **类型化活动**: 支持多种活动类型，满足不同业务需求
- **灵活配置**: JSON配置方式，支持复杂的活动规则
- **数据完整性**: 完善的关联关系和约束，确保数据一致性

#### 11.2 功能特色
- **实时统计**: 完整的活动数据统计和分析
- **用户体验**: 直观的界面设计和流畅的交互体验
- **权限控制**: 细粒度的权限管理，支持多角色操作
- **移动适配**: 响应式设计，支持多端访问

#### 11.3 技术创新
- **事件驱动**: 基于事件的活动状态管理
- **缓存策略**: 多层缓存提升系统性能
- **并发处理**: 乐观锁和事务保证数据安全
- **扩展性**: 插件化架构支持自定义活动类型

### 12. 风险评估与应对

#### 12.1 技术风险
- **并发问题**: 通过数据库锁和事务机制解决
- **性能瓶颈**: 使用缓存和数据库优化策略
- **数据一致性**: 严格的事务控制和数据验证

#### 12.2 业务风险
- **活动作弊**: 实施多重验证和限制机制
- **库存超卖**: 原子性操作和实时库存检查
- **用户体验**: 充分的测试和用户反馈收集

### 13. 下一步行动计划

#### 13.1 立即行动 (本周)
1. **创建数据库迁移文件**
   ```bash
   # 在server目录下执行
   npx sequelize-cli migration:generate --name create-promotions-table
   npx sequelize-cli migration:generate --name create-promotion-products-table
   npx sequelize-cli migration:generate --name create-promotion-participations-table
   npx sequelize-cli migration:generate --name create-bundle-products-table
   ```

2. **创建基础模型文件**
   - `server/models/promotion.js`
   - `server/models/promotionProduct.js`
   - `server/models/promotionParticipation.js`
   - `server/models/bundleProduct.js`

3. **更新模型关联关系**
   - 修改 `server/models/index.js`

#### 13.2 短期目标 (1-2周)
1. **完成后端基础架构**
   - 实现所有数据模型
   - 创建基础API接口
   - 编写服务层逻辑

2. **开发核心功能**
   - 活动创建和管理
   - 基础的活动参与逻辑
   - 简单的统计功能

#### 13.3 中期目标 (3-4周)
1. **完善前端界面**
   - 管理后台活动管理页面
   - 用户端活动展示页面
   - 移动端适配

2. **高级功能开发**
   - 复杂活动类型支持
   - 详细的数据统计
   - 活动推荐功能

#### 13.4 长期规划 (1-2个月)
1. **系统优化**
   - 性能调优
   - 安全加固
   - 监控告警

2. **功能扩展**
   - 活动模板系统
   - 自动化营销
   - 数据分析报表

### 14. 成功指标

#### 14.1 技术指标
- **系统稳定性**: 99.9%可用性
- **响应时间**: API响应时间<200ms
- **并发处理**: 支持1000+并发用户
- **数据准确性**: 0错误率的活动数据

#### 14.2 业务指标
- **用户参与度**: 活动参与率>30%
- **转化效果**: 活动转化率>15%
- **用户满意度**: 用户反馈评分>4.5/5
- **运营效率**: 活动创建时间<10分钟

---

## 🎯 结语

本活动促销功能设计方案基于光年小卖部现有技术架构，充分考虑了系统的扩展性、稳定性和用户体验。通过模块化的设计和完善的功能规划，将为平台带来强大的营销能力和更好的用户体验。

**核心价值**:
- 🚀 **提升用户活跃度**: 丰富的活动类型吸引用户参与
- 💰 **增加平台收益**: 有效的促销策略提升销售转化
- 🎯 **精准营销**: 基于数据的个性化活动推荐
- 📈 **运营效率**: 自动化的活动管理降低运营成本

**技术保障**:
- 🔒 **数据安全**: 完善的权限控制和数据保护
- ⚡ **高性能**: 优化的数据库设计和缓存策略
- 🔧 **易维护**: 清晰的代码结构和完整的文档
- 📱 **多端支持**: 响应式设计适配各种设备

期待这个活动促销模块能够为光年小卖部带来更大的商业价值和用户价值！
```
```
