#!/usr/bin/env node

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api';

// 测试支付偏好分析修复
async function testPaymentPreferenceFix() {
  console.log('=== 支付偏好分析修复验证测试 ===\n');

  try {
    // 1. 登录获取token
    console.log('1. 管理员登录...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: '超管',
        email: '<EMAIL>',
        password: '654321'
      })
    });

    const loginData = await loginResponse.json();
    if (!loginData.token) {
      throw new Error('登录失败');
    }
    console.log('✅ 登录成功');

    const token = loginData.token;
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // 2. 测试API数据结构
    console.log('\n2. 测试API数据结构...');
    
    const response = await fetch(`${BASE_URL}/exchanges/payment-preference?period=month`, {
      method: 'GET',
      headers
    });

    if (!response.ok) {
      throw new Error(`API调用失败: ${response.status} ${response.statusText}`);
    }

    const rawData = await response.json();
    console.log('原始API响应结构:');
    console.log('- success:', rawData.success);
    console.log('- data存在:', !!rawData.data);
    
    if (rawData.success && rawData.data) {
      const data = rawData.data;
      console.log('- data.overview存在:', !!data.overview);
      
      if (data.overview) {
        console.log('\n修复验证 - 概览数据:');
        console.log('- totalOrders:', data.overview.totalOrders);
        console.log('- rmbOrders:', data.overview.rmbOrders);
        console.log('- lyOrders:', data.overview.lyOrders);
        console.log('- rmbPercentage:', data.overview.rmbPercentage);
        console.log('- lyPercentage:', data.overview.lyPercentage);
        
        // 验证数据是否符合预期
        if (data.overview.totalOrders > 0) {
          console.log('✅ 数据正常：总订单数 > 0');
          
          if (data.overview.lyOrders > 0 && data.overview.rmbOrders === 0) {
            console.log('✅ 单一光年币支付场景正确识别');
          } else if (data.overview.rmbOrders > 0 && data.overview.lyOrders === 0) {
            console.log('✅ 单一人民币支付场景正确识别');
          } else if (data.overview.rmbOrders > 0 && data.overview.lyOrders > 0) {
            console.log('✅ 混合支付场景正确识别');
          }
          
          // 验证百分比计算
          const expectedLyPercentage = Math.round((data.overview.lyOrders / data.overview.totalOrders) * 100);
          const expectedRmbPercentage = Math.round((data.overview.rmbOrders / data.overview.totalOrders) * 100);
          
          if (Math.abs(data.overview.lyPercentage - expectedLyPercentage) <= 1) {
            console.log('✅ 光年币百分比计算正确');
          } else {
            console.log('⚠️  光年币百分比计算可能有误');
          }
          
          if (Math.abs(data.overview.rmbPercentage - expectedRmbPercentage) <= 1) {
            console.log('✅ 人民币百分比计算正确');
          } else {
            console.log('⚠️  人民币百分比计算可能有误');
          }
          
        } else {
          console.log('❌ 数据异常：总订单数为0，但数据库中应该有订单');
          return { success: false, error: '数据异常：总订单数为0' };
        }
      }
      
      // 验证其他数据
      console.log('\n其他数据验证:');
      console.log('- 趋势数据点数:', data.paymentTrend ? data.paymentTrend.length : 0);
      console.log('- 分类数据数量:', data.categoryPreference ? data.categoryPreference.length : 0);
      console.log('- 用户分层数据:', data.userSegments ? 'OK' : 'Missing');
      
      if (data.userSegments) {
        console.log('  - 仅人民币用户:', data.userSegments.rmbOnly);
        console.log('  - 仅光年币用户:', data.userSegments.lyOnly);
        console.log('  - 混合支付用户:', data.userSegments.mixed);
      }
      
    } else {
      console.log('❌ API响应结构异常');
      return { success: false, error: 'API响应结构异常' };
    }

    // 3. 测试不同时间周期
    console.log('\n3. 测试不同时间周期...');
    const periods = ['week', 'month', 'quarter', 'year'];
    const periodResults = {};
    
    for (const period of periods) {
      const periodResponse = await fetch(`${BASE_URL}/exchanges/payment-preference?period=${period}`, {
        method: 'GET',
        headers
      });
      
      if (periodResponse.ok) {
        const periodRawData = await periodResponse.json();
        if (periodRawData.success && periodRawData.data && periodRawData.data.overview) {
          const overview = periodRawData.data.overview;
          periodResults[period] = overview;
          console.log(`${period}: ${overview.totalOrders}单 (人民币${overview.rmbOrders}, 光年币${overview.lyOrders})`);
        }
      }
    }

    // 4. 验证数据一致性
    console.log('\n4. 验证数据一致性...');
    if (periodResults.year && periodResults.month) {
      if (periodResults.year.totalOrders >= periodResults.month.totalOrders) {
        console.log('✅ 时间周期数据一致性正常');
      } else {
        console.log('⚠️  时间周期数据一致性异常');
      }
    }

    // 5. 前端显示预期
    console.log('\n5. 前端显示预期...');
    const monthData = periodResults.month || rawData.data.overview;
    
    if (monthData.totalOrders === 0) {
      console.log('前端应显示: "暂无数据" 警告');
    } else if (monthData.totalOrders < 10) {
      console.log('前端应显示: "数据量较少" 提示');
    } else {
      console.log('前端应显示: 正常数据分析');
    }
    
    if (monthData.rmbOrders === 0 && monthData.lyOrders > 0) {
      console.log('前端应显示: 单一光年币支付卡片');
    } else if (monthData.lyOrders === 0 && monthData.rmbOrders > 0) {
      console.log('前端应显示: 单一人民币支付卡片');
    } else {
      console.log('前端应显示: 双饼图对比');
    }

    console.log('\n🎉 支付偏好分析修复验证通过！');
    
    return {
      success: true,
      message: '支付偏好分析功能修复成功',
      data: {
        monthlyOrders: monthData.totalOrders,
        yearlyOrders: periodResults.year ? periodResults.year.totalOrders : 'N/A',
        paymentMethod: monthData.lyOrders > 0 ? '光年币为主' : '人民币为主',
        displayMode: monthData.totalOrders === 0 ? '暂无数据' : 
                    monthData.totalOrders < 10 ? '数据量少' : '正常显示'
      }
    };

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// 运行测试
testPaymentPreferenceFix().then(result => {
  if (result.success) {
    console.log('\n📋 修复验证总结:');
    Object.entries(result.data).forEach(([key, value]) => {
      console.log(`• ${key}: ${value}`);
    });
    
    console.log('\n✅ 修复成功！前端应该能正常显示支付偏好分析数据了。');
    process.exit(0);
  } else {
    console.error('\n💥 修复验证失败，请检查错误信息');
    process.exit(1);
  }
}).catch(error => {
  console.error('测试执行出错:', error);
  process.exit(1);
});
