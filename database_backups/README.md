# 数据库备份与恢复说明

## 概述

本目录包含了从服务器备份的MySQL数据库文件，以及相关的备份和恢复脚本。

## 目录结构

```
database_backups/
├── README.md                           # 本说明文件
├── feishu_mall_backup_YYYYMMDD_HHMMSS.sql  # 数据库备份文件
└── ...                                 # 其他备份文件

scripts/
├── backup_database.sh                  # 数据库备份脚本
└── restore_database.sh                 # 数据库恢复脚本
```

## 服务器信息

- **服务器IP**: **************
- **数据库名**: feishu_mall
- **数据库用户**: root
- **备份方式**: 通过SSH连接服务器执行mysqldump

## 使用方法

### 1. 备份数据库

```bash
# 执行备份脚本
./scripts/backup_database.sh
```

**功能说明**:
- 自动连接服务器并备份数据库
- 备份文件命名格式: `feishu_mall_backup_YYYYMMDD_HHMMSS.sql`
- 自动创建备份目录
- 显示备份文件大小和行数
- 自动清理旧备份（保留最近10个）

### 2. 恢复数据库

```bash
# 查看可用备份文件
./scripts/restore_database.sh

# 恢复指定备份文件
./scripts/restore_database.sh database_backups/feishu_mall_backup_20250723_170609.sql
```

**功能说明**:
- 支持从任意备份文件恢复
- 恢复前自动创建当前数据库备份
- 提供确认提示，防止误操作
- 显示详细的恢复信息

### 3. 手动操作

#### 手动备份
```bash
# 通过SSH备份
ssh root@************** "mysqldump -u root -ppassword feishu_mall" > backup.sql

# 本地备份（如果MySQL允许远程连接）
mysqldump -h ************** -u root -ppassword feishu_mall > backup.sql
```

#### 手动恢复
```bash
# 恢复到本地数据库
mysql -u root -ppassword feishu_mall < backup.sql

# 恢复到远程数据库（如果允许）
mysql -h ************** -u root -ppassword feishu_mall < backup.sql
```

## 备份文件说明

### 当前备份文件

- `feishu_mall_backup_20250723_174548.sql` - 2025年7月23日 17:45 备份 **[最新]**
  - 文件大小: 1.17MB (1,194,531 bytes)
  - 行数: 1045行
  - 包含完整的数据库结构和数据
  - **备份状态**: 删除指定用户订单数据后的最新状态
  - **表数量**: 27个表
  - **数据插入语句**: 25个表包含数据

- `feishu_mall_backup_20250723_170609.sql` - 2025年7月23日 17:06 备份
  - 文件大小: ~1.2MB
  - 行数: 1037行
  - 包含完整的数据库结构和数据
  - **备份状态**: 删除用户订单数据前的状态

### 备份内容

备份文件包含以下内容:
- 数据库结构（表结构、索引、约束等）
- 所有表数据
- 存储过程和函数（如果有）
- 触发器（如果有）
- 视图（如果有）

## 注意事项

### 安全提醒
1. **备份文件包含敏感数据**，请妥善保管
2. **不要将备份文件提交到版本控制系统**
3. **定期清理旧备份文件**，避免占用过多磁盘空间

### 网络要求
1. 备份需要能够SSH连接到服务器
2. 确保服务器SSH服务正常运行
3. 确保本地网络能够访问服务器

### 数据库要求
1. 恢复时需要本地MySQL服务运行
2. 确保目标数据库存在
3. 确保有足够的权限执行恢复操作

## 故障排除

### 常见问题

1. **SSH连接失败**
   - 检查服务器IP和端口
   - 确认SSH密钥或密码正确
   - 检查网络连接

2. **备份文件为空**
   - 检查服务器MySQL服务状态
   - 确认数据库用户权限
   - 检查磁盘空间

3. **恢复失败**
   - 检查本地MySQL服务状态
   - 确认目标数据库存在
   - 检查备份文件完整性

### 联系信息

如遇到问题，请检查:
1. 服务器连接状态
2. MySQL服务状态
3. 权限配置
4. 网络连接

## 更新日志

- 2025-07-23 17:45: 创建删除用户订单数据后的完整备份
  - 备份文件: feishu_mall_backup_20250723_174548.sql
  - 使用完整的mysqldump参数，包含所有表结构、数据、索引、约束
  - 删除了5个指定用户的所有订单相关数据后的最新状态
- 2025-07-23 17:06: 创建初始备份和脚本
  - 备份文件: feishu_mall_backup_20250723_170609.sql
  - 删除用户订单数据前的原始状态
