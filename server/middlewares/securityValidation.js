/**
 * 安全验证中间件
 * 提供统一的输入验证和安全检查功能
 */

const crypto = require('crypto');
const path = require('path');

// 安全配置常量
const SECURITY_CONFIG = {
  // 文件上传安全配置
  fileUpload: {
    allowedMimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
    maxFileSize: 5 * 1024 * 1024, // 5MB
    maxFilenameLength: 255
  },
  
  // 输入验证规则
  validation: {
    username: {
      minLength: 2,
      maxLength: 30,
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_]+$/
    },
    email: {
      maxLength: 254,
      pattern: /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,
      allowedDomains: ['@guanghe.tv']
    },
    password: {
      minLength: 8,
      maxLength: 128,
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
    },
    searchTerm: {
      maxLength: 100
    },
    mobile: {
      pattern: /^1[3-9]\d{9}$/
    }
  }
};

/**
 * 验证用户名
 */
const validateUsername = (username) => {
  if (!username || typeof username !== 'string') {
    return { valid: false, message: '用户名不能为空' };
  }
  
  const trimmed = username.trim();
  const rules = SECURITY_CONFIG.validation.username;
  
  if (trimmed.length < rules.minLength || trimmed.length > rules.maxLength) {
    return { valid: false, message: `用户名长度必须在${rules.minLength}-${rules.maxLength}个字符之间` };
  }
  
  if (!rules.pattern.test(trimmed)) {
    return { valid: false, message: '用户名只能包含中文、英文、数字和下划线' };
  }
  
  return { valid: true, value: trimmed };
};

/**
 * 验证邮箱
 */
const validateEmail = (email) => {
  if (!email || typeof email !== 'string') {
    return { valid: false, message: '邮箱不能为空' };
  }
  
  const trimmed = email.trim().toLowerCase();
  const rules = SECURITY_CONFIG.validation.email;
  
  if (trimmed.length > rules.maxLength) {
    return { valid: false, message: '邮箱地址过长' };
  }
  
  if (!rules.pattern.test(trimmed)) {
    return { valid: false, message: '邮箱格式不正确' };
  }
  
  const hasAllowedDomain = rules.allowedDomains.some(domain => trimmed.endsWith(domain));
  if (!hasAllowedDomain) {
    return { valid: false, message: '邮箱域名不被允许' };
  }
  
  return { valid: true, value: trimmed };
};

/**
 * 验证密码强度
 */
const validatePassword = (password) => {
  if (!password || typeof password !== 'string') {
    return { valid: false, message: '密码不能为空' };
  }
  
  const rules = SECURITY_CONFIG.validation.password;
  
  if (password.length < rules.minLength || password.length > rules.maxLength) {
    return { valid: false, message: `密码长度必须在${rules.minLength}-${rules.maxLength}个字符之间` };
  }
  
  if (!rules.pattern.test(password)) {
    return { valid: false, message: '密码必须包含至少一个大写字母、一个小写字母、一个数字和一个特殊字符' };
  }
  
  return { valid: true, value: password };
};

/**
 * 验证搜索关键词
 */
const validateSearchTerm = (searchTerm) => {
  if (!searchTerm) {
    return { valid: true, value: '' };
  }
  
  const rules = SECURITY_CONFIG.validation.searchTerm;
  const trimmed = searchTerm.toString().trim();
  
  if (trimmed.length > rules.maxLength) {
    return { valid: false, message: '搜索关键词过长' };
  }
  
  // 清理危险字符，防止SQL注入
  const sanitized = trimmed.replace(/[%_\\]/g, '\\$&');
  
  return { valid: true, value: sanitized };
};

/**
 * 验证手机号
 */
const validateMobile = (mobile) => {
  if (!mobile) {
    return { valid: true, value: null };
  }
  
  const trimmed = mobile.toString().trim();
  const rules = SECURITY_CONFIG.validation.mobile;
  
  if (!rules.pattern.test(trimmed)) {
    return { valid: false, message: '手机号格式不正确' };
  }
  
  return { valid: true, value: trimmed };
};

/**
 * 验证文件上传安全性
 */
const validateFileUpload = (file) => {
  const config = SECURITY_CONFIG.fileUpload;
  
  // 检查文件大小
  if (file.size > config.maxFileSize) {
    return { valid: false, message: `文件大小不能超过${config.maxFileSize / (1024 * 1024)}MB` };
  }
  
  // 检查文件类型
  if (!config.allowedMimeTypes.includes(file.mimetype)) {
    return { valid: false, message: '只允许上传 JPEG、PNG、GIF、WebP 格式的图片' };
  }
  
  // 检查文件扩展名
  const extension = path.extname(file.name).toLowerCase();
  if (!config.allowedExtensions.includes(extension)) {
    return { valid: false, message: '文件扩展名不符合要求' };
  }
  
  // 检查文件名长度
  if (file.name.length > config.maxFilenameLength) {
    return { valid: false, message: '文件名过长' };
  }
  
  return { valid: true };
};

/**
 * 生成安全的文件名
 */
const generateSecureFilename = (originalFilename) => {
  const extension = path.extname(originalFilename).toLowerCase();
  const randomName = crypto.randomBytes(16).toString('hex');
  return `${randomName}${extension}`;
};

/**
 * 验证文件路径安全性（防止路径遍历攻击）
 */
const validateFilePath = (filePath, allowedDirectory) => {
  const normalizedPath = path.normalize(filePath);
  const normalizedAllowedDir = path.normalize(allowedDirectory);
  
  return normalizedPath.startsWith(normalizedAllowedDir);
};

/**
 * 清理HTML内容，防止XSS攻击
 */
const sanitizeHtml = (input) => {
  if (!input || typeof input !== 'string') {
    return input;
  }
  
  return input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
};

/**
 * 验证数字范围
 */
const validateNumberRange = (value, min, max, fieldName) => {
  const num = parseInt(value, 10);
  
  if (isNaN(num)) {
    return { valid: false, message: `${fieldName}必须是数字` };
  }
  
  if (num < min || num > max) {
    return { valid: false, message: `${fieldName}必须在${min}-${max}之间` };
  }
  
  return { valid: true, value: num };
};

/**
 * 用户注册验证中间件
 */
const validateUserRegistration = (req, res, next) => {
  const { username, email, password, mobile } = req.body;
  
  // 验证用户名
  const usernameResult = validateUsername(username);
  if (!usernameResult.valid) {
    return res.status(400).json({ message: usernameResult.message });
  }
  req.body.username = usernameResult.value;
  
  // 验证邮箱
  const emailResult = validateEmail(email);
  if (!emailResult.valid) {
    return res.status(400).json({ message: emailResult.message });
  }
  req.body.email = emailResult.value;
  
  // 验证密码
  const passwordResult = validatePassword(password);
  if (!passwordResult.valid) {
    return res.status(400).json({ message: passwordResult.message });
  }
  
  // 验证手机号（可选）
  if (mobile) {
    const mobileResult = validateMobile(mobile);
    if (!mobileResult.valid) {
      return res.status(400).json({ message: mobileResult.message });
    }
    req.body.mobile = mobileResult.value;
  }
  
  next();
};

/**
 * 商品搜索验证中间件
 */
const validateProductSearch = (req, res, next) => {
  if (req.query.search) {
    const searchResult = validateSearchTerm(req.query.search);
    if (!searchResult.valid) {
      return res.status(400).json({ message: searchResult.message });
    }
    req.query.search = searchResult.value;
  }
  
  // 验证分类筛选
  if (req.query.categories) {
    const categoryIds = req.query.categories.split(',')
      .map(id => parseInt(id))
      .filter(id => !isNaN(id) && id > 0);
    
    if (categoryIds.length > 50) {
      return res.status(400).json({ message: '分类筛选数量过多' });
    }
    
    req.query.categoryIds = categoryIds;
  }
  
  next();
};

module.exports = {
  validateUsername,
  validateEmail,
  validatePassword,
  validateSearchTerm,
  validateMobile,
  validateFileUpload,
  generateSecureFilename,
  validateFilePath,
  sanitizeHtml,
  validateNumberRange,
  validateUserRegistration,
  validateProductSearch,
  SECURITY_CONFIG
};
