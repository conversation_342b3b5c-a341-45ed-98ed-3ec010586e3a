import request from './index'

/**
 * 库存管理API接口
 * 提供商品职场库存管理的前端API调用
 */

/**
 * 获取指定商品的职场库存分布
 * @param {number} productId - 商品ID
 * @returns {Promise} API响应
 */
export const getProductWorkplaceStocks = (productId) => {
  return request({
    url: `/products/${productId}/workplace-stocks`,
    method: 'GET'
  })
}

/**
 * 批量更新商品在各职场的库存
 * @param {number} productId - 商品ID
 * @param {Object} data - 更新数据
 * @param {Array} data.stockUpdates - 库存更新数组
 * @param {string} data.reason - 操作原因
 * @returns {Promise} API响应
 */
export const updateProductWorkplaceStocks = (productId, data) => {
  return request({
    url: `/products/${productId}/workplace-stocks`,
    method: 'PUT',
    data
  })
}

/**
 * 职场间库存转移
 * @param {Object} data - 转移数据
 * @param {number} data.productId - 商品ID
 * @param {number} data.fromWorkplaceId - 源职场ID
 * @param {number} data.toWorkplaceId - 目标职场ID
 * @param {number} data.quantity - 转移数量
 * @param {string} data.reason - 转移原因
 * @returns {Promise} API响应
 */
export const transferStock = (data) => {
  console.log('🔧 transferStock函数被调用，参数:', data)
  console.log('🔧 request函数类型:', typeof request)
  console.log('🔧 request函数:', request)

  const promise = request({
    url: '/stocks/transfer',
    method: 'POST',
    data
  })

  console.log('🔧 request返回的Promise:', promise)
  console.log('🔧 Promise类型:', typeof promise)
  console.log('🔧 是否为Promise:', promise instanceof Promise)

  return promise
}

/**
 * 获取职场库存统计
 * @param {number} workplaceId - 职场ID
 * @returns {Promise} API响应
 */
export const getWorkplaceStockSummary = (workplaceId) => {
  return request({
    url: `/workplaces/${workplaceId}/stock-summary`,
    method: 'GET'
  })
}

/**
 * 获取库存操作日志
 * @param {Object} params - 查询参数
 * @param {number} params.productId - 商品ID（可选）
 * @param {number} params.workplaceId - 职场ID（可选）
 * @param {string} params.operationType - 操作类型（可选）
 * @param {string} params.startDate - 开始日期（可选）
 * @param {string} params.endDate - 结束日期（可选）
 * @param {number} params.page - 页码（默认1）
 * @param {number} params.limit - 每页数量（默认20）
 * @returns {Promise} API响应
 */
export const getStockOperationLogs = (params = {}) => {
  return request({
    url: '/stocks/operation-logs',
    method: 'GET',
    params
  })
}

/**
 * 保存操作记录
 * @param {Object} operationData - 操作记录数据
 * @returns {Promise} API响应
 */
export const saveOperationRecord = (operationData) => {
  return request({
    url: '/stocks/operation-records',
    method: 'POST',
    data: operationData
  })
}

/**
 * 获取所有职场列表（用于下拉选择）
 * @returns {Promise} API响应
 */
export const getWorkplaces = () => {
  return request({
    url: '/workplaces',
    method: 'GET'
  })
}

/**
 * 获取商品库存趋势数据
 * @param {Object} params - 查询参数
 * @param {number} params.productId - 商品ID
 * @param {string} params.startDate - 开始日期 (YYYY-MM-DD)
 * @param {string} params.endDate - 结束日期 (YYYY-MM-DD)
 * @param {number} params.workplaceId - 职场ID（可选，不传则获取所有职场）
 * @returns {Promise} API响应
 */
export const getProductStockTrend = (params) => {
  return request({
    url: '/stocks/product-trend',
    method: 'GET',
    params
  })
}

/**
 * 获取商品列表（包含职场库存信息）
 * @param {Object} params - 查询参数
 * @param {boolean} params.includeWorkplaceStocks - 是否包含职场库存信息
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.search - 搜索关键词
 * @param {number} params.categoryId - 分类ID
 * @returns {Promise} API响应
 */
export const getProductsWithStocks = (params = {}) => {
  return request({
    url: '/products',
    method: 'GET',
    params: {
      ...params,
      includeWorkplaceStocks: true
    }
  })
}

/**
 * 获取库存统计仪表板数据
 * @returns {Promise} API响应
 */
export const getStockDashboardData = async () => {
  try {
    // 获取所有职场的库存统计
    const workplacesResponse = await getWorkplaces()
    const workplaces = workplacesResponse.data || []
    
    const summaryPromises = workplaces
      .filter(workplace => workplace.isActive)
      .map(workplace => getWorkplaceStockSummary(workplace.id))
    
    const summaries = await Promise.all(summaryPromises)
    
    return {
      success: true,
      data: {
        workplaces: workplaces.filter(w => w.isActive),
        summaries: summaries.map(s => s.data)
      }
    }
  } catch (error) {
    console.error('获取库存仪表板数据失败:', error)
    throw error
  }
}

/**
 * 批量获取多个商品的职场库存信息
 * @param {Array} productIds - 商品ID数组
 * @returns {Promise} API响应
 */
export const getBatchProductWorkplaceStocks = async (productIds) => {
  try {
    const promises = productIds.map(id => getProductWorkplaceStocks(id))
    const results = await Promise.all(promises)
    
    return {
      success: true,
      data: results.map(r => r.data)
    }
  } catch (error) {
    console.error('批量获取商品职场库存失败:', error)
    throw error
  }
}

/**
 * 获取库存告警信息
 * @returns {Promise} API响应
 */
export const getStockAlerts = async () => {
  try {
    // 获取所有商品的职场库存信息
    const productsResponse = await getProductsWithStocks({ limit: 1000 })
    const products = productsResponse.data || []
    
    const alerts = []
    
    products.forEach(product => {
      if (product.workplaceStocks) {
        product.workplaceStocks.forEach(stock => {
          if (stock.isLowStock) {
            alerts.push({
              productId: product.id,
              productName: product.name,
              workplaceId: stock.workplaceId,
              workplaceName: stock.workplaceName,
              currentStock: stock.availableStock,
              minStockAlert: stock.minStockAlert,
              alertLevel: stock.availableStock === 0 ? 'critical' : 'warning'
            })
          }
        })
      }
    })
    
    return {
      success: true,
      data: alerts
    }
  } catch (error) {
    console.error('获取库存告警信息失败:', error)
    throw error
  }
}
