# Light Year Store - E-commerce Platform

## Project Description
Light Year Store is a B2C e-commerce platform built with Vue 3 and Express, supporting product management, content publishing, user management, and data analysis. The project aims to provide a complete e-commerce solution for businesses.

## Technology Stack
- **Frontend**: Vue 3.3.4, Element Plus 2.3.8, Pinia 3.0.2, Vue Router 4.5.0
- **Backend**: Express, Sequelize, MySQL/SQLite
- **Build Tool**: Vite 4.4.0
- **Others**: ECharts 5, WangEditor 5.1.23, Axios 1.8.4

## Development Environment Requirements
- Node.js >= 14.0.0
- npm >= 6.0.0
- MySQL or SQLite (development)

## Project Structure
```
project-root/
├── src/                   # Frontend source code
│   ├── api/               # API request encapsulation
│   ├── components/        # Common components
│   ├── router/            # Router configuration
│   ├── stores/            # State management
│   ├── styles/            # Global styles
│   ├── utils/             # Utility functions
│   ├── views/             # Page views
│   ├── App.vue            # Root component
│   └── main.js            # Application entry
│
├── server/                # Backend source code  
│   ├── config/            # Configuration files
│   ├── controllers/       # Business controllers
│   ├── middlewares/       # Middleware
│   ├── models/            # Data models
│   ├── routes/            # Route definitions
│   └── server.js          # Server entry
```

## Quick Start

### Install Dependencies
```bash
npm install
```

### Run Development Mode
```bash
# Start frontend development server
npm run dev

# Start backend server (in another terminal)
cd server && node server.js
```

### Build Production Version
```bash
npm run build
```

## Deployment Guide

### Production Environment Deployment
```bash
# Build frontend
npm run build

# Deploy frontend (Nginx)
Deploy the dist directory to Nginx server

# Start backend (using PM2)
cd server
npm install -g pm2
pm2 start server.js
```

### Baota Panel Deployment

#### Frontend Deployment
1. Build the project locally: `npm run build`
2. Login to Baota Panel, create a site
3. Upload all files from the `dist` directory to the site root
4. Configure rewrite rules (for Vue routing):
```
location / {
  try_files $uri $uri/ /index.html;
}
```
5. Enable SSL (if HTTPS is needed)
6. Enable Gzip compression for better performance

#### Backend Deployment
1. Upload server code to the server
2. Install Node.js environment (via Baota Panel software management)
3. Install project dependencies: `npm install`
4. Use PM2 to manage Node application (via Baota PM2 manager plugin)
5. Configure reverse proxy to forward API requests to Node service

## Main Features

### Completed Features
- User authentication and permission management
- Product management system (CRUD, categories, images)
- Content management system (announcements, rich text)
- User feedback system
- Data statistics and visualization

### Features in Progress
- Rich text editor optimization
- Data dashboard improvements
- Database optimization

### Planned Features
- Order system
- Payment system integration
- User center enhancement
- Performance and security optimization

## User Guide

### User Authentication
- Support for user registration and login
- JWT authentication system
- Permission control middleware

### Product Management
- Product creation, editing, deletion, and querying
- Product category management
- Product image upload
- Bulk product import and export

### Content Management
- Announcement publishing and management
- Rich text editing and display
- Image upload service

## Maintainer

If you have any questions, please contact the project maintainer.

## Version History

- 1.0.0: Basic features completed, including user, product, content, and feedback systems

*Documentation updated: July 2024*
