<template>
  <div class="notification-settings">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <el-icon class="title-icon"><ChatDotRound /></el-icon>
            通知管理
          </h1>
          <p class="page-description">配置和管理飞书群机器人通知功能</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 连接状态卡片 -->
    <div class="status-cards">
      <el-card class="status-card" :class="{ 'connected': webhookConfigured, 'disconnected': !webhookConfigured }">
        <div class="status-content">
          <div class="status-icon">
            <el-icon v-if="webhookConfigured" class="success-icon"><SuccessFilled /></el-icon>
            <el-icon v-else class="error-icon"><CircleCloseFilled /></el-icon>
          </div>
          <div class="status-info">
            <h3 class="status-title">
              {{ webhookConfigured ? '飞书机器人已连接' : '飞书机器人未连接' }}
            </h3>
            <p class="status-desc">
              {{ webhookConfigured ? '通知功能正常运行' : '请配置Webhook地址' }}
            </p>
          </div>
          <div class="status-actions">
            <el-button
              @click="testWebhookConnection"
              :loading="testingConnection"
              :type="webhookConfigured ? 'success' : 'primary'"
              size="small"
            >
              <el-icon><Link /></el-icon>
              {{ webhookConfigured ? '重新测试' : '测试连接' }}
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 批量操作工具栏 -->
    <div class="batch-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button
            @click="batchEnableNotifications"
            :loading="batchOperating"
            :disabled="!webhookConfigured"
            type="success"
            size="small"
          >
            <el-icon><Check /></el-icon>
            全部启用
          </el-button>
          <el-button
            @click="batchDisableNotifications"
            :loading="batchOperating"
            type="warning"
            size="small"
          >
            <el-icon><Close /></el-icon>
            全部禁用
          </el-button>
        </el-button-group>
      </div>
      <div class="toolbar-right">
        <el-button @click="showWebhookDialog = true" type="primary" size="small">
          <el-icon><Setting /></el-icon>
          配置Webhook
        </el-button>
      </div>
    </div>

    <!-- 通知配置网格 -->
    <div class="notification-grid" v-loading="configsLoading">
      <!-- 业务通知卡片 -->
      <div class="config-section">
        <div class="section-header">
          <h3 class="section-title">
            <el-icon class="section-icon"><Briefcase /></el-icon>
            业务通知
          </h3>
          <span class="section-count">{{ businessConfigs.length }} 项</span>
        </div>
        <div class="config-cards">
          <div
            v-for="config in businessConfigs"
            :key="config.notificationType"
            class="config-card"
            :class="{ 'enabled': config.enabled, 'disabled': !config.enabled }"
          >
            <div class="card-header">
              <div class="card-title">{{ config.typeName }}</div>
              <el-switch
                v-model="config.enabled"
                @change="updateNotificationConfig(config)"
                :disabled="!webhookConfigured || updatingConfigs.includes(config.notificationType)"
                :loading="updatingConfigs.includes(config.notificationType)"
                size="large"
              />
            </div>
            <div class="card-content">
              <p class="card-description">{{ getConfigDescription(config.notificationType) }}</p>
              <div v-if="config.scheduleTime" class="schedule-info">
                <el-tag type="info" size="small">
                  <el-icon><Clock /></el-icon>
                  {{ config.scheduleTime }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Webhook配置对话框 -->
    <el-dialog
      v-model="showWebhookDialog"
      title="配置飞书Webhook"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="webhookForm" label-width="120px">
        <el-form-item label="Webhook URL">
          <el-input
            v-model="webhookForm.url"
            placeholder="请输入飞书群机器人Webhook地址"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showWebhookDialog = false">取消</el-button>
        <el-button type="primary" @click="saveWebhookConfig" :loading="savingWebhook">
          保存配置
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  ChatDotRound, Refresh, SuccessFilled, CircleCloseFilled, Link,
  Check, Close, Setting, Briefcase, Clock
} from '@element-plus/icons-vue';

// 响应式数据
const loading = ref(false);
const webhookConfigured = ref(false);
const testingConnection = ref(false);
const batchOperating = ref(false);
const configsLoading = ref(false);
const showWebhookDialog = ref(false);
const savingWebhook = ref(false);
const updatingConfigs = ref([]);

const businessConfigs = ref([]);
const webhookForm = reactive({
  url: ''
});

// 计算属性
const getConfigDescription = (type) => {
  const descriptions = {
    'order_created': '用户下单时发送通知',
    'order_completed': '订单完成时发送通知',
    'stock_low': '库存不足时发送警告',
    'user_registered': '新用户注册时发送通知'
  };
  return descriptions[type] || '暂无描述';
};

// 方法
const refreshData = async () => {
  loading.value = true;
  try {
    await loadNotificationConfigs();
    ElMessage.success('数据刷新成功');
  } catch (error) {
    ElMessage.error('数据刷新失败');
  } finally {
    loading.value = false;
  }
};

const loadNotificationConfigs = async () => {
  configsLoading.value = true;
  try {
    // 模拟API调用
    businessConfigs.value = [
      { notificationType: 'order_created', typeName: '订单创建', enabled: true },
      { notificationType: 'order_completed', typeName: '订单完成', enabled: false },
      { notificationType: 'stock_low', typeName: '库存警告', enabled: true },
      { notificationType: 'user_registered', typeName: '用户注册', enabled: false }
    ];
  } finally {
    configsLoading.value = false;
  }
};

const testWebhookConnection = async () => {
  testingConnection.value = true;
  try {
    // 模拟测试连接
    await new Promise(resolve => setTimeout(resolve, 1000));
    webhookConfigured.value = true;
    ElMessage.success('连接测试成功');
  } catch (error) {
    ElMessage.error('连接测试失败');
  } finally {
    testingConnection.value = false;
  }
};

const updateNotificationConfig = async (config) => {
  updatingConfigs.value.push(config.notificationType);
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    ElMessage.success(`${config.typeName}配置已更新`);
  } catch (error) {
    ElMessage.error('配置更新失败');
    config.enabled = !config.enabled; // 回滚状态
  } finally {
    updatingConfigs.value = updatingConfigs.value.filter(type => type !== config.notificationType);
  }
};

const batchEnableNotifications = async () => {
  batchOperating.value = true;
  try {
    businessConfigs.value.forEach(config => config.enabled = true);
    ElMessage.success('已启用所有通知');
  } finally {
    batchOperating.value = false;
  }
};

const batchDisableNotifications = async () => {
  batchOperating.value = true;
  try {
    businessConfigs.value.forEach(config => config.enabled = false);
    ElMessage.success('已禁用所有通知');
  } finally {
    batchOperating.value = false;
  }
};

const saveWebhookConfig = async () => {
  savingWebhook.value = true;
  try {
    // 模拟保存配置
    await new Promise(resolve => setTimeout(resolve, 1000));
    webhookConfigured.value = true;
    showWebhookDialog.value = false;
    ElMessage.success('Webhook配置保存成功');
  } catch (error) {
    ElMessage.error('配置保存失败');
  } finally {
    savingWebhook.value = false;
  }
};

// 生命周期
onMounted(() => {
  loadNotificationConfigs();
});
</script>

<style scoped>
.notification-settings {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  color: #3b82f6;
}

.page-description {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

.status-cards {
  margin-bottom: 24px;
}

.status-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.status-card.connected {
  border-left: 4px solid #10b981;
}

.status-card.disconnected {
  border-left: 4px solid #ef4444;
}

.status-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-icon .success-icon {
  color: #10b981;
  font-size: 24px;
}

.status-icon .error-icon {
  color: #ef4444;
  font-size: 24px;
}

.status-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.status-desc {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.batch-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 16px 24px;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.notification-grid {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.section-icon {
  color: #3b82f6;
}

.section-count {
  background: #f3f4f6;
  color: #6b7280;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
}

.config-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.config-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s;
}

.config-card.enabled {
  border-color: #10b981;
  background: #f0fdf4;
}

.config-card.disabled {
  border-color: #e5e7eb;
  background: #f9fafb;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-title {
  font-weight: 600;
  color: #1f2937;
}

.card-description {
  color: #6b7280;
  font-size: 14px;
  margin: 0 0 8px 0;
}

.schedule-info {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
