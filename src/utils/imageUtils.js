/**
 * 图片URL处理工具函数
 * 用于处理不同环境下的图片URL，确保正确显示
 */

import { detectEnvironment, getEnvironmentBaseUrl, fixUrlForEnvironment } from './environmentDetector';

// 获取当前API基础URL
const getBaseUrl = () => {
  const env = detectEnvironment();
  // 强制返回正确的本地开发环境URL
  if (env.isLocalhost || env.isDevelopment) {
    return import.meta.env.VITE_DEV_SERVER_URL || 'http://localhost:3000';
  }
  return getEnvironmentBaseUrl();
};

/**
 * 获取默认商品图片URL
 * @param {string} size - 图片尺寸，如 '300x300'
 * @returns {string} - 默认图片URL
 */
export const getDefaultProductImage = (size = '300x300') => {
  // 优先使用本地SVG文件，它更可靠
  return '/default-product.svg';
};

/**
 * 修复图片URL，确保能正确显示
 * 处理本地环境和生产环境的URL差异
 *
 * @param {string} url - 原始图片URL
 * @returns {string} - 修复后的URL
 */
export const fixImageUrl = (url) => {
  if (!url || url.trim() === '') {
    return getDefaultProductImage();
  }

  // 检测并替换不可访问的外部占位图服务
  if (url.includes('via.placeholder.com')) {
    console.log('检测到via.placeholder.com URL，替换为本地占位图:', url);
    return getDefaultProductImage();
  }

  // 处理后端设置的占位图路径
  if (url.includes('/images/placeholder/')) {
    console.log('检测到后端占位图路径，使用本地占位图:', url);
    return getDefaultProductImage();
  }

  console.log('fixImageUrl开始处理:', url);
  const env = detectEnvironment();
  const baseUrl = getBaseUrl();
  console.log('当前基础URL:', baseUrl);
  
  // 专门处理支付收款码图片路径
  if (url.includes('/uploads/payment/')) {
    console.log('检测到支付收款码URL路径');

    // 确保使用正确的环境域名
    const oldServerIp = import.meta.env.VITE_OLD_SERVER_IP || '**************';
    const prodServerUrl = import.meta.env.VITE_PROD_SERVER_URL || 'https://store-api.chongyangqisi.com';
    const devServerUrl = import.meta.env.VITE_DEV_SERVER_URL || 'http://localhost:3000';

    if (env.isLocalhost && (url.includes(oldServerIp) || url.includes(prodServerUrl.replace('https://', '').replace('http://', '')))) {
      console.log('本地环境但使用了生产域名，修正支付码URL');
      // 从URL中提取路径部分
      const urlPath = url.replace(/^https?:\/\/[^\/]+/g, '');
      // 构建新的本地URL
      const localUrl = `${devServerUrl}${urlPath}`;
      console.log('修正后的支付码URL:', localUrl);
      return localUrl;
    }

    // 生产环境：将旧IP地址替换为新域名
    if (!env.isLocalhost && url.includes(oldServerIp)) {
      console.log('生产环境检测到旧IP地址，修正支付码URL');
      const fixedUrl = url.replace(new RegExp(`https?:\\/\\/${oldServerIp.replace('.', '\\.')}(?::\\d+)?`, 'g'), prodServerUrl);
      console.log('修正后的支付码URL:', fixedUrl);
      return fixedUrl;
    }
  }
  
  // 首先处理旧IP地址的情况（生产环境）
  const oldServerIp = import.meta.env.VITE_OLD_SERVER_IP || '**************';
  const prodServerUrl = import.meta.env.VITE_PROD_SERVER_URL || 'https://store-api.chongyangqisi.com';

  if (!env.isLocalhost && url.includes(oldServerIp)) {
    console.log('生产环境检测到旧IP地址，修正为新域名');
    const fixedUrl = url.replace(new RegExp(`https?:\\/\\/${oldServerIp.replace('.', '\\.')}(?::\\d+)?`, 'g'), prodServerUrl);
    console.log('修正后的URL:', fixedUrl);
    return fixedUrl;
  }

  // 然后尝试使用环境检测工具修正URL
  const envFixedUrl = fixUrlForEnvironment(url);
  if (envFixedUrl !== url) {
    console.log('环境检测工具修正URL:', url, '->', envFixedUrl);
    return envFixedUrl;
  }
  
  // 已经是完整的URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    console.log('URL已是完整URL，无需修改:', url);
    return url;
  }
  
  // 处理native-resource格式
  if (url.startsWith('native-resource://')) {
    const fixedUrl = url.replace('native-resource://', '/');
    console.log('处理native-resource格式:', url, '->', fixedUrl);
    return fixedUrl;
  }
  
  // 处理以localhost:3000开头的URL，替换为当前环境的baseUrl
  const devServerUrl = import.meta.env.VITE_DEV_SERVER_URL || 'http://localhost:3000';
  if (url.includes(devServerUrl.replace('http://', '').replace('https://', ''))) {
    const fixedUrl = url.replace(new RegExp(devServerUrl.replace('http://', 'http://').replace('https://', 'https://'), 'g'), baseUrl);
    console.log('处理localhost URL:', url, '->', fixedUrl);
    return fixedUrl;
  }
  
  // 处理支付码图片地址
  if (url.includes('/uploads/payment/')) {
    // 确保使用正确的基础URL
    const fixedUrl = `${baseUrl}${url.startsWith('/') ? '' : '/'}${url.replace(/^(https?:\/\/[^\/]+)?/, '')}`;
    console.log('处理支付码图片路径:', url, '->', fixedUrl);
    return fixedUrl;
  }
  
  // 处理相对路径，添加baseUrl
  if (url.startsWith('/')) {
    const fixedUrl = `${baseUrl}${url}`;
    console.log('处理绝对路径:', url, '->', fixedUrl);
    return fixedUrl;
  }
  
  // 其他情况，添加/前缀
  const fixedUrl = `/${url}`;
  console.log('处理其他情况:', url, '->', fixedUrl);
  return fixedUrl;
};

/**
 * 处理HTML内容中的图片URL
 * 
 * @param {string} html - HTML内容
 * @returns {string} - 处理后的HTML
 */
export const processHtmlImages = (html) => {
  if (!html) return '';
  
  try {
    // 替换所有img标签中的src属性
    return html.replace(/<img[^>]+src="([^"]+)"[^>]*>/g, (match, src) => {
      const fixedSrc = fixImageUrl(src);
      return match.replace(src, fixedSrc);
    });
  } catch (error) {
    console.error('处理HTML内容中的图片错误:', error);
    return html;
  }
};

export default {
  fixImageUrl,
  processHtmlImages,
  getDefaultProductImage
};