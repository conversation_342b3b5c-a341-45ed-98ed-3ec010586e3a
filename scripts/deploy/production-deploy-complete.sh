#!/bin/bash

# 光年小卖部 - 生产环境一键部署脚本
# 服务器: **************
# 部署目录: /www/wwwroot/workyy
# 使用方法: ./production-deploy-complete.sh

# 设置颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# 项目配置
PROJECT_DIR="/www/wwwroot/workyy"
SERVER_DIR="$PROJECT_DIR/server"
LOGS_DIR="$PROJECT_DIR/logs"
BACKUP_DIR="$PROJECT_DIR/backups"
PM2_APP_NAME="feishu-mall-api"

# 数据库配置
DB_HOST="localhost"
DB_PORT=3306
DB_USER="root"
DB_PASSWORD="password"
DB_NAME="feishu_mall"

# 服务器配置
SERVER_IP="**************"
BACKEND_PORT=3000
FRONTEND_PORT=80

echo -e "${YELLOW}===== 光年小卖部 - 生产环境一键部署 =====${NC}"
echo -e "${BLUE}服务器: $SERVER_IP${NC}"
echo -e "${BLUE}部署目录: $PROJECT_DIR${NC}"
echo -e "${BLUE}开始时间: $(date)${NC}"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}错误: 请使用root用户执行此脚本${NC}"
    exit 1
fi

# 函数：执行命令并检查结果
execute_command() {
    local cmd="$1"
    local description="$2"
    
    echo -e "${BLUE}正在执行: $description${NC}"
    if eval "$cmd"; then
        echo -e "${GREEN}✓ $description 成功${NC}"
    else
        echo -e "${RED}✗ $description 失败${NC}"
        exit 1
    fi
}

# 函数：创建必要目录
create_directories() {
    echo -e "${BLUE}===== 创建必要目录 =====${NC}"
    
    execute_command "mkdir -p $PROJECT_DIR" "创建项目目录"
    execute_command "mkdir -p $LOGS_DIR" "创建日志目录"
    execute_command "mkdir -p $BACKUP_DIR" "创建备份目录"
    execute_command "mkdir -p $SERVER_DIR/uploads" "创建上传目录"
}

# 函数：安装系统依赖
install_system_dependencies() {
    echo -e "${BLUE}===== 安装系统依赖 =====${NC}"
    
    execute_command "yum update -y" "更新系统包"
    execute_command "yum install -y wget curl git vim unzip" "安装基础工具"
    
    # 检查Node.js是否已安装
    if ! command -v node &> /dev/null; then
        echo -e "${YELLOW}安装Node.js...${NC}"
        execute_command "curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -" "添加Node.js源"
        execute_command "yum install -y nodejs" "安装Node.js"
    else
        echo -e "${GREEN}✓ Node.js已安装: $(node --version)${NC}"
    fi
    
    # 检查PM2是否已安装
    if ! command -v pm2 &> /dev/null; then
        echo -e "${YELLOW}安装PM2...${NC}"
        execute_command "npm install -g pm2" "安装PM2"
    else
        echo -e "${GREEN}✓ PM2已安装: $(pm2 --version)${NC}"
    fi
    
    # 检查Nginx是否已安装
    if ! command -v nginx &> /dev/null; then
        echo -e "${YELLOW}安装Nginx...${NC}"
        execute_command "yum install -y nginx" "安装Nginx"
        execute_command "systemctl enable nginx" "设置Nginx开机自启"
    else
        echo -e "${GREEN}✓ Nginx已安装${NC}"
    fi
    
    # 检查MySQL是否已安装
    if ! command -v mysql &> /dev/null; then
        echo -e "${YELLOW}安装MySQL...${NC}"
        execute_command "yum install -y mysql-server" "安装MySQL"
        execute_command "systemctl start mysqld" "启动MySQL"
        execute_command "systemctl enable mysqld" "设置MySQL开机自启"
        echo -e "${YELLOW}请手动运行 mysql_secure_installation 配置MySQL安全设置${NC}"
    else
        echo -e "${GREEN}✓ MySQL已安装${NC}"
    fi
}

# 函数：克隆或更新项目代码
deploy_project_code() {
    echo -e "${BLUE}===== 部署项目代码 =====${NC}"
    
    cd $PROJECT_DIR
    
    if [ -d ".git" ]; then
        echo -e "${YELLOW}项目已存在，更新代码...${NC}"
        execute_command "git fetch origin" "获取远程更新"
        execute_command "git checkout feat/reset" "切换到feat/reset分支"
        execute_command "git pull origin feat/reset" "拉取最新代码"
    else
        echo -e "${YELLOW}首次部署，克隆项目...${NC}"
        execute_command "git clone -b feat/reset https://gitee.com/your-username/workyy.git ." "克隆项目代码"
    fi
}

# 函数：安装项目依赖
install_project_dependencies() {
    echo -e "${BLUE}===== 安装项目依赖 =====${NC}"
    
    cd $PROJECT_DIR
    
    # 安装前端依赖
    execute_command "npm install" "安装前端依赖"
    
    # 安装后端依赖（排除nodejieba）
    cd $SERVER_DIR
    execute_command "npm install --omit=optional" "安装后端依赖"
    
    # 确保移除nodejieba
    if npm list nodejieba &> /dev/null; then
        execute_command "npm uninstall nodejieba" "移除nodejieba依赖"
    fi
    
    cd $PROJECT_DIR
}

# 函数：配置环境文件
configure_environment() {
    echo -e "${BLUE}===== 配置环境文件 =====${NC}"
    
    # 配置后端环境文件
    cat > "$SERVER_DIR/.env.production" << EOF
# 数据库配置
DB_HOST=$DB_HOST
DB_PORT=$DB_PORT
DB_USER=$DB_USER
DB_PASSWORD=$DB_PASSWORD
DB_NAME=$DB_NAME

# 应用配置
NODE_ENV=production
PORT=$BACKEND_PORT
SERVER_URL=http://$SERVER_IP:$BACKEND_PORT

# JWT配置
JWT_SECRET=production_jwt_secret_$(date +%s)_$(openssl rand -hex 16)
JWT_EXPIRES_IN=1h
JWT_LONG_EXPIRES_IN=30d

# 飞书应用配置
FEISHU_APP_ID=cli_a66b3b2dcab8d013
FEISHU_APP_SECRET=5Fa8aatAGZ2Dv6K5VZhAWhbhjzE4lT2r
FEISHU_REDIRECT_URI=http://$SERVER_IP:$BACKEND_PORT/api/feishu/callback

# 飞书机器人配置
FEISHU_BOT_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/e6cff700-4172-4039-a700-43c8f43765fc

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880

# CORS配置
CORS_ORIGIN=http://$SERVER_IP,http://$SERVER_IP:$FRONTEND_PORT
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS

# 日志配置
LOG_LEVEL=info
EOF
    
    echo -e "${GREEN}✓ 后端环境配置已创建${NC}"
    
    # 配置前端环境文件
    cat > "$PROJECT_DIR/.env.production" << EOF
# 生产环境API配置
VITE_API_URL=http://$SERVER_IP/api

# 应用配置
VITE_APP_TITLE=光年小卖部
VITE_APP_BASE_URL=/
VITE_NODE_ENV=production
VITE_DEV_TOOLS=false
VITE_DEBUG=false
EOF
    
    echo -e "${GREEN}✓ 前端环境配置已创建${NC}"
}

# 函数：构建前端项目
build_frontend() {
    echo -e "${BLUE}===== 构建前端项目 =====${NC}"
    
    cd $PROJECT_DIR
    execute_command "npm run build" "构建前端项目"
    
    # 验证构建结果
    if [ -d "dist" ] && [ -f "dist/index.html" ]; then
        echo -e "${GREEN}✓ 前端构建成功${NC}"
    else
        echo -e "${RED}✗ 前端构建失败${NC}"
        exit 1
    fi
}

# 函数：配置数据库
setup_database() {
    echo -e "${BLUE}===== 配置数据库 =====${NC}"
    
    # 检查MySQL服务状态
    if ! systemctl is-active --quiet mysqld; then
        execute_command "systemctl start mysqld" "启动MySQL服务"
    fi
    
    # 检查数据库是否存在
    if mysql -u $DB_USER -p$DB_PASSWORD -e "USE $DB_NAME;" 2>/dev/null; then
        echo -e "${GREEN}✓ 数据库 $DB_NAME 已存在${NC}"
    else
        echo -e "${YELLOW}创建数据库 $DB_NAME...${NC}"
        mysql -u $DB_USER -p$DB_PASSWORD -e "CREATE DATABASE $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null || true
    fi
    
    # 初始化数据库
    cd $SERVER_DIR
    export NODE_ENV=production
    execute_command "npm run init-db" "初始化数据库"
}

# 函数：配置PM2
setup_pm2() {
    echo -e "${BLUE}===== 配置PM2 =====${NC}"
    
    # 创建PM2配置文件
    cat > "$SERVER_DIR/ecosystem.config.js" << EOF
module.exports = {
  apps: [{
    name: '$PM2_APP_NAME',
    script: 'server.js',
    instances: 1,
    exec_mode: 'fork',
    env_production: {
      NODE_ENV: 'production',
      PORT: $BACKEND_PORT
    },
    log_file: '../logs/combined.log',
    out_file: '../logs/out.log',
    error_file: '../logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm Z',
    restart_delay: 1000,
    max_restarts: 5,
    min_uptime: '10s',
    max_memory_restart: '1G'
  }]
};
EOF
    
    echo -e "${GREEN}✓ PM2配置文件已创建${NC}"
    
    # 停止现有应用（如果存在）
    pm2 stop $PM2_APP_NAME 2>/dev/null || true
    pm2 delete $PM2_APP_NAME 2>/dev/null || true
    
    # 启动应用
    cd $SERVER_DIR
    execute_command "pm2 start ecosystem.config.js --env production" "启动PM2应用"
    execute_command "pm2 save" "保存PM2配置"
    
    # 设置开机自启动
    pm2 startup 2>/dev/null || true
}

# 函数：配置Nginx
setup_nginx() {
    echo -e "${BLUE}===== 配置Nginx =====${NC}"
    
    # 备份原配置
    if [ -f "/etc/nginx/conf.d/workyy.conf" ]; then
        cp /etc/nginx/conf.d/workyy.conf /etc/nginx/conf.d/workyy.conf.backup.$(date +%Y%m%d_%H%M%S)
    fi
    
    # 创建Nginx配置文件
    cat > "/etc/nginx/conf.d/workyy.conf" << 'EOF'
server {
    listen 80;
    server_name **************;
    
    root /www/wwwroot/workyy;
    index index.html index.htm;
    
    client_max_body_size 50M;
    
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 前端静态文件
    location / {
        root /www/wwwroot/workyy/dist;
        try_files $uri $uri/ /index.html;
        index index.html;
        
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
        
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }
    
    # 上传文件
    location /uploads/ {
        alias /www/wwwroot/workyy/server/uploads/;
        expires 1y;
        add_header Cache-Control "public";
        access_log off;
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    access_log /www/wwwroot/workyy/logs/nginx_access.log;
    error_log /www/wwwroot/workyy/logs/nginx_error.log;
}
EOF
    
    echo -e "${GREEN}✓ Nginx配置文件已创建${NC}"
    
    # 测试配置并重启
    execute_command "nginx -t" "测试Nginx配置"
    execute_command "systemctl restart nginx" "重启Nginx服务"
}

# 主函数
main() {
    create_directories
    install_system_dependencies
    deploy_project_code
    install_project_dependencies
    configure_environment
    build_frontend
    setup_database
    setup_pm2
    setup_nginx
    
    echo -e "${GREEN}===== 部署完成 =====${NC}"
    echo -e "${BLUE}前端访问地址: http://$SERVER_IP${NC}"
    echo -e "${BLUE}后端API地址: http://$SERVER_IP/api${NC}"
    echo -e "${BLUE}健康检查: http://$SERVER_IP/health${NC}"
    echo -e "${YELLOW}请访问前端地址验证部署是否成功${NC}"
}

# 执行主函数
main
