#!/usr/bin/env node

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api';

// 调试支付偏好分析前端数据问题
async function debugPaymentPreferenceFrontend() {
  console.log('=== 支付偏好分析前端数据问题调试 ===\n');

  try {
    // 1. 登录获取token
    console.log('1. 管理员登录...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: '超管',
        email: '<EMAIL>',
        password: '654321'
      })
    });

    const loginData = await loginResponse.json();
    if (!loginData.token) {
      throw new Error('登录失败');
    }
    console.log('✅ 登录成功');

    const token = loginData.token;
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // 2. 测试API响应结构
    console.log('\n2. 测试API响应结构...');
    
    const response = await fetch(`${BASE_URL}/exchanges/payment-preference?period=month`, {
      method: 'GET',
      headers
    });

    if (!response.ok) {
      throw new Error(`API调用失败: ${response.status} ${response.statusText}`);
    }

    const apiData = await response.json();
    console.log('API响应状态:', response.status);
    console.log('API响应头:', Object.fromEntries(response.headers.entries()));
    console.log('API响应结构:');
    console.log('- success:', apiData.success);
    console.log('- data存在:', !!apiData.data);
    
    if (apiData.data) {
      console.log('- data.overview存在:', !!apiData.data.overview);
      console.log('- data.paymentTrend存在:', !!apiData.data.paymentTrend);
      console.log('- data.categoryPreference存在:', !!apiData.data.categoryPreference);
      console.log('- data.userSegments存在:', !!apiData.data.userSegments);
      
      if (apiData.data.overview) {
        console.log('\n概览数据详情:');
        console.log('- totalOrders:', apiData.data.overview.totalOrders);
        console.log('- rmbOrders:', apiData.data.overview.rmbOrders);
        console.log('- lyOrders:', apiData.data.overview.lyOrders);
        console.log('- rmbPercentage:', apiData.data.overview.rmbPercentage);
        console.log('- lyPercentage:', apiData.data.overview.lyPercentage);
        console.log('- rmbAmount:', apiData.data.overview.rmbAmount);
        console.log('- lyAmount:', apiData.data.overview.lyAmount);
      }
    }

    // 3. 检查数据类型和值
    console.log('\n3. 检查数据类型和值...');
    if (apiData.data && apiData.data.overview) {
      const overview = apiData.data.overview;
      console.log('数据类型检查:');
      console.log('- totalOrders类型:', typeof overview.totalOrders, '值:', overview.totalOrders);
      console.log('- rmbOrders类型:', typeof overview.rmbOrders, '值:', overview.rmbOrders);
      console.log('- lyOrders类型:', typeof overview.lyOrders, '值:', overview.lyOrders);
      
      // 检查是否为0的判断
      console.log('\n零值判断:');
      console.log('- totalOrders === 0:', overview.totalOrders === 0);
      console.log('- totalOrders == 0:', overview.totalOrders == 0);
      console.log('- totalOrders > 0:', overview.totalOrders > 0);
      console.log('- lyOrders === 0:', overview.lyOrders === 0);
      console.log('- lyOrders > 0:', overview.lyOrders > 0);
      
      // 检查单一支付方式判断
      const isSinglePaymentMethod = (overview.rmbOrders === 0 && overview.lyOrders > 0) ||
                                   (overview.lyOrders === 0 && overview.rmbOrders > 0);
      console.log('- 单一支付方式判断:', isSinglePaymentMethod);
      
      if (overview.rmbOrders === 0 && overview.lyOrders > 0) {
        console.log('- 主导支付方式: 光年币');
      } else if (overview.lyOrders === 0 && overview.rmbOrders > 0) {
        console.log('- 主导支付方式: 人民币');
      } else {
        console.log('- 主导支付方式: 混合或无数据');
      }
    }

    // 4. 测试不同时间周期
    console.log('\n4. 测试不同时间周期的数据...');
    const periods = ['week', 'month', 'quarter', 'year'];
    
    for (const period of periods) {
      const periodResponse = await fetch(`${BASE_URL}/exchanges/payment-preference?period=${period}`, {
        method: 'GET',
        headers
      });
      
      if (periodResponse.ok) {
        const periodData = await periodResponse.json();
        if (periodData.success && periodData.data && periodData.data.overview) {
          const overview = periodData.data.overview;
          console.log(`${period}周期: 总订单${overview.totalOrders}, 人民币${overview.rmbOrders}, 光年币${overview.lyOrders}`);
        } else {
          console.log(`${period}周期: 数据结构异常`);
        }
      } else {
        console.log(`${period}周期: API调用失败`);
      }
    }

    // 5. 模拟前端数据处理逻辑
    console.log('\n5. 模拟前端数据处理逻辑...');
    if (apiData.success && apiData.data) {
      const data = apiData.data;
      
      // 模拟前端的数据赋值
      const paymentData = {
        overview: {
          totalOrders: 0,
          rmbOrders: 0,
          lyOrders: 0,
          rmbPercentage: 0,
          lyPercentage: 0,
          rmbAmount: 0,
          lyAmount: 0
        },
        paymentTrend: [],
        categoryPreference: [],
        userSegments: {
          rmbOnly: 0,
          lyOnly: 0,
          mixed: 0,
          preferences: {
            highValueRmb: 0,
            frequentLy: 0
          }
        }
      };
      
      // 模拟 Object.assign(paymentData.overview, data.overview);
      Object.assign(paymentData.overview, data.overview);
      paymentData.paymentTrend = data.paymentTrend || [];
      paymentData.categoryPreference = data.categoryPreference || [];
      Object.assign(paymentData.userSegments, data.userSegments);
      
      console.log('前端数据处理后:');
      console.log('- paymentData.overview.totalOrders:', paymentData.overview.totalOrders);
      console.log('- paymentData.overview.lyOrders:', paymentData.overview.lyOrders);
      console.log('- paymentData.paymentTrend.length:', paymentData.paymentTrend.length);
      console.log('- paymentData.categoryPreference.length:', paymentData.categoryPreference.length);
      
      // 模拟前端的条件判断
      console.log('\n前端条件判断模拟:');
      console.log('- totalOrders === 0 (显示暂无数据):', paymentData.overview.totalOrders === 0);
      console.log('- totalOrders < 10 (显示数据量少):', paymentData.overview.totalOrders < 10);
      console.log('- totalOrders > 0 (显示图表):', paymentData.overview.totalOrders > 0);
      
      const isSingle = (paymentData.overview.rmbOrders === 0 && paymentData.overview.lyOrders > 0) ||
                      (paymentData.overview.lyOrders === 0 && paymentData.overview.rmbOrders > 0);
      console.log('- 单一支付方式 (显示特殊卡片):', isSingle);
    }

    // 6. 检查可能的前端问题
    console.log('\n6. 可能的前端问题分析...');
    
    if (apiData.success && apiData.data && apiData.data.overview.totalOrders > 0) {
      console.log('✅ API数据正常，问题可能在前端');
      console.log('可能的原因:');
      console.log('1. Vue响应性问题 - reactive数据未正确更新');
      console.log('2. 组件渲染时机问题 - 数据加载完成前组件已渲染');
      console.log('3. 条件判断逻辑问题 - v-if条件不正确');
      console.log('4. 异步数据处理问题 - await/async处理不当');
      console.log('5. 浏览器缓存问题 - 旧版本代码缓存');
    } else {
      console.log('❌ API数据异常，问题在后端');
    }

    return {
      success: true,
      apiData: apiData,
      diagnosis: 'API数据正常，问题可能在前端Vue组件'
    };

  } catch (error) {
    console.error('\n❌ 调试失败:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// 运行调试
debugPaymentPreferenceFrontend().then(result => {
  if (result.success) {
    console.log('\n🔍 调试完成，请检查前端Vue组件的数据绑定和渲染逻辑');
    process.exit(0);
  } else {
    console.error('\n💥 调试失败，请检查错误信息');
    process.exit(1);
  }
}).catch(error => {
  console.error('调试执行出错:', error);
  process.exit(1);
});
