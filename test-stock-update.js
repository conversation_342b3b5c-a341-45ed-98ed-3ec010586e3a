/**
 * 库存更新功能测试脚本
 * 测试单个商品编辑和批量库存调整功能
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';
const TEST_ADMIN_TOKEN = 'your_admin_token_here'; // 需要替换为实际的管理员token

// 测试配置
const config = {
  headers: {
    'Authorization': `Bearer ${TEST_ADMIN_TOKEN}`,
    'Content-Type': 'application/json'
  }
};

// 测试用例
async function testStockUpdate() {
  console.log('🧪 开始库存更新功能测试...\n');

  try {
    // 1. 获取商品列表
    console.log('1️⃣ 获取商品列表...');
    const productsResponse = await axios.get(`${API_BASE}/products?page=1&limit=5`, config);
    const products = productsResponse.data.products;
    
    if (!products || products.length === 0) {
      console.log('❌ 没有找到商品，测试终止');
      return;
    }

    console.log(`✅ 找到 ${products.length} 个商品`);
    products.forEach(p => {
      console.log(`   - ${p.name}: 库存=${p.stock}, 模式=${p.stockManagementType || 'unknown'}`);
    });

    // 2. 测试单个商品编辑（职场分配模式）
    console.log('\n2️⃣ 测试单个商品编辑（职场分配模式）...');
    const workplaceProduct = products.find(p => p.stockManagementType === 'workplace');
    
    if (workplaceProduct) {
      console.log(`测试商品: ${workplaceProduct.name} (ID: ${workplaceProduct.id})`);
      
      try {
        const updateResponse = await axios.put(
          `${API_BASE}/products/${workplaceProduct.id}`,
          {
            name: workplaceProduct.name,
            stock: workplaceProduct.stock + 10 // 尝试增加库存
          },
          config
        );
        
        console.log('❌ 职场分配模式商品应该不允许直接修改库存');
      } catch (error) {
        if (error.response && error.response.status === 400) {
          console.log('✅ 正确阻止了职场分配模式商品的直接库存修改');
          console.log(`   响应: ${error.response.data.message}`);
        } else {
          console.log('❌ 意外错误:', error.message);
        }
      }
    }

    // 3. 测试批量库存更新（职场分配模式）
    console.log('\n3️⃣ 测试批量库存更新（职场分配模式）...');
    const workplaceProductIds = products
      .filter(p => p.stockManagementType === 'workplace')
      .map(p => p.id);

    if (workplaceProductIds.length > 0) {
      try {
        const bulkResponse = await axios.post(
          `${API_BASE}/products/bulk-stock`,
          {
            ids: workplaceProductIds,
            value: 5,
            operation: 'add'
          },
          config
        );
        
        console.log('✅ 批量库存更新响应:', bulkResponse.data.message);
        if (bulkResponse.data.workplaceModeCount > 0) {
          console.log(`✅ 正确识别了 ${bulkResponse.data.workplaceModeCount} 个职场分配模式商品`);
        }
      } catch (error) {
        console.log('❌ 批量库存更新失败:', error.response?.data?.message || error.message);
      }
    }

    // 4. 测试单一库存模式商品（如果有的话）
    console.log('\n4️⃣ 测试单一库存模式商品...');
    const singleProduct = products.find(p => p.stockManagementType === 'single');
    
    if (singleProduct) {
      console.log(`测试商品: ${singleProduct.name} (ID: ${singleProduct.id})`);
      
      try {
        const originalStock = singleProduct.stock;
        const newStock = originalStock + 1;
        
        const updateResponse = await axios.put(
          `${API_BASE}/products/${singleProduct.id}`,
          {
            name: singleProduct.name,
            stock: newStock
          },
          config
        );
        
        console.log('✅ 单一库存模式商品库存更新成功');
        
        // 验证更新结果
        const verifyResponse = await axios.get(`${API_BASE}/products/${singleProduct.id}`, config);
        const updatedProduct = verifyResponse.data;
        
        if (updatedProduct.stock === newStock) {
          console.log(`✅ 库存更新验证成功: ${originalStock} → ${updatedProduct.stock}`);
        } else {
          console.log(`❌ 库存更新验证失败: 期望 ${newStock}, 实际 ${updatedProduct.stock}`);
        }
        
      } catch (error) {
        console.log('❌ 单一库存模式商品更新失败:', error.response?.data?.message || error.message);
      }
    } else {
      console.log('ℹ️ 没有找到单一库存模式的商品');
    }

    console.log('\n🎉 库存更新功能测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  testStockUpdate();
}

module.exports = { testStockUpdate };
