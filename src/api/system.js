import api from './index';

/**
 * 系统重置
 * 删除除管理员以外的用户、所有订单、公告、日志，保留商品和分类，将商品库存设为50
 * @returns {Promise<Object>} 重置操作结果和统计信息
 */
export const resetSystem = async () => {
  try {
    const response = await api.post('/system/reset');
    console.log('系统重置API原始响应:', JSON.stringify(response, null, 2));
    
    // 检查删除的数据
    if (response.stats && response.stats.deleted) {
      const deleted = response.stats.deleted;
      console.log('后端返回的删除数据:', JSON.stringify(deleted, null, 2));
      // 检查是否有非零值
      const hasNonZeroValues = Object.values(deleted).some(val => val > 0);
      console.log('是否有非零删除记录:', hasNonZeroValues);
      
      // 确保所有值都是数字类型
      Object.keys(deleted).forEach(key => {
        if (typeof deleted[key] !== 'number') {
          console.warn(`删除的 ${key} 不是数字类型:`, deleted[key]);
          deleted[key] = parseInt(deleted[key]) || 0;
        }
      });
    } else {
      console.error('API响应中缺少删除统计数据:', response);
    }
    
    // 验证响应格式
    if (!response.stats || !response.stats.deleted) {
      console.error('API响应格式异常:', response);
      // 如果缺少必要的数据结构，添加默认值
      if (!response.stats) {
        response.stats = {};
      }
      if (!response.stats.deleted) {
        response.stats.deleted = {
          users: 0,
          exchanges: 0,
          announcements: 0,
          feedbacks: 0,
          notifications: 0,
          logs: 0
        };
      }
      if (!response.stats.updated) {
        response.stats.updated = { products: 0 };
      }
    }
    
    return response;
  } catch (error) {
    console.error('系统重置API错误:', error);
    throw error;
  }
};

/**
 * 获取支付收款码信息
 * @returns {Promise<Object>} 支付码信息，包含URL和上传时间
 */
export const getPaymentQRCode = async () => {
  try {
    console.log('开始获取支付收款码信息...');
    const response = await api.get('/system/payment-qrcode');
    console.log('获取支付码响应详情:', JSON.stringify(response));
    return response;
  } catch (error) {
    console.error('获取支付码错误:', error);
    if (error.response) {
      console.error('错误响应状态:', error.response.status);
      console.error('错误响应数据:', error.response.data);
    }
    throw error;
  }
};

/**
 * 上传新的支付收款码
 * @param {FormData} formData 包含支付码图片的表单数据
 * @returns {Promise<Object>} 上传结果
 */
export const uploadPaymentQRCode = async (formData) => {
  try {
    console.log('开始上传支付收款码...');
    // 检查formData是否包含file字段
    console.log('FormData包含的字段:', [...formData.keys()]);
    if (formData.has('file')) {
      const file = formData.get('file');
      console.log('上传文件信息:', {
        name: file.name,
        type: file.type,
        size: file.size,
      });
    } else {
      console.warn('警告: FormData中缺少file字段');
    }
    
    const response = await api.post('/system/payment-qrcode', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 30000 // 延长超时时间到30秒
    });
    console.log('上传支付码响应详情:', JSON.stringify(response));
    return response;
  } catch (error) {
    console.error('上传支付码错误:', error);
    if (error.response) {
      console.error('错误响应状态:', error.response.status);
      console.error('错误响应数据:', error.response.data);
    } else if (error.request) {
      console.error('请求已发送但未收到响应，可能是网络问题或服务器超时');
    } else {
      console.error('请求配置错误:', error.message);
    }
    throw error;
  }
};

/**
 * 删除当前的支付收款码
 * @returns {Promise<Object>} 删除结果
 */
export const deletePaymentQRCode = async () => {
  try {
    const response = await api.delete('/system/payment-qrcode');
    console.log('删除支付码响应:', response);
    return response;
  } catch (error) {
    console.error('删除支付码错误:', error);
    throw error;
  }
};

/**
 * 获取职场列表（分页）
 * @param {number} page - 页码
 * @param {number} limit - 每页数量
 * @param {string} search - 搜索关键词
 * @returns {Promise<Object>} 包含职场列表和分页信息的响应
 */
export const getWorkplaces = async (page = 1, limit = 10, search = '') => {
  try {
    const params = { page, limit };
    if (search) params.search = search;
    
    const response = await api.get('/system/workplaces', { params });
    console.log('获取职场列表响应:', response);
    return response;
  } catch (error) {
    console.error('获取职场列表错误:', error);
    throw error;
  }
};

/**
 * 获取所有活跃职场（用于下拉选择）
 * @returns {Promise<Array>} 职场数组
 */
export const getAllActiveWorkplaces = async () => {
  try {
    const response = await api.get('/system/workplaces/active');
    console.log('获取活跃职场响应:', response);
    return response;
  } catch (error) {
    console.error('获取活跃职场错误:', error);
    throw error;
  }
};

/**
 * 获取活跃职场数量统计（用于库存管理模式判断）
 * @returns {Promise<Object>} 职场数量统计信息
 */
export const getActiveWorkplaceCount = async () => {
  try {
    const response = await api.get('/system/workplaces/count');
    console.log('获取活跃职场数量响应:', response);
    return response;
  } catch (error) {
    console.error('获取活跃职场数量错误:', error);
    throw error;
  }
};

/**
 * 获取单个职场详情
 * @param {number} id - 职场ID
 * @returns {Promise<Object>} 职场详情
 */
export const getWorkplace = async (id) => {
  try {
    const response = await api.get(`/system/workplaces/${id}`);
    console.log('获取职场详情响应:', response);
    return response;
  } catch (error) {
    console.error('获取职场详情错误:', error);
    throw error;
  }
};

/**
 * 创建新职场
 * @param {Object} data - 职场数据
 * @param {string} data.name - 职场名称
 * @param {string} data.code - 职场代码
 * @param {string} data.description - 职场描述
 * @param {boolean} data.isActive - 是否激活
 * @returns {Promise<Object>} 创建结果
 */
export const createWorkplace = async (data) => {
  try {
    const response = await api.post('/system/workplaces', data);
    console.log('创建职场响应:', response);
    return response;
  } catch (error) {
    console.error('创建职场错误:', error);
    throw error;
  }
};

/**
 * 更新职场
 * @param {number} id - 职场ID
 * @param {Object} data - 职场更新数据
 * @returns {Promise<Object>} 更新结果
 */
export const updateWorkplace = async (id, data) => {
  try {
    const response = await api.put(`/system/workplaces/${id}`, data);
    console.log('更新职场响应:', response);
    return response;
  } catch (error) {
    console.error('更新职场错误:', error);
    throw error;
  }
};

/**
 * 删除职场
 * @param {number} id - 职场ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteWorkplace = async (id) => {
  try {
    const response = await api.delete(`/system/workplaces/${id}`);
    console.log('删除职场响应:', response);
    return response;
  } catch (error) {
    console.error('删除职场错误:', error);
    throw error;
  }
}; 