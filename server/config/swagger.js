const swaggerJsDoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

// Swagger配置
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: '光年小卖部 API 文档',
      version: '1.0.0',
      description: '光年小卖部电商平台API接口文档',
      contact: {
        name: '技术支持',
        email: '<EMAIL>'
      },
      license: {
        name: '私有许可',
        url: 'https://example.com/license'
      }
    },
    servers: [
      {
        url: '/api',
        description: 'API 服务器'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: [
    './routes/*.js',
    './controllers/*.js',
    './models/*.js'
  ]
};

const swaggerSpec = swaggerJsDoc(swaggerOptions);

// 定义Swagger中间件
const swaggerDocs = (app) => {
  // Swagger页面
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));

  // Swagger JSON
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(swaggerSpec);
  });

  console.log('Swagger文档已生成，访问路径: /api-docs');
};

module.exports = swaggerDocs;
