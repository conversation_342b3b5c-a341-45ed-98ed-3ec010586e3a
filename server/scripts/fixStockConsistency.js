/**
 * 库存一致性修复脚本
 * 修复products表和product_workplace_stocks表之间的数据不一致问题
 */

const { sequelize } = require('../config/database');
const { Product, ProductWorkplaceStock, Exchange } = require('../models');

/**
 * 修复单个商品的库存一致性
 * @param {number} productId - 商品ID
 * @param {boolean} dryRun - 是否为试运行模式
 */
async function fixProductStockConsistency(productId, dryRun = true) {
  const transaction = await sequelize.transaction();
  
  try {
    // 获取商品信息
    const product = await Product.findByPk(productId, { transaction });
    if (!product) {
      throw new Error(`商品 ID ${productId} 不存在`);
    }

    // 获取职场库存总和
    const workplaceStockTotal = await ProductWorkplaceStock.getTotalStockByProduct(productId);
    
    // 获取实际兑换数量（只计算已完成和已批准的）
    const actualExchangeCount = await Exchange.sum('quantity', {
      where: {
        productId,
        status: ['completed', 'approved']
      },
      transaction
    }) || 0;

    // 计算应该的库存数量
    // 假设初始库存 = 当前职场库存总和 + 实际兑换数量
    const calculatedInitialStock = workplaceStockTotal.totalStock + actualExchangeCount;
    const calculatedCurrentStock = calculatedInitialStock - actualExchangeCount;

    console.log(`\n=== 商品: ${product.name} (ID: ${productId}) ===`);
    console.log(`当前 products.stock: ${product.stock}`);
    console.log(`当前 products.exchangeCount: ${product.exchangeCount}`);
    console.log(`职场库存总和: ${workplaceStockTotal.totalStock}`);
    console.log(`实际兑换数量: ${actualExchangeCount}`);
    console.log(`计算得出的当前库存: ${calculatedCurrentStock}`);
    console.log(`推算的初始库存: ${calculatedInitialStock}`);

    // 检查是否需要修复
    const needsStockFix = product.stock !== workplaceStockTotal.totalStock;
    const needsExchangeCountFix = product.exchangeCount !== actualExchangeCount;

    if (needsStockFix || needsExchangeCountFix) {
      console.log(`\n🔧 需要修复:`);
      if (needsStockFix) {
        console.log(`  - 库存不一致: products.stock(${product.stock}) != 职场库存总和(${workplaceStockTotal.totalStock})`);
      }
      if (needsExchangeCountFix) {
        console.log(`  - 兑换数量不一致: products.exchangeCount(${product.exchangeCount}) != 实际兑换数量(${actualExchangeCount})`);
      }

      if (!dryRun) {
        // 执行修复：将products.stock同步为职场库存总和
        await product.update({
          stock: workplaceStockTotal.totalStock,
          exchangeCount: actualExchangeCount
        }, { transaction });

        console.log(`✅ 修复完成:`);
        console.log(`  - products.stock: ${product.stock} -> ${workplaceStockTotal.totalStock}`);
        console.log(`  - products.exchangeCount: ${product.exchangeCount} -> ${actualExchangeCount}`);
      } else {
        console.log(`📋 试运行模式 - 将执行以下修复:`);
        console.log(`  - products.stock: ${product.stock} -> ${workplaceStockTotal.totalStock}`);
        console.log(`  - products.exchangeCount: ${product.exchangeCount} -> ${actualExchangeCount}`);
      }
    } else {
      console.log(`✅ 数据一致，无需修复`);
    }

    if (dryRun) {
      await transaction.rollback();
    } else {
      await transaction.commit();
    }

    return {
      productId,
      productName: product.name,
      needsStockFix,
      needsExchangeCountFix,
      currentStock: product.stock,
      workplaceStockTotal: workplaceStockTotal.totalStock,
      currentExchangeCount: product.exchangeCount,
      actualExchangeCount,
      fixed: !dryRun && (needsStockFix || needsExchangeCountFix)
    };

  } catch (error) {
    await transaction.rollback();
    console.error(`修复商品 ${productId} 失败:`, error);
    throw error;
  }
}

/**
 * 修复所有商品的库存一致性
 * @param {boolean} dryRun - 是否为试运行模式
 */
async function fixAllProductsStockConsistency(dryRun = true) {
  try {
    console.log(`\n🔍 开始${dryRun ? '检查' : '修复'}所有商品的库存一致性...\n`);

    // 获取所有有职场库存的商品
    const productsWithWorkplaceStocks = await sequelize.query(`
      SELECT DISTINCT p.id, p.name 
      FROM products p 
      INNER JOIN product_workplace_stocks pws ON p.id = pws.productId 
      WHERE p.status = 'active'
      ORDER BY p.id
    `, {
      type: sequelize.QueryTypes.SELECT
    });

    console.log(`找到 ${productsWithWorkplaceStocks.length} 个商品需要检查\n`);

    const results = [];
    let needsFixCount = 0;

    for (const product of productsWithWorkplaceStocks) {
      try {
        const result = await fixProductStockConsistency(product.id, dryRun);
        results.push(result);
        
        if (result.needsStockFix || result.needsExchangeCountFix) {
          needsFixCount++;
        }
      } catch (error) {
        console.error(`处理商品 ${product.name} (ID: ${product.id}) 时出错:`, error.message);
      }
    }

    // 输出汇总报告
    console.log(`\n📊 汇总报告:`);
    console.log(`总检查商品数: ${results.length}`);
    console.log(`需要修复的商品数: ${needsFixCount}`);
    
    if (dryRun && needsFixCount > 0) {
      console.log(`\n💡 要执行实际修复，请运行: node scripts/fixStockConsistency.js --fix`);
    } else if (!dryRun && needsFixCount > 0) {
      console.log(`\n✅ 修复完成！`);
    } else {
      console.log(`\n✅ 所有商品库存数据一致，无需修复`);
    }

    return results;

  } catch (error) {
    console.error('修复过程中发生错误:', error);
    throw error;
  }
}

/**
 * 修复特定商品 "一片草坪"
 */
async function fixGrasslandProduct(dryRun = true) {
  try {
    console.log(`\n🌱 专项修复 "一片草坪" 商品库存一致性...\n`);
    
    const result = await fixProductStockConsistency(17, dryRun);
    
    if (dryRun && (result.needsStockFix || result.needsExchangeCountFix)) {
      console.log(`\n💡 要执行实际修复，请运行: node scripts/fixStockConsistency.js --fix-grassland`);
    }
    
    return result;
  } catch (error) {
    console.error('修复 "一片草坪" 商品失败:', error);
    throw error;
  }
}

// 命令行执行逻辑
if (require.main === module) {
  const args = process.argv.slice(2);
  const isFixMode = args.includes('--fix');
  const isGrasslandMode = args.includes('--fix-grassland');
  const isDryRun = !isFixMode && !isGrasslandMode;

  (async () => {
    try {
      if (isGrasslandMode) {
        await fixGrasslandProduct(false);
      } else if (args.includes('--grassland')) {
        await fixGrasslandProduct(true);
      } else {
        await fixAllProductsStockConsistency(isDryRun);
      }
    } catch (error) {
      console.error('脚本执行失败:', error);
      process.exit(1);
    } finally {
      await sequelize.close();
    }
  })();
}

module.exports = {
  fixProductStockConsistency,
  fixAllProductsStockConsistency,
  fixGrasslandProduct
};
