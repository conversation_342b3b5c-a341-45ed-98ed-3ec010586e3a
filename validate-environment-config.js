#!/usr/bin/env node

/**
 * 环境配置验证脚本
 * 验证所有环境变量配置是否正确，确保硬编码已被完全替换
 */

import fs from 'fs';
import path from 'path';

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查文件是否存在真正的硬编码（排除作为默认值的情况）
function checkHardcodedUrls(filePath, description) {
  if (!fs.existsSync(filePath)) {
    log(`❌ 文件不存在: ${filePath}`, 'red');
    return false;
  }

  const content = fs.readFileSync(filePath, 'utf8');

  // 检查是否有不使用环境变量的硬编码
  const problematicPatterns = [
    // 直接赋值硬编码（不是默认值）
    /baseUrl\s*=\s*['"]https?:\/\/(?:localhost:3000|47\.122\.122\.245|store-api\.chongyangqisi\.com)['"]/g,
    /const\s+\w+\s*=\s*['"]https?:\/\/(?:localhost:3000|47\.122\.122\.245|store-api\.chongyangqisi\.com)['"]/g,
    /let\s+\w+\s*=\s*['"]https?:\/\/(?:localhost:3000|47\.122\.122\.245|store-api\.chongyangqisi\.com)['"]/g,
    // 在条件语句中直接使用硬编码
    /if\s*\([^)]*['"]https?:\/\/(?:localhost:3000|47\.122\.122\.245|store-api\.chongyangqisi\.com)['"]/g,
    // 在函数调用中直接使用硬编码
    /\w+\(['"]https?:\/\/(?:localhost:3000|47\.122\.122\.245|store-api\.chongyangqisi\.com)['"]\)/g
  ];

  let hasProblematicHardcoded = false;
  const issues = [];

  problematicPatterns.forEach((pattern, index) => {
    const matches = content.match(pattern);
    if (matches) {
      hasProblematicHardcoded = true;
      issues.push(`发现问题硬编码模式 ${index + 1}: ${matches.join(', ')}`);
    }
  });

  // 检查是否使用了环境变量
  const hasEnvVarUsage = content.includes('import.meta.env.VITE_') ||
                        content.includes('process.env.DEV_') ||
                        content.includes('process.env.PROD_') ||
                        content.includes('process.env.TEST_');

  if (hasProblematicHardcoded) {
    log(`❌ ${description} 仍有问题硬编码:`, 'red');
    issues.forEach(issue => log(`   ${issue}`, 'yellow'));
    return false;
  } else if (hasEnvVarUsage) {
    log(`✅ ${description} 已使用环境变量`, 'green');
    return true;
  } else {
    log(`⚠️  ${description} 未检测到环境变量使用`, 'yellow');
    return true; // 不算失败，但需要注意
  }
}

// 检查环境变量配置文件
function checkEnvFile(filePath, description, requiredVars) {
  if (!fs.existsSync(filePath)) {
    log(`❌ 环境配置文件不存在: ${filePath}`, 'red');
    return false;
  }

  const content = fs.readFileSync(filePath, 'utf8');
  let allPresent = true;

  log(`🔍 检查 ${description}:`, 'blue');
  
  requiredVars.forEach(varName => {
    const regex = new RegExp(`^${varName}=`, 'm');
    if (regex.test(content)) {
      log(`   ✅ ${varName}`, 'green');
    } else {
      log(`   ❌ 缺失: ${varName}`, 'red');
      allPresent = false;
    }
  });

  return allPresent;
}

// 主验证函数
function validateEnvironmentConfig() {
  log('🚀 开始验证环境配置...', 'blue');
  log('', 'reset');

  let allPassed = true;

  // 1. 检查环境配置文件
  log('📋 第一步：检查环境配置文件', 'blue');
  
  const backendRequiredVars = [
    'DEV_SERVER_URL', 'DEV_API_URL', 
    'PROD_SERVER_URL', 'PROD_API_URL', 'PROD_FRONTEND_URL',
    'OLD_SERVER_IP'
  ];
  
  const frontendRequiredVars = [
    'VITE_DEV_SERVER_URL', 'VITE_DEV_API_URL',
    'VITE_PROD_SERVER_URL', 'VITE_PROD_API_URL', 'VITE_PROD_FRONTEND_URL',
    'VITE_OLD_SERVER_IP'
  ];

  const testRequiredVars = [
    ...backendRequiredVars,
    'TEST_SERVER_URL', 'TEST_API_URL', 'TEST_FRONTEND_URL'
  ];

  const frontendTestRequiredVars = [
    ...frontendRequiredVars,
    'VITE_TEST_SERVER_URL', 'VITE_TEST_API_URL', 'VITE_TEST_FRONTEND_URL'
  ];

  allPassed &= checkEnvFile('server/.env.development', '后端开发环境', backendRequiredVars);
  allPassed &= checkEnvFile('server/.env.production', '后端生产环境', backendRequiredVars);
  allPassed &= checkEnvFile('server/.env.test', '后端测试环境', testRequiredVars);
  allPassed &= checkEnvFile('.env.development', '前端开发环境', frontendRequiredVars);
  allPassed &= checkEnvFile('.env.production', '前端生产环境', frontendRequiredVars);
  allPassed &= checkEnvFile('.env.test', '前端测试环境', frontendTestRequiredVars);

  log('', 'reset');

  // 2. 检查代码文件中的硬编码
  log('📋 第二步：检查代码文件硬编码', 'blue');
  
  const filesToCheck = [
    ['src/utils/environmentDetector.js', '前端环境检测工具'],
    ['src/utils/imageUtils.js', '前端图片工具'],
    ['server/controllers/uploadController.js', '后端上传控制器'],
    ['server/controllers/systemController.js', '后端系统控制器'],
    ['server/server.js', '后端服务器启动文件']
  ];

  filesToCheck.forEach(([filePath, description]) => {
    allPassed &= checkHardcodedUrls(filePath, description);
  });

  log('', 'reset');

  // 3. 总结
  if (allPassed) {
    log('🎉 所有验证通过！环境变量配置替换成功！', 'green');
    log('✅ 所有硬编码已被环境变量替换', 'green');
    log('✅ 所有环境配置文件包含必要的环境变量', 'green');
  } else {
    log('❌ 验证失败，请检查上述问题', 'red');
    process.exit(1);
  }
}

// 运行验证
validateEnvironmentConfig();
