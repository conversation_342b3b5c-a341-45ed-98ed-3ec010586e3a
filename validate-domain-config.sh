#!/bin/bash

# 域名配置验证脚本
# 验证所有配置文件是否正确更新为新域名

set -e

echo "======================================"
echo "域名配置验证脚本"
echo "======================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 验证函数
validate_file() {
    local file=$1
    local description=$2
    local should_contain=$3
    local should_not_contain=$4
    
    echo -n "检查 $description... "
    
    if [ ! -f "$file" ]; then
        echo -e "${RED}❌ 文件不存在${NC}"
        return 1
    fi
    
    # 检查应该包含的内容
    for pattern in $should_contain; do
        if ! grep -q "$pattern" "$file"; then
            echo -e "${RED}❌ 缺少: $pattern${NC}"
            return 1
        fi
    done
    
    # 检查不应该包含的内容
    for pattern in $should_not_contain; do
        if grep -q "$pattern" "$file"; then
            echo -e "${YELLOW}⚠️ 仍包含旧配置: $pattern${NC}"
            return 1
        fi
    done
    
    echo -e "${GREEN}✅ 通过${NC}"
    return 0
}

# 验证前端环境配置
echo "🔍 验证前端配置..."
validate_file ".env.production" "前端生产环境配置" \
    "store-api.chongyangqisi.com store.chongyangqisi.com VITE_ENABLE_HTTPS=true" \
    "**************"

# 验证后端环境配置
echo "🔍 验证后端配置..."
validate_file "server/.env.production" "后端生产环境配置" \
    "store-api.chongyangqisi.com store.chongyangqisi.com" \
    "**************"

# 验证前端工具文件
echo "🔍 验证前端工具文件..."
validate_file "src/utils/environmentDetector.js" "环境检测工具" \
    "store-api.chongyangqisi.com" \
    ""

validate_file "src/utils/imageUtils.js" "图片工具" \
    "store-api.chongyangqisi.com" \
    ""

# 验证后端控制器文件
echo "🔍 验证后端控制器文件..."
validate_file "server/controllers/uploadController.js" "上传控制器" \
    "process.env.SERVER_URL" \
    "**************"

validate_file "server/controllers/feishuController.js" "飞书控制器" \
    "store.chongyangqisi.com" \
    "**************"

validate_file "server/controllers/systemController.js" "系统控制器" \
    "process.env.SERVER_URL" \
    "**************"

validate_file "server/controllers/productImageController.js" "商品图片控制器" \
    "process.env.SERVER_URL" \
    "**************"

validate_file "server/routes/announcements.js" "公告路由" \
    "process.env.SERVER_URL" \
    "**************"

# 验证前端组件
echo "🔍 验证前端组件..."
validate_file "src/components/PaymentQRCode.vue" "支付二维码组件" \
    "store-api.chongyangqisi.com" \
    ""

# 验证nginx配置
echo "🔍 验证nginx配置..."
if [ -f "nginx-production-domain.conf" ]; then
    echo -n "检查nginx域名配置... "
    if grep -q "store.chongyangqisi.com" nginx-production-domain.conf && \
       grep -q "store-api.chongyangqisi.com" nginx-production-domain.conf; then
        echo -e "${GREEN}✅ 通过${NC}"
        
        # 检查SSL证书路径
        echo -n "检查SSL证书配置... "
        if grep -q "/path/to/" nginx-production-domain.conf; then
            echo -e "${YELLOW}⚠️ 需要更新SSL证书路径${NC}"
        else
            echo -e "${GREEN}✅ 通过${NC}"
        fi
    else
        echo -e "${RED}❌ 域名配置错误${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ nginx域名配置文件不存在${NC}"
fi

# 检查是否有遗漏的IP地址引用
echo "🔍 搜索遗漏的IP地址引用..."
echo -n "检查代码文件中的IP地址... "

# 搜索除了备份文件、node_modules、dist之外的文件
IP_REFS=$(find . -type f \( -name "*.js" -o -name "*.vue" -o -name "*.json" -o -name "*.env*" \) \
    ! -path "./node_modules/*" \
    ! -path "./server/node_modules/*" \
    ! -path "./dist/*" \
    ! -path "./backup/*" \
    ! -path "./backups/*" \
    -exec grep -l "47\.122\.122\.245" {} \; 2>/dev/null || true)

if [ -n "$IP_REFS" ]; then
    echo -e "${YELLOW}⚠️ 发现遗漏的IP地址引用:${NC}"
    echo "$IP_REFS"
else
    echo -e "${GREEN}✅ 无遗漏的IP地址引用${NC}"
fi

# 生成配置摘要
echo ""
echo "======================================"
echo "📋 配置摘要"
echo "======================================"
echo "前端域名: https://store.chongyangqisi.com"
echo "后端API域名: https://store-api.chongyangqisi.com"
echo "HTTPS支持: 已启用"
echo "CORS配置: 已更新"
echo "飞书回调: 已更新"
echo ""

# 检查构建状态
if [ -d "dist" ] && [ -f "dist/index.html" ]; then
    echo -e "${GREEN}✅ 前端已构建${NC}"
    
    # 检查构建文件中的配置
    echo -n "检查构建文件配置... "
    if grep -r "47\.122\.122\.245" dist/ >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️ 构建文件中仍有旧IP地址，建议重新构建${NC}"
    else
        echo -e "${GREEN}✅ 构建文件配置正确${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ 前端未构建，请运行 npm run build${NC}"
fi

echo ""
echo "======================================"
echo "🎯 下一步操作建议"
echo "======================================"
echo "1. 如果所有检查都通过，可以部署到服务器"
echo "2. 确保域名DNS解析正确指向服务器"
echo "3. 配置SSL证书并更新nginx配置"
echo "4. 在服务器上重启服务"
echo "5. 测试所有功能是否正常"
echo ""
echo "✨ 配置验证完成！"
