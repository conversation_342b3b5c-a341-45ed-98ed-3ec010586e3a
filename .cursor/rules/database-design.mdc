---
description:
globs:
alwaysApply: false
---
# 光年小卖部数据库设计规范

## 数据库类型
项目使用MongoDB作为主要数据存储，同时也使用MySQL/SQLite作为关系型数据库。

## 模型定义
数据模型定义在[server/models](mdc:server/models)目录中。

## MongoDB设计规范

### 集合命名
- 使用复数形式，如`products`而非`product`
- 使用小写字母，多个单词用下划线连接

### 字段命名
- 使用驼峰命名法，如`createdAt`
- 字段名应有明确含义，避免缩写
- 主键统一使用`_id`

### 模型结构
- 每个模型应定义清晰的类型和验证规则
- 使用Mongoose模式定义
- 添加`timestamps: true`自动管理创建和更新时间

### 索引设计
- 对频繁查询的字段创建索引
- 对需要全文检索的字段创建文本索引
- 复合索引遵循"最左前缀"原则

### 模型示例
```javascript
const productSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: true
  }
}, { timestamps: true });

// 创建索引
productSchema.index({ name: 'text', description: 'text' });
productSchema.index({ category: 1 });
productSchema.index({ createdAt: -1 });
```

## 关系设计

### 引用关系
- 使用ObjectId引用其他集合，如`category`字段引用`Category`集合
- 通过Mongoose的`populate`方法加载关联数据

### 嵌入关系
- 对于1:1或1:少量关系，可以考虑使用嵌入式文档
- 嵌入式文档适合不需要单独查询的数据

## 数据迁移
- 使用Sequelize-CLI管理数据库迁移
- 迁移命令：`npm run migrate`
- 回滚命令：`npm run migrate:undo`

## 数据验证
- 在模型层面定义验证规则，确保数据完整性
- 在API层面也需要进行输入验证
- 使用express-validator进行请求参数验证
