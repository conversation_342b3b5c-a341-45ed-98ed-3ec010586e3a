require('dotenv').config();

// CORS配置
const corsOrigins = process.env.CORS_ORIGIN ?
  process.env.CORS_ORIGIN.split(',') :
  [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:8080',
    'http://127.0.0.1:5173',
    'http://**************:5173',
    'http://**************:5174',
    'http://**************:8080'
  ];
const corsMethods = process.env.CORS_METHODS || 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS';

module.exports = {
  // 服务器配置
  server: {
    port: process.env.PORT || 3000,
    env: process.env.NODE_ENV || 'development'
  },
  
  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'your-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '1h', // 默认1小时过期
    longExpiresIn: process.env.JWT_LONG_EXPIRES_IN || '30d' // 默认30天过期(记住我)
  },
  
  // 文件上传配置
  upload: {
    // 上传目录
    directory: process.env.UPLOAD_DIR || 'uploads',
    // 最大文件大小 (5MB)
    maxSize: process.env.MAX_FILE_SIZE || 5 * 1024 * 1024,
    // 允许的文件类型
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
  },
  
  // 分页默认配置
  pagination: {
    defaultLimit: 10,
    maxLimit: 100
  },
  
  // CORS配置
  cors: {
    origin: corsOrigins,
    methods: corsMethods.split(','),
    credentials: true,
    exposedHeaders: ['Content-Disposition'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    preflightContinue: false,
    optionsSuccessStatus: 204
  }
}; 