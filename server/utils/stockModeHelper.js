/**
 * 库存管理模式辅助工具
 * 提供动态判断和管理库存模式的功能
 */

const { Workplace } = require('../models');

/**
 * 获取推荐的库存管理模式
 * @param {Object} transaction - 数据库事务对象（可选）
 * @returns {Promise<Object>} 返回推荐的库存管理模式信息
 */
async function getRecommendedStockMode(transaction = null) {
  try {
    const workplaceCount = await Workplace.count({ transaction });
    
    return {
      workplaceCount,
      recommendedMode: workplaceCount <= 1 ? 'single' : 'workplace',
      shouldUseWorkplaceMode: workplaceCount > 1,
      description: workplaceCount <= 1 
        ? '单职场环境，推荐使用单一库存模式'
        : '多职场环境，推荐使用职场分配模式'
    };
  } catch (error) {
    console.error('获取推荐库存模式失败:', error);
    return {
      workplaceCount: 0,
      recommendedMode: 'single',
      shouldUseWorkplaceMode: false,
      description: '无法确定职场数量，默认使用单一库存模式'
    };
  }
}

/**
 * 检查商品是否应该允许直接库存操作
 * @param {Object} product - 商品对象
 * @param {Object} transaction - 数据库事务对象（可选）
 * @returns {Promise<Object>} 返回是否允许操作的信息
 */
async function canDirectlyUpdateStock(product, transaction = null) {
  const modeInfo = await getRecommendedStockMode(transaction);
  
  // 单职场环境下，无论商品设置什么模式，都允许直接操作
  if (!modeInfo.shouldUseWorkplaceMode) {
    return {
      allowed: true,
      reason: '单职场环境，允许直接库存操作'
    };
  }
  
  // 多职场环境下，只有单一库存模式的商品才允许直接操作
  if (product.stockManagementType === 'single') {
    return {
      allowed: true,
      reason: '单一库存模式，允许直接库存操作'
    };
  }
  
  return {
    allowed: false,
    reason: '多职场环境下的职场分配模式商品，请通过库存管理页面调整各职场库存'
  };
}

/**
 * 同步商品库存到职场库存（仅在单职场环境下）
 * @param {number} productId - 商品ID
 * @param {number} newStock - 新库存数量
 * @param {Object} transaction - 数据库事务对象（可选）
 * @returns {Promise<boolean>} 返回是否成功同步
 */
async function syncStockToWorkplace(productId, newStock, transaction = null) {
  try {
    const modeInfo = await getRecommendedStockMode(transaction);
    
    // 只在单职场环境下进行同步
    if (modeInfo.shouldUseWorkplaceMode) {
      return false; // 多职场环境不进行自动同步
    }
    
    const { ProductWorkplaceStock } = require('../models');
    
    // 更新职场库存
    const [updatedCount] = await ProductWorkplaceStock.update(
      { stock: newStock },
      { 
        where: { productId },
        transaction 
      }
    );
    
    console.log(`同步商品 ${productId} 库存到职场: ${newStock}, 更新记录数: ${updatedCount}`);
    return updatedCount > 0;
    
  } catch (error) {
    console.error('同步库存到职场失败:', error);
    return false;
  }
}

/**
 * 批量更新商品的库存管理模式（基于当前职场数量）
 * @param {Object} transaction - 数据库事务对象（可选）
 * @returns {Promise<Object>} 返回更新结果
 */
async function updateAllProductsStockMode(transaction = null) {
  try {
    const modeInfo = await getRecommendedStockMode(transaction);
    const { Product } = require('../models');
    
    const [updatedCount] = await Product.update(
      { 
        stockManagementType: modeInfo.recommendedMode,
        stockSyncedAt: modeInfo.recommendedMode === 'workplace' ? new Date() : null
      },
      { 
        where: {},
        transaction 
      }
    );
    
    return {
      success: true,
      updatedCount,
      newMode: modeInfo.recommendedMode,
      workplaceCount: modeInfo.workplaceCount,
      message: `已将 ${updatedCount} 个商品更新为 ${modeInfo.description}`
    };
    
  } catch (error) {
    console.error('批量更新商品库存模式失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  getRecommendedStockMode,
  canDirectlyUpdateStock,
  syncStockToWorkplace,
  updateAllProductsStockMode
};
