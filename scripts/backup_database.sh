#!/bin/bash

# ====================================
# 数据库备份脚本
# ====================================
# 此脚本用于备份服务器上的MySQL数据库到本地
# 使用方法：./scripts/backup_database.sh

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务器配置
SERVER_IP="**************"
SERVER_USER="root"
SERVER_PASSWORD="Aa@123456"
DB_USER="root"
DB_PASSWORD="password"
DB_NAME="feishu_mall"

# 本地备份目录
BACKUP_DIR="database_backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="${BACKUP_DIR}/feishu_mall_backup_${TIMESTAMP}.sql"

echo -e "${BLUE}=====================================${NC}"
echo -e "${BLUE}     数据库备份脚本启动${NC}"
echo -e "${BLUE}=====================================${NC}"

# 创建备份目录
if [ ! -d "$BACKUP_DIR" ]; then
    echo -e "${YELLOW}创建备份目录: $BACKUP_DIR${NC}"
    mkdir -p "$BACKUP_DIR"
fi

echo -e "${YELLOW}开始备份数据库...${NC}"
echo -e "${YELLOW}服务器: $SERVER_IP${NC}"
echo -e "${YELLOW}数据库: $DB_NAME${NC}"
echo -e "${YELLOW}备份文件: $BACKUP_FILE${NC}"

# 执行备份
ssh "$SERVER_USER@$SERVER_IP" "mysqldump -u $DB_USER -p$DB_PASSWORD $DB_NAME" > "$BACKUP_FILE"

# 检查备份是否成功
if [ $? -eq 0 ] && [ -s "$BACKUP_FILE" ]; then
    # 获取文件大小
    FILE_SIZE=$(ls -lh "$BACKUP_FILE" | awk '{print $5}')
    LINE_COUNT=$(wc -l < "$BACKUP_FILE")
    
    echo -e "${GREEN}=====================================${NC}"
    echo -e "${GREEN}     备份成功完成！${NC}"
    echo -e "${GREEN}=====================================${NC}"
    echo -e "${GREEN}备份文件: $BACKUP_FILE${NC}"
    echo -e "${GREEN}文件大小: $FILE_SIZE${NC}"
    echo -e "${GREEN}行数: $LINE_COUNT${NC}"
    echo -e "${GREEN}备份时间: $(date)${NC}"
    
    # 显示备份文件列表
    echo -e "\n${BLUE}当前所有备份文件:${NC}"
    ls -lht "$BACKUP_DIR"/*.sql | head -10
    
    # 清理旧备份（保留最近10个）
    BACKUP_COUNT=$(ls -1 "$BACKUP_DIR"/*.sql 2>/dev/null | wc -l)
    if [ "$BACKUP_COUNT" -gt 10 ]; then
        echo -e "\n${YELLOW}清理旧备份文件（保留最近10个）...${NC}"
        ls -t "$BACKUP_DIR"/*.sql | tail -n +11 | xargs rm -f
        echo -e "${GREEN}清理完成${NC}"
    fi
    
else
    echo -e "${RED}=====================================${NC}"
    echo -e "${RED}     备份失败！${NC}"
    echo -e "${RED}=====================================${NC}"
    echo -e "${RED}请检查网络连接和服务器配置${NC}"
    
    # 删除空的备份文件
    if [ -f "$BACKUP_FILE" ] && [ ! -s "$BACKUP_FILE" ]; then
        rm -f "$BACKUP_FILE"
        echo -e "${YELLOW}已删除空的备份文件${NC}"
    fi
    
    exit 1
fi

echo -e "\n${BLUE}备份脚本执行完成${NC}"
