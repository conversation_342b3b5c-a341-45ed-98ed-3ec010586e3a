<template>
  <div class="hot-product-management">
    <div class="page-header">
      <h1>热门商品管理</h1>
      <p class="page-description">配置和管理自动热门商品识别规则</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6" v-for="(stat, key) in stats" :key="key">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="24">
                  <Trophy v-if="key === 'all'" />
                  <Calendar v-else-if="key === '30d'" />
                  <Clock v-else-if="key === '7d'" />
                  <Sunny v-else />
                </el-icon>
              </div>
              <div class="stat-info">
                <h3>{{ stat.timeRangeLabel }}</h3>
                <p class="stat-number">{{ stat.currentCount }}/{{ stat.maxCount }}</p>
                <p class="stat-status" :class="{ 'enabled': stat.enabled, 'disabled': !stat.enabled }">
                  {{ stat.enabled ? '已启用' : '已禁用' }}
                </p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 配置表单 -->
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>热门商品规则配置</span>
          <div class="header-actions">
            <el-button type="primary" @click="triggerUpdate" :loading="updating">
              <el-icon><Refresh /></el-icon>
              手动更新
            </el-button>
            <el-button type="success" @click="saveConfigs" :loading="saving">
              <el-icon><Check /></el-icon>
              保存配置
            </el-button>
          </div>
        </div>
      </template>

      <el-form :model="configForm" label-width="120px">
        <div v-for="config in configForm.configs" :key="config.timeRange" class="config-section">
          <div class="config-header">
            <h3>
              <el-icon class="config-icon">
                <Trophy v-if="config.timeRange === 'all'" />
                <Calendar v-else-if="config.timeRange === '30d'" />
                <Clock v-else-if="config.timeRange === '7d'" />
                <Sunny v-else />
              </el-icon>
              {{ getTimeRangeLabel(config.timeRange) }}
            </h3>
            <el-switch
              v-model="config.enabled"
              active-text="启用"
              inactive-text="禁用"
              @change="onConfigChange"
            />
          </div>

          <el-row :gutter="20" v-if="config.enabled">
            <el-col :span="8">
              <el-form-item label="热门商品数量">
                <el-input-number
                  v-model="config.maxCount"
                  :min="1"
                  :max="50"
                  @change="onConfigChange"
                />
                <div class="form-tip">该时间维度的热门商品数量上限</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="最小兑换量">
                <el-input-number
                  v-model="config.minExchangeCount"
                  :min="0"
                  @change="onConfigChange"
                />
                <div class="form-tip">商品成为热门的最小兑换量要求</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="兑换量权重">
                <el-input-number
                  v-model="config.exchangeWeight"
                  :min="0"
                  :max="10"
                  :step="0.1"
                  :precision="1"
                  @change="onConfigChange"
                />
                <div class="form-tip">兑换量在热门度计算中的权重</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" v-if="config.enabled">
            <el-col :span="8">
              <el-form-item label="库存权重">
                <el-input-number
                  v-model="config.stockWeight"
                  :min="0"
                  :max="10"
                  :step="0.1"
                  :precision="1"
                  @change="onConfigChange"
                />
                <div class="form-tip">库存奖励在热门度计算中的权重</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="自动更新">
                <el-switch
                  v-model="config.autoUpdateEnabled"
                  active-text="开启"
                  inactive-text="关闭"
                  @change="onConfigChange"
                />
                <div class="form-tip">是否自动定时更新热门商品</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="更新频率">
                <el-input
                  v-model="config.updateFrequency"
                  placeholder="0 */1 * * *"
                  @input="onConfigChange"
                />
                <div class="form-tip">Cron表达式，如：0 */1 * * * (每小时)</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </el-card>

    <!-- 热门商品预览 -->
    <el-card class="preview-card">
      <template #header>
        <div class="card-header">
          <span>当前热门商品</span>
          <el-select v-model="selectedTimeRange" @change="loadHotProducts" style="width: 150px">
            <el-option
              v-for="option in timeRangeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>
      </template>

      <div v-if="hotProducts.length > 0" class="hot-products-grid">
        <div v-for="(product, index) in hotProducts" :key="product.id" class="hot-product-item">
          <div class="rank-badge">{{ index + 1 }}</div>
          <div class="product-info">
            <h4>{{ product.name }}</h4>
            <p class="product-stats">
              评分: {{ product.hotScore }} | 兑换量: {{ product.exchangeCount }}
            </p>
            <div class="product-tags">
              <el-tag v-if="product.isHot" type="warning" size="small">手动热门</el-tag>
              <el-tag v-if="product.isAutoHot" type="success" size="small">自动热门</el-tag>
              <el-tag v-if="product.isNew" type="primary" size="small">新品</el-tag>
            </div>
          </div>
        </div>
      </div>
      <el-empty v-else description="暂无热门商品" />
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Trophy, Calendar, Clock, Sunny, Refresh, Check } from '@element-plus/icons-vue'
import { hotProductApi } from '../../api/hotProduct'

// 响应式数据
const stats = ref({})
const configForm = reactive({
  configs: []
})
const hotProducts = ref([])
const selectedTimeRange = ref('all')
const saving = ref(false)
const updating = ref(false)

// 时间维度选项
const timeRangeOptions = [
  { value: 'all', label: '累积热门' },
  { value: '30d', label: '30天热门' },
  { value: '7d', label: '7天热门' },
  { value: '1d', label: '今日热门' }
]

// 获取时间维度标签
const getTimeRangeLabel = (timeRange) => {
  const option = timeRangeOptions.find(opt => opt.value === timeRange)
  return option ? option.label : timeRange
}

// 加载统计数据
const loadStats = async () => {
  try {
    const response = await hotProductApi.getStats()
    console.log('统计数据API响应:', response)

    // 由于API响应拦截器返回response.data，所以这里直接使用response
    if (response && typeof response === 'object') {
      stats.value = response
    } else {
      console.warn('统计数据API响应无效:', response)
      stats.value = {}
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error(`加载统计数据失败: ${error.message || '未知错误'}`)
    stats.value = {}
  }
}

// 加载配置
const loadConfigs = async () => {
  try {
    const response = await hotProductApi.getConfigs()
    console.log('配置数据API响应:', response)

    // 由于API响应拦截器返回response.data，所以这里直接使用response
    if (response && Array.isArray(response)) {
      configForm.configs = response
    } else {
      console.warn('配置数据API响应格式异常:', response)
      configForm.configs = []
    }
  } catch (error) {
    console.error('加载配置失败:', error)
    ElMessage.error(`加载配置失败: ${error.message || '未知错误'}`)
    configForm.configs = []
  }
}

// 加载热门商品
const loadHotProducts = async () => {
  try {
    const response = await hotProductApi.getHotProducts(selectedTimeRange.value)
    console.log('热门商品API响应:', response)

    // 由于API响应拦截器返回response.data，所以这里直接使用response
    if (response && typeof response === 'object' && response.products && Array.isArray(response.products)) {
      hotProducts.value = response.products
    } else {
      console.warn('热门商品API响应格式异常:', response)
      hotProducts.value = []
    }
  } catch (error) {
    console.error('加载热门商品失败:', error)
    ElMessage.error(`加载热门商品失败: ${error.message || '未知错误'}`)
    hotProducts.value = []
  }
}

// 配置变更处理
const onConfigChange = () => {
  // 可以在这里添加实时预览逻辑
}

// 保存配置
const saveConfigs = async () => {
  try {
    saving.value = true
    await hotProductApi.updateConfigs(configForm.configs)
    ElMessage.success('配置保存成功')
    await loadStats()
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    saving.value = false
  }
}

// 手动触发更新
const triggerUpdate = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要手动更新所有时间维度的热门商品吗？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    updating.value = true
    const response = await hotProductApi.triggerUpdate()
    ElMessage.success(response.data.message)

    // 刷新数据
    await Promise.all([
      loadStats(),
      loadHotProducts()
    ])
  } catch (error) {
    if (error !== 'cancel') {
      console.error('手动更新失败:', error)
      ElMessage.error('手动更新失败')
    }
  } finally {
    updating.value = false
  }
}

// 初始化
onMounted(async () => {
  await Promise.all([
    loadStats(),
    loadConfigs(),
    loadHotProducts()
  ])
})
</script>

<style scoped>
.hot-product-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 16px;
  color: #409eff;
}

.stat-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #303133;
}

.stat-number {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: bold;
  color: #409eff;
}

.stat-status {
  margin: 0;
  font-size: 12px;
}

.stat-status.enabled {
  color: #67c23a;
}

.stat-status.disabled {
  color: #f56c6c;
}

.config-card,
.preview-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.config-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.config-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  color: #303133;
}

.config-icon {
  margin-right: 8px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.hot-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.hot-product-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #fafafa;
}

.rank-badge {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 12px;
  flex-shrink: 0;
}

.product-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.product-stats {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #606266;
}

.product-tags {
  display: flex;
  gap: 4px;
}
</style>
