# ====================================
# 开发环境配置文件 (server/.env.development)
# ====================================
# 此文件用于本地开发环境，请勿在生产环境使用
# 生产环境请使用 server/.env.production 文件
# 测试环境请使用 server/.env.test 文件
# ====================================
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=feishu_mall

# 应用配置
NODE_ENV=development
PORT=3000
SERVER_URL=http://localhost:3000

# JWT配置
JWT_SECRET=development_secret_key_please_change_in_production
JWT_EXPIRES_IN=1h
JWT_LONG_EXPIRES_IN=30d

# 飞书应用配置
FEISHU_APP_ID=cli_a66b3b2dcab8d013
FEISHU_APP_SECRET=5Fa8aatAGZ2Dv6K5VZhAWhbhjzE4lT2r
FEISHU_REDIRECT_URI=http://localhost:3000/api/feishu/callback

# 飞书机器人（测试环境）
FEISHU_BOT_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/e6eed328-0e65-4127-9a78-a3d1eb421136

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880

# ====================================
# CORS跨域配置
# ====================================
# 允许的前端域名（开发环境：localhost + 本机IP用于移动设备测试）
CORS_ORIGIN=http://localhost:5173,http://localhost:5174,http://localhost:8080,http://127.0.0.1:5173,http://**************:5173
# 允许的HTTP方法
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS

# 日志配置
LOG_LEVEL=info

# 前端URL配置（用于移动端飞书登录回调）
FRONTEND_URL=http://localhost:5173

# 环境检测配置
# 本地开发环境URL配置
DEV_SERVER_URL=http://localhost:3000
DEV_API_URL=http://localhost:3000/api

# 生产环境URL配置（用于环境检测和URL修正）
PROD_SERVER_URL=https://store-api.chongyangqisi.com
PROD_API_URL=https://store-api.chongyangqisi.com/api
PROD_FRONTEND_URL=https://store.chongyangqisi.com

# 旧服务器IP（用于兼容性检测）
OLD_SERVER_IP=**************
