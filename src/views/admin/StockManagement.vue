<template>
  <div class="stock-management">
    <!-- 离线警告 -->
    <el-alert
      v-if="showOfflineWarning"
      title="网络连接已断开"
      description="当前处于离线状态，库存操作可能无法正常执行。请检查网络连接。"
      type="warning"
      :closable="false"
      show-icon
      class="offline-warning"
    />

    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <i class="fas fa-warehouse"></i>
          库存管理
        </h1>
        <p class="page-description">管理商品在各职场的库存分配和转移</p>
      </div>
      <div class="header-actions">
        <el-button
          v-if="hasStockManagementPermission('undo')"
          type="warning"
          icon="el-icon-refresh-left"
          @click="undoLastOperation"
          :disabled="recentOperations.length === 0"
          title="撤销最近的操作"
          size="small"
        >
          撤销操作
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-refresh"
          @click="forceRefreshData"
          :loading="loading"
          title="刷新库存数据"
        >
          刷新数据
        </el-button>

        <!-- 数据同步状态指示器 -->
        <div class="sync-status" v-if="!isOnline">
          <el-icon class="sync-icon offline"><Warning /></el-icon>
          <span class="sync-text">离线模式</span>
        </div>
        <div class="sync-status" v-else-if="loading">
          <el-icon class="sync-icon syncing is-loading"><Loading /></el-icon>
          <span class="sync-text">同步中...</span>
        </div>
        <div class="sync-status" v-else>
          <el-icon class="sync-icon online"><CircleCheck /></el-icon>
          <span class="sync-text">已同步</span>
        </div>
        <el-button
          v-if="hasStockManagementPermission('logs')"
          type="success"
          icon="el-icon-document"
          @click="showOperationLogs"
        >
          操作日志
        </el-button>
        <el-button
          type="info"
          icon="el-icon-question"
          @click="showHelpDialogFunc"
          title="查看操作帮助"
          size="small"
        >
          帮助
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stats-card">
        <div class="stats-icon">
          <i class="fas fa-boxes"></i>
        </div>
        <div class="stats-content">
          <div class="stats-value">{{ totalProducts }}</div>
          <div class="stats-label">管理商品</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-icon">
          <i class="fas fa-building"></i>
        </div>
        <div class="stats-content">
          <div class="stats-value">{{ totalWorkplaces }}</div>
          <div class="stats-label">活跃职场</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-icon">
          <i class="fas fa-cubes"></i>
        </div>
        <div class="stats-content">
          <div class="stats-value">{{ totalStock }}</div>
          <div class="stats-label">总库存</div>
        </div>
      </div>
      <div class="stats-card alert" v-if="alertCount > 0">
        <div class="stats-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="stats-content">
          <div class="stats-value">{{ alertCount }}</div>
          <div class="stats-label">库存告警</div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filters">
      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索商品名称..."
          prefix-icon="el-icon-search"
          @input="handleSearch"
          clearable
        />
      </div>
      <div class="filter-box">
        <el-select
          v-model="selectedCategory"
          placeholder="选择分类"
          @change="handleCategoryChange"
          clearable
        >
          <el-option
            v-for="category in categories"
            :key="category.id"
            :label="category.name"
            :value="category.id"
          />
        </el-select>
      </div>
      <div class="filter-box">
        <el-select
          v-model="selectedWorkplace"
          placeholder="选择职场"
          @change="handleWorkplaceChange"
          clearable
        >
          <el-option
            v-for="workplace in workplaces"
            :key="workplace.id"
            :label="workplace.name"
            :value="workplace.id"
          />
        </el-select>
      </div>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedProducts.length > 0 && hasStockManagementPermission('batch')">
        <el-button-group>
          <el-button
            type="primary"
            size="small"
            @click="showBatchEditDialogFunc"
          >
            批量编辑 ({{ selectedProducts.length }})
          </el-button>
          <el-button
            type="success"
            size="small"
            @click="batchQuickAdjust('add', 10)"
          >
            批量+10
          </el-button>
          <el-button
            type="warning"
            size="small"
            @click="batchQuickAdjust('subtract', 10)"
          >
            批量-10
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 商品库存列表 -->
    <div class="stock-table-container">
      <el-table
        :data="filteredProducts"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        :default-expand-all="false"
        row-key="id"
        class="stock-management-table"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="expand">
          <template #default="{ row }">
            <StockDetailPanel 
              :product="row"
              @update-stock="handleUpdateStock"
              @transfer-stock="handleTransferStock"
            />
          </template>
        </el-table-column>
        
        <el-table-column prop="id" label="ID" />

        <el-table-column label="商品信息">
          <template #default="{ row }">
            <div class="product-info">
              <img
                :src="fixImageUrl(row.imageUrl)"
                :alt="row.name"
                class="product-image"
                @error="handleImageError"
              />
              <div class="product-details">
                <div class="product-name">{{ row.name }}</div>
                <div class="product-category">{{ row.Category?.name || '未分类' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="总库存" align="center">
          <template #default="{ row }">
            <div class="stock-summary">
              <div class="total-stock">
                <span class="stock-value">{{ row.totalStock || row.stock || 0 }}</span>
                <div class="quick-stock-actions" v-if="row.workplaceStocks && row.workplaceStocks.length > 0">
                  <el-button-group size="small">
                    <el-button
                      size="small"
                      type="success"
                      @click="quickAdjustStock(row, 'add', 1)"
                      title="增加1个库存"
                    >
                      +1
                    </el-button>
                    <el-button
                      size="small"
                      type="success"
                      @click="quickAdjustStock(row, 'add', 10)"
                      title="增加10个库存"
                    >
                      +10
                    </el-button>
                    <el-button
                      size="small"
                      type="warning"
                      @click="quickAdjustStock(row, 'subtract', 1)"
                      title="减少1个库存"
                      :disabled="(row.totalStock || row.stock || 0) < 1"
                    >
                      -1
                    </el-button>
                    <el-button
                      size="small"
                      type="warning"
                      @click="quickAdjustStock(row, 'subtract', 10)"
                      title="减少10个库存"
                      :disabled="(row.totalStock || row.stock || 0) < 10"
                    >
                      -10
                    </el-button>
                  </el-button-group>
                </div>
                <div class="no-workplace-allocation" v-else>
                  <el-tooltip content="请先进行职场库存分配后再使用快速调整功能" placement="top">
                    <span class="allocation-hint">未分配职场</span>
                  </el-tooltip>
                </div>
              </div>
              <div class="available-stock">可用: {{ row.totalAvailableStock || row.stock || 0 }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="职场分布">
          <template #default="{ row }">
            <div class="workplace-stocks">
              <div 
                v-for="stock in row.workplaceStocks || []"
                :key="stock.workplaceId"
                class="workplace-stock-item"
                :class="{ 'low-stock': stock.isLowStock }"
              >
                <span class="workplace-name">{{ stock.workplaceName }}</span>
                <span class="stock-amount">{{ stock.availableStock }}</span>
                <el-tag
                  v-if="stock.isLowStock"
                  type="warning"
                  size="small"
                >
                  告警
                </el-tag>
              </div>
              <div v-if="!row.workplaceStocks || row.workplaceStocks.length === 0" class="no-workplace-stocks">
                <div class="stock-allocation-guide">
                  <el-icon class="guide-icon"><InfoFilled /></el-icon>
                  <div class="guide-content">
                    <span class="guide-text">未分配职场库存</span>
                    <span class="guide-hint">请前往库存管理操作，手动编辑分配各职场库存</span>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" align="center">
          <template #default="{ row }">
            <el-tag
              :type="getStockStatusType(row)"
              size="small"
            >
              {{ getStockStatusText(row) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          width="240"
          align="center"
          fixed="right"
          class-name="operation-column"
        >
          <template #default="{ row }">
            <div class="operation-buttons">
              <el-button
                v-if="hasStockManagementPermission('edit')"
                type="primary"
                size="small"
                @click="editStock(row)"
                class="op-btn"
              >
                编辑
              </el-button>
              <el-button
                v-if="hasStockManagementPermission('transfer')"
                type="success"
                size="small"
                @click="transferStock(row)"
                class="op-btn"
              >
                转移
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="viewProductOperationHistory(row)"
                class="op-btn"
                title="查看该商品的操作历史"
              >
                操作历史
              </el-button>
              <span v-if="!hasStockManagementPermission('edit') && !hasStockManagementPermission('transfer')" class="no-permission-hint">
                无操作权限
              </span>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
        />
      </div>
    </div>

    <!-- 库存编辑对话框 -->
    <StockEditDialog
      v-model="showEditDialog"
      :product="selectedProduct"
      :workplaces="workplaces"
      @confirm="handleStockUpdate"
    />

    <!-- 库存转移对话框 -->
    <StockTransferDialog
      ref="transferDialogRef"
      v-model="showTransferDialog"
      :product="selectedProduct"
      :workplaces="workplaces"
      @confirm="handleStockTransfer"
    />

    <!-- 批量编辑对话框 -->
    <BatchStockEditDialog
      v-model="showBatchEditDialog"
      :products="selectedProducts"
      :workplaces="workplaces"
      @confirm="handleBatchStockEdit"
    />

    <!-- 操作日志对话框 -->
    <OperationLogsDialog
      v-model="showLogsDialog"
    />

    <!-- 商品操作历史对话框 -->
    <ProductOperationHistoryDialog
      v-model="showProductHistoryDialog"
      :product="selectedProductForHistory"
    />

    <!-- 职场选择对话框 -->
    <el-dialog
      v-model="showWorkplaceSelectDialogVisible"
      title="选择目标职场"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="workplace-select-content">
        <div class="operation-info">
          <p><strong>商品:</strong> {{ workplaceSelectData.productName }}</p>
          <p><strong>操作:</strong> {{ workplaceSelectData.operationText }} {{ workplaceSelectData.amount }} 个</p>
        </div>

        <div class="workplace-select-form">
          <el-form-item label="选择目标职场:" required>
            <el-select
              v-model="selectedWorkplaceId"
              placeholder="请选择职场"
              style="width: 100%"
              size="large"
            >
              <el-option
                v-for="option in workplaceSelectData.options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              >
                <div class="workplace-option">
                  <span class="workplace-name">{{ option.workplaceName }}</span>
                  <span class="workplace-stock">当前: {{ option.currentStock }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </div>

        <div class="operation-tip">
          <el-alert
            title="操作提示"
            description="请选择要调整库存的具体职场，操作将只影响该职场的库存。"
            type="info"
            :closable="false"
            show-icon
          />
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelWorkplaceSelect">取消</el-button>
          <el-button
            type="primary"
            @click="confirmWorkplaceSelect"
            :disabled="!selectedWorkplaceId"
          >
            下一步
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 帮助对话框 -->
    <el-dialog
      v-model="showHelpDialog"
      title="库存管理操作帮助"
      width="700px"
      :close-on-click-modal="false"
    >
      <div class="help-content">
        <div class="help-section">
          <h4><i class="fas fa-mouse-pointer"></i> 快速操作</h4>
          <ul>
            <li><strong>快速调整：</strong>鼠标悬停在总库存数字上，会显示快速调整按钮（+1, +10, -1, -10）</li>
            <li><strong>职场库存要求：</strong>快速调整功能需要商品已分配职场库存，未分配的商品会显示"未分配职场"提示</li>
            <li><strong>批量选择：</strong>勾选表格左侧的复选框，可以选择多个商品进行批量操作</li>
            <li><strong>撤销操作：</strong>点击页面顶部的"撤销操作"按钮可以撤销最近的库存调整</li>
          </ul>
        </div>

        <div class="help-section">
          <h4><i class="fas fa-edit"></i> 编辑功能</h4>
          <ul>
            <li><strong>单个编辑：</strong>点击"编辑"按钮可以详细编辑单个商品的职场库存分配</li>
            <li><strong>批量编辑：</strong>选择多个商品后，点击"批量编辑"可以同时调整多个商品的库存</li>
            <li><strong>库存转移：</strong>点击"转移"按钮可以在不同职场之间转移库存</li>
          </ul>
        </div>

        <div class="help-section">
          <h4><i class="fas fa-shield-alt"></i> 安全提示</h4>
          <ul>
            <li><strong>操作确认：</strong>所有库存变更操作都需要确认，请仔细核对后再执行</li>
            <li><strong>操作原因：</strong>系统会要求填写操作原因，用于审计和追踪</li>
            <li><strong>操作记录：</strong>所有操作都会记录在操作日志中，可以随时查看</li>
          </ul>
        </div>

        <div class="help-section">
          <h4><i class="fas fa-search"></i> 筛选和搜索</h4>
          <ul>
            <li><strong>商品搜索：</strong>在搜索框中输入商品名称进行快速查找</li>
            <li><strong>分类筛选：</strong>选择商品分类来筛选特定类别的商品</li>
            <li><strong>职场筛选：</strong>选择职场来查看该职场的库存分布</li>
          </ul>
        </div>

        <div class="help-section">
          <h4><i class="fas fa-exclamation-triangle"></i> 注意事项</h4>
          <ul>
            <li>库存调整会立即生效，请谨慎操作</li>
            <li>减少库存时，系统会检查是否有足够的库存</li>
            <li>批量操作会同时影响多个商品，请确认选择正确</li>
            <li>如有疑问，请联系系统管理员</li>
          </ul>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { InfoFilled, Warning, Loading, CircleCheck } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import {
  getProductsWithStocks,
  getWorkplaces,
  getStockAlerts,
  updateProductWorkplaceStocks,
  transferStock,
  saveOperationRecord
} from '@/api/stockManagement'
import { getCategories } from '@/api/categories'
import { fixImageUrl, getDefaultProductImage } from '@/utils/imageUtils'
import StockDetailPanel from '@/components/admin/StockDetailPanel.vue'
import StockEditDialog from '@/components/admin/StockEditDialog.vue'
import StockTransferDialog from '@/components/admin/StockTransferDialog.vue'
import BatchStockEditDialog from '@/components/admin/BatchStockEditDialog.vue'
import OperationLogsDialog from '@/components/admin/OperationLogsDialog.vue'
import ProductOperationHistoryDialog from '@/components/admin/ProductOperationHistoryDialog.vue'

export default {
  name: 'StockManagement',
  components: {
    StockDetailPanel,
    StockEditDialog,
    StockTransferDialog,
    BatchStockEditDialog,
    OperationLogsDialog,
    ProductOperationHistoryDialog,
    InfoFilled,
    Warning,
    Loading,
    CircleCheck
  },
  setup() {
    // 组件引用
    const transferDialogRef = ref(null)

    // 响应式数据
    const loading = ref(false)
    const products = ref([])
    const workplaces = ref([])
    const categories = ref([])
    const alerts = ref([])
    
    // 搜索和筛选
    const searchKeyword = ref('')
    const selectedCategory = ref(null)
    const selectedWorkplace = ref(null)
    
    // 分页
    const currentPage = ref(1)
    const pageSize = ref(20)
    const totalCount = ref(0)
    
    // 对话框状态
    const showEditDialog = ref(false)
    const showTransferDialog = ref(false)
    const showLogsDialog = ref(false)
    const showBatchEditDialog = ref(false)
    const showHelpDialog = ref(false)
    const showProductHistoryDialog = ref(false)
    const selectedProduct = ref(null)
    const selectedProducts = ref([])
    const selectedProductForHistory = ref(null)

    // 操作历史记录
    const recentOperations = ref([])
    const maxRecentOperations = 10

    // 网络状态
    const isOnline = ref(navigator.onLine)
    const showOfflineWarning = ref(false)

    // 职场选择对话框
    const showWorkplaceSelectDialogVisible = ref(false)
    const selectedWorkplaceId = ref('')
    const workplaceSelectData = ref({
      productName: '',
      operationText: '',
      amount: 0,
      options: []
    })
    const workplaceSelectResolve = ref(null)
    const workplaceSelectReject = ref(null)

    // 计算属性
    const totalProducts = computed(() => products.value.length)
    const totalWorkplaces = computed(() => workplaces.value.filter(w => w.isActive).length)
    const totalStock = computed(() => {
      return products.value.reduce((sum, product) => {
        return sum + (product.totalStock || product.stock || 0)
      }, 0)
    })
    const alertCount = computed(() => alerts.value.length)

    const filteredProducts = computed(() => {
      let filtered = products.value

      // 搜索过滤
      if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase()
        filtered = filtered.filter(product => 
          product.name.toLowerCase().includes(keyword)
        )
      }

      // 分类过滤
      if (selectedCategory.value) {
        filtered = filtered.filter(product => 
          product.categoryId === selectedCategory.value
        )
      }

      // 职场过滤
      if (selectedWorkplace.value) {
        filtered = filtered.filter(product => 
          product.workplaceStocks?.some(stock => 
            stock.workplaceId === selectedWorkplace.value
          )
        )
      }

      return filtered
    })

    // 方法
    const loadData = async () => {
      loading.value = true
      try {
        const [productsRes, workplacesRes, categoriesRes, alertsRes] = await Promise.all([
          getProductsWithStocks({ 
            page: currentPage.value, 
            limit: pageSize.value 
          }),
          getWorkplaces(),
          getCategories(),
          getStockAlerts()
        ])

        products.value = productsRes.data || []
        totalCount.value = productsRes.total || 0
        workplaces.value = workplacesRes.data || []
        categories.value = categoriesRes.data || []
        alerts.value = alertsRes.data || []

      } catch (error) {
        console.error('加载数据失败:', error)
        ElMessage.error('加载数据失败')
      } finally {
        loading.value = false
      }
    }

    const forceRefreshData = async () => {
      console.log('🔄 执行强制刷新...')

      // 清除浏览器缓存
      if ('caches' in window) {
        try {
          const cacheNames = await caches.keys()
          for (const cacheName of cacheNames) {
            await caches.delete(cacheName)
          }
          console.log('✅ 已清除浏览器缓存')
        } catch (error) {
          console.warn('清除缓存失败:', error)
        }
      }

      // 重置页面状态
      currentPage.value = 1

      // 清空现有数据，强制重新渲染
      products.value = []
      workplaces.value = []
      categories.value = []
      alerts.value = []

      // 等待DOM更新
      await nextTick()

      // 强制重新加载数据，使用时间戳参数防止缓存
      loading.value = true
      try {
        const timestamp = Date.now()
        const [productsRes, workplacesRes, categoriesRes, alertsRes] = await Promise.all([
          getProductsWithStocks({
            page: currentPage.value,
            limit: pageSize.value,
            _forceRefresh: timestamp
          }),
          getWorkplaces(),
          getCategories(),
          getStockAlerts()
        ])

        products.value = productsRes.data || []
        totalCount.value = productsRes.total || 0
        workplaces.value = workplacesRes.data || []
        categories.value = categoriesRes.data || []
        alerts.value = alertsRes.data || []

        console.log('✅ 强制刷新完成，商品数量:', products.value.length)
      } catch (error) {
        console.error('强制刷新失败:', error)
        ElMessage.error('强制刷新失败')
      } finally {
        loading.value = false
      }
    }

    const handleSearch = () => {
      // 搜索时重置到第一页
      currentPage.value = 1
    }

    const handleCategoryChange = () => {
      currentPage.value = 1
    }

    const handleWorkplaceChange = () => {
      currentPage.value = 1
    }

    const handleSizeChange = (size) => {
      pageSize.value = size
      loadData()
    }

    const handleCurrentChange = (page) => {
      currentPage.value = page
      loadData()
    }

    const getStockStatusType = (product) => {
      const totalStock = product.totalAvailableStock || product.stock || 0
      if (totalStock === 0) return 'danger'
      if (product.workplaceStocks?.some(stock => stock.isLowStock)) return 'warning'
      return 'success'
    }

    const getStockStatusText = (product) => {
      const totalStock = product.totalAvailableStock || product.stock || 0
      if (totalStock === 0) return '缺货'
      if (product.workplaceStocks?.some(stock => stock.isLowStock)) return '告警'
      return '正常'
    }

    const editStock = (product) => {
      selectedProduct.value = product
      showEditDialog.value = true
    }

    const showTransferDialog_func = (product) => {
      selectedProduct.value = product
      showTransferDialog.value = true
    }

    const showOperationLogs = () => {
      // 权限检查
      if (!hasStockManagementPermission('logs')) {
        ElMessage.error('您没有查看操作日志的权限')
        return
      }
      showLogsDialog.value = true
    }

    const showHelpDialogFunc = () => {
      showHelpDialog.value = true
    }

    // 查看商品操作历史
    const viewProductOperationHistory = (product) => {
      selectedProductForHistory.value = product
      showProductHistoryDialog.value = true
    }

    const handleImageError = (event) => {
      event.target.src = getDefaultProductImage('80x80')
    }



    const handleStockUpdate = async (updateData) => {
      try {
        // 添加加载状态
        const loadingMessage = ElMessage({
          message: '正在更新库存...',
          type: 'info',
          duration: 0,
          showClose: false
        })

        const result = await updateProductWorkplaceStocks(selectedProduct.value.id, updateData)

        // 关闭加载消息
        loadingMessage.close()

        // 检查API响应
        if (result && result.success !== false) {
          ElMessage.success({
            message: '库存更新成功',
            duration: 3000,
            showClose: true
          })

          // 记录操作
          addOperationRecord({
            type: 'stock_update',
            productId: selectedProduct.value.id,
            productName: selectedProduct.value.name,
            operation: 'update',
            reason: updateData.reason,
            timestamp: new Date()
          })

          showEditDialog.value = false
          await loadData() // 等待数据刷新完成
        } else {
          throw new Error(result?.message || '更新失败')
        }
      } catch (error) {
        console.error('库存更新失败:', error)

        // 显示详细错误信息
        const errorMessage = error.response?.data?.message || error.message || '库存更新失败'
        ElMessage.error({
          message: errorMessage,
          duration: 5000,
          showClose: true
        })

        // 如果是网络错误，提供重试选项
        if (error.code === 'NETWORK_ERROR' || error.response?.status >= 500) {
          ElMessageBox.confirm(
            '网络连接异常，是否重试？',
            '连接错误',
            {
              confirmButtonText: '重试',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).then(() => {
            handleStockUpdate(updateData)
          }).catch(() => {
            // 用户取消重试
          })
        }
      }
    }

    const handleStockTransfer = async (transferData) => {
      try {
        // 添加加载状态
        const loadingMessage = ElMessage({
          message: '正在转移库存...',
          type: 'info',
          duration: 0,
          showClose: false
        })

        const result = await transferStock(transferData)

        // 关闭加载消息
        loadingMessage.close()

        // 检查API响应格式
        if (result && result.success !== false) {
          ElMessage.success({
            message: result.message || '库存转移成功',
            duration: 3000,
            showClose: true
          })

          // 记录操作
          addOperationRecord({
            type: 'stock_transfer',
            productId: selectedProduct.value.id,
            productName: selectedProduct.value.name,
            operation: 'transfer',
            fromWorkplace: transferData.fromWorkplaceId,
            toWorkplace: transferData.toWorkplaceId,
            quantity: transferData.quantity,
            reason: transferData.reason,
            timestamp: new Date()
          })
        } else {
          throw new Error(result?.message || '转移失败')
        }

        showTransferDialog.value = false
        await forceRefreshData() // 刷新数据
      } catch (error) {
        console.error('库存转移失败:', error)

        // 显示具体的错误信息
        const errorMessage = error.response?.data?.message || error.message || '库存转移失败'
        ElMessage.error({
          message: errorMessage,
          duration: 5000,
          showClose: true
        })

        // 如果是网络错误，提供重试选项
        if (error.code === 'NETWORK_ERROR' || error.response?.status >= 500) {
          ElMessageBox.confirm(
            '网络连接异常，是否重试？',
            '连接错误',
            {
              confirmButtonText: '重试',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).then(() => {
            handleStockTransfer(transferData)
          }).catch(() => {
            // 用户取消重试
          })
        }
      } finally {
        // 重置转移对话框的loading状态
        if (transferDialogRef.value && transferDialogRef.value.resetLoading) {
          transferDialogRef.value.resetLoading()
        }
      }
    }

    const handleUpdateStock = () => {
      // 从详情面板触发的更新
      loadData()
    }

    const handleTransferStock = () => {
      // 从详情面板触发的转移
      loadData()
    }

    // 快速调整库存
    const quickAdjustStock = async (product, operation, amount) => {
      try {
        // 权限检查
        if (!hasStockManagementPermission('edit')) {
          ElMessage.error('您没有库存编辑权限')
          return
        }

        // 数量限制检查
        if (!checkOperationLimits(amount)) {
          return
        }

        // 数据验证
        const currentStock = product.totalStock || product.stock || 0

        // 检查减少操作是否会导致负库存
        if (operation === 'subtract' && currentStock < amount) {
          ElMessage.warning(`当前库存(${currentStock})不足，无法减少${amount}个`)
          return
        }

        // 高风险操作检查（大量调整）
        const riskLevel = amount >= 50 ? 'high' : amount >= 10 ? 'medium' : 'low'
        if (riskLevel === 'high') {
          const confirmed = await requireSecondaryConfirmation('high')
          if (!confirmed) return
        }

        // 显示职场选择和确认对话框
        const operationText = operation === 'add' ? '增加' : '减少'

        // 创建职场选择对话框
        await showWorkplaceSelectionDialog(product, operation, amount, operationText)
      } catch (error) {
        // 用户取消操作
        console.log('用户取消了快速库存调整')
      }
    }

    // 权限控制系统
    const userPermissions = ref({
      canViewStock: true,
      canEditStock: true,
      canTransferStock: true,
      canBatchEdit: true,
      canDeleteStock: false,
      canViewLogs: true,
      maxStockAdjustment: 100, // 单次最大调整数量
      requiresApproval: false, // 是否需要审批
      canUndoOperations: true
    })

    // 显示职场选择对话框
    const showWorkplaceSelectionDialog = async (product, operation, amount, operationText) => {
      // 获取商品的职场库存信息
      const workplaceOptions = product.workplaceStocks.map(stock => ({
        label: `${stock.workplaceName} (当前: ${stock.stock})`,
        value: stock.workplaceId,
        currentStock: stock.stock,
        workplaceName: stock.workplaceName
      }))

      let selectedWorkplaceId = null

      // 如果只有一个职场，直接使用该职场
      if (workplaceOptions.length === 1) {
        selectedWorkplaceId = workplaceOptions[0].value
      } else {
        // 多个职场时，使用自定义对话框让用户选择
        selectedWorkplaceId = await showWorkplaceSelectDialog(product, workplaceOptions, amount, operationText)
      }

      // 获取选中职场的信息
      const selectedWorkplace = workplaceOptions.find(w => w.value === selectedWorkplaceId)
      if (!selectedWorkplace) {
        throw new Error('无效的职场选择')
      }

      // 计算职场库存变化
      const currentWorkplaceStock = selectedWorkplace.currentStock
      const newWorkplaceStock = operation === 'add'
        ? currentWorkplaceStock + amount
        : Math.max(0, currentWorkplaceStock - amount)

      // 显示最终确认对话框
      const confirmMessage = `
        <div style="text-align: left;">
          <p><strong>商品:</strong> ${product.name}</p>
          <p><strong>目标职场:</strong> ${selectedWorkplace.workplaceName}</p>
          <p><strong>操作:</strong> ${operationText} ${amount} 个</p>
          <p><strong>职场库存变化:</strong> ${currentWorkplaceStock} → ${newWorkplaceStock}</p>
          <p style="color: #f56c6c; margin-top: 10px;">此操作将立即生效，请确认无误后继续。</p>
        </div>
      `

      await ElMessageBox.confirm(confirmMessage, '快速库存调整确认', {
        confirmButtonText: '确定执行',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      })

      // 执行库存调整
      await performQuickStockAdjustmentWithWorkplace(product, operation, amount, selectedWorkplaceId)

      // 记录操作历史
      addOperationRecord({
        type: 'quick_adjust',
        productId: product.id,
        productName: product.name,
        operation,
        amount,
        workplaceId: selectedWorkplaceId,
        workplaceName: selectedWorkplace.workplaceName,
        oldStock: currentWorkplaceStock,
        newStock: newWorkplaceStock,
        timestamp: new Date()
      })

      ElMessage.success({
        message: `${product.name} 在 ${selectedWorkplace.workplaceName} 的库存${operationText}成功`,
        duration: 3000,
        showClose: true
      })

      loadData() // 刷新数据
    }

    // 显示职场选择对话框（使用Element Plus组件）
    const showWorkplaceSelectDialog = (product, workplaceOptions, amount, operationText) => {
      return new Promise((resolve, reject) => {
        // 设置对话框数据
        workplaceSelectData.value = {
          productName: product.name,
          operationText,
          amount,
          options: workplaceOptions
        }

        // 重置选择
        selectedWorkplaceId.value = ''

        // 设置Promise回调
        workplaceSelectResolve.value = resolve
        workplaceSelectReject.value = reject

        // 显示对话框
        showWorkplaceSelectDialogVisible.value = true
      })
    }

    // 确认职场选择
    const confirmWorkplaceSelect = () => {
      if (!selectedWorkplaceId.value) {
        ElMessage.warning('请选择目标职场')
        return
      }

      showWorkplaceSelectDialogVisible.value = false
      if (workplaceSelectResolve.value) {
        workplaceSelectResolve.value(selectedWorkplaceId.value)
      }
    }

    // 取消职场选择
    const cancelWorkplaceSelect = () => {
      showWorkplaceSelectDialogVisible.value = false
      if (workplaceSelectReject.value) {
        workplaceSelectReject.value(new Error('用户取消选择职场'))
      }
    }

    // 显示批量职场选择对话框
    const showBatchWorkplaceSelectDialog = (products, workplaceOptions, amount, operationText) => {
      return new Promise((resolve, reject) => {
        // 设置对话框数据
        workplaceSelectData.value = {
          productName: `${products.length} 个商品`,
          operationText: `批量${operationText}`,
          amount,
          options: workplaceOptions
        }

        // 重置选择
        selectedWorkplaceId.value = ''

        // 设置Promise回调
        workplaceSelectResolve.value = resolve
        workplaceSelectReject.value = reject

        // 显示对话框
        showWorkplaceSelectDialogVisible.value = true
      })
    }

    // 检查库存管理权限
    const hasStockManagementPermission = (action = 'edit') => {
      switch (action) {
        case 'view':
          return userPermissions.value.canViewStock
        case 'edit':
          return userPermissions.value.canEditStock
        case 'transfer':
          return userPermissions.value.canTransferStock
        case 'batch':
          return userPermissions.value.canBatchEdit
        case 'delete':
          return userPermissions.value.canDeleteStock
        case 'logs':
          return userPermissions.value.canViewLogs
        case 'undo':
          return userPermissions.value.canUndoOperations
        default:
          return false
      }
    }

    // 检查操作数量限制
    const checkOperationLimits = (amount, operation = 'adjust') => {
      if (operation === 'adjust' && amount > userPermissions.value.maxStockAdjustment) {
        ElMessage.error(`单次调整数量不能超过 ${userPermissions.value.maxStockAdjustment} 个`)
        return false
      }
      return true
    }

    // 敏感操作二次确认
    const requireSecondaryConfirmation = async (riskLevel = 'medium') => {
      const confirmMessages = {
        low: '请确认此操作',
        medium: '此操作将影响库存数据，请仔细确认',
        high: '⚠️ 高风险操作！此操作可能对业务造成重大影响，请务必确认无误！'
      }

      try {
        await ElMessageBox.confirm(
          confirmMessages[riskLevel],
          '安全确认',
          {
            confirmButtonText: '我已确认，继续执行',
            cancelButtonText: '取消操作',
            type: riskLevel === 'high' ? 'error' : 'warning',
            customClass: riskLevel === 'high' ? 'high-risk-confirm' : ''
          }
        )
        return true
      } catch {
        return false
      }
    }

    // 添加操作记录
    const addOperationRecord = (operation) => {
      const userId = getCurrentUserId()
      const userName = getCurrentUserName()

      // 创建完整的操作记录对象
      const operationRecord = {
        id: generateOperationId(),
        timestamp: new Date().toISOString(),
        userId: userId,
        userName: userName,
        type: operation.type,
        operation: operation.operation,
        productId: operation.productId,
        productName: operation.productName,
        workplaceId: operation.workplaceId || null,
        workplaceName: operation.workplaceName || null,
        fromWorkplaceId: operation.fromWorkplace || null,
        toWorkplaceId: operation.toWorkplace || null,
        quantity: operation.quantity || operation.amount || null,
        oldStock: operation.oldStock || null,
        newStock: operation.newStock || null,
        reason: operation.reason || '',
        details: operation.details || {},
        ipAddress: getClientIP(),
        userAgent: navigator.userAgent,
        sessionId: getSessionId(),
        // 批量操作相关
        productCount: operation.productCount || null,
        productNames: operation.productNames || null,
        // 状态信息
        status: 'completed',
        errorMessage: null
      }

      // 添加到本地记录
      recentOperations.value.unshift(operationRecord)

      // 保持最大记录数限制
      if (recentOperations.value.length > maxRecentOperations) {
        recentOperations.value = recentOperations.value.slice(0, maxRecentOperations)
      }

      // 保存到localStorage
      saveOperationRecordsToLocal()

      // 发送到服务器（如果有API）
      sendOperationRecordToServer(operationRecord)
    }

    // 生成操作ID
    const generateOperationId = () => {
      return 'op_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
    }

    // 获取当前用户ID
    const getCurrentUserId = () => {
      // 从authStore获取用户信息
      const authStore = useAuthStore()
      return authStore.user?.id || 'unknown'
    }

    // 获取当前用户名
    const getCurrentUserName = () => {
      // 从authStore获取用户信息
      const authStore = useAuthStore()
      return authStore.user?.username || '未知用户'
    }

    // 获取客户端IP（简化版）
    const getClientIP = () => {
      // 在实际应用中，这应该从服务器获取
      return 'unknown'
    }

    // 获取会话ID
    const getSessionId = () => {
      let sessionId = sessionStorage.getItem('sessionId')
      if (!sessionId) {
        sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
        sessionStorage.setItem('sessionId', sessionId)
      }
      return sessionId
    }

    // 保存操作记录到本地存储
    const saveOperationRecordsToLocal = () => {
      try {
        localStorage.setItem('stockManagement_recentOperations', JSON.stringify(recentOperations.value))
      } catch (error) {
        console.warn('无法保存操作记录到localStorage:', error)
      }
    }

    // 发送操作记录到服务器
    const sendOperationRecordToServer = async (operationRecord) => {
      try {
        // 调用实际的API保存操作记录
        await saveOperationRecord(operationRecord)
        console.log('操作记录已保存到服务器:', operationRecord)
      } catch (error) {
        console.error('发送操作记录到服务器失败:', error)
        // 将失败的记录存储到队列中，稍后重试
        const failedRecords = JSON.parse(localStorage.getItem('failedOperationRecords') || '[]')
        failedRecords.push(operationRecord)
        localStorage.setItem('failedOperationRecords', JSON.stringify(failedRecords))
      }
    }

    // 从localStorage加载操作记录
    const loadOperationRecords = () => {
      try {
        const saved = localStorage.getItem('stockManagement_recentOperations')
        if (saved) {
          const records = JSON.parse(saved)
          // 验证记录格式并过滤无效记录
          recentOperations.value = records.filter(record =>
            record && record.id && record.timestamp && record.type
          )
        }
      } catch (error) {
        console.warn('无法从localStorage加载操作记录:', error)
        recentOperations.value = []
      }
    }

    // 撤销最近的操作
    const undoLastOperation = async () => {
      // 权限检查
      if (!hasStockManagementPermission('undo')) {
        ElMessage.error('您没有撤销操作权限')
        return
      }

      if (recentOperations.value.length === 0) {
        ElMessage.warning('没有可撤销的操作')
        return
      }

      const lastOperation = recentOperations.value[0]

      try {
        await ElMessageBox.confirm(
          `确定要撤销对 ${lastOperation.productName} 的库存操作吗？`,
          '撤销操作确认',
          {
            confirmButtonText: '确定撤销',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        // 执行撤销操作
        const product = products.value.find(p => p.id === lastOperation.productId)
        if (product) {
          // 计算撤销操作
          const reverseOperation = lastOperation.operation === 'add' ? 'subtract' : 'add'
          await performQuickStockAdjustment(product, reverseOperation, lastOperation.amount)

          // 移除已撤销的操作记录
          recentOperations.value.shift()

          ElMessage.success('操作撤销成功')
          loadData()
        } else {
          ElMessage.error('找不到相关商品，无法撤销操作')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('撤销操作失败:', error)
          ElMessage.error('撤销操作失败')
        }
      }
    }

    // 执行针对特定职场的快速库存调整
    const performQuickStockAdjustmentWithWorkplace = async (product, operation, amount, targetWorkplaceId) => {
      // 检查商品是否有职场库存分配
      if (!product.workplaceStocks || product.workplaceStocks.length === 0) {
        throw new Error(`商品 "${product.name}" 尚未分配职场库存，请先进行职场库存分配后再进行快速调整`)
      }

      // 找到目标职场的库存信息
      const targetWorkplace = product.workplaceStocks.find(stock => stock.workplaceId === targetWorkplaceId)
      if (!targetWorkplace) {
        throw new Error('找不到指定的职场库存信息')
      }

      // 计算新的库存值
      const currentWorkplaceStock = targetWorkplace.stock
      let newWorkplaceStock

      if (operation === 'add') {
        newWorkplaceStock = currentWorkplaceStock + amount
      } else if (operation === 'subtract') {
        newWorkplaceStock = Math.max(0, currentWorkplaceStock - amount)
        if (currentWorkplaceStock < amount) {
          throw new Error(`${targetWorkplace.workplaceName} 当前库存(${currentWorkplaceStock})不足，无法减少${amount}个`)
        }
      } else {
        throw new Error('无效的操作类型')
      }

      // 构建库存更新数据 - 只更新目标职场，其他职场保持不变
      const stockUpdates = product.workplaceStocks.map(stock => {
        if (stock.workplaceId === targetWorkplaceId) {
          return {
            workplaceId: stock.workplaceId,
            stock: newWorkplaceStock,
            minStockAlert: stock.minStockAlert || 10,
            maxStockLimit: stock.maxStockLimit
          }
        } else {
          return {
            workplaceId: stock.workplaceId,
            stock: stock.stock, // 保持原有库存
            minStockAlert: stock.minStockAlert || 10,
            maxStockLimit: stock.maxStockLimit
          }
        }
      })

      // 调用库存更新API
      await updateProductWorkplaceStocks(product.id, {
        stockUpdates,
        reason: `快速${operation === 'add' ? '增加' : '减少'}库存 ${amount} 个 (职场: ${targetWorkplace.workplaceName})`
      })
    }

    // 执行快速库存调整的具体逻辑（保留原有的按比例分配逻辑，用于批量操作）
    const performQuickStockAdjustment = async (product, operation, amount) => {
      // 计算新的库存值
      const currentStock = product.totalStock || product.stock || 0
      let newStock

      if (operation === 'add') {
        newStock = currentStock + amount
      } else if (operation === 'subtract') {
        newStock = Math.max(0, currentStock - amount) // 确保库存不为负数
      } else {
        throw new Error('无效的操作类型')
      }

      // 检查商品是否有职场库存分配
      if (!product.workplaceStocks || product.workplaceStocks.length === 0) {
        // 没有职场库存分配，提示用户先进行分配
        throw new Error(`商品 "${product.name}" 尚未分配职场库存，请先进行职场库存分配后再进行快速调整`)
      }

      // 按比例调整各职场库存
      const stockUpdates = product.workplaceStocks.map(stock => {
        const ratio = currentStock > 0 ? stock.stock / currentStock : 1 / product.workplaceStocks.length
        const adjustedStock = Math.round(newStock * ratio)

        return {
          workplaceId: stock.workplaceId,
          stock: adjustedStock,
          minStockAlert: stock.minStockAlert || 10,
          maxStockLimit: stock.maxStockLimit
        }
      })

      // 调用库存更新API
      await updateProductWorkplaceStocks(product.id, {
        stockUpdates,
        reason: `快速${operation === 'add' ? '增加' : '减少'}库存 ${amount} 个`
      })
    }

    // 处理表格选择变化
    const handleSelectionChange = (selection) => {
      selectedProducts.value = selection
    }

    // 显示批量编辑对话框
    const showBatchEditDialogFunc = () => {
      console.log('批量编辑按钮被点击')
      console.log('选中商品数量:', selectedProducts.value.length)
      console.log('批量编辑权限:', hasStockManagementPermission('batch'))

      // 权限检查
      if (!hasStockManagementPermission('batch')) {
        ElMessage.error('您没有批量编辑权限')
        return
      }

      if (selectedProducts.value.length === 0) {
        ElMessage.warning('请先选择要编辑的商品')
        return
      }

      // 批量操作风险提示
      if (selectedProducts.value.length > 10) {
        ElMessage.warning('批量操作商品数量较多，请谨慎操作')
      }

      console.log('准备显示批量编辑对话框')
      showBatchEditDialog.value = true
      console.log('批量编辑对话框状态:', showBatchEditDialog.value)
    }

    // 批量快速调整
    const batchQuickAdjust = async (operation, amount) => {
      // 权限检查
      if (!hasStockManagementPermission('batch')) {
        ElMessage.error('您没有批量操作权限')
        return
      }

      if (selectedProducts.value.length === 0) {
        ElMessage.warning('请先选择要调整的商品')
        return
      }

      // 数量限制检查
      if (!checkOperationLimits(amount)) {
        return
      }

      // 批量操作风险评估
      const totalAffectedStock = selectedProducts.value.reduce((sum, product) => {
        return sum + (product.totalStock || product.stock || 0)
      }, 0)

      const riskLevel = selectedProducts.value.length > 20 || totalAffectedStock > 1000 ? 'high' : 'medium'

      if (riskLevel === 'high') {
        const confirmed = await requireSecondaryConfirmation('high')
        if (!confirmed) return
      }

      try {
        const operationText = operation === 'add' ? '增加' : '减少'

        // 检查所有选中商品是否都有职场库存分配
        const productsWithoutWorkplace = selectedProducts.value.filter(product =>
          !product.workplaceStocks || product.workplaceStocks.length === 0
        )

        if (productsWithoutWorkplace.length > 0) {
          const productNames = productsWithoutWorkplace.map(p => p.name).join('、')
          ElMessage.error(`以下商品尚未分配职场库存，无法进行批量调整：${productNames}`)
          return
        }

        // 获取所有可用的职场（取所有商品的职场交集）
        const allWorkplaces = new Map()
        selectedProducts.value.forEach(product => {
          product.workplaceStocks.forEach(stock => {
            if (!allWorkplaces.has(stock.workplaceId)) {
              allWorkplaces.set(stock.workplaceId, {
                workplaceId: stock.workplaceId,
                workplaceName: stock.workplaceName,
                totalStock: 0,
                productCount: 0
              })
            }
            const workplace = allWorkplaces.get(stock.workplaceId)
            workplace.totalStock += stock.stock
            workplace.productCount += 1
          })
        })

        // 转换为选项数组
        const workplaceOptions = Array.from(allWorkplaces.values())
          .filter(workplace => workplace.productCount === selectedProducts.value.length) // 只显示所有商品都有的职场
          .map(workplace => ({
            label: `${workplace.workplaceName} (总库存: ${workplace.totalStock})`,
            value: workplace.workplaceId,
            workplaceName: workplace.workplaceName,
            currentStock: workplace.totalStock
          }))

        if (workplaceOptions.length === 0) {
          ElMessage.error('选中的商品没有共同的职场库存分配，无法进行批量调整')
          return
        }

        // 选择目标职场
        let selectedWorkplaceId
        if (workplaceOptions.length === 1) {
          selectedWorkplaceId = workplaceOptions[0].value
        } else {
          selectedWorkplaceId = await showBatchWorkplaceSelectDialog(selectedProducts.value, workplaceOptions, amount, operationText)
        }

        // 获取选中职场信息
        const selectedWorkplace = workplaceOptions.find(w => w.value === selectedWorkplaceId)

        // 最终确认
        await ElMessageBox.confirm(
          `确定要为选中的 ${selectedProducts.value.length} 个商品在 ${selectedWorkplace.workplaceName} 职场${operationText}库存 ${amount} 个吗？\n\n⚠️ 此操作将立即生效且不可撤销`,
          '批量库存调整确认',
          {
            confirmButtonText: '确定执行',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        )

        // 批量执行库存调整
        const promises = selectedProducts.value.map(product =>
          performQuickStockAdjustmentWithWorkplace(product, operation, amount, selectedWorkplaceId)
        )

        await Promise.all(promises)

        // 记录批量操作历史
        addOperationRecord({
          type: 'batch_quick_adjust',
          operation,
          amount,
          workplaceId: selectedWorkplaceId,
          workplaceName: selectedWorkplace.workplaceName,
          productCount: selectedProducts.value.length,
          productNames: selectedProducts.value.map(p => p.name).join('、'),
          timestamp: new Date()
        })

        ElMessage.success(`批量${operationText}库存成功`)
        selectedProducts.value = [] // 清空选择
        loadData() // 刷新数据
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量库存调整失败:', error)
          ElMessage.error(`批量库存调整失败: ${error.message || '未知错误'}`)
        }
      }
    }

    // 处理批量库存编辑
    const handleBatchStockEdit = async (batchData) => {
      try {
        console.log('批量编辑数据:', batchData)

        // 根据预览数据执行批量更新
        const promises = batchData.previewData.map(async (item) => {
          const product = selectedProducts.value.find(p => p.id === item.productId)
          if (!product) return

          // 构建库存更新数据
          const stockUpdates = []

          if (batchData.applyScope === 'total') {
            // 更新总库存 - 如果有职场库存，按比例分配
            if (product.workplaceStocks && product.workplaceStocks.length > 0) {
              const totalCurrentStock = product.workplaceStocks.reduce((sum, stock) => sum + stock.stock, 0)

              product.workplaceStocks.forEach(stock => {
                const ratio = totalCurrentStock > 0 ? stock.stock / totalCurrentStock : 1 / product.workplaceStocks.length
                const newStock = Math.round(item.newStock * ratio)

                stockUpdates.push({
                  workplaceId: stock.workplaceId,
                  stock: newStock,
                  minStockAlert: stock.minStockAlert || 10,
                  maxStockLimit: stock.maxStockLimit
                })
              })
            } else {
              // 没有职场库存，直接设置总库存
              stockUpdates.push({
                workplaceId: null,
                stock: item.newStock,
                minStockAlert: 10,
                maxStockLimit: null
              })
            }
          } else if (batchData.applyScope === 'workplace') {
            // 更新特定职场库存
            if (product.workplaceStocks) {
              product.workplaceStocks.forEach(stock => {
                if (stock.workplaceId === batchData.targetWorkplaceId) {
                  // 更新目标职场
                  stockUpdates.push({
                    workplaceId: stock.workplaceId,
                    stock: item.newStock,
                    minStockAlert: stock.minStockAlert || 10,
                    maxStockLimit: stock.maxStockLimit
                  })
                } else {
                  // 保持其他职场不变
                  stockUpdates.push({
                    workplaceId: stock.workplaceId,
                    stock: stock.stock,
                    minStockAlert: stock.minStockAlert || 10,
                    maxStockLimit: stock.maxStockLimit
                  })
                }
              })
            }
          }

          // 调用API更新库存
          return updateProductWorkplaceStocks(product.id, {
            stockUpdates,
            reason: batchData.reason
          })
        })

        await Promise.all(promises)

        // 记录批量编辑操作
        addOperationRecord({
          type: 'batch_edit',
          operation: batchData.operationType,
          productCount: selectedProducts.value.length,
          productNames: selectedProducts.value.map(p => p.name).join('、'),
          workplaceId: batchData.applyScope === 'workplace' ? batchData.targetWorkplaceId : null,
          workplaceName: batchData.applyScope === 'workplace' ?
            workplaces.value.find(w => w.id === batchData.targetWorkplaceId)?.name : null,
          reason: batchData.reason,
          details: {
            operationType: batchData.operationType,
            value: batchData.value,
            applyScope: batchData.applyScope,
            previewData: batchData.previewData
          }
        })

        ElMessage.success('批量库存编辑成功')
        showBatchEditDialog.value = false
        selectedProducts.value = [] // 清空选择
        loadData() // 刷新数据
      } catch (error) {
        console.error('批量库存编辑失败:', error)
        ElMessage.error(`批量库存编辑失败: ${error.message || '未知错误'}`)
      }
    }

    // 网络状态监听
    const handleOnline = () => {
      isOnline.value = true
      showOfflineWarning.value = false
      ElMessage.success('网络连接已恢复')
      // 网络恢复后重新加载数据
      loadData()
    }

    const handleOffline = () => {
      isOnline.value = false
      showOfflineWarning.value = true
      ElMessage.warning('网络连接已断开，部分功能可能无法使用')
    }

    // 生命周期
    onMounted(() => {
      loadData()
      loadOperationRecords()

      // 添加网络状态监听
      window.addEventListener('online', handleOnline)
      window.addEventListener('offline', handleOffline)
    })

    // 清理监听器
    onUnmounted(() => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    })

    return {
      // 组件引用
      transferDialogRef,

      // 响应式数据
      loading,
      products,
      workplaces,
      categories,
      alerts,
      searchKeyword,
      selectedCategory,
      selectedWorkplace,
      currentPage,
      pageSize,
      totalCount,
      showEditDialog,
      showTransferDialog,
      showLogsDialog,
      showBatchEditDialog,
      showHelpDialog,
      showProductHistoryDialog,
      selectedProduct,
      selectedProducts,
      selectedProductForHistory,
      recentOperations,
      userPermissions,
      isOnline,
      showOfflineWarning,
      showWorkplaceSelectDialogVisible,
      selectedWorkplaceId,
      workplaceSelectData,
      confirmWorkplaceSelect,
      cancelWorkplaceSelect,
      
      // 计算属性
      totalProducts,
      totalWorkplaces,
      totalStock,
      alertCount,
      filteredProducts,
      
      // 方法
      forceRefreshData,
      handleSearch,
      handleCategoryChange,
      handleWorkplaceChange,
      handleSizeChange,
      handleCurrentChange,
      getStockStatusType,
      getStockStatusText,
      editStock,
      transferStock: showTransferDialog_func,
      showOperationLogs,
      showHelpDialog,
      showHelpDialogFunc,
      handleImageError,
      handleStockUpdate,
      handleStockTransfer,
      handleUpdateStock,
      handleTransferStock,
      quickAdjustStock,
      handleSelectionChange,
      showBatchEditDialog,
      showBatchEditDialogFunc,
      batchQuickAdjust,
      handleBatchStockEdit,
      undoLastOperation,
      hasStockManagementPermission,
      viewProductOperationHistory,
      fixImageUrl,
      getDefaultProductImage
    }
  }
}
</script>

<style scoped>
.stock-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 离线警告样式 */
.offline-warning {
  margin-bottom: 20px;
  border-radius: 8px;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content .page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-content .page-title i {
  color: #409eff;
}

.page-description {
  color: #909399;
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 同步状态指示器 */
.sync-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.sync-icon.online {
  color: #67c23a;
}

.sync-icon.offline {
  color: #f56c6c;
}

.sync-icon.syncing {
  color: #409eff;
}

.sync-text {
  color: #606266;
  font-weight: 500;
}

/* 职场选择对话框样式 */
.workplace-select-content {
  padding: 10px 0;
}

.operation-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.operation-info p {
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
}

.workplace-select-form {
  margin-bottom: 20px;
}

.workplace-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.workplace-name {
  font-weight: 500;
  color: #303133;
}

.workplace-stock {
  font-size: 12px;
  color: #909399;
  background-color: #f0f2f5;
  padding: 2px 8px;
  border-radius: 12px;
}

.operation-tip {
  margin-top: 15px;
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stats-card {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.2s;
}

.stats-card:hover {
  transform: translateY(-2px);
}

.stats-card.alert {
  border-left: 4px solid #f56c6c;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;
  font-size: 20px;
}

.stats-card.alert .stats-icon {
  background: linear-gradient(135deg, #f56c6c, #e6a23c);
}

.stats-content {
  flex: 1;
}

.stats-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

/* 搜索筛选 */
.search-filters {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-box {
  flex: 1;
  max-width: 300px;
}

.filter-box {
  min-width: 150px;
}

/* 批量操作 */
.batch-actions {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.batch-actions .el-button-group .el-button {
  font-size: 12px;
  padding: 8px 12px;
}

/* 表格容器 */
.stock-table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

/* 商品信息 */
.product-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-image {
  width: 48px;
  height: 48px;
  border-radius: 6px;
  object-fit: cover;
  border: 1px solid #ebeef5;
}

.product-details {
  flex: 1;
}

.product-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.product-category {
  font-size: 12px;
  color: #909399;
}

/* 库存汇总 */
.stock-summary {
  text-align: center;
}

.total-stock {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.stock-value {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.quick-stock-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.stock-summary:hover .quick-stock-actions {
  opacity: 1;
}

.quick-stock-actions .el-button-group .el-button {
  padding: 2px 6px;
  font-size: 11px;
  border-radius: 2px;
}

.quick-stock-actions .el-button-group .el-button:first-child {
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}

.quick-stock-actions .el-button-group .el-button:last-child {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}

.available-stock {
  font-size: 12px;
  color: #67c23a;
  margin-top: 2px;
}

/* 未分配职场提示 */
.no-workplace-allocation {
  margin-top: 4px;
}

.allocation-hint {
  font-size: 11px;
  color: #f56c6c;
  background-color: #fef0f0;
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid #fbc4c4;
  cursor: help;
}

/* 职场库存分布 */
.workplace-stocks {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.workplace-stock-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #f0f9ff;
  border: 1px solid #e1f5fe;
  border-radius: 4px;
  font-size: 12px;
}

.workplace-stock-item.low-stock {
  background: #fef7e0;
  border-color: #fde68a;
}

.workplace-name {
  color: #606266;
  font-weight: 500;
}

.stock-amount {
  color: #303133;
  font-weight: 600;
}

.no-workplace-stocks {
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin: 4px 0;
}

.stock-allocation-guide {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.guide-icon {
  color: #409eff;
  font-size: 16px;
  margin-top: 1px;
  flex-shrink: 0;
}

.guide-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.guide-text {
  color: #606266;
  font-size: 13px;
  font-weight: 500;
}

.guide-hint {
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

.text-muted {
  color: #c0c4cc;
}

/* 分页 */
.pagination-container {
  padding: 20px;
  display: flex;
  justify-content: center;
}

/* 表格容器样式 */
.stock-table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: visible;
  position: relative;

  /* 确保容器有足够的空间显示固定列 */
  .el-table {
    position: relative;
  }
}

/* 主表格样式 */
.stock-management-table {
  width: 100%;

  /* 确保表格能够正常显示所有数据 */
  min-height: auto;

  /* 固定列样式优化 */
  :deep(.el-table__fixed-right) {
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
    z-index: 10;
    background-color: #fff;
  }

  /* 操作列样式 */
  :deep(.operation-column) {
    background-color: #fff;
  }

  /* 固定列单元格样式 */
  :deep(.el-table__fixed-right .el-table__cell) {
    background-color: #fff;
    border-right: none;
  }
}

/* 表格行高度优化 */
.stock-management-table :deep(.el-table__row) {
  min-height: 64px;
}

/* 表格单元格内容对齐 */
.stock-management-table :deep(.el-table .cell) {
  padding: 8px 12px;
  line-height: 1.4;
}

/* 表格滚动条样式 */
.stock-management-table :deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
}

.stock-management-table :deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 4px;
}

.stock-management-table :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 4px;
}

.stock-management-table :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}

/* 操作按钮容器优化 */
.operation-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

/* 操作按钮样式优化 */
.op-btn {
  padding: 6px 12px;
  font-size: 12px;
}

/* 帮助对话框样式 */
.help-content {
  max-height: 60vh;
  overflow-y: auto;
}

.help-section {
  margin-bottom: 24px;
}

.help-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.help-section h4 i {
  color: #409eff;
}

.help-section ul {
  margin: 0;
  padding-left: 20px;
}

.help-section li {
  margin-bottom: 8px;
  line-height: 1.6;
  color: #606266;
}

.help-section li strong {
  color: #303133;
}

/* 权限和安全相关样式 */
.no-permission-hint {
  color: #c0c4cc;
  font-size: 12px;
  font-style: italic;
}

/* 高风险操作确认对话框样式 */
:deep(.high-risk-confirm) {
  .el-message-box__header {
    background-color: #fef0f0;
    border-bottom: 1px solid #fbc4c4;
  }

  .el-message-box__title {
    color: #f56c6c;
    font-weight: 600;
  }

  .el-message-box__content {
    color: #f56c6c;
  }

  .el-button--primary {
    background-color: #f56c6c;
    border-color: #f56c6c;
  }

  .el-button--primary:hover {
    background-color: #f78989;
    border-color: #f78989;
  }
}

/* 权限受限的操作按钮样式 */
.operation-buttons .el-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}


</style>
