# 活动促销功能前端设计

## 1. 组件架构设计

### 1.1 管理后台组件

```
src/views/admin/promotions/
├── PromotionManagement.vue          # 活动管理主页面
├── PromotionForm.vue                # 活动创建/编辑表单
├── PromotionList.vue                # 活动列表组件
├── PromotionDetail.vue              # 活动详情页面
├── PromotionStatistics.vue          # 活动统计分析
└── components/
    ├── PromotionCard.vue            # 活动卡片组件
    ├── RuleEditor.vue               # 规则编辑器
    ├── ProductSelector.vue          # 商品选择器
    ├── ParticipantsList.vue         # 参与者列表
    └── StatisticsChart.vue          # 统计图表组件
```

### 1.2 用户端组件

```
src/views/promotions/
├── PromotionCenter.vue              # 活动中心页面
├── PromotionDetail.vue              # 活动详情页面
└── components/
    ├── PromotionBanner.vue          # 活动横幅
    ├── PromotionCard.vue            # 活动卡片
    ├── DiscountCalculator.vue       # 优惠计算器
    └── ParticipationHistory.vue     # 参与历史

src/components/
├── PromotionTag.vue                 # 促销标签
├── DiscountBadge.vue                # 优惠徽章
└── PromotionModal.vue               # 活动弹窗
```

### 1.3 API封装

```
src/api/
└── promotions.js                    # 活动相关API
```

### 1.4 状态管理

```
src/stores/
└── promotions.js                    # 活动状态管理
```

## 2. 核心组件设计

### 2.1 活动管理主页面 (PromotionManagement.vue)

**功能特性**:
- 活动列表展示和筛选
- 活动状态管理（启用/暂停/结束）
- 快速创建活动入口
- 活动效果概览

**主要方法**:
```javascript
{
  // 数据获取
  fetchPromotions(),
  fetchPromotionStats(),
  
  // 状态管理
  togglePromotionStatus(id, status),
  deletePromotion(id),
  
  // 导航
  goToCreate(),
  goToEdit(id),
  goToDetail(id)
}
```

### 2.2 活动表单组件 (PromotionForm.vue)

**表单字段**:
```javascript
{
  basic: {
    name: '',
    description: '',
    type: 'discount',
    banner_image: ''
  },
  timing: {
    start_time: '',
    end_time: ''
  },
  limits: {
    max_participants: null,
    max_uses_per_user: 1
  },
  rules: {
    type: 'discount',
    config: {}
  },
  scope: {
    applicable_products: [],
    applicable_categories: []
  },
  display: {
    priority: 0,
    is_featured: false
  }
}
```

**验证规则**:
```javascript
{
  name: [
    { required: true, message: '请输入活动名称' },
    { min: 2, max: 100, message: '活动名称长度在2-100个字符' }
  ],
  start_time: [
    { required: true, message: '请选择开始时间' }
  ],
  end_time: [
    { required: true, message: '请选择结束时间' },
    { validator: validateEndTime }
  ]
}
```

### 2.3 规则编辑器 (RuleEditor.vue)

**支持的规则类型**:
- 折扣活动 (discount)
- 满减活动 (full_reduction)
- 限时优惠 (limited_time)
- 套餐优惠 (bundle)
- 积分倍数 (points_multiplier)

**组件结构**:
```vue
<template>
  <div class="rule-editor">
    <el-select v-model="ruleType" @change="onRuleTypeChange">
      <el-option label="折扣活动" value="discount" />
      <el-option label="满减活动" value="full_reduction" />
      <!-- 其他选项 -->
    </el-select>
    
    <!-- 动态规则配置组件 -->
    <component 
      :is="currentRuleComponent" 
      v-model="ruleConfig"
      @change="onRuleChange"
    />
  </div>
</template>
```

### 2.4 商品选择器 (ProductSelector.vue)

**功能特性**:
- 支持按分类筛选商品
- 支持搜索商品名称
- 支持批量选择
- 显示已选商品列表

**组件接口**:
```javascript
props: {
  modelValue: Array,  // 已选商品ID列表
  multiple: Boolean,  // 是否支持多选
  categories: Array   // 可选分类列表
},
emits: ['update:modelValue', 'change']
```

### 2.5 活动统计组件 (PromotionStatistics.vue)

**统计维度**:
- 参与人数趋势
- 订单数量趋势
- 收入统计
- 优惠金额统计
- 转化率分析

**图表类型**:
- 折线图：趋势分析
- 柱状图：对比分析
- 饼图：占比分析
- 数据卡片：关键指标

## 3. 用户端页面设计

### 3.1 活动中心页面 (PromotionCenter.vue)

**页面布局**:
```vue
<template>
  <div class="promotion-center">
    <!-- 精选活动轮播 -->
    <FeaturedPromotions />
    
    <!-- 活动分类导航 -->
    <PromotionCategories />
    
    <!-- 活动列表 -->
    <PromotionList />
    
    <!-- 我的参与记录 -->
    <MyParticipations />
  </div>
</template>
```

### 3.2 活动详情页面 (PromotionDetail.vue)

**页面内容**:
- 活动基本信息
- 活动规则说明
- 适用商品列表
- 参与按钮
- 参与记录

### 3.3 促销标签组件 (PromotionTag.vue)

**标签类型**:
- 折扣标签：显示折扣百分比
- 满减标签：显示满减金额
- 限时标签：显示倒计时
- 热门标签：显示参与人数

## 4. 状态管理设计

### 4.1 Pinia Store (promotions.js)

```javascript
export const usePromotionsStore = defineStore('promotions', {
  state: () => ({
    // 活动列表
    promotions: [],
    activePromotions: [],
    
    // 当前活动
    currentPromotion: null,
    
    // 统计数据
    statistics: {},
    
    // 加载状态
    loading: false,
    
    // 分页信息
    pagination: {
      page: 1,
      limit: 10,
      total: 0
    }
  }),
  
  getters: {
    // 获取活跃活动
    getActivePromotions: (state) => state.activePromotions,
    
    // 获取精选活动
    getFeaturedPromotions: (state) => 
      state.activePromotions.filter(p => p.is_featured),
    
    // 根据类型获取活动
    getPromotionsByType: (state) => (type) =>
      state.activePromotions.filter(p => p.type === type)
  },
  
  actions: {
    // 获取活动列表
    async fetchPromotions(params = {}) {
      this.loading = true;
      try {
        const response = await promotionsAPI.getPromotions(params);
        this.promotions = response.data.promotions;
        this.pagination = response.data.pagination;
      } finally {
        this.loading = false;
      }
    },
    
    // 获取活跃活动
    async fetchActivePromotions() {
      const response = await promotionsAPI.getActivePromotions();
      this.activePromotions = response.data.promotions;
    },
    
    // 参与活动
    async participatePromotion(promotionId, data) {
      return await promotionsAPI.participatePromotion(promotionId, data);
    }
  }
});
```

## 5. 路由设计

### 5.1 管理后台路由

```javascript
{
  path: '/admin/promotions',
  component: () => import('@/views/admin/promotions/PromotionManagement.vue'),
  meta: { requiresAuth: true, requiresAdmin: true, title: '活动管理' },
  children: [
    {
      path: 'create',
      component: () => import('@/views/admin/promotions/PromotionForm.vue'),
      meta: { title: '创建活动' }
    },
    {
      path: 'edit/:id',
      component: () => import('@/views/admin/promotions/PromotionForm.vue'),
      meta: { title: '编辑活动' }
    },
    {
      path: 'detail/:id',
      component: () => import('@/views/admin/promotions/PromotionDetail.vue'),
      meta: { title: '活动详情' }
    }
  ]
}
```

### 5.2 用户端路由

```javascript
{
  path: '/promotions',
  component: () => import('@/views/promotions/PromotionCenter.vue'),
  meta: { title: '活动中心' }
},
{
  path: '/promotions/:id',
  component: () => import('@/views/promotions/PromotionDetail.vue'),
  meta: { title: '活动详情' }
}
```
