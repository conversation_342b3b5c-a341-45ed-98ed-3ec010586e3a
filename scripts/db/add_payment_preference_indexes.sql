-- 支付偏好分析功能数据库索引优化脚本
-- 执行前请确保已备份数据库

USE feishu_mall;

-- 检查当前索引状态
SELECT
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX
FROM information_schema.STATISTICS
WHERE TABLE_SCHEMA = 'feishu_mall'
  AND TABLE_NAME = 'exchanges'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- 1. 为支付方式和创建时间创建复合索引（用于支付趋势分析）
-- 先检查索引是否存在，不存在则创建
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.STATISTICS
     WHERE TABLE_SCHEMA = 'feishu_mall'
       AND TABLE_NAME = 'exchanges'
       AND INDEX_NAME = 'idx_exchanges_payment_created') = 0,
    'CREATE INDEX idx_exchanges_payment_created ON exchanges(paymentMethod, createdAt, status)',
    'SELECT "索引 idx_exchanges_payment_created 已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 为用户ID和支付方式创建复合索引（用于用户分层分析）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.STATISTICS
     WHERE TABLE_SCHEMA = 'feishu_mall'
       AND TABLE_NAME = 'exchanges'
       AND INDEX_NAME = 'idx_exchanges_user_payment') = 0,
    'CREATE INDEX idx_exchanges_user_payment ON exchanges(userId, paymentMethod, createdAt)',
    'SELECT "索引 idx_exchanges_user_payment 已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 为商品ID和支付方式创建复合索引（用于分类偏好分析）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.STATISTICS
     WHERE TABLE_SCHEMA = 'feishu_mall'
       AND TABLE_NAME = 'exchanges'
       AND INDEX_NAME = 'idx_exchanges_product_payment') = 0,
    'CREATE INDEX idx_exchanges_product_payment ON exchanges(productId, paymentMethod, status)',
    'SELECT "索引 idx_exchanges_product_payment 已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 为状态和创建时间创建复合索引（用于有效订单筛选）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.STATISTICS
     WHERE TABLE_SCHEMA = 'feishu_mall'
       AND TABLE_NAME = 'exchanges'
       AND INDEX_NAME = 'idx_exchanges_status_created') = 0,
    'CREATE INDEX idx_exchanges_status_created ON exchanges(status, createdAt)',
    'SELECT "索引 idx_exchanges_status_created 已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 为总金额和支付方式创建复合索引（用于金额统计）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.STATISTICS
     WHERE TABLE_SCHEMA = 'feishu_mall'
       AND TABLE_NAME = 'exchanges'
       AND INDEX_NAME = 'idx_exchanges_amount_payment') = 0,
    'CREATE INDEX idx_exchanges_amount_payment ON exchanges(totalAmount, paymentMethod, status)',
    'SELECT "索引 idx_exchanges_amount_payment 已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证索引创建结果
SELECT
    'exchanges表索引创建完成' as message,
    COUNT(*) as index_count
FROM information_schema.STATISTICS
WHERE TABLE_SCHEMA = 'feishu_mall'
  AND TABLE_NAME = 'exchanges'
  AND INDEX_NAME LIKE 'idx_exchanges_%';

-- 显示新创建的索引
SELECT
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX,
    CARDINALITY
FROM information_schema.STATISTICS
WHERE TABLE_SCHEMA = 'feishu_mall'
  AND TABLE_NAME = 'exchanges'
  AND INDEX_NAME IN (
    'idx_exchanges_payment_created',
    'idx_exchanges_user_payment',
    'idx_exchanges_product_payment',
    'idx_exchanges_status_created',
    'idx_exchanges_amount_payment'
  )
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 分析表以更新统计信息
ANALYZE TABLE exchanges;

-- 显示优化建议
SELECT
    '索引优化完成' as status,
    '建议定期执行 ANALYZE TABLE exchanges 以保持统计信息准确性' as recommendation;
