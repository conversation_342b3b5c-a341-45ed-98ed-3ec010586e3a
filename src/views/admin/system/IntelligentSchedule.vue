<template>
  <ErrorBoundary>
    <div class="schedule-container">
    <el-card shadow="never" class="schedule-card">
      <template #header>
        <div class="card-header">
          <h2>智能发送时间控制</h2>
          <div class="header-right">
            <el-button type="primary" @click="handleCreateSchedule">
              <el-icon><Plus /></el-icon>新建调度
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索过滤 -->
      <div class="filter-container">
        <el-form :inline="true" :model="searchForm" class="demo-form-inline">
          <el-form-item label="调度名称">
            <el-input v-model="searchForm.search" placeholder="搜索调度名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="通知类型">
            <el-select v-model="searchForm.notificationType" placeholder="选择通知类型" clearable>
              <el-option label="订单消息" value="exchange"></el-option>
              <el-option label="系统通知" value="system"></el-option>
              <el-option label="用户消息" value="user"></el-option>
              <el-option label="日报周报" value="daily_report"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="调度类型">
            <el-select v-model="searchForm.scheduleType" placeholder="选择调度类型" clearable>
              <el-option label="固定时间" value="fixed"></el-option>
              <el-option label="智能调度" value="smart"></el-option>
              <el-option label="条件触发" value="conditional"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.enabled" placeholder="选择状态" clearable>
              <el-option label="启用" :value="true"></el-option>
              <el-option label="禁用" :value="false"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 调度列表 -->
      <el-table :data="scheduleList" style="width: 100%" v-loading="loading" border>
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="scheduleName" label="调度名称" min-width="150"></el-table-column>
        <el-table-column prop="notificationType" label="通知类型" width="120">
          <template #default="scope">
            <el-tag
              :type="getNotificationTypeTag(scope.row.notificationType)"
              effect="plain"
            >
              {{ getNotificationTypeName(scope.row.notificationType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="scheduleType" label="调度类型" width="120">
          <template #default="scope">
            <el-tag :type="getScheduleTypeTag(scope.row.scheduleType)">
              {{ getScheduleTypeName(scope.row.scheduleType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="cronExpression" label="Cron表达式" width="150">
          <template #default="scope">
            <span v-if="scope.row.cronExpression">{{ scope.row.cronExpression }}</span>
            <span v-else class="text-muted">--</span>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.priority > 10" type="danger" size="small">高 ({{ scope.row.priority }})</el-tag>
            <el-tag v-else-if="scope.row.priority > 5" type="warning" size="small">中 ({{ scope.row.priority }})</el-tag>
            <el-tag v-else type="info" size="small">低 ({{ scope.row.priority }})</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="enabled" label="状态" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.enabled"
              active-text="启用"
              inactive-text="禁用"
              @change="handleStatusChange(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleViewDetails(scope.row)">详情</el-button>
            <el-button size="small" @click="handleCalculateNextTime(scope.row)">下次发送</el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          v-model:current-page="page"
          v-model:page-size="pageSize"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </el-card>

    <!-- 系统指标卡片 -->
    <el-card shadow="never" class="metrics-card">
      <template #header>
        <div class="card-header">
          <h3>系统指标</h3>
          <el-button size="small" @click="fetchSystemMetrics">刷新</el-button>
        </div>
      </template>
      <div class="metrics-content" v-loading="metricsLoading">
        <div class="metric-item">
          <div class="metric-label">CPU 使用率</div>
          <el-progress
            :percentage="systemMetrics.cpu"
            :color="getCpuProgressColor(systemMetrics.cpu)"
            :format="format => `${format}%`"
          ></el-progress>
        </div>
        <div class="metric-item">
          <div class="metric-label">内存使用率</div>
          <el-progress
            :percentage="systemMetrics.memory"
            :color="getMemoryProgressColor(systemMetrics.memory)"
            :format="format => `${format}%`"
          ></el-progress>
        </div>
        <div class="metric-update-time" v-if="systemMetrics.lastUpdated">
          最后更新时间: {{ formatDate(systemMetrics.lastUpdated) }}
        </div>
      </div>
    </el-card>
    </div>
  </ErrorBoundary>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import axios from 'axios';
import ErrorBoundary from '@/components/ErrorBoundary.vue';

// 数据定义
const loading = ref(false);
const metricsLoading = ref(false);
const scheduleList = ref([]);
const total = ref(0);
const page = ref(1);
const pageSize = ref(10);
const systemMetrics = reactive({
  cpu: 0,
  memory: 0,
  lastUpdated: null
});

// 搜索表单
const searchForm = reactive({
  search: '',
  notificationType: '',
  scheduleType: '',
  enabled: undefined,
});

// 定时器引用
let metricsInterval = null;

// 初始化
onMounted(() => {
  fetchSchedules();
  fetchSystemMetrics();
  // 每60秒刷新一次系统指标
  metricsInterval = setInterval(fetchSystemMetrics, 60000);
});

// 组件销毁时清除定时器
onUnmounted(() => {
  if (metricsInterval) {
    clearInterval(metricsInterval);
    metricsInterval = null;
  }
});

// 获取调度列表
const fetchSchedules = async () => {
  loading.value = true;
  try {
    const params = {
      page: page.value,
      pageSize: pageSize.value,
      ...searchForm
    };

    const response = await axios.get('/api/sending-schedules', { params });

    if (response.data.code === 0) {
      scheduleList.value = response.data.data?.data || [];
      total.value = response.data.data?.total || 0;
    } else {
      ElMessage.error(response.data.message || '获取调度列表失败');
      // 设置默认值避免页面崩溃
      scheduleList.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取调度列表失败:', error);
    ElMessage.error('获取调度列表失败: ' + (error.response?.data?.message || error.message));
    // 设置默认值避免页面崩溃
    scheduleList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 获取系统指标
const fetchSystemMetrics = async () => {
  metricsLoading.value = true;
  try {
    const response = await axios.get('/api/sending-schedules/system-metrics');

    if (response.data.code === 0) {
      systemMetrics.cpu = response.data.data?.cpu || 0;
      systemMetrics.memory = response.data.data?.memory || 0;
      systemMetrics.lastUpdated = response.data.data?.lastUpdated || new Date().toISOString();
    } else {
      console.error('获取系统指标失败:', response.data.message);
      // 设置默认值
      systemMetrics.cpu = 0;
      systemMetrics.memory = 0;
      systemMetrics.lastUpdated = new Date().toISOString();
    }
  } catch (error) {
    console.error('获取系统指标失败:', error);
    // 设置默认值避免页面崩溃
    systemMetrics.cpu = 0;
    systemMetrics.memory = 0;
    systemMetrics.lastUpdated = new Date().toISOString();
  } finally {
    metricsLoading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  page.value = 1;
  fetchSchedules();
};

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = '';
  });
  searchForm.enabled = undefined;
  handleSearch();
};

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val;
  fetchSchedules();
};

const handleCurrentChange = (val) => {
  page.value = val;
  fetchSchedules();
};

// 新建调度
const handleCreateSchedule = () => {
  // 跳转到调度表单页面
  // TODO: 实现调度表单页面和路由
  ElMessage.info('跳转到调度创建页面');
};

// 编辑调度
const handleEdit = (row) => {
  // 跳转到调度表单页面
  // TODO: 实现调度表单页面和路由
  ElMessage.info(`跳转到调度编辑页面，ID: ${row.id}`);
};

// 查看详情
const handleViewDetails = (row) => {
  // TODO: 实现详情页面或对话框
  ElMessage.info(`查看调度详情，ID: ${row.id}`);
};

// 计算下次发送时间
const handleCalculateNextTime = async (row) => {
  try {
    const response = await axios.post('/api/sending-schedules/calculate-next-time', {
      scheduleId: row.id,
      messageType: row.notificationType
    });

    if (response.data.code === 0) {
      ElMessage.success(`下次发送时间: ${response.data.data.formattedTime}`);
    } else {
      ElMessage.error(response.data.message || '计算失败');
    }
  } catch (error) {
    console.error('计算下次发送时间失败:', error);
    ElMessage.error('计算失败: ' + (error.response?.data?.message || error.message));
  }
};

// 删除调度
const handleDelete = (row) => {
  ElMessageBox.confirm(
    '此操作将永久删除该调度, 是否继续?',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const response = await axios.delete(`/api/sending-schedules/${row.id}`);

      if (response.data.code === 0) {
        ElMessage.success('删除成功');
        fetchSchedules();
      } else {
        ElMessage.error(response.data.message || '删除失败');
      }
    } catch (error) {
      console.error('删除调度失败:', error);
      ElMessage.error('删除失败: ' + (error.response?.data?.message || error.message));
    }
  }).catch(() => {
    // 取消删除
  });
};

// 修改状态
const handleStatusChange = async (row) => {
  try {
    const response = await axios.post(`/api/sending-schedules/${row.id}/toggle`, {
      enabled: row.enabled
    });

    if (response.data.code === 0) {
      ElMessage.success(`${row.enabled ? '启用' : '禁用'}成功`);
    } else {
      ElMessage.error(response.data.message || '操作失败');
      // 恢复原值
      row.enabled = !row.enabled;
    }
  } catch (error) {
    console.error('修改状态失败:', error);
    ElMessage.error('修改状态失败: ' + (error.response?.data?.message || error.message));
    // 恢复原值
    row.enabled = !row.enabled;
  }
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleString();
};

// 获取通知类型标签样式
const getNotificationTypeTag = (type) => {
  const typeMap = {
    exchange: 'success',
    system: 'danger',
    user: 'primary',
    daily_report: 'info'
  };
  return typeMap[type] || '';
};

// 获取通知类型名称
const getNotificationTypeName = (type) => {
  const typeMap = {
    exchange: '订单消息',
    system: '系统通知',
    user: '用户消息',
    daily_report: '日报周报'
  };
  return typeMap[type] || type;
};

// 获取调度类型标签样式
const getScheduleTypeTag = (type) => {
  const typeMap = {
    fixed: '',
    smart: 'success',
    conditional: 'warning'
  };
  return typeMap[type] || '';
};

// 获取调度类型名称
const getScheduleTypeName = (type) => {
  const typeMap = {
    fixed: '固定时间',
    smart: '智能调度',
    conditional: '条件触发'
  };
  return typeMap[type] || type;
};

// 获取CPU进度条颜色
const getCpuProgressColor = (value) => {
  if (value < 60) return '#67c23a';
  if (value < 80) return '#e6a23c';
  return '#f56c6c';
};

// 获取内存进度条颜色
const getMemoryProgressColor = (value) => {
  if (value < 70) return '#67c23a';
  if (value < 85) return '#e6a23c';
  return '#f56c6c';
};
</script>

<style scoped>
.schedule-container {
  padding: 16px;
}

.schedule-card, .metrics-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.filter-container {
  margin-bottom: 16px;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.text-muted {
  color: #909399;
}

.metrics-content {
  padding: 8px;
}

.metric-item {
  margin-bottom: 16px;
}

.metric-label {
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
}

.metric-update-time {
  font-size: 12px;
  color: #909399;
  text-align: right;
  margin-top: 8px;
}
</style>
