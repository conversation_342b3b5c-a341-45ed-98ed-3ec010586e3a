#!/usr/bin/env node

/**
 * 并发控制测试
 * 测试库存扣减的并发安全性
 */

const { sequelize } = require('../server/config/database');
const { Exchange, Product, User, Category } = require('../server/models');
const bcrypt = require('../server/node_modules/bcryptjs');

// 测试配置
const CONCURRENT_ORDERS = 10; // 并发订单数量
const INITIAL_STOCK = 5; // 初始库存

async function setupTestData() {
  console.log('=== 设置并发测试数据 ===');

  // 创建测试用户
  const hashedPassword = await bcrypt.hash('test123', 10);
  const user = await User.create({
    username: '并发测试用户',
    email: '<EMAIL>',
    password: hashedPassword,
    role: 'user',
    department: '测试部门',
    authType: 'feishu',
    isActive: true
  });

  // 创建测试分类
  const category = await Category.create({
    name: '并发测试分类',
    description: '用于并发测试的分类',
    sortOrder: 1
  });

  // 创建测试商品
  const product = await Product.create({
    name: '并发测试商品',
    categoryId: category.id,
    lyPrice: 100,
    rmbPrice: 25,
    description: '用于测试并发控制的商品',
    stock: INITIAL_STOCK,
    exchangeCount: 0,
    isHot: false,
    isNew: false,
    status: 'active'
  });

  console.log(`测试数据创建完成: 用户ID=${user.id}, 商品ID=${product.id}, 初始库存=${INITIAL_STOCK}`);

  return { user, product, category };
}

async function createConcurrentOrder(userId, productId, orderIndex) {
  const transaction = await sequelize.transaction();

  try {
    console.log(`[订单${orderIndex}] 开始创建订单...`);

    // 使用行锁获取商品信息
    const product = await Product.findByPk(productId, {
      lock: transaction.LOCK.UPDATE,
      transaction
    });

    if (!product) {
      await transaction.rollback();
      return { success: false, error: '商品不存在', orderIndex };
    }

    if (product.stock < 1) {
      await transaction.rollback();
      return { success: false, error: '库存不足', orderIndex };
    }

    // 创建订单
    const exchange = await Exchange.create({
      userId,
      productId,
      quantity: 1,
      unitPrice: product.lyPrice,
      totalAmount: product.lyPrice,
      priceType: 'ly',
      paymentMethod: 'ly',
      contactInfo: '测试联系方式',
      location: '测试地址',
      status: 'pending'
    }, { transaction });

    // 更新库存
    await product.update({
      stock: product.stock - 1,
      exchangeCount: (product.exchangeCount || 0) + 1
    }, { transaction });

    await transaction.commit();

    console.log(`[订单${orderIndex}] 创建成功，订单ID=${exchange.id}，剩余库存=${product.stock - 1}`);

    return { success: true, exchangeId: exchange.id, orderIndex };
  } catch (error) {
    await transaction.rollback();
    console.log(`[订单${orderIndex}] 创建失败: ${error.message}`);
    return { success: false, error: error.message, orderIndex };
  }
}

async function runConcurrencyTest() {
  console.log('=== 开始并发控制测试 ===');
  console.log(`并发订单数量: ${CONCURRENT_ORDERS}`);
  console.log(`初始库存: ${INITIAL_STOCK}`);
  console.log(`预期成功订单数: ${INITIAL_STOCK}`);
  console.log(`预期失败订单数: ${CONCURRENT_ORDERS - INITIAL_STOCK}`);

  const { user, product, category } = await setupTestData();

  // 创建并发订单的Promise数组
  const orderPromises = [];
  for (let i = 1; i <= CONCURRENT_ORDERS; i++) {
    orderPromises.push(createConcurrentOrder(user.id, product.id, i));
  }

  // 并发执行所有订单创建
  console.log('\n=== 并发执行订单创建 ===');
  const startTime = Date.now();
  const results = await Promise.all(orderPromises);
  const endTime = Date.now();

  // 统计结果
  const successOrders = results.filter(r => r.success);
  const failedOrders = results.filter(r => !r.success);

  console.log('\n=== 并发测试结果 ===');
  console.log(`执行时间: ${endTime - startTime}ms`);
  console.log(`成功订单数: ${successOrders.length}`);
  console.log(`失败订单数: ${failedOrders.length}`);

  // 验证库存一致性
  const finalProduct = await Product.findByPk(product.id);
  console.log(`最终库存: ${finalProduct.stock}`);
  console.log(`最终兑换数量: ${finalProduct.exchangeCount}`);

  // 验证数据库中的订单数量
  const dbOrderCount = await Exchange.count({
    where: { productId: product.id }
  });
  console.log(`数据库中的订单数量: ${dbOrderCount}`);

  // 验证结果
  const expectedSuccessCount = INITIAL_STOCK;
  const expectedFailCount = CONCURRENT_ORDERS - INITIAL_STOCK;
  const expectedFinalStock = INITIAL_STOCK - successOrders.length;

  let testPassed = true;

  if (successOrders.length !== expectedSuccessCount) {
    console.log(`❌ 成功订单数不符合预期: 期望${expectedSuccessCount}, 实际${successOrders.length}`);
    testPassed = false;
  }

  if (failedOrders.length !== expectedFailCount) {
    console.log(`❌ 失败订单数不符合预期: 期望${expectedFailCount}, 实际${failedOrders.length}`);
    testPassed = false;
  }

  if (finalProduct.stock !== expectedFinalStock) {
    console.log(`❌ 最终库存不符合预期: 期望${expectedFinalStock}, 实际${finalProduct.stock}`);
    testPassed = false;
  }

  if (dbOrderCount !== successOrders.length) {
    console.log(`❌ 数据库订单数量不符合预期: 期望${successOrders.length}, 实际${dbOrderCount}`);
    testPassed = false;
  }

  // 检查失败原因
  console.log('\n=== 失败订单分析 ===');
  const stockErrors = failedOrders.filter(r => r.error === '库存不足');
  console.log(`库存不足错误: ${stockErrors.length}个`);

  const otherErrors = failedOrders.filter(r => r.error !== '库存不足');
  if (otherErrors.length > 0) {
    console.log(`其他错误: ${otherErrors.length}个`);
    otherErrors.forEach(r => {
      console.log(`  订单${r.orderIndex}: ${r.error}`);
    });
  }

  // 清理测试数据
  console.log('\n=== 清理测试数据 ===');
  await Exchange.destroy({ where: { userId: user.id } });
  await Product.destroy({ where: { id: product.id } });
  await Category.destroy({ where: { id: category.id } });
  await User.destroy({ where: { id: user.id } });
  console.log('✅ 测试数据清理完成');

  if (testPassed) {
    console.log('\n🎉 并发控制测试通过！');
    console.log('✅ 库存扣减并发安全');
    console.log('✅ 数据一致性保证');
    console.log('✅ 事务控制正常');
  } else {
    console.log('\n❌ 并发控制测试失败！');
    process.exit(1);
  }
}

async function main() {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    await runConcurrencyTest();

  } catch (error) {
    console.error('并发测试失败:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// 运行测试
if (require.main === module) {
  main();
}

module.exports = { runConcurrencyTest };
