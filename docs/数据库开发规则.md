# 数据库开发规则

## 📋 概述

本文档制定了项目中数据库开发的强制性规范和约束，所有团队成员必须严格遵守。违反这些规则可能导致数据丢失、系统故障或部署失败。

## 🚫 强制性禁止事项

### 1. 绝对禁止的操作
- ❌ **禁止直接在生产数据库执行DDL语句**
- ❌ **禁止跳过迁移文件直接修改数据库结构**
- ❌ **禁止在没有备份的情况下执行结构性变更**
- ❌ **禁止删除或修改已部署的迁移文件**
- ❌ **禁止在迁移文件中使用硬编码的数据**
- ❌ **禁止在没有回退逻辑的情况下创建迁移**

### 2. 数据安全红线
- ❌ **禁止在迁移中直接删除包含数据的表**
- ❌ **禁止在没有数据迁移方案的情况下修改字段类型**
- ❌ **禁止在生产环境手动执行DELETE或TRUNCATE**
- ❌ **禁止绕过事务执行批量数据操作**

## ✅ 强制性要求

### 1. 备份要求
```bash
# 任何数据库操作前必须执行
npm run db:backup

# 重要操作前必须手动备份并命名
mysqldump -u root -ppassword feishu_mall > backups/feishu_mall_backup_$(date +%Y%m%d_%H%M%S)_feature_name.sql
```

**强制规则**：
- ✅ 每次迁移前必须自动备份
- ✅ 重要功能开发前必须手动备份
- ✅ 生产部署前必须创建命名备份
- ✅ 备份文件必须包含时间戳和功能描述

### 2. 迁移文件编写标准

#### 2.1 文件结构要求
```javascript
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 必须使用事务
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // 详细的操作注释
      // 1. 创建新表
      await queryInterface.createTable('table_name', {
        // 字段定义
      }, { transaction });

      // 2. 添加索引
      await queryInterface.addIndex('table_name', ['field_name'], {
        name: 'idx_table_field',
        transaction
      });

      await transaction.commit();
      console.log('✅ [迁移名称] 执行成功');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ [迁移名称] 执行失败:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    // 必须提供完整的回退逻辑
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // 按相反顺序回退操作
      await queryInterface.removeIndex('table_name', 'idx_table_field', { transaction });
      await queryInterface.dropTable('table_name', { transaction });

      await transaction.commit();
      console.log('✅ [迁移名称] 回退成功');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ [迁移名称] 回退失败:', error);
      throw error;
    }
  }
};
```

#### 2.2 字段定义标准
```javascript
// 标准字段定义模板
{
  id: {
    type: Sequelize.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },
  name: {
    type: Sequelize.STRING(255),
    allowNull: false,
    comment: '名称字段',
    validate: {
      notEmpty: true,
      len: [1, 255]
    }
  },
  status: {
    type: Sequelize.ENUM('active', 'inactive'),
    allowNull: false,
    defaultValue: 'active',
    comment: '状态：active-活跃，inactive-非活跃'
  },
  createdAt: {
    type: Sequelize.DATE,
    allowNull: false,
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
    comment: '创建时间'
  },
  updatedAt: {
    type: Sequelize.DATE,
    allowNull: false,
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
    comment: '更新时间'
  }
}
```

### 3. 命名规范

#### 3.1 表名规范
- ✅ 使用复数形式：`users`, `products`, `orders`
- ✅ 使用snake_case：`user_profiles`, `order_items`
- ✅ 关联表命名：`user_roles`, `product_categories`

#### 3.2 字段名规范
- ✅ 使用camelCase（Sequelize模型）：`firstName`, `createdAt`
- ✅ 数据库使用snake_case映射：`first_name`, `created_at`
- ✅ 外键命名：`userId`, `categoryId`
- ✅ 布尔字段前缀：`isActive`, `hasPermission`

#### 3.3 索引命名规范
```javascript
// 普通索引
await queryInterface.addIndex('users', ['email'], {
  name: 'idx_users_email',
  unique: true
});

// 复合索引
await queryInterface.addIndex('orders', ['userId', 'status'], {
  name: 'idx_orders_user_status'
});

// 外键索引
await queryInterface.addIndex('order_items', ['orderId'], {
  name: 'idx_order_items_order_id'
});
```

### 4. 数据类型标准

#### 4.1 推荐数据类型
```javascript
// 字符串类型
Sequelize.STRING(255)        // 短文本，默认255字符
Sequelize.TEXT               // 长文本
Sequelize.TEXT('medium')     // 中等长度文本

// 数值类型
Sequelize.INTEGER            // 整数
Sequelize.BIGINT            // 大整数
Sequelize.DECIMAL(10, 2)    // 精确小数（价格等）
Sequelize.FLOAT             // 浮点数

// 日期时间
Sequelize.DATE              // 日期时间
Sequelize.DATEONLY          // 仅日期

// 布尔类型
Sequelize.BOOLEAN           // 布尔值

// 枚举类型
Sequelize.ENUM('value1', 'value2', 'value3')

// JSON类型（MySQL 5.7+）
Sequelize.JSON              // JSON数据
```

#### 4.2 字段约束要求
```javascript
{
  field_name: {
    type: Sequelize.STRING(255),
    allowNull: false,           // 明确指定是否允许NULL
    defaultValue: 'default',    // 提供默认值
    unique: true,              // 唯一约束
    comment: '字段说明',        // 必须添加注释
    validate: {                // 数据验证
      notEmpty: true,
      len: [1, 255],
      isEmail: true            // 根据需要添加验证规则
    }
  }
}
```

## 🔄 操作流程规范

### 1. 开发环境操作流程
```bash
# 1. 检查当前状态（强制）
npm run db:check

# 2. 创建迁移文件
cd server
npx sequelize-cli migration:generate --name descriptive_name

# 3. 编写迁移内容（遵循模板）
# 4. 本地测试（强制）
npm run migrate:safe

# 5. 测试回退（强制）
npm run migrate:undo

# 6. 重新执行迁移
npm run migrate:safe

# 7. 提交代码
git add .
git commit -m "feat: 数据库迁移 - 功能描述"
```

### 2. 生产环境部署流程
```bash
# 1. 部署前备份（强制）
npm run db:backup

# 2. 记录当前状态
mysql -u root -ppassword feishu_mall -e "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'feishu_mall';"

# 3. 执行迁移
npm run migrate:safe

# 4. 验证结果
npm run db:check

# 5. 功能验证
# 测试相关API和功能
```

### 3. 紧急回退流程
```bash
# 1. 立即停止服务
pm2 stop all

# 2. 执行迁移回退
npm run migrate:undo

# 3. 如果回退失败，恢复备份
mysql -u root -ppassword feishu_mall < backups/latest_backup.sql

# 4. 验证数据完整性
npm run db:check

# 5. 重启服务
pm2 start all
```

## 📝 代码审查要求

### 1. 迁移文件审查清单
- [ ] 文件命名符合规范
- [ ] 包含完整的up和down方法
- [ ] 使用事务保护所有操作
- [ ] 字段定义包含完整约束和注释
- [ ] 索引命名符合规范
- [ ] 外键关系正确定义
- [ ] 包含详细的操作日志
- [ ] 错误处理完整

### 2. 模型文件审查清单
- [ ] 模型名称使用PascalCase
- [ ] 表名映射正确
- [ ] 字段映射使用field属性
- [ ] 关联关系定义正确
- [ ] 验证规则完整
- [ ] 钩子函数合理使用

## ⚠️ 违规处理

### 1. 轻微违规（警告）
- 迁移文件缺少注释
- 字段命名不规范
- 缺少数据验证

### 2. 严重违规（拒绝合并）
- 缺少回退逻辑
- 未使用事务保护
- 直接操作生产数据库
- 删除已部署的迁移文件

### 3. 危险违规（立即处理）
- 在生产环境直接执行DDL
- 删除包含数据的表
- 绕过备份机制

## 🛠️ 工具和命令

### 1. 必须使用的命令
```bash
# 数据库检查
npm run db:check

# 安全迁移
npm run migrate:safe

# 手动备份
npm run db:backup

# 迁移回退
npm run migrate:undo
```

### 2. 禁止使用的命令
```bash
# 禁止直接执行迁移
npx sequelize-cli db:migrate

# 禁止强制迁移
npx sequelize-cli db:migrate --force

# 禁止直接连接生产数据库执行DDL
mysql -u root -ppassword feishu_mall -e "ALTER TABLE..."
```

## 📞 应急联系

### 数据库问题升级流程
1. **Level 1**: 开发者自行解决（使用备份恢复）
2. **Level 2**: 技术负责人介入（数据恢复和问题分析）
3. **Level 3**: 系统管理员介入（服务器级别问题）

### 紧急情况处理
- 🚨 数据丢失：立即停止服务，恢复最近备份
- 🚨 迁移失败：执行回退操作，分析失败原因
- 🚨 服务无法启动：检查数据库连接和表结构完整性

**记住：数据安全永远是第一优先级！**

## 📚 最佳实践示例

### 1. 标准表创建示例
```javascript
// 20250715120000-create-user-profiles.js
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // 创建用户资料表
      await queryInterface.createTable('user_profiles', {
        id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          comment: '主键ID'
        },
        userId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: '用户ID',
          references: {
            model: 'users',
            key: 'id'
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE'
        },
        avatar: {
          type: Sequelize.STRING(500),
          allowNull: true,
          comment: '头像URL'
        },
        bio: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: '个人简介'
        },
        settings: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: '用户设置JSON'
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
          comment: '创建时间'
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
          comment: '更新时间'
        }
      }, {
        transaction,
        comment: '用户资料表'
      });

      // 添加索引
      await queryInterface.addIndex('user_profiles', ['userId'], {
        unique: true,
        name: 'idx_user_profiles_user_id',
        transaction
      });

      await transaction.commit();
      console.log('✅ 用户资料表创建成功');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ 用户资料表创建失败:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      await queryInterface.dropTable('user_profiles', { transaction });
      await transaction.commit();
      console.log('✅ 用户资料表删除成功');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ 用户资料表删除失败:', error);
      throw error;
    }
  }
};
```

### 2. 字段修改示例
```javascript
// 20250715120001-modify-products-add-tags.js
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // 添加标签字段
      await queryInterface.addColumn('products', 'tags', {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '商品标签JSON数组'
      }, { transaction });

      // 添加搜索关键词字段
      await queryInterface.addColumn('products', 'searchKeywords', {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '搜索关键词，用于全文搜索'
      }, { transaction });

      // 添加索引
      await queryInterface.addIndex('products', ['searchKeywords'], {
        name: 'idx_products_search_keywords',
        type: 'FULLTEXT',
        transaction
      });

      await transaction.commit();
      console.log('✅ 商品表字段添加成功');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ 商品表字段添加失败:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // 删除索引
      await queryInterface.removeIndex('products', 'idx_products_search_keywords', { transaction });

      // 删除字段
      await queryInterface.removeColumn('products', 'searchKeywords', { transaction });
      await queryInterface.removeColumn('products', 'tags', { transaction });

      await transaction.commit();
      console.log('✅ 商品表字段删除成功');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ 商品表字段删除失败:', error);
      throw error;
    }
  }
};
```

## 🔍 代码审查模板

### Pull Request检查模板
```markdown
## 数据库变更审查

### 基本信息
- [ ] 迁移文件命名符合规范
- [ ] 包含详细的变更说明
- [ ] 影响范围已明确标识

### 技术审查
- [ ] 使用事务保护所有操作
- [ ] 包含完整的up和down逻辑
- [ ] 字段定义包含完整约束和注释
- [ ] 索引命名符合规范
- [ ] 外键关系正确定义

### 安全审查
- [ ] 不包含危险的删除操作
- [ ] 数据迁移方案合理
- [ ] 回退逻辑经过测试
- [ ] 不会导致数据丢失

### 测试验证
- [ ] 本地迁移测试通过
- [ ] 回退测试通过
- [ ] 相关API功能正常
- [ ] 性能影响可接受

### 部署准备
- [ ] 备份策略已制定
- [ ] 部署步骤已明确
- [ ] 回退方案已准备
- [ ] 监控指标已设置
```

## 📋 团队培训清单

### 新成员入职培训
- [ ] 阅读数据库开发规则文档
- [ ] 学习数据库开发工作流
- [ ] 熟悉项目数据库架构
- [ ] 掌握迁移文件编写规范
- [ ] 了解备份和恢复流程
- [ ] 练习紧急处理流程

### 定期培训内容
- [ ] 数据库安全最佳实践
- [ ] 性能优化技巧
- [ ] 新工具和命令使用
- [ ] 故障案例分析
- [ ] 规范更新说明

## 🔗 相关文档链接

- [数据库开发工作流](./数据库开发工作流.md) - 详细操作流程
- [数据库版本管理指南](./数据库版本管理指南.md) - 版本管理机制
- [项目结构文档](./PROJECT_STRUCTURE.md) - 项目架构说明

## 📞 违规举报和建议

### 举报渠道
- 技术负责人：发现违规操作立即报告
- 代码审查：在PR中标记违规问题
- 团队会议：定期讨论规范执行情况

### 改进建议
- 规范更新建议：通过Issue提交
- 工具改进：提交功能需求
- 流程优化：团队讨论决定

---

**本规则文档具有强制执行力，所有团队成员必须严格遵守！**
