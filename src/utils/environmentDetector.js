/**
 * 环境检测工具
 * 用于自动检测当前运行环境并提供相应的配置
 */

// 检测当前环境类型
export const detectEnvironment = () => {
  const isProduction = import.meta.env.MODE === 'production';
  const isDevelopment = import.meta.env.MODE === 'development';
  const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
  
  // 额外的环境判断
  const apiUrl = import.meta.env.VITE_API_URL || '';
  const prodServerUrl = import.meta.env.VITE_PROD_SERVER_URL || 'https://store-api.chongyangqisi.com';
  const devServerUrl = import.meta.env.VITE_DEV_SERVER_URL || 'http://localhost:3000';
  const oldServerIp = import.meta.env.VITE_OLD_SERVER_IP || '**************';

  const isProductionApi = apiUrl.includes(prodServerUrl.replace('https://', '').replace('http://', ''));
  const isDevApi = apiUrl.includes('localhost') || apiUrl.includes('127.0.0.1');
  
  // 强制本地开发环境总是使用localhost
  const forcedLocalDevelopment = isLocalhost;
  
  // 检测环境不匹配情况
  const environmentMismatch = (isProduction && !isProductionApi) || (isDevelopment && !isDevApi);
  
  // 打印环境检测结果，便于调试
  console.log('环境检测结果:', {
    isProduction,
    isDevelopment,
    isLocalhost,
    apiUrl,
    isProductionApi,
    isDevApi,
    forcedLocalDevelopment,
    environmentMismatch
  });
  
  return {
    isProduction,
    isDevelopment,
    isLocalhost,
    apiUrl,
    isProductionApi,
    isDevApi,
    forcedLocalDevelopment,
    environmentMismatch
  };
};

// 获取当前环境的基础URL
export const getEnvironmentBaseUrl = () => {
  const env = detectEnvironment();
  
  // 本地开发环境强制使用localhost:3000
  if (env.isLocalhost || env.isDevelopment) {
    const baseUrl = import.meta.env.VITE_DEV_SERVER_URL || 'http://localhost:3000';
    console.log('本地开发环境，使用URL:', baseUrl);
    return baseUrl;
  }
  
  // 首先尝试从环境变量获取
  let baseUrl = import.meta.env.VITE_API_URL?.replace('/api', '') || '';
  
  // 检查是否与当前环境匹配
  if (env.environmentMismatch) {
    console.warn('检测到API URL与当前环境不匹配，进行修正');
    
    // 根据当前环境选择正确的URL
    if (env.isLocalhost || env.isDevelopment) {
      baseUrl = import.meta.env.VITE_DEV_SERVER_URL || 'http://localhost:3000';
    } else {
      baseUrl = import.meta.env.VITE_PROD_SERVER_URL || 'https://store-api.chongyangqisi.com';
    }
  }
  
  // 如果仍然没有值，使用自动检测
  if (!baseUrl) {
    const devUrl = import.meta.env.VITE_DEV_SERVER_URL || 'http://localhost:3000';
    const prodUrl = import.meta.env.VITE_PROD_SERVER_URL || 'https://store-api.chongyangqisi.com';
    baseUrl = env.isLocalhost ? devUrl : prodUrl;
  }
  
  console.log('获取环境基础URL:', baseUrl);
  return baseUrl;
};

// 修正URL以匹配当前环境
export const fixUrlForEnvironment = (url) => {
  if (!url) return '';
  
  const env = detectEnvironment();
  
  // 打印更详细的URL处理日志
  console.log('修正URL开始:', url);
  console.log('当前是否本地环境:', env.isLocalhost);
  
  // 确保本地环境时总是使用localhost
  if (env.isLocalhost) {
    const oldServerIp = import.meta.env.VITE_OLD_SERVER_IP || '**************';
    const prodServerUrl = import.meta.env.VITE_PROD_SERVER_URL || 'https://store-api.chongyangqisi.com';
    const devServerUrl = import.meta.env.VITE_DEV_SERVER_URL || 'http://localhost:3000';

    if (url.includes(oldServerIp) || url.includes(prodServerUrl.replace('https://', '').replace('http://', ''))) {
      const prodDomain = prodServerUrl.replace('https://', '').replace('http://', '');
      const fixedUrl = url.replace(new RegExp(`https?:\\/\\/(${oldServerIp.replace('.', '\\.')}(?::\\d+)?|${prodDomain.replace('.', '\\.')})`, 'g'), devServerUrl);
      console.log('本地环境检测到生产URL，进行修正:', url, '->', fixedUrl);

      // 添加缓存破坏参数
      const timestamp = new Date().getTime();
      const urlWithTime = fixedUrl.includes('?')
        ? `${fixedUrl}&t=${timestamp}`
        : `${fixedUrl}?t=${timestamp}`;

      return urlWithTime;
    }
  }

  // 生产环境：将旧IP地址替换为新域名
  if (!env.isLocalhost) {
    const oldServerIp = import.meta.env.VITE_OLD_SERVER_IP || '**************';
    const prodServerUrl = import.meta.env.VITE_PROD_SERVER_URL || 'https://store-api.chongyangqisi.com';

    if (url.includes(oldServerIp)) {
      const fixedUrl = url.replace(new RegExp(`https?:\\/\\/${oldServerIp.replace('.', '\\.')}(?::\\d+)?`, 'g'), prodServerUrl);
      console.log('生产环境检测到旧IP地址，修正为新域名:', url, '->', fixedUrl);
      return fixedUrl;
    }
  }
  
  // 检测URL环境
  const prodServerUrl = import.meta.env.VITE_PROD_SERVER_URL || 'https://store-api.chongyangqisi.com';
  const oldServerIp = import.meta.env.VITE_OLD_SERVER_IP || '**************';

  const isProductionUrl = url.includes(prodServerUrl.replace('https://', '').replace('http://', ''));
  const isOldProductionUrl = url.includes(oldServerIp);
  const isLocalhostUrl = url.includes('localhost');

  console.log('URL环境检测:', { isProductionUrl, isOldProductionUrl, isLocalhostUrl });

  // 已经是正确环境的URL，不需修改
  if (env.isLocalhost && isLocalhostUrl) {
    console.log('URL已匹配本地环境，无需修改');
    return url;
  }

  if (!env.isLocalhost && isProductionUrl && !isLocalhostUrl && !isOldProductionUrl) {
    console.log('URL已匹配生产环境，无需修改');
    return url;
  }

  // 如果是本地环境但URL是生产环境的，替换为localhost
  if (env.isLocalhost && (isProductionUrl || isOldProductionUrl)) {
    console.log('本地环境检测到生产URL，进行修正');
    const devServerUrl = import.meta.env.VITE_DEV_SERVER_URL || 'http://localhost:3000';
    const prodDomain = prodServerUrl.replace('https://', '').replace('http://', '');
    const fixedUrl = url.replace(new RegExp(`https?:\\/\\/(${oldServerIp.replace('.', '\\.')}(?::\\d+)?|${prodDomain.replace('.', '\\.')})`, 'g'), devServerUrl);
    console.log('修正后的URL:', fixedUrl);
    return fixedUrl;
  }

  // 如果是生产环境但URL是本地环境的，替换为生产域名
  if (!env.isLocalhost && isLocalhostUrl) {
    console.log('生产环境检测到本地URL，进行修正');
    // 强制使用生产环境URL
    const prodServerUrl = import.meta.env.VITE_PROD_SERVER_URL || 'https://store-api.chongyangqisi.com';
    const fixedUrl = url.replace(/http:\/\/localhost(?::\d+)?/g, prodServerUrl);
    console.log('修正后的URL:', fixedUrl);
    return fixedUrl;
  }

  // 特殊处理：如果URL不包含域名但以/uploads开头，添加正确的域名前缀
  if (url.startsWith('/uploads/') && !url.startsWith('http')) {
    console.log('检测到相对路径的uploads URL，添加域名');
    const devServerUrl = import.meta.env.VITE_DEV_SERVER_URL || 'http://localhost:3000';
    const prodServerUrl = import.meta.env.VITE_PROD_SERVER_URL || 'https://store-api.chongyangqisi.com';
    const baseUrl = env.isLocalhost ? devServerUrl : prodServerUrl;
    const fixedUrl = `${baseUrl}${url}`;
    console.log('添加域名后的URL:', fixedUrl);
    return fixedUrl;
  }

  // 处理特殊情况：没有域名前缀的URL，但包含了uploads/payment路径
  if (url.includes('/uploads/payment/') && !url.startsWith('http')) {
    console.log('检测到相对路径的uploads/payment URL，添加域名');
    const devServerUrl = import.meta.env.VITE_DEV_SERVER_URL || 'http://localhost:3000';
    const prodServerUrl = import.meta.env.VITE_PROD_SERVER_URL || 'https://store-api.chongyangqisi.com';
    const baseUrl = env.isLocalhost ? devServerUrl : prodServerUrl;
    const fixedUrl = `${baseUrl}${url.startsWith('/') ? '' : '/'}${url}`;
    console.log('添加域名后的URL:', fixedUrl);
    return fixedUrl;
  }
  
  console.log('URL无需修正:', url);
  return url;
};

export default {
  detectEnvironment,
  getEnvironmentBaseUrl,
  fixUrlForEnvironment
}; 