# 生产环境部署指南 - IP访问版

## 前置准备

1. 确保服务器已安装Node.js (14.x或更高版本)
2. 确保服务器已安装MySQL (5.7或更高版本)
3. 确保已安装PM2: `npm install -g pm2`
4. 确保已安装Nginx: `apt-get install nginx` (Ubuntu/Debian) 或 `yum install nginx` (CentOS)

## 配置步骤

### 1. 已配置前端生产环境配置

已配置项目根目录下的 `.env.production` 文件：

```
VITE_API_URL=http://**************:3000/api
```

### 2. 已配置后端生产环境配置

已配置 `server/.env.production` 文件：

```
SERVER_URL=http://**************:3000

# 数据库配置
DB_NAME=feishu_mall
DB_USER=root
DB_PASSWORD=password
DB_HOST=localhost
DB_PORT=3306

# JWT配置
JWT_SECRET=JO/Ssvef59AR5zFMx5m/MGMin34aMPT0KY6sIcqwowA=

# CORS配置
CORS_ORIGIN=http://**************,http://**************:3000

# 飞书应用配置 
FEISHU_APP_ID=cli_a7f521a6fe32501c
FEISHU_APP_SECRET=s8DAHMcDcCSd9329wAQ0LgsXUmUCZjVZ
FEISHU_REDIRECT_URI=http://**************:3000/api/feishu/callback
```

## 部署流程

### 1. 构建前端

```bash
# 安装依赖
npm install

# 构建生产版本
npm run build
```

构建完成后，前端资源会生成到 `dist` 目录。

### 2. 部署后端

```bash
# 进入服务器目录
cd server

# 安装生产依赖
npm install --omit=dev

# 使用PM2启动服务
pm2 start server.js --name exchange-mall-api

# 设置PM2开机自启动
pm2 startup
pm2 save
```

### 3. 配置Nginx (使前端使用80端口访问)

创建Nginx配置文件:

```bash
sudo nano /etc/nginx/sites-available/exchange-mall
```

添加以下配置:

```nginx
server {
    listen 80;
    server_name **************;

    # 前端静态资源
    location / {
        root /path/to/your/project/dist;  # 替换为您的dist目录的实际路径
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # 上传文件目录
    location /uploads {
        alias /path/to/your/project/server/uploads;  # 替换为您的uploads目录的实际路径
    }
}
```

启用配置并重启Nginx:

```bash
sudo ln -s /etc/nginx/sites-available/exchange-mall /etc/nginx/sites-enabled/
sudo nginx -t  # 检查配置是否有语法错误
sudo systemctl restart nginx
```

### 4. 运行部署脚本

项目中已包含部署脚本 `production-deploy.sh`，可以直接运行：

```bash
chmod +x production-deploy.sh
./production-deploy.sh
```

## 验证部署

1. 访问前端: `http://**************` (80端口，无需指定)
2. 测试API: `http://**************:3000/api/health` 或 `http://**************/api/health`
3. 查看后端日志: `pm2 logs exchange-mall-api`

## 端口访问注意事项

1. 确保服务器防火墙已开放80端口和3000端口
2. 如使用云服务器，请在安全组中开放相应端口
3. 后端API服务依然运行在3000端口，但通过Nginx代理，可以直接通过80端口访问

## 常见问题排查

1. 如遇到前端白屏问题，检查前端API地址配置是否正确
2. 如API无法访问，检查Nginx配置、服务器防火墙和安全组设置
3. 如数据库连接失败，检查数据库配置和权限设置
4. 如Nginx配置出错，查看错误日志: `sudo cat /var/log/nginx/error.log`
