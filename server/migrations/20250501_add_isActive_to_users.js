'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 添加isActive字段到users表
    await queryInterface.addColumn('users', 'isActive', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '账户是否处于活动状态，false表示禁用'
    });

    // 将所有现有用户的isActive字段设置为true
    await queryInterface.sequelize.query(`
      UPDATE users SET isActive = true;
    `);

    console.log('成功添加isActive字段到users表并设置默认值');
  },

  async down(queryInterface, Sequelize) {
    // 删除isActive字段
    await queryInterface.removeColumn('users', 'isActive');
    console.log('成功删除isActive字段');
  }
};
