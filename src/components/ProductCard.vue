<template>
  <div class="product-card">
    <!-- 商品图片 - 如果有多张图片，显示第一张 -->
    <div class="product-img" @click.stop="handleImageClick">
      <el-image
        :src="productImage"
        fit="cover"
        :alt="product.name"
        lazy
        :preview-src-list="getImageSrcList()"
        :initial-index="0"
        :preview-teleported="true"
        hide-on-click-modal
      >
        <template #error>
          <div class="image-placeholder">
            <img :src="getDefaultProductImage()" alt="默认商品图片" style="width: 100%; height: 100%; object-fit: cover;" />
          </div>
        </template>
      </el-image>
      <!-- 标签 -->
      <div class="product-tags">
        <el-tag v-if="product.isNew" type="success" size="small" effect="plain" class="tag-new">新品</el-tag>
        <!-- 手动设置的热门标签 -->
        <el-tag v-if="product.isHot && !product.isAutoHot" type="danger" size="small" effect="plain" class="tag-hot">热门</el-tag>
        <!-- 自动识别的热门标签，显示时间维度 -->
        <el-tag v-if="product.isAutoHot" type="warning" size="small" effect="plain" class="tag-auto-hot">
          {{ getHotLabel(product.hotTimeRange) }}
        </el-tag>
        <!-- 热门度评分显示（仅自动热门商品） -->
        <el-tag v-if="product.isAutoHot && product.hotScore" type="info" size="small" effect="plain" class="tag-score">
          评分: {{ product.hotScore }}
        </el-tag>
      </div>
      <!-- 多图指示器 -->
      <div v-if="hasMultipleImages" class="multi-image-indicator">
        <el-icon><picture-filled /></el-icon>
        <span>{{ product.images?.length || 0 }}</span>
      </div>
    </div>

    <!-- 商品信息 - 点击打开详情 -->
    <div class="product-info" @click="handleInfoClick">
      <h3 class="product-name">{{ product.name }}</h3>

      <!-- 价格显示部分 - 仿照效果图样式 -->
      <div class="price-container">
        <div class="price-row light-bg">
          <span class="currency-label">光年币:</span>
          <span class="currency-value">{{ product.lyPrice }}</span>
        </div>

        <div class="price-row dark-bg">
          <span class="currency-label">人民币:</span>
          <span class="currency-value">{{ product.rmbPrice }}元</span>
        </div>

        <div class="status-row">
          <div class="exchange-count">已兑换/售卖：{{ product.exchangeCount || 0 }}+</div>
          <div class="stock-status" :class="{ 'in-stock': product.stock > 0, 'out-of-stock': product.stock === 0 }">
            {{ product.stockStatus || (product.stock > 0 ? '有货' : '缺货') }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { PictureFilled } from '@element-plus/icons-vue';
import { computed } from 'vue';
import { fixImageUrl, getDefaultProductImage } from '../utils/imageUtils';

const props = defineProps({
  product: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['click', 'image-click']);

// 点击商品信息区域打开详情页
const handleInfoClick = () => {
  emit('click', props.product);
};

// 点击商品图片区域全屏预览
const handleImageClick = (event) => {
  event.stopPropagation(); // 防止冒泡到卡片
  emit('image-click', props.product);

  // Element Plus的el-image组件会自动处理预览，无需额外处理
};

// 检查产品是否有多张图片
const hasMultipleImages = computed(() => {
  return props.product.images && props.product.images.length > 1;
});

// 获取商品的主图片，优先使用图片数组的第一张，然后是单独的imageUrl
const productImage = computed(() => {
  try {
    // 首先尝试使用images数组
    if (props.product.images && props.product.images.length > 0) {
      const firstImage = props.product.images[0];

      // 检查是否是对象形式
      if (typeof firstImage === 'object' && firstImage.imageUrl) {
        return fixImageUrl(firstImage.imageUrl);
      }

      // 如果是字符串形式
      if (typeof firstImage === 'string') {
        return fixImageUrl(firstImage);
      }
    }

    // 检查是否有单独的imageUrl
    if (props.product.imageUrl && typeof props.product.imageUrl === 'string' && props.product.imageUrl.trim() !== '') {
      return fixImageUrl(props.product.imageUrl);
    }

    // 如果没有任何图片，返回默认占位图
    return getDefaultProductImage('300x300');
  } catch (error) {
    console.error('获取商品图片出错:', error);
    return getDefaultProductImage('300x300');
  }
});

// 获取所有图片URL列表，用于全屏预览
const getImageSrcList = () => {
  const srcList = [];

  // 如果有图片数组，先收集里面的链接
  if (props.product.images && props.product.images.length > 0) {
    props.product.images.forEach(image => {
      if (typeof image === 'object' && image.imageUrl) {
        srcList.push(fixImageUrl(image.imageUrl));
      } else if (typeof image === 'string') {
        srcList.push(fixImageUrl(image));
      }
    });
  }
  // 如果没有图片数组但有单独的imageUrl
  else if (props.product.imageUrl && typeof props.product.imageUrl === 'string' && props.product.imageUrl.trim() !== '') {
    srcList.push(fixImageUrl(props.product.imageUrl));
  }

  // 如果没有任何图片，添加默认占位图
  if (srcList.length === 0) {
    srcList.push(getDefaultProductImage('300x300'));
  }

  return srcList;
};

// 获取热门标签文本
const getHotLabel = (timeRange) => {
  const labels = {
    'all': '累积热门',
    '30d': '30天热门',
    '7d': '7天热门',
    '1d': '今日热门'
  };
  return labels[timeRange] || '热门';
};
</script>

<style scoped>
.product-card {
  background-color: #fff;
  border-radius: 18px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  transform-origin: center;
  will-change: transform, box-shadow;
  border: 1px solid rgba(0,0,0,0.05);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.product-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  z-index: 1;
}

.product-card:active {
  transform: translateY(-2px) scale(0.98);
  transition: all 0.1s ease;
}

/* 图片容器固定高度 */
.product-img {
  position: relative;
  height: 320px;
  min-height: 320px;
  width: 100%;
  overflow: hidden;
  background-color: #f5f7fa;
  cursor: zoom-in; /* 表示可放大 */
}

/* el-image 样式强制覆盖 */
.product-img :deep(.el-image) {
  width: 100% !important;
  height: 100% !important;
  display: block !important;
}

/* 强制图片填充整个区域并居中 */
.product-img :deep(.el-image__inner) {
  object-fit: cover !important;
  width: 100% !important;
  height: 100% !important;
  min-height: 320px !important;
  object-position: center !important;
}

/* 强制包装元素占满容器 */
.product-img :deep(.el-image__wrapper) {
  display: block !important;
  width: 100% !important;
  height: 100% !important;
}

/* 处理加载错误和占位图显示 */
.product-img :deep(.el-image__error),
.product-img :deep(.el-image__placeholder) {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.product-card:hover .product-img :deep(.el-image__inner) {
  transform: scale(1.08);
  transition: transform 0.7s ease;
}

.image-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  background-color: #f5f7fa;
  color: #909399;
}

.product-tags {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  z-index: 2;
}

.product-tags .el-tag {
  animation: tagSlideIn 0.5s ease-out;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  font-weight: bold;
  letter-spacing: 0.5px;
  padding: 8px 12px;
  font-size: 14px;
}

.tag-new {
  background-color: #41b883 !important;
  color: white !important;
  border: none !important;
}

.tag-hot {
  background-color: #ff5252 !important;
  color: white !important;
  border: none !important;
}

.tag-auto-hot {
  background-color: #f39c12 !important;
  color: white !important;
  border: none !important;
}

.tag-score {
  background-color: #6c757d !important;
  color: white !important;
  border: none !important;
  font-size: 12px !important;
  padding: 6px 10px !important;
}

.product-tags .el-tag:hover {
  transform: translateX(-5px) rotate(-2deg);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

@keyframes tagSlideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.multi-image-indicator {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  z-index: 2;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
}

.product-card:hover .multi-image-indicator {
  background-color: rgba(64, 158, 255, 0.8);
  transform: translateY(-5px);
  padding: 5px 15px;
}

.product-info {
  padding: 20px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
  border-top: 1px solid rgba(0,0,0,0.03);
  cursor: pointer;
}

.product-card:hover .product-info {
  background-color: #f9fafb;
}

.product-name {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 15px;
  line-height: 1.4;
  height: 50px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  transition: color 0.3s ease;
  text-align: center;
}

.product-card:hover .product-name {
  color: #409EFF;
  transform: translateY(-2px);
}

/* 价格容器和价格行样式 - 仿照效果图 */
.price-container {
  display: flex;
  flex-direction: column;
  margin-top: auto;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  font-size: 16px;
}

.light-bg {
  background-color: #f0f9ff;
  color: #1890ff;
}

.dark-bg {
  background-color: #e8f5e9;
  color: #52c41a;
}

.currency-label {
  font-weight: 500;
}

.currency-value {
  font-weight: 700;
  font-size: 18px;
}

/* 添加一个新的状态行以包含兑换量和库存状态 */
.status-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background-color: #f5f7fa;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.exchange-count {
  font-weight: 600;
  color: #1890ff;
  display: flex;
  align-items: center;
}

.stock-status {
  text-align: right;
  font-weight: 500;
  padding: 0;
}

.in-stock {
  color: #52c41a;
}

.out-of-stock {
  color: #ff5252;
}

/* 媒体查询保持图片区域高度一致 */
@media (max-width: 992px) {
  .product-img {
    height: 280px;
    min-height: 280px;
  }

  .product-img :deep(.el-image__inner) {
    min-height: 280px !important;
  }

  .product-name {
    font-size: 16px;
    height: 44px;
  }

  .price-row {
    padding: 10px 12px;
    font-size: 14px;
  }

  .currency-value {
    font-size: 16px;
  }

  .status-row {
    padding: 10px 12px;
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .product-img {
    height: 240px;
    min-height: 240px;
  }

  .product-img :deep(.el-image__inner) {
    min-height: 240px !important;
  }

  .product-info {
    padding: 15px;
  }

  .product-name {
    margin-bottom: 12px;
    font-size: 15px;
    height: 42px;
  }

  .price-row {
    padding: 8px 10px;
    font-size: 13px;
  }

  .currency-value {
    font-size: 15px;
  }

  .status-row {
    padding: 8px 10px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .product-img {
    height: 200px;
    min-height: 200px;
  }

  .product-img :deep(.el-image__inner) {
    min-height: 200px !important;
  }

  .product-name {
    font-size: 14px;
    height: 40px;
    margin-bottom: 10px;
  }

  .product-info {
    padding: 12px;
  }

  .price-row {
    padding: 6px 8px;
    font-size: 12px;
  }

  .currency-value {
    font-size: 14px;
  }

  .status-row {
    padding: 6px 8px;
    font-size: 12px;
    flex-direction: column;
    align-items: flex-start;
  }

  .exchange-count {
    margin-bottom: 4px;
  }

  .stock-status {
    align-self: flex-end;
  }
}

/* 确保el-image组件的预览图片全屏且可缩放 */
:deep(.el-image-viewer__wrapper) {
  z-index: 9999 !important;
}

:deep(.el-image-viewer__img) {
  max-height: 90vh;
  max-width: 90vw;
}

:deep(.el-image-viewer__actions) {
  opacity: 0.9;
  padding: 12px;
}

:deep(.el-image-viewer__close) {
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  font-size: 24px;
}
</style>
