<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>职场库存管理系统修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-button {
            background-color: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background-color: #337ecc;
        }
        .test-button.success {
            background-color: #67C23A;
        }
        .test-button.error {
            background-color: #F56C6C;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .result.success {
            background-color: #f0f9ff;
            border: 1px solid #67C23A;
            color: #333;
        }
        .result.error {
            background-color: #fef0f0;
            border: 1px solid #F56C6C;
            color: #333;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success {
            background-color: #67C23A;
        }
        .status-error {
            background-color: #F56C6C;
        }
        .status-pending {
            background-color: #E6A23C;
        }
        .quick-links {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .quick-link {
            background-color: #409EFF;
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
        }
        .quick-link:hover {
            background-color: #337ecc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 职场库存管理系统修复验证</h1>
        <p>验证两个关键问题的修复状态：</p>
        <ul>
            <li><strong>问题1</strong>: 库存管理页面显示空白</li>
            <li><strong>问题2</strong>: 商品管理页面职场库存显示效果不符合预期</li>
        </ul>
        
        <div class="quick-links">
            <a href="http://localhost:5173/admin/stock-management" class="quick-link" target="_blank">📊 库存管理页面</a>
            <a href="http://localhost:5173/admin/products" class="quick-link" target="_blank">📦 商品管理页面</a>
            <a href="http://localhost:5173/admin/login" class="quick-link" target="_blank">🔐 管理员登录</a>
        </div>
    </div>

    <div class="container">
        <div class="test-section">
            <div class="test-title">🔍 问题1: API接口验证</div>
            <p>检查库存管理页面所需的API接口是否正常响应</p>
            
            <button class="test-button" onclick="testProductsWithStocksAPI()">
                <span class="status-indicator status-pending" id="products-status"></span>
                测试商品库存API
            </button>
            
            <button class="test-button" onclick="testWorkplacesAPI()">
                <span class="status-indicator status-pending" id="workplaces-status"></span>
                测试职场列表API
            </button>
            
            <button class="test-button" onclick="testCategoriesAPI()">
                <span class="status-indicator status-pending" id="categories-status"></span>
                测试分类列表API
            </button>
            
            <div id="api-results"></div>
        </div>

        <div class="test-section">
            <div class="test-title">📊 问题2: 数据结构验证</div>
            <p>检查商品数据是否包含正确的职场库存信息</p>
            
            <button class="test-button" onclick="testProductDataStructure()">
                <span class="status-indicator status-pending" id="structure-status"></span>
                验证数据结构
            </button>
            
            <button class="test-button" onclick="testActiveWorkplaces()">
                <span class="status-indicator status-pending" id="active-workplaces-status"></span>
                验证活跃职场
            </button>
            
            <div id="structure-results"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🎯 综合测试</div>
            <p>模拟完整的页面加载流程</p>
            
            <button class="test-button" onclick="runFullTest()">
                <span class="status-indicator status-pending" id="full-test-status"></span>
                运行完整测试
            </button>
            
            <div id="full-test-results"></div>
        </div>
    </div>

    <script>
        // API基础配置
        const API_BASE = 'http://localhost:3000/api';
        
        // 获取认证token (模拟管理员登录)
        const getAuthToken = () => {
            return localStorage.getItem('token') || '';
        };

        // 通用API调用函数
        async function apiCall(url, options = {}) {
            const token = getAuthToken();
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    ...(token && { 'Authorization': `Bearer ${token}` })
                }
            };
            
            const response = await fetch(url, { ...defaultOptions, ...options });
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(`API错误 (${response.status}): ${data.message || '未知错误'}`);
            }
            
            return data;
        }

        // 更新状态指示器
        function updateStatus(elementId, status) {
            const element = document.getElementById(elementId);
            if (element) {
                element.className = `status-indicator status-${status}`;
            }
        }

        // 显示结果
        function showResult(containerId, content, isSuccess = true) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = content;
            container.appendChild(resultDiv);
        }

        // 测试商品库存API
        async function testProductsWithStocksAPI() {
            updateStatus('products-status', 'pending');
            try {
                const data = await apiCall(`${API_BASE}/products?includeWorkplaceStocks=true&page=1&limit=5`);
                updateStatus('products-status', 'success');
                
                const result = `✅ 商品库存API测试成功
返回数据结构: ${JSON.stringify({
                    dataLength: data.data?.length || 0,
                    total: data.total,
                    hasWorkplaceStocks: data.data?.[0]?.workplaceStocks ? '是' : '否',
                    sampleProduct: data.data?.[0] ? {
                        id: data.data[0].id,
                        name: data.data[0].name,
                        stockManagementType: data.data[0].stockManagementType,
                        totalAvailableStock: data.data[0].totalAvailableStock,
                        workplaceStocksCount: data.data[0].workplaceStocks?.length || 0
                    } : '无数据'
                }, null, 2)}`;
                
                showResult('api-results', result, true);
            } catch (error) {
                updateStatus('products-status', 'error');
                showResult('api-results', `❌ 商品库存API测试失败: ${error.message}`, false);
            }
        }

        // 测试职场列表API
        async function testWorkplacesAPI() {
            updateStatus('workplaces-status', 'pending');
            try {
                const data = await apiCall(`${API_BASE}/system/workplaces/active`);
                updateStatus('workplaces-status', 'success');
                
                const result = `✅ 职场列表API测试成功
活跃职场数量: ${data.length}
职场列表: ${JSON.stringify(data.map(wp => ({ id: wp.id, name: wp.name, code: wp.code })), null, 2)}`;
                
                showResult('api-results', result, true);
            } catch (error) {
                updateStatus('workplaces-status', 'error');
                showResult('api-results', `❌ 职场列表API测试失败: ${error.message}`, false);
            }
        }

        // 测试分类列表API
        async function testCategoriesAPI() {
            updateStatus('categories-status', 'pending');
            try {
                const data = await apiCall(`${API_BASE}/categories`);
                updateStatus('categories-status', 'success');
                
                const result = `✅ 分类列表API测试成功
分类数量: ${data.length}
分类列表: ${JSON.stringify(data.map(cat => ({ id: cat.id, name: cat.name })), null, 2)}`;
                
                showResult('api-results', result, true);
            } catch (error) {
                updateStatus('categories-status', 'error');
                showResult('api-results', `❌ 分类列表API测试失败: ${error.message}`, false);
            }
        }

        // 测试产品数据结构
        async function testProductDataStructure() {
            updateStatus('structure-status', 'pending');
            try {
                const data = await apiCall(`${API_BASE}/products?includeWorkplaceStocks=true&page=1&limit=3`);
                updateStatus('structure-status', 'success');
                
                if (!data.data || data.data.length === 0) {
                    throw new Error('没有返回商品数据');
                }
                
                const product = data.data[0];
                const hasWorkplaceStocks = product.workplaceStocks && Array.isArray(product.workplaceStocks);
                
                const result = `✅ 数据结构验证成功
商品ID: ${product.id}
商品名称: ${product.name}
库存管理类型: ${product.stockManagementType}
总库存: ${product.totalAvailableStock || product.stock}
职场库存数据: ${hasWorkplaceStocks ? '存在' : '不存在'}
${hasWorkplaceStocks ? `职场库存详情: ${JSON.stringify(product.workplaceStocks.map(ws => ({
    workplaceId: ws.workplaceId,
    workplaceName: ws.workplaceName,
    availableStock: ws.availableStock,
    isLowStock: ws.isLowStock
})), null, 2)}` : ''}`;
                
                showResult('structure-results', result, true);
            } catch (error) {
                updateStatus('structure-status', 'error');
                showResult('structure-results', `❌ 数据结构验证失败: ${error.message}`, false);
            }
        }

        // 测试活跃职场
        async function testActiveWorkplaces() {
            updateStatus('active-workplaces-status', 'pending');
            try {
                const workplaces = await apiCall(`${API_BASE}/system/workplaces/active`);
                const products = await apiCall(`${API_BASE}/products?includeWorkplaceStocks=true&page=1&limit=1`);
                
                updateStatus('active-workplaces-status', 'success');
                
                const result = `✅ 活跃职场验证成功
活跃职场数量: ${workplaces.length}
职场信息: ${JSON.stringify(workplaces, null, 2)}

商品职场库存匹配验证:
${products.data?.[0]?.workplaceStocks ? 
    products.data[0].workplaceStocks.map(ws => 
        `- ${ws.workplaceName} (ID: ${ws.workplaceId}): ${ws.availableStock}库存`
    ).join('\n') : 
    '无职场库存数据'
}`;
                
                showResult('structure-results', result, true);
            } catch (error) {
                updateStatus('active-workplaces-status', 'error');
                showResult('structure-results', `❌ 活跃职场验证失败: ${error.message}`, false);
            }
        }

        // 运行完整测试
        async function runFullTest() {
            updateStatus('full-test-status', 'pending');
            document.getElementById('full-test-results').innerHTML = '';
            
            try {
                // 模拟库存管理页面的完整加载流程
                const [productsRes, workplacesRes, categoriesRes] = await Promise.all([
                    apiCall(`${API_BASE}/products?includeWorkplaceStocks=true&page=1&limit=10`),
                    apiCall(`${API_BASE}/system/workplaces/active`),
                    apiCall(`${API_BASE}/categories`)
                ]);
                
                updateStatus('full-test-status', 'success');
                
                const result = `🎉 完整测试通过！

📊 库存管理页面数据加载验证:
- 商品数据: ${productsRes.data?.length || 0} 条记录
- 职场数据: ${workplacesRes.length} 个活跃职场
- 分类数据: ${categoriesRes.length} 个分类
- 数据结构: ${productsRes.data?.[0]?.workplaceStocks ? '✅ 包含职场库存' : '❌ 缺少职场库存'}

📦 商品管理页面职场库存列验证:
活跃职场列表: ${workplacesRes.map(wp => wp.name).join(', ')}

🔧 修复状态总结:
问题1 (库存管理页面空白): ${productsRes.data?.length > 0 ? '✅ 已修复' : '❌ 仍存在问题'}
问题2 (职场库存列显示): ${workplacesRes.length > 0 ? '✅ 已修复' : '❌ 仍存在问题'}

💡 建议操作:
1. 使用管理员账户登录: <EMAIL> / 654321
2. 访问库存管理页面验证数据显示
3. 访问商品管理页面验证职场库存列显示`;
                
                showResult('full-test-results', result, true);
            } catch (error) {
                updateStatus('full-test-status', 'error');
                showResult('full-test-results', `❌ 完整测试失败: ${error.message}`, false);
            }
        }

        // 页面加载时自动检查基础连接
        window.onload = function() {
            console.log('职场库存管理系统修复验证页面已加载');
            console.log('管理员登录信息: <EMAIL> / 654321');
        };
    </script>
</body>
</html>
