# ====================================
# 前端测试环境配置文件 (.env.test)
# ====================================
# 此文件用于前端测试环境构建和部署，请勿在本地开发环境使用
# 本地开发请使用 .env.development 文件
# 对应后端配置文件：server/.env.test

# 运行环境：development（开发）/ test（测试）/ production（生产）

# ====================================
# 前端测试服务器配置
# ====================================
# 前端API基础URL（指向测试环境后端服务）
VITE_API_URL=http://**************:3000/api

# 应用标题
VITE_APP_TITLE=光年小卖部

# 应用基础路径
VITE_APP_BASE_URL=/

# ====================================
# 测试环境特定配置
# ====================================
# 运行环境标识
VITE_NODE_ENV=test

# 是否启用开发工具（测试环境建议开启）
VITE_DEV_TOOLS=true

# 是否显示调试信息（测试环境建议开启）
VITE_DEBUG=true

# API请求超时时间（毫秒）
VITE_API_TIMEOUT=15000

# ====================================
# 后端服务配置（用于前端调用）
# ====================================
# 后端服务器地址
VITE_SERVER_URL=http://**************:3000

# WebSocket连接地址（如果需要）
VITE_WS_URL=ws://**************:3000

# ====================================
# 飞书相关配置（前端使用）
# ====================================
# 飞书应用ID（前端可能需要）
VITE_FEISHU_APP_ID=cli_a66b3b2dcab8d013

# 飞书登录回调地址（前端路由）
VITE_FEISHU_CALLBACK_URL=http://**************/feishu/callback

# ====================================
# 性能优化配置
# ====================================
# 是否启用Gzip压缩
VITE_ENABLE_GZIP=true

# 是否启用代码分割
VITE_CODE_SPLITTING=true

# 是否启用Tree Shaking
VITE_TREE_SHAKING=true

# ====================================
# 安全配置
# ====================================
# 是否启用HTTPS（如果配置了SSL证书）
VITE_ENABLE_HTTPS=false

# CSP策略（内容安全策略）
VITE_CSP_ENABLED=true

# ====================================
# 环境检测配置（前端使用）
# ====================================
# 本地开发环境URL配置（用于环境检测和URL修正）
VITE_DEV_SERVER_URL=http://localhost:3000
VITE_DEV_API_URL=http://localhost:3000/api

# 生产环境URL配置
VITE_PROD_SERVER_URL=https://store-api.chongyangqisi.com
VITE_PROD_API_URL=https://store-api.chongyangqisi.com/api
VITE_PROD_FRONTEND_URL=https://store.chongyangqisi.com

# 测试环境URL配置（当前环境）
VITE_TEST_SERVER_URL=http://**************:3000
VITE_TEST_API_URL=http://**************:3000/api
VITE_TEST_FRONTEND_URL=http://**************

# 旧服务器IP（用于兼容性检测）
VITE_OLD_SERVER_IP=**************
