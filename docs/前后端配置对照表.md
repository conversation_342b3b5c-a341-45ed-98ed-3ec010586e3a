# 前后端配置对照表

## 📋 配置文件对应关系

| 环境 | 前端配置文件 | 后端配置文件 | 说明 |
|------|-------------|-------------|------|
| **开发环境** | `.env` | `server/.env` | 本地开发使用 |
| **生产环境** | `.env.production` | `server/.env.production` | 生产部署使用 |

## 🔗 关键配置项对照

### 开发环境配置对照

| 配置类别 | 前端配置项 | 前端配置值 | 后端配置项 | 后端配置值 | 说明 |
|----------|------------|------------|------------|------------|------|
| **环境标识** | `VITE_NODE_ENV` | `development` | `NODE_ENV` | `development` | 环境类型标识 |
| **API地址** | `VITE_API_URL` | `http://localhost:3000/api` | `SERVER_URL` | `http://localhost:3000` | 前端调用后端API |
| **服务器地址** | `VITE_SERVER_URL` | `http://localhost:3000` | `SERVER_URL` | `http://localhost:3000` | 后端服务地址 |
| **应用标题** | `VITE_APP_TITLE` | `光年小卖部` | - | - | 前端应用标题 |
| **基础路径** | `VITE_APP_BASE_URL` | `/` | - | - | 前端路由基础路径 |
| **飞书应用ID** | `VITE_FEISHU_APP_ID` | `cli_a66b3b2dcab8d013` | `FEISHU_APP_ID` | `cli_a66b3b2dcab8d013` | 飞书应用标识 |
| **开发工具** | `VITE_DEV_TOOLS` | `true` | - | - | 前端开发工具 |
| **调试模式** | `VITE_DEBUG` | `true` | `LOG_LEVEL` | `info` | 调试和日志 |

### 生产环境配置对照

| 配置类别 | 前端配置项 | 前端配置值 | 后端配置项 | 后端配置值 | 说明 |
|----------|------------|------------|------------|------------|------|
| **环境标识** | `VITE_NODE_ENV` | `production` | `NODE_ENV` | `production` | 环境类型标识 |
| **API地址** | `VITE_API_URL` | `http://**************/api` | `SERVER_URL` | `http://**************:3000` | 前端调用后端API |
| **服务器地址** | `VITE_SERVER_URL` | `http://**************:3000` | `SERVER_URL` | `http://**************:3000` | 后端服务地址 |
| **应用标题** | `VITE_APP_TITLE` | `光年小卖部` | - | - | 前端应用标题 |
| **基础路径** | `VITE_APP_BASE_URL` | `/` | - | - | 前端路由基础路径 |
| **飞书应用ID** | `VITE_FEISHU_APP_ID` | `cli_a66b3b2dcab8d013` | `FEISHU_APP_ID` | `cli_a66b3b2dcab8d013` | 飞书应用标识 |
| **开发工具** | `VITE_DEV_TOOLS` | `false` | - | - | 前端开发工具（生产关闭） |
| **调试模式** | `VITE_DEBUG` | `false` | `LOG_LEVEL` | `info` | 调试和日志（生产关闭） |

## 🌐 网络配置对照

### 开发环境网络配置

```
前端开发服务器: http://localhost:5173
后端API服务器:  http://localhost:3000
前端API调用:    http://localhost:3000/api
飞书回调地址:    http://localhost:3000/api/feishu/callback
CORS允许源:     http://localhost:5173,http://localhost:5174,...
```

### 生产环境网络配置

```
前端服务器:     http://************** (端口80)
后端API服务器:  http://**************:3000
前端API调用:    http://**************/api (通过Nginx代理)
               或 http://**************:3000/api (直接访问)
飞书回调地址:    http://**************:3000/api/feishu/callback
CORS允许源:     http://**************,http://**************:80
```

## 🔧 配置验证命令

### 验证前端配置

```bash
# 验证前端配置文件
node scripts/validate-frontend-config.cjs
```

### 验证后端配置

```bash
# 验证后端配置文件
node scripts/validate-config.cjs
```

### 验证前后端配置一致性

```bash
# 同时验证前后端配置
node scripts/validate-frontend-config.cjs && node scripts/validate-config.cjs
```

## 📊 配置项完整性检查清单

### 前端必需配置项

- [ ] `VITE_API_URL` - API基础URL
- [ ] `VITE_APP_TITLE` - 应用标题
- [ ] `VITE_APP_BASE_URL` - 应用基础路径
- [ ] `VITE_NODE_ENV` - 环境标识

### 后端必需配置项

- [ ] `NODE_ENV` - 运行环境
- [ ] `PORT` - 服务端口
- [ ] `SERVER_URL` - 服务器URL
- [ ] `DB_HOST` - 数据库主机
- [ ] `DB_NAME` - 数据库名称
- [ ] `JWT_SECRET` - JWT密钥
- [ ] `FEISHU_APP_ID` - 飞书应用ID
- [ ] `FEISHU_APP_SECRET` - 飞书应用密钥
- [ ] `FEISHU_REDIRECT_URI` - 飞书回调URL
- [ ] `CORS_ORIGIN` - CORS允许源

## 🚨 常见配置错误

### 1. API URL不匹配

**错误示例：**
```env
# 前端配置
VITE_API_URL=http://localhost:3000/api

# 后端配置
SERVER_URL=http://localhost:8080  # 端口不匹配
```

**正确配置：**
```env
# 前端配置
VITE_API_URL=http://localhost:3000/api

# 后端配置
SERVER_URL=http://localhost:3000  # 端口匹配
```

### 2. 环境标识不一致

**错误示例：**
```env
# 前端配置
VITE_NODE_ENV=development

# 后端配置
NODE_ENV=production  # 环境不一致
```

**正确配置：**
```env
# 前端配置
VITE_NODE_ENV=production

# 后端配置
NODE_ENV=production  # 环境一致
```

### 3. CORS配置遗漏

**错误示例：**
```env
# 后端配置
CORS_ORIGIN=http://localhost:3000  # 缺少前端域名
```

**正确配置：**
```env
# 后端配置
CORS_ORIGIN=http://localhost:5173,http://localhost:3000  # 包含前端域名
```

## 🔄 配置同步流程

### 1. 修改配置时

1. **同时修改对应的前后端配置文件**
2. **保持配置逻辑一致性**
3. **运行验证脚本检查**
4. **更新相关文档**

### 2. 部署前检查

1. **验证配置文件完整性**
2. **检查前后端配置一致性**
3. **确认网络配置正确**
4. **测试API连接**

### 3. 部署后验证

1. **检查前端应用加载**
2. **验证API调用正常**
3. **测试飞书登录功能**
4. **监控错误日志**

---

**总结：** 通过详细的配置对照表，确保前后端配置的一致性和正确性，避免因配置不匹配导致的部署问题。
