<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公告内容预览功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #409eff;
            padding-bottom: 8px;
        }
        
        /* 模拟表格样式 */
        .mock-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #ebeef5;
            background: white;
        }
        
        .mock-table th,
        .mock-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ebeef5;
            vertical-align: middle;
        }
        
        .mock-table th {
            background: #fafafa;
            font-weight: bold;
            color: #333;
        }
        
        /* 内容预览列样式 */
        .content-preview-container {
            display: flex;
            align-items: center;
            gap: 8px;
            max-width: 220px;
        }
        
        .content-preview-text {
            flex: 1;
            line-height: 1.4;
            font-size: 12px;
            color: #606266;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .preview-btn {
            flex-shrink: 0;
            padding: 4px 8px;
            font-size: 12px;
            background: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .preview-btn:hover {
            background: #337ecc;
        }
        
        /* 模拟预览对话框 */
        .preview-dialog {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: flex-start;
            padding-top: 5vh;
            z-index: 1000;
        }
        
        .preview-dialog.show {
            display: flex;
        }
        
        .preview-dialog-content {
            width: 600px;
            max-width: 95%;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            max-height: 85vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .preview-dialog-header {
            padding: 20px;
            border-bottom: 1px solid #ebeef5;
            font-weight: bold;
            font-size: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #909399;
        }
        
        .close-btn:hover {
            color: #333;
        }
        
        .preview-dialog-body {
            padding: 20px;
            overflow-y: auto;
            flex: 1;
        }
        
        .preview-meta {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            color: white;
        }
        
        .tag.success { background: #67c23a; }
        .tag.info { background: #909399; }
        .tag.warning { background: #e6a23c; }
        
        .preview-time {
            color: #909399;
            font-size: 14px;
            margin-left: auto;
        }
        
        .preview-text {
            line-height: 1.6;
            max-height: 400px;
            overflow-y: auto;
            padding-right: 8px;
            margin-top: 15px;
        }
        
        .formatted-content {
            color: #333;
            font-size: 14px;
        }
        
        .formatted-content strong {
            font-weight: bold;
            color: #409eff;
        }
        
        .formatted-content em {
            font-style: italic;
            color: #67c23a;
        }
        
        /* 滚动条样式 */
        .preview-text::-webkit-scrollbar {
            width: 6px;
        }
        
        .preview-text::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        
        .preview-text::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        
        .preview-text::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .preview-dialog-content {
                width: 95%;
                max-height: 90vh;
            }
            
            .preview-dialog-body {
                padding: 15px;
            }
            
            .preview-text {
                max-height: 300px;
            }
            
            .preview-meta {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .preview-time {
                margin-left: 0;
                margin-top: 5px;
            }
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .feature-list li:before {
            content: "✓";
            color: #67c23a;
            font-weight: bold;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <h1>公告内容预览功能测试</h1>
    
    <div class="test-section">
        <div class="test-title">功能特性说明</div>
        <ul class="feature-list">
            <li>在表格中显示公告内容的简短预览（50字符）</li>
            <li>点击"预览"按钮弹出完整内容预览对话框</li>
            <li>预览对话框支持格式化显示（换行、粗体、斜体）</li>
            <li>支持图片展示和轮播功能</li>
            <li>响应式设计，移动端友好</li>
            <li>美观的滚动条样式</li>
        </ul>
    </div>
    
    <div class="test-section">
        <div class="test-title">管理后台表格预览效果</div>
        <table class="mock-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>标题</th>
                    <th>内容预览</th>
                    <th>类型</th>
                    <th>状态</th>
                    <th>创建时间</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>13</td>
                    <td>热门商品智能识别系统上线</td>
                    <td>
                        <div class="content-preview-container">
                            <div class="content-preview-text">
                                (2025年6月9日) - 热门商品智能识别系统上线 - 🚀 重大功能: 全新热门商品自动识别系统正式上线...
                            </div>
                            <button class="preview-btn" onclick="showPreview('announcement1')">
                                👁 预览
                            </button>
                        </div>
                    </td>
                    <td><span class="tag success">系统更新</span></td>
                    <td><span class="tag info">已发布</span></td>
                    <td>2025/06/10 15:24:52</td>
                </tr>
                <tr>
                    <td>12</td>
                    <td>新品上线通知</td>
                    <td>
                        <div class="content-preview-container">
                            <div class="content-preview-text">
                                亲爱的用户，我们很高兴地宣布一批新商品已经上线，包括最新的数码产品、生活用品等...
                            </div>
                            <button class="preview-btn" onclick="showPreview('announcement2')">
                                👁 预览
                            </button>
                        </div>
                    </td>
                    <td><span class="tag warning">新品</span></td>
                    <td><span class="tag success">已发布</span></td>
                    <td>2025/06/08 10:30:15</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <!-- 预览对话框 -->
    <div class="preview-dialog" id="previewDialog">
        <div class="preview-dialog-content">
            <div class="preview-dialog-header">
                <span id="previewTitle">公告预览</span>
                <button class="close-btn" onclick="closePreview()">&times;</button>
            </div>
            <div class="preview-dialog-body">
                <div class="preview-meta">
                    <span class="tag success" id="previewType">系统更新</span>
                    <span class="tag info" id="previewStatus">已发布</span>
                    <span class="preview-time" id="previewTime">创建时间: 2025/06/10 15:24:52</span>
                </div>
                
                <div class="preview-text">
                    <div class="formatted-content" id="previewContent">
                        <!-- 内容将通过JavaScript填充 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟公告数据
        const announcements = {
            announcement1: {
                title: '热门商品智能识别系统上线',
                type: '系统更新',
                status: '已发布',
                time: '2025/06/10 15:24:52',
                content: `(2025年6月9日) - 热门商品智能识别系统上线
- 🚀 **重大功能**: 全新热门商品自动识别系统正式上线
- ✨ **智能识别算法**:
  - 基于兑换量的自动热门商品识别机制
  - 支持四个时间维度：累积/30天/7天/今日热门
  - 智能评分算法：兑换量权重 + 库存奖励机制
  - 可配置的热门商品数量上限和最小兑换量要求
- ✨ **管理后台功能**:
  - 热门商品规则配置页面，支持实时参数调整
  - 四个时间维度的独立配置管理
  - 手动触发更新功能，支持一键刷新所有维度
  - 热门商品统计报表和历史记录查询
- ✨ **前端展示优化**:
  - 商品卡片智能热门标签显示（区分手动/自动热门）
  - 热门商品排行榜组件，支持多时间维度切换
  - 首页热门商品展示区域集成
  - 商品详情页热门度信息展示
- ✨ **自动化机制**:
  - 集成定时任务系统，每小时自动更新热门商品
  - 历史数据记录和过期数据清理机制
  - 并发安全的更新机制，防止重复执行`
            },
            announcement2: {
                title: '新品上线通知',
                type: '新品',
                status: '已发布',
                time: '2025/06/08 10:30:15',
                content: `亲爱的用户，我们很高兴地宣布一批新商品已经上线！

**本次上线的商品包括：**
- 📱 **数码产品**: 最新款智能手机配件、充电器、数据线等
- 🏠 **生活用品**: 家居装饰、厨房用品、清洁用品等
- 📚 **办公用品**: 文具、笔记本、办公设备等
- 🎮 **娱乐用品**: 游戏周边、玩具、运动用品等

**兑换说明：**
- 所有新品均支持光年币兑换
- 部分商品支持现金购买
- 库存有限，先到先得

**特别提醒：**
- 请及时查看商品详情和库存情况
- 如有疑问，请联系前台工作人员
- 感谢大家的支持！`
            }
        };

        // 格式化内容
        function formatContent(content) {
            return content
                .replace(/\n/g, '<br>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>');
        }

        // 显示预览
        function showPreview(announcementId) {
            const announcement = announcements[announcementId];
            if (!announcement) return;

            document.getElementById('previewTitle').textContent = announcement.title;
            document.getElementById('previewType').textContent = announcement.type;
            document.getElementById('previewStatus').textContent = announcement.status;
            document.getElementById('previewTime').textContent = '创建时间: ' + announcement.time;
            document.getElementById('previewContent').innerHTML = formatContent(announcement.content);
            
            document.getElementById('previewDialog').classList.add('show');
        }

        // 关闭预览
        function closePreview() {
            document.getElementById('previewDialog').classList.remove('show');
        }

        // 点击背景关闭
        document.getElementById('previewDialog').addEventListener('click', function(e) {
            if (e.target === this) {
                closePreview();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closePreview();
            }
        });
    </script>
</body>
</html>
