/**
 * 价格快照功能测试
 * 测试商品价格更新不会影响历史订单的价格
 */

const { sequelize } = require('../server/config/database');
const { Product, Exchange, ProductPriceHistory, User, Category } = require('../server/models');

// 测试数据
let testUser, testCategory, testProduct, testExchange;

async function setupTestData() {
  console.log('设置测试数据...');

  // 创建测试用户
  testUser = await User.create({
    username: 'test_user_price',
    email: '<EMAIL>',
    password: 'test123',
    role: 'user',
    points: 1000
  });

  // 创建测试分类
  testCategory = await Category.create({
    name: '测试分类_价格',
    description: '用于价格测试的分类'
  });

  // 创建测试商品
  testProduct = await Product.create({
    name: '测试商品_价格快照',
    categoryId: testCategory.id,
    lyPrice: 100,
    rmbPrice: 25.00,
    description: '用于测试价格快照功能的商品',
    stock: 50,
    status: 'active'
  });

  console.log(`测试数据创建完成: 用户ID=${testUser.id}, 商品ID=${testProduct.id}`);
}

async function testPriceSnapshot() {
  console.log('\n=== 测试价格快照功能 ===');

  try {
    // 1. 创建订单（使用当前价格）
    console.log('\n1. 创建订单...');

    // 手动设置价格快照（模拟控制器逻辑）
    const unitPrice = parseFloat(testProduct.rmbPrice);
    const priceType = 'rmb';

    testExchange = await Exchange.create({
      userId: testUser.id,
      productId: testProduct.id,
      quantity: 2,
      unitPrice: unitPrice,
      priceType: priceType,
      paymentMethod: 'rmb',
      contactInfo: '测试联系方式',
      location: '测试地址',
      status: 'pending'
    });

    console.log(`订单创建成功: ID=${testExchange.id}`);
    console.log(`价格快照: 单价=${testExchange.unitPrice}, 总额=${testExchange.totalAmount}`);

    // 验证价格快照
    if (testExchange.unitPrice !== parseFloat(testProduct.rmbPrice)) {
      throw new Error(`价格快照错误: 期望 ${testProduct.rmbPrice}, 实际 ${testExchange.unitPrice}`);
    }

    if (testExchange.totalAmount !== testExchange.unitPrice * testExchange.quantity) {
      throw new Error(`总额计算错误: 期望 ${testExchange.unitPrice * testExchange.quantity}, 实际 ${testExchange.totalAmount}`);
    }

    console.log('✅ 订单价格快照正确');

    // 2. 更新商品价格
    console.log('\n2. 更新商品价格...');
    const oldPrice = testProduct.rmbPrice;
    const newPrice = 30.00;

    await testProduct.update({
      rmbPrice: newPrice
    }, {
      userId: testUser.id,
      changeReason: '测试价格更新'
    });

    console.log(`商品价格已更新: ${oldPrice} -> ${newPrice}`);

    // 3. 验证价格历史记录
    console.log('\n3. 验证价格历史记录...');
    const priceHistory = await ProductPriceHistory.findOne({
      where: { productId: testProduct.id },
      order: [['createdAt', 'DESC']]
    });

    if (!priceHistory) {
      throw new Error('价格历史记录未创建');
    }

    if (parseFloat(priceHistory.oldRmbPrice) !== parseFloat(oldPrice) || parseFloat(priceHistory.newRmbPrice) !== parseFloat(newPrice)) {
      throw new Error(`价格历史记录错误: 期望 ${oldPrice} -> ${newPrice}, 实际 ${priceHistory.oldRmbPrice} -> ${priceHistory.newRmbPrice}`);
    }

    console.log('✅ 价格历史记录正确');

    // 4. 验证历史订单价格不变
    console.log('\n4. 验证历史订单价格不变...');
    await testExchange.reload();

    if (parseFloat(testExchange.unitPrice) !== parseFloat(oldPrice)) {
      throw new Error(`历史订单价格被错误修改: 期望 ${oldPrice}, 实际 ${testExchange.unitPrice}`);
    }

    console.log('✅ 历史订单价格保持不变');

    // 5. 创建新订单验证使用新价格
    console.log('\n5. 创建新订单验证使用新价格...');
    const newExchange = await Exchange.create({
      userId: testUser.id,
      productId: testProduct.id,
      quantity: 1,
      unitPrice: newPrice,
      priceType: 'rmb',
      paymentMethod: 'rmb',
      contactInfo: '测试联系方式2',
      location: '测试地址2',
      status: 'pending'
    });

    if (parseFloat(newExchange.unitPrice) !== parseFloat(newPrice)) {
      throw new Error(`新订单价格错误: 期望 ${newPrice}, 实际 ${newExchange.unitPrice}`);
    }

    console.log('✅ 新订单使用新价格');

    // 6. 测试光年币价格
    console.log('\n6. 测试光年币价格快照...');
    const lyExchange = await Exchange.create({
      userId: testUser.id,
      productId: testProduct.id,
      quantity: 1,
      unitPrice: testProduct.lyPrice,
      priceType: 'ly',
      paymentMethod: 'ly',
      contactInfo: '测试联系方式3',
      location: '测试地址3',
      status: 'pending'
    });

    if (parseFloat(lyExchange.unitPrice) !== parseFloat(testProduct.lyPrice)) {
      throw new Error(`光年币价格快照错误: 期望 ${testProduct.lyPrice}, 实际 ${lyExchange.unitPrice}`);
    }

    if (lyExchange.priceType !== 'ly') {
      throw new Error(`价格类型错误: 期望 ly, 实际 ${lyExchange.priceType}`);
    }

    console.log('✅ 光年币价格快照正确');

    console.log('\n🎉 所有测试通过！');
    return true;

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    return false;
  }
}

async function cleanupTestData() {
  console.log('\n清理测试数据...');

  try {
    // 删除测试订单
    await Exchange.destroy({
      where: { userId: testUser.id }
    });

    // 删除价格历史
    await ProductPriceHistory.destroy({
      where: { productId: testProduct.id }
    });

    // 删除测试商品
    await testProduct.destroy();

    // 删除测试分类
    await testCategory.destroy();

    // 删除测试用户
    await testUser.destroy();

    console.log('✅ 测试数据清理完成');

  } catch (error) {
    console.error('清理测试数据失败:', error);
  }
}

async function runTests() {
  try {
    console.log('=== 价格快照功能测试 ===\n');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功\n');

    // 设置测试数据
    await setupTestData();

    // 运行测试
    const success = await testPriceSnapshot();

    // 清理测试数据
    await cleanupTestData();

    if (success) {
      console.log('\n🎉 所有测试通过！价格快照功能正常工作。');
      process.exit(0);
    } else {
      console.log('\n❌ 测试失败！');
      process.exit(1);
    }

  } catch (error) {
    console.error('\n❌ 测试运行失败:', error);
    await cleanupTestData();
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests();
}

module.exports = {
  setupTestData,
  testPriceSnapshot,
  cleanupTestData,
  runTests
};
