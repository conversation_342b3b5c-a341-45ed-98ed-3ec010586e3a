const express = require('express');
const router = express.Router();
const announcementController = require('../controllers/announcementController');
const { authenticate } = require('../middlewares/auth');
const { isAdmin } = require('../middlewares/admin');
const upload = require('../middlewares/upload');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const config = require('../config/config');

// 确保上传目录存在
const uploadDir = path.join(__dirname, '..', config.upload.directory);
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
  console.log('创建上传目录:', uploadDir);
} else {
  console.log('上传目录已存在:', uploadDir);
}

// 获取公告列表 - 公开访问
router.get('/', announcementController.getAnnouncements);

// 获取单个公告详情 - 公开访问
router.get('/:id', announcementController.getAnnouncementById);

// 专门用于上传图片的路由 - 直接处理文件上传
router.post('/upload', authenticate, isAdmin, (req, res) => {
  console.log('开始处理图片上传');
  
  // 创建一个临时的multer实例进行直接处理
  const storage = multer.diskStorage({
    destination: function (req, file, cb) {
      console.log('设置上传文件目录:', uploadDir);
      cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
      // 生成唯一文件名，防止文件名冲突
      const uniqueSuffix = crypto.randomBytes(16).toString('hex');
      const extension = path.extname(file.originalname);
      const filename = uniqueSuffix + extension;
      console.log('生成上传文件名:', filename, '原始文件名:', file.originalname);
      cb(null, filename);
    }
  });
  
  // 文件过滤器
  const fileFilter = (req, file, cb) => {
    // 检查文件类型是否允许
    const allowedTypes = config.upload.allowedTypes || ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    console.log('上传文件类型:', file.mimetype, '允许的类型:', allowedTypes);
    
    if (allowedTypes.includes(file.mimetype)) {
      console.log('文件类型验证通过');
      cb(null, true);
    } else {
      console.error('文件类型不允许:', file.mimetype);
      cb(new Error(`只允许以下文件类型: ${allowedTypes.join(', ')}`), false);
    }
  };
  
  // 创建一个临时multer实例并立即使用
  const singleUpload = multer({
    storage: storage,
    limits: {
      fileSize: config.upload.maxSize // 使用配置中的文件大小限制
    },
    fileFilter: fileFilter
  }).single('image');
  
  // 手动执行上传并处理错误
  singleUpload(req, res, function(err) {
    if (err) {
      console.error('上传错误详情:', err);
      
      if (err instanceof multer.MulterError) {
        console.error('Multer错误:', err.code, err.message);
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(413).json({ 
            message: `文件大小超过限制，最大允许 ${config.upload.maxSize / (1024 * 1024)} MB`
          });
        }
        return res.status(400).json({ message: `上传错误: ${err.message}` });
      } else {
        console.error('非Multer错误:', err.message);
        return res.status(400).json({ message: err.message });
      }
    }
    
    if (!req.file) {
      console.error('未找到上传的文件');
      return res.status(400).json({ message: '没有上传文件' });
    }
    
    // 构建图片URL - 确保在生产环境中使用正确的服务器地址
    let baseUrl;

    // 检查当前环境
    const isProduction = process.env.NODE_ENV === 'production';
    const isLocalDevelopment = process.env.NODE_ENV === 'development';

    if (isProduction) {
      // 生产环境：强制使用生产服务器地址
      baseUrl = process.env.SERVER_URL || 'https://store-api.chongyangqisi.com';
      console.log('生产环境：使用固定的生产服务器地址');
    } else if (isLocalDevelopment) {
      // 开发环境：使用localhost
      baseUrl = 'http://localhost:3000';
      console.log('开发环境：使用localhost地址');
    } else {
      // 其他情况：使用相对路径
      baseUrl = '';
      console.log('其他环境：使用相对路径');
    }

    const imageUrl = baseUrl ? `${baseUrl}/uploads/${req.file.filename}` : `uploads/${req.file.filename}`;
    console.log('成功上传图片，环境:', process.env.NODE_ENV, '图片URL:', imageUrl);
    
    // 返回图片URL给前端
    res.json({ imageUrl });
  });
});

// 以下路由需要管理员权限
// 创建新公告
router.post('/', authenticate, isAdmin, upload.single('image'), upload.handleError, announcementController.createAnnouncement);

// 更新公告
router.put('/:id', authenticate, isAdmin, upload.single('image'), upload.handleError, announcementController.updateAnnouncement);

// 更新公告状态
router.put('/:id/status', authenticate, isAdmin, announcementController.updateAnnouncementStatus);

// 删除公告
router.delete('/:id', authenticate, isAdmin, announcementController.deleteAnnouncement);

module.exports = router; 