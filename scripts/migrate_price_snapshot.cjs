#!/usr/bin/env node

/**
 * 价格快照数据迁移脚本
 * 用于将现有订单数据迁移到新的价格快照结构
 */

const { sequelize } = require('../server/config/database');
const { Exchange, Product } = require('../server/models');

async function migrateExistingOrders() {
  console.log('开始迁移现有订单的价格快照数据...');
  
  try {
    // 开启事务
    const transaction = await sequelize.transaction();
    
    try {
      // 查找所有没有价格快照的订单
      const ordersWithoutSnapshot = await Exchange.findAll({
        where: {
          unitPrice: null
        },
        include: [
          {
            model: Product,
            attributes: ['id', 'name', 'lyPrice', 'rmbPrice']
          }
        ],
        transaction
      });

      console.log(`找到 ${ordersWithoutSnapshot.length} 个需要迁移的订单`);

      let successCount = 0;
      let failCount = 0;

      for (const order of ordersWithoutSnapshot) {
        try {
          if (!order.Product) {
            console.warn(`订单 ${order.id} 关联的商品不存在，跳过`);
            failCount++;
            continue;
          }

          // 根据支付方式确定价格快照
          let unitPrice, priceType;
          if (order.paymentMethod === 'ly') {
            unitPrice = order.Product.lyPrice;
            priceType = 'ly';
          } else {
            unitPrice = parseFloat(order.Product.rmbPrice);
            priceType = 'rmb';
          }

          // 更新订单的价格快照
          await order.update({
            unitPrice: unitPrice,
            priceType: priceType,
            totalAmount: unitPrice * order.quantity
          }, { transaction });

          console.log(`订单 ${order.id} 价格快照迁移完成: ${unitPrice} x ${order.quantity} = ${unitPrice * order.quantity}`);
          successCount++;

        } catch (error) {
          console.error(`订单 ${order.id} 迁移失败:`, error.message);
          failCount++;
        }
      }

      // 提交事务
      await transaction.commit();
      
      console.log(`\n迁移完成！`);
      console.log(`成功: ${successCount} 个订单`);
      console.log(`失败: ${failCount} 个订单`);
      
      return { successCount, failCount };

    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }

  } catch (error) {
    console.error('迁移过程中发生错误:', error);
    throw error;
  }
}

async function validateMigration() {
  console.log('\n开始验证迁移结果...');
  
  try {
    // 检查是否还有未迁移的订单
    const unmigrated = await Exchange.count({
      where: {
        unitPrice: null
      }
    });

    if (unmigrated > 0) {
      console.warn(`警告: 仍有 ${unmigrated} 个订单未完成迁移`);
      return false;
    }

    // 检查价格快照的一致性
    const inconsistentOrders = await Exchange.findAll({
      include: [
        {
          model: Product,
          attributes: ['id', 'lyPrice', 'rmbPrice']
        }
      ],
      where: sequelize.literal(`
        (paymentMethod = 'ly' AND unitPrice != Product.lyPrice) OR
        (paymentMethod = 'rmb' AND unitPrice != Product.rmbPrice)
      `)
    });

    if (inconsistentOrders.length > 0) {
      console.log(`发现 ${inconsistentOrders.length} 个订单的价格快照与当前商品价格不一致（这是正常的，说明价格快照功能正常工作）`);
      
      // 显示前5个示例
      for (let i = 0; i < Math.min(5, inconsistentOrders.length); i++) {
        const order = inconsistentOrders[i];
        const currentPrice = order.paymentMethod === 'ly' ? order.Product.lyPrice : order.Product.rmbPrice;
        console.log(`  订单 ${order.id}: 快照价格 ${order.unitPrice}, 当前价格 ${currentPrice}`);
      }
    }

    console.log('✅ 迁移验证通过！');
    return true;

  } catch (error) {
    console.error('验证过程中发生错误:', error);
    return false;
  }
}

// 主函数
async function main() {
  try {
    console.log('=== 价格快照数据迁移工具 ===\n');
    
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功\n');

    // 执行迁移
    const result = await migrateExistingOrders();
    
    // 验证迁移结果
    const isValid = await validateMigration();
    
    if (isValid) {
      console.log('\n🎉 迁移成功完成！');
      process.exit(0);
    } else {
      console.log('\n❌ 迁移验证失败，请检查日志');
      process.exit(1);
    }

  } catch (error) {
    console.error('\n❌ 迁移失败:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  migrateExistingOrders,
  validateMigration
};
