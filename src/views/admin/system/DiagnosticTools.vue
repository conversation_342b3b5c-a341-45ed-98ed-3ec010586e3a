<template>
  <ErrorBoundary>
    <div class="diagnostic-container">
    <el-card shadow="never" class="diagnostic-card">
      <template #header>
        <div class="card-header">
          <h2>高级诊断工具</h2>
          <div class="header-right">
            <el-button type="success" @click="handleRunComprehensive">
              <el-icon><Monitor /></el-icon>全面诊断
            </el-button>
          </div>
        </div>
      </template>

      <!-- 诊断工具菜单 -->
      <div class="tools-container">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-card shadow="hover" class="tool-card" @click="handleSystemHealth">
              <div class="tool-icon">
                <el-icon><CPU /></el-icon>
              </div>
              <div class="tool-name">系统健康检查</div>
              <div class="tool-desc">检测当前系统的CPU、内存使用率和运行状态</div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover" class="tool-card" @click="handleWebhookTest">
              <div class="tool-icon">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="tool-name">Webhook连接测试</div>
              <div class="tool-desc">测试飞书Webhook连接状态和响应时间</div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover" class="tool-card" @click="handleMessageAnalysis">
              <div class="tool-icon">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="tool-name">消息发送分析</div>
              <div class="tool-desc">分析消息发送成功率和各类型消息状态</div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 搜索过滤 -->
      <div class="filter-container">
        <el-form :inline="true" :model="searchForm" class="demo-form-inline">
          <el-form-item label="诊断类型">
            <el-select v-model="searchForm.diagnosticType" placeholder="选择诊断类型" clearable>
              <el-option label="系统健康" value="system_health"></el-option>
              <el-option label="Webhook连接" value="webhook_connection"></el-option>
              <el-option label="消息发送" value="message_delivery"></el-option>
              <el-option label="全面诊断" value="comprehensive"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
              <el-option label="成功" value="success"></el-option>
              <el-option label="警告" value="warning"></el-option>
              <el-option label="错误" value="error"></el-option>
              <el-option label="进行中" value="pending"></el-option>
              <el-option label="已解决" value="resolved"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              clearable
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 诊断记录列表 -->
      <el-table :data="diagnosticList" style="width: 100%" v-loading="loading" border>
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="diagnosticType" label="诊断类型" width="120">
          <template #default="scope">
            <el-tag
              :type="getDiagnosticTypeTag(scope.row.diagnosticType)"
            >
              {{ getDiagnosticTypeName(scope.row.diagnosticType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag
              :type="getStatusTag(scope.row.status)"
              effect="dark"
            >
              {{ getStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="details" label="诊断详情" min-width="200">
          <template #default="scope">
            <div v-if="scope.row.details" class="details-preview">
              <template v-if="scope.row.diagnosticType === 'system_health'">
                <div><strong>CPU:</strong> {{ scope.row.details.memory?.usage || 0 }}%</div>
                <div><strong>内存:</strong> {{ scope.row.details.cpu?.usage || 0 }}%</div>
                <div v-if="scope.row.details.status"><strong>状态:</strong> {{ scope.row.details.status }}</div>
              </template>
              <template v-else-if="scope.row.diagnosticType === 'webhook_connection'">
                <div><strong>Webhook地址:</strong> {{ scope.row.details.webhookUrl }}</div>
              </template>
              <template v-else-if="scope.row.diagnosticType === 'message_delivery'">
                <div><strong>成功率:</strong> {{ scope.row.details.stats?.successRate || 0 }}%</div>
                <div><strong>总消息:</strong> {{ scope.row.details.stats?.total || 0 }}</div>
              </template>
              <template v-else>
                <div class="json-preview">
                  {{ JSON.stringify(scope.row.details).substring(0, 100) }}...
                </div>
              </template>
            </div>
            <span v-else class="text-muted">无详情</span>
          </template>
        </el-table-column>
        <el-table-column prop="errorMessage" label="错误信息" min-width="150">
          <template #default="scope">
            <div v-if="scope.row.errorMessage" class="error-message">
              {{ scope.row.errorMessage }}
            </div>
            <span v-else class="text-muted">--</span>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="resolvedAt" label="解决时间" width="180">
          <template #default="scope">
            <span v-if="scope.row.resolvedAt">{{ formatDate(scope.row.resolvedAt) }}</span>
            <span v-else class="text-muted">--</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleViewDetails(scope.row)">查看详情</el-button>
            <el-button v-if="scope.row.status === 'error' || scope.row.status === 'warning'"
              size="small" type="primary" @click="handleResolve(scope.row)">解决</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          v-model:current-page="page"
          v-model:page-size="pageSize"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </el-card>
  </div>

  <!-- Webhook测试对话框 -->
  <el-dialog
    v-model="webhookDialogVisible"
    title="Webhook连接测试"
    width="50%"
  >
    <el-form :model="webhookForm" ref="webhookFormRef" label-width="120px">
      <el-form-item label="Webhook地址" prop="webhookUrl" :rules="[
        { required: true, message: '请输入Webhook地址', trigger: 'blur' },
        { pattern: /^https?:\/\/.+/, message: '请输入有效的URL', trigger: 'blur' }
      ]">
        <el-input v-model="webhookForm.webhookUrl" placeholder="请输入飞书群机器人的Webhook地址"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitWebhookTest">开始测试</el-button>
      </el-form-item>
    </el-form>
    <div v-if="testResult" class="test-result">
      <div class="result-header" :class="{'success': testResult.success, 'error': !testResult.success}">
        <el-icon v-if="testResult.success"><SuccessFilled /></el-icon>
        <el-icon v-else><WarningFilled /></el-icon>
        <span>{{ testResult.message }}</span>
      </div>
      <div v-if="testResult.error" class="result-error">
        错误信息: {{ testResult.error }}
      </div>
      <div class="result-time">
        测试时间: {{ formatDate(testResult.timestamp) }}
      </div>
    </div>
  </el-dialog>

  <!-- 消息分析对话框 -->
  <el-dialog
    v-model="analysisDialogVisible"
    title="消息发送分析"
    width="60%"
  >
    <el-form :model="analysisForm" ref="analysisFormRef" label-width="120px">
      <el-form-item label="时间范围">
        <el-date-picker
          v-model="analysisForm.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="通知类型">
        <el-select v-model="analysisForm.notificationType" placeholder="选择通知类型" clearable>
          <el-option label="全部" value=""></el-option>
          <el-option label="订单消息" value="exchange"></el-option>
          <el-option label="系统通知" value="system"></el-option>
          <el-option label="用户消息" value="user"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitAnalysis">开始分析</el-button>
      </el-form-item>
    </el-form>
    <div v-if="analysisResult" class="analysis-result">
      <div class="analysis-header">
        <h3>分析结果</h3>
        <p>分析时段: {{ analysisResult.period?.start }} 至 {{ analysisResult.period?.end }}</p>
      </div>
      <div class="analysis-summary">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-value">{{ analysisResult.total || 0 }}</div>
              <div class="metric-label">总消息数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-value">{{ analysisResult.success || 0 }}</div>
              <div class="metric-label">成功数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-value">{{ analysisResult.failed || 0 }}</div>
              <div class="metric-label">失败数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-value">{{ analysisResult.successRate || 0 }}%</div>
              <div class="metric-label">成功率</div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div v-if="analysisResult.byType" class="analysis-by-type">
        <h4>各类型消息情况</h4>
        <el-table :data="formatTypeData(analysisResult.byType)" border size="small">
          <el-table-column prop="type" label="消息类型"></el-table-column>
          <el-table-column prop="total" label="总数"></el-table-column>
          <el-table-column prop="success" label="成功"></el-table-column>
          <el-table-column prop="failed" label="失败"></el-table-column>
          <el-table-column prop="rate" label="成功率">
            <template #default="scope">
              {{ scope.row.rate }}%
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-if="analysisResult.failureReasons" class="analysis-failures">
        <h4>失败原因分析</h4>
        <el-table :data="formatReasonData(analysisResult.failureReasons)" border size="small">
          <el-table-column prop="reason" label="失败原因"></el-table-column>
          <el-table-column prop="count" label="次数"></el-table-column>
          <el-table-column prop="percentage" label="占比">
            <template #default="scope">
              {{ scope.row.percentage }}%
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </el-dialog>

  <!-- 详情对话框 -->
  <el-dialog
    v-model="detailsDialogVisible"
    title="诊断详情"
    width="70%"
  >
    <div v-if="selectedDiagnostic" class="diagnostic-details">
      <div class="details-header">
        <div class="details-title">
          <el-tag :type="getDiagnosticTypeTag(selectedDiagnostic.diagnosticType)" size="large">
            {{ getDiagnosticTypeName(selectedDiagnostic.diagnosticType) }}
          </el-tag>
          <el-tag :type="getStatusTag(selectedDiagnostic.status)" effect="dark" class="ml-2" size="large">
            {{ getStatusName(selectedDiagnostic.status) }}
          </el-tag>
        </div>
        <div class="details-time">
          <p>创建时间: {{ formatDate(selectedDiagnostic.createdAt) }}</p>
          <p v-if="selectedDiagnostic.resolvedAt">解决时间: {{ formatDate(selectedDiagnostic.resolvedAt) }}</p>
        </div>
      </div>
      <div v-if="selectedDiagnostic.errorMessage" class="details-error">
        <h4>错误信息</h4>
        <pre>{{ selectedDiagnostic.errorMessage }}</pre>
      </div>
      <div v-if="selectedDiagnostic.resolution" class="details-resolution">
        <h4>解决方案</h4>
        <pre>{{ selectedDiagnostic.resolution }}</pre>
      </div>
      <div class="details-content">
        <h4>详细数据</h4>
        <pre>{{ JSON.stringify(selectedDiagnostic.details, null, 2) }}</pre>
      </div>
    </div>
  </el-dialog>

  <!-- 解决问题对话框 -->
  <el-dialog
    v-model="resolveDialogVisible"
    title="解决问题"
    width="50%"
  >
    <el-form :model="resolveForm" ref="resolveFormRef" label-width="100px">
      <el-form-item label="问题ID">
        <span>{{ resolveForm.id }}</span>
      </el-form-item>
      <el-form-item label="诊断类型">
        <span>{{ getDiagnosticTypeName(resolveForm.diagnosticType) }}</span>
      </el-form-item>
      <el-form-item label="错误信息">
        <div class="error-info">{{ resolveForm.errorMessage }}</div>
      </el-form-item>
      <el-form-item label="解决方案" prop="resolution" :rules="[
        { required: true, message: '请输入解决方案', trigger: 'blur' }
      ]">
        <el-input
          v-model="resolveForm.resolution"
          type="textarea"
          rows="4"
          placeholder="请详细描述问题的解决方案"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitResolve">提交解决方案</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>

  <!-- 系统健康结果对话框 -->
  <el-dialog
    v-model="healthDialogVisible"
    title="系统健康检查"
    width="60%"
  >
    <div v-if="healthResult" class="health-result" v-loading="healthLoading">
      <div class="health-header">
        <h3>系统状态:
          <el-tag :type="getHealthStatusTag(healthResult.status)" size="large">
            {{ getHealthStatusName(healthResult.status) }}
          </el-tag>
        </h3>
        <p>检查时间: {{ formatDate(healthResult.timestamp) }}</p>
      </div>
      <div class="health-metrics">
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>CPU使用率</h4>
            <el-progress
              :percentage="healthResult.memory?.usage || 0"
              :color="getMemoryProgressColor(healthResult.memory?.usage || 0)"
              :format="format => `${format}%`"
            ></el-progress>
            <div class="metric-details">
              <p>核心数: {{ healthResult.cpu?.cores || 0 }}</p>
              <p>负载: {{ healthResult.cpu?.loadAvg?.[0]?.toFixed(2) || 0 }}</p>
            </div>
          </el-col>
          <el-col :span="12">
            <h4>内存使用率</h4>
            <el-progress
              :percentage="healthResult.memory?.usage || 0"
              :color="getMemoryProgressColor(healthResult.memory?.usage || 0)"
              :format="format => `${format}%`"
            ></el-progress>
            <div class="metric-details">
              <p>总内存: {{ formatBytes(healthResult.memory?.total || 0) }}</p>
              <p>可用内存: {{ formatBytes(healthResult.memory?.free || 0) }}</p>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="health-uptime">
        <h4>系统运行时间</h4>
        <p>{{ healthResult.uptime?.formatted || '0小时0分钟' }}</p>
      </div>
      <div v-if="healthResult.issues && healthResult.issues.length > 0" class="health-issues">
        <h4>检测到的问题</h4>
        <ul>
          <li v-for="(issue, index) in healthResult.issues" :key="index">
            {{ issue }}
          </li>
        </ul>
      </div>
    </div>
  </el-dialog>
  </ErrorBoundary>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Monitor, Connection, DataAnalysis, SuccessFilled, WarningFilled } from '@element-plus/icons-vue';
import axios from 'axios';
import ErrorBoundary from '@/components/ErrorBoundary.vue';

// 数据定义
const loading = ref(false);
const healthLoading = ref(false);
const diagnosticList = ref([]);
const total = ref(0);
const page = ref(1);
const pageSize = ref(10);
const webhookDialogVisible = ref(false);
const analysisDialogVisible = ref(false);
const detailsDialogVisible = ref(false);
const resolveDialogVisible = ref(false);
const healthDialogVisible = ref(false);
const selectedDiagnostic = ref(null);
const testResult = ref(null);
const analysisResult = ref(null);
const healthResult = ref(null);
const webhookFormRef = ref(null);
const analysisFormRef = ref(null);
const resolveFormRef = ref(null);

// 搜索表单
const searchForm = reactive({
  diagnosticType: '',
  status: '',
  dateRange: null
});

// Webhook测试表单
const webhookForm = reactive({
  webhookUrl: ''
});

// 分析表单
const analysisForm = reactive({
  dateRange: null,
  notificationType: ''
});

// 解决问题表单
const resolveForm = reactive({
  id: null,
  diagnosticType: '',
  errorMessage: '',
  resolution: ''
});

// 初始化
onMounted(() => {
  fetchDiagnostics();
});

// 获取诊断记录列表
const fetchDiagnostics = async () => {
  loading.value = true;
  try {
    const params = {
      page: page.value,
      pageSize: pageSize.value,
      diagnosticType: searchForm.diagnosticType,
      status: searchForm.status
    };

    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0];
      params.endDate = searchForm.dateRange[1];
    }

    const response = await axios.get('/api/notification-diagnostics', { params });

    if (response.data.code === 0) {
      diagnosticList.value = response.data.data?.data || [];
      total.value = response.data.data?.total || 0;
    } else {
      ElMessage.error(response.data.message || '获取诊断记录失败');
      // 设置默认值避免页面崩溃
      diagnosticList.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取诊断记录失败:', error);
    ElMessage.error('获取诊断记录失败: ' + (error.response?.data?.message || error.message));
    // 设置默认值避免页面崩溃
    diagnosticList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  page.value = 1;
  fetchDiagnostics();
};

// 重置搜索
const resetSearch = () => {
  searchForm.diagnosticType = '';
  searchForm.status = '';
  searchForm.dateRange = null;
  handleSearch();
};

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val;
  fetchDiagnostics();
};

const handleCurrentChange = (val) => {
  page.value = val;
  fetchDiagnostics();
};

// 系统健康检查
const handleSystemHealth = async () => {
  healthDialogVisible.value = true;
  healthLoading.value = true;
  healthResult.value = null;

  try {
    const response = await axios.get('/api/notification-diagnostics/system-health');

    if (response.data.code === 0) {
      healthResult.value = response.data.data;
    } else {
      ElMessage.error(response.data.message || '系统健康检查失败');
    }
  } catch (error) {
    console.error('系统健康检查失败:', error);
    ElMessage.error('系统健康检查失败: ' + (error.response?.data?.message || error.message));
  } finally {
    healthLoading.value = false;
  }
};

// Webhook测试
const handleWebhookTest = () => {
  webhookDialogVisible.value = true;
  webhookForm.webhookUrl = '';
  testResult.value = null;
};

// 提交Webhook测试
const submitWebhookTest = async () => {
  if (!webhookFormRef.value) return;

  await webhookFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const response = await axios.post('/api/notification-diagnostics/test-webhook', {
          webhookUrl: webhookForm.webhookUrl
        });

        if (response.data.code === 0) {
          testResult.value = response.data.data;
        } else {
          ElMessage.error(response.data.message || '测试失败');
        }
      } catch (error) {
        console.error('Webhook测试失败:', error);
        ElMessage.error('Webhook测试失败: ' + (error.response?.data?.message || error.message));
      }
    }
  });
};

// 消息发送分析
const handleMessageAnalysis = () => {
  analysisDialogVisible.value = true;
  analysisForm.dateRange = null;
  analysisForm.notificationType = '';
  analysisResult.value = null;
};

// 提交分析请求
const submitAnalysis = async () => {
  try {
    let startDate, endDate;

    if (analysisForm.dateRange && analysisForm.dateRange.length === 2) {
      [startDate, endDate] = analysisForm.dateRange;
    }

    const response = await axios.post('/api/notification-diagnostics/analyze-message-delivery', {
      startDate,
      endDate,
      notificationType: analysisForm.notificationType
    });

    if (response.data.code === 0) {
      analysisResult.value = response.data.data;
    } else {
      ElMessage.error(response.data.message || '分析失败');
    }
  } catch (error) {
    console.error('消息发送分析失败:', error);
    ElMessage.error('消息发送分析失败: ' + (error.response?.data?.message || error.message));
  }
};

// 查看详情
const handleViewDetails = (row) => {
  selectedDiagnostic.value = row;
  detailsDialogVisible.value = true;
};

// 解决问题
const handleResolve = (row) => {
  resolveForm.id = row.id;
  resolveForm.diagnosticType = row.diagnosticType;
  resolveForm.errorMessage = row.errorMessage;
  resolveForm.resolution = '';
  resolveDialogVisible.value = true;
};

// 提交解决方案
const submitResolve = async () => {
  if (!resolveFormRef.value) return;

  await resolveFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const response = await axios.post(`/api/notification-diagnostics/${resolveForm.id}/resolve`, {
          resolution: resolveForm.resolution
        });

        if (response.data.code === 0) {
          ElMessage.success('问题已解决');
          resolveDialogVisible.value = false;
          fetchDiagnostics();
        } else {
          ElMessage.error(response.data.message || '提交解决方案失败');
        }
      } catch (error) {
        console.error('提交解决方案失败:', error);
        ElMessage.error('提交解决方案失败: ' + (error.response?.data?.message || error.message));
      }
    }
  });
};

// 运行全面诊断
const handleRunComprehensive = async () => {
  try {
    const response = await axios.post('/api/notification-diagnostics/comprehensive');

    if (response.data.code === 0) {
      ElMessage.success('全面诊断已启动');
      setTimeout(() => {
        fetchDiagnostics();
      }, 1000);
    } else {
      ElMessage.error(response.data.message || '启动全面诊断失败');
    }
  } catch (error) {
    console.error('启动全面诊断失败:', error);
    ElMessage.error('启动全面诊断失败: ' + (error.response?.data?.message || error.message));
  }
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleString();
};

// 格式化字节大小
const formatBytes = (bytes) => {
  if (!bytes) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 格式化类型数据
const formatTypeData = (byType) => {
  if (!byType) return [];

  return Object.entries(byType).map(([type, data]) => {
    const typeName = getNotificationTypeName(type);
    const rate = data.total > 0 ? ((data.success / data.total) * 100).toFixed(2) : '0.00';

    return {
      type: typeName,
      total: data.total,
      success: data.success,
      failed: data.failed,
      rate
    };
  });
};

// 格式化失败原因数据
const formatReasonData = (reasons) => {
  if (!reasons) return [];

  const total = Object.values(reasons).reduce((sum, count) => sum + count, 0);

  return Object.entries(reasons).map(([reason, count]) => {
    const percentage = total > 0 ? ((count / total) * 100).toFixed(2) : '0.00';
    const reasonName = getReasonName(reason);

    return {
      reason: reasonName,
      count,
      percentage
    };
  });
};

// 获取诊断类型标签样式
const getDiagnosticTypeTag = (type) => {
  const typeMap = {
    system_health: 'success',
    webhook_connection: 'primary',
    message_delivery: 'warning',
    comprehensive: 'info'
  };
  return typeMap[type] || '';
};

// 获取诊断类型名称
const getDiagnosticTypeName = (type) => {
  const typeMap = {
    system_health: '系统健康',
    webhook_connection: 'Webhook连接',
    message_delivery: '消息发送',
    comprehensive: '全面诊断'
  };
  return typeMap[type] || type;
};

// 获取状态标签样式
const getStatusTag = (status) => {
  const statusMap = {
    success: 'success',
    warning: 'warning',
    error: 'danger',
    pending: 'info',
    resolved: 'primary'
  };
  return statusMap[status] || '';
};

// 获取状态名称
const getStatusName = (status) => {
  const statusMap = {
    success: '成功',
    warning: '警告',
    error: '错误',
    pending: '进行中',
    resolved: '已解决'
  };
  return statusMap[status] || status;
};

// 获取通知类型名称
const getNotificationTypeName = (type) => {
  const typeMap = {
    exchange: '订单消息',
    system: '系统通知',
    user: '用户消息',
    daily_report: '日报周报'
  };
  return typeMap[type] || type;
};

// 获取失败原因名称
const getReasonName = (reason) => {
  const reasonMap = {
    network_error: '网络错误',
    timeout: '超时',
    rate_limit: '频率限制',
    invalid_content: '内容无效',
    authentication_error: '认证错误',
    server_error: '服务器错误'
  };
  return reasonMap[reason] || reason;
};

// 获取健康状态标签样式
const getHealthStatusTag = (status) => {
  const statusMap = {
    normal: 'success',
    warning: 'warning',
    critical: 'danger'
  };
  return statusMap[status] || 'info';
};

// 获取健康状态名称
const getHealthStatusName = (status) => {
  const statusMap = {
    normal: '正常',
    warning: '警告',
    critical: '严重'
  };
  return statusMap[status] || '未知';
};

// 获取内存进度条颜色
const getMemoryProgressColor = (value) => {
  if (value < 70) return '#67c23a';
  if (value < 85) return '#e6a23c';
  return '#f56c6c';
};
</script>

<style scoped>
.diagnostic-container {
  padding: 16px;
}

.diagnostic-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.tools-container {
  margin-bottom: 24px;
}

.tool-card {
  height: 160px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 20px;
}

.tool-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tool-icon {
  font-size: 36px;
  margin-bottom: 12px;
  color: #409EFF;
}

.tool-name {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.tool-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.filter-container {
  margin-bottom: 16px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #EBEEF5;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.text-muted {
  color: #909399;
}

.details-preview {
  font-size: 12px;
  line-height: 1.5;
}

.json-preview {
  font-family: monospace;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.error-message {
  color: #F56C6C;
  font-size: 12px;
}

.test-result {
  margin-top: 20px;
  padding: 16px;
  border-radius: 4px;
  background-color: #F5F7FA;
}

.result-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  margin-bottom: 12px;
}

.result-header .el-icon {
  margin-right: 8px;
  font-size: 20px;
}

.result-header.success {
  color: #67C23A;
}

.result-header.error {
  color: #F56C6C;
}

.result-error {
  color: #F56C6C;
  margin-bottom: 12px;
  font-family: monospace;
  white-space: pre-wrap;
  padding: 8px;
  background-color: #FEF0F0;
  border-radius: 4px;
}

.result-time {
  font-size: 12px;
  color: #909399;
}

.analysis-result {
  margin-top: 20px;
}

.analysis-header {
  margin-bottom: 16px;
}

.analysis-header h3 {
  margin-bottom: 8px;
}

.analysis-header p {
  font-size: 12px;
  color: #909399;
}

.analysis-summary {
  margin-bottom: 24px;
}

.metric-card {
  background-color: #F5F7FA;
  border-radius: 4px;
  padding: 16px;
  text-align: center;
}

.metric-value {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 8px;
}

.metric-label {
  font-size: 14px;
  color: #606266;
}

.analysis-by-type, .analysis-failures {
  margin-bottom: 24px;
}

.analysis-by-type h4, .analysis-failures h4 {
  margin-bottom: 12px;
}

.diagnostic-details {
  padding: 16px;
}

.details-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  align-items: center;
}

.details-title {
  display: flex;
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.details-time {
  font-size: 12px;
  color: #909399;
  text-align: right;
}

.details-time p {
  margin: 4px 0;
}

.details-error, .details-resolution, .details-content {
  margin-bottom: 24px;
}

.details-error h4, .details-resolution h4, .details-content h4 {
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 500;
}

.details-error pre, .details-resolution pre, .details-content pre {
  background-color: #F5F7FA;
  padding: 12px;
  border-radius: 4px;
  overflow: auto;
  max-height: 300px;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.5;
}

.error-info {
  color: #F56C6C;
  background-color: #FEF0F0;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 80px;
  overflow: auto;
}

.health-result {
  padding: 16px;
}

.health-header {
  margin-bottom: 24px;
}

.health-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.health-header p {
  font-size: 12px;
  color: #909399;
}

.health-metrics {
  margin-bottom: 24px;
}

.health-metrics h4 {
  margin-bottom: 12px;
}

.metric-details {
  margin-top: 12px;
  font-size: 12px;
  color: #606266;
  display: flex;
  justify-content: space-between;
}

.health-uptime {
  margin-bottom: 24px;
}

.health-issues {
  background-color: #FEF0F0;
  padding: 16px;
  border-radius: 4px;
}

.health-issues h4 {
  color: #F56C6C;
  margin-bottom: 12px;
}

.health-issues ul {
  padding-left: 20px;
  margin: 0;
}

.health-issues li {
  margin-bottom: 8px;
}
</style>
