#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

echo -e "${YELLOW}===== 项目结构优化脚本 =====${NC}"

# 项目路径
PROJECT_DIR="$(pwd)"
SERVER_DIR="$PROJECT_DIR/server"

# 创建备份
echo -e "${BLUE}正在创建备份...${NC}"
BACKUP_DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="$PROJECT_DIR/backups/backup_$BACKUP_DATE"
mkdir -p "$BACKUP_DIR"

# 备份重要文件
cp -r "$PROJECT_DIR/uploads" "$BACKUP_DIR/"
cp -r "$PROJECT_DIR/public/uploads" "$BACKUP_DIR/public_uploads"
cp -r "$PROJECT_DIR/server/uploads" "$BACKUP_DIR/server_uploads"
echo -e "${GREEN}备份完成: $BACKUP_DIR${NC}"

# 1. 统一上传目录
echo -e "${BLUE}正在统一上传目录...${NC}"

# 确保服务器上传目录存在并有正确的子目录
mkdir -p "$SERVER_DIR/uploads/images"
mkdir -p "$SERVER_DIR/uploads/payment"
mkdir -p "$SERVER_DIR/uploads/temp"

# 复制非重复文件到服务器上传目录
find "$PROJECT_DIR/uploads" -type f -exec cp -n {} "$SERVER_DIR/uploads/" \;
find "$PROJECT_DIR/public/uploads" -type f -exec cp -n {} "$SERVER_DIR/uploads/" \;

# 删除根目录的uploads
rm -rf "$PROJECT_DIR/uploads"
rm -rf "$PROJECT_DIR/public/uploads"

# 创建从public到server/uploads的符号链接
ln -sf "$SERVER_DIR/uploads" "$PROJECT_DIR/public/uploads"

echo -e "${GREEN}上传目录已统一到 server/uploads${NC}"

# 2. 整理脚本文件
echo -e "${BLUE}正在整理脚本文件...${NC}"

# 创建scripts目录（如果不存在）
mkdir -p "$PROJECT_DIR/scripts/deploy"
mkdir -p "$PROJECT_DIR/scripts/db"
mkdir -p "$PROJECT_DIR/scripts/utils"

# 移动根目录下的脚本文件到scripts目录
mv "$PROJECT_DIR/"*.sh "$PROJECT_DIR/scripts/" 2>/dev/null
# 排除当前脚本
mv "$PROJECT_DIR/scripts/cleanup-project.sh" "$PROJECT_DIR/"

# 移动server目录下的脚本到scripts/db目录
mv "$SERVER_DIR/"*migration*.js "$PROJECT_DIR/scripts/db/" 2>/dev/null
mv "$SERVER_DIR/"*fix*.js "$PROJECT_DIR/scripts/db/" 2>/dev/null
mv "$SERVER_DIR/"*test*.js "$PROJECT_DIR/scripts/utils/" 2>/dev/null

echo -e "${GREEN}脚本文件已整理${NC}"

# 3. 整理临时文件
echo -e "${BLUE}正在清理临时文件...${NC}"

# 清理temp目录中不需要的文件，但保留.gitkeep
find "$PROJECT_DIR/temp" -type f -not -name ".gitkeep" -exec rm {} \;

# 保留temp目录结构
mkdir -p "$PROJECT_DIR/temp/cache"
mkdir -p "$PROJECT_DIR/temp/logs"
mkdir -p "$PROJECT_DIR/temp/tests"
mkdir -p "$PROJECT_DIR/temp/uploads"

# 在每个子目录中添加.gitkeep
touch "$PROJECT_DIR/temp/cache/.gitkeep"
touch "$PROJECT_DIR/temp/logs/.gitkeep"
touch "$PROJECT_DIR/temp/tests/.gitkeep"
touch "$PROJECT_DIR/temp/uploads/.gitkeep"

echo -e "${GREEN}临时文件已清理${NC}"

# 4. 整理文档文件
echo -e "${BLUE}正在整理文档文件...${NC}"

# 创建docs目录（如果不存在）
mkdir -p "$PROJECT_DIR/docs/dev"
mkdir -p "$PROJECT_DIR/docs/api"
mkdir -p "$PROJECT_DIR/docs/deploy"

# 移动根目录下的markdown文档到docs目录
find "$PROJECT_DIR/" -maxdepth 1 -name "*.md" -not -name "README.md" -exec mv {} "$PROJECT_DIR/docs/" \;

# 移动backup_docs中的文件到docs目录
find "$PROJECT_DIR/backup_docs/" -name "*.md" -exec cp {} "$PROJECT_DIR/docs/dev/" \;

# 移动server/docs中的文件到docs/api目录
find "$SERVER_DIR/docs/" -name "*.md" -exec cp {} "$PROJECT_DIR/docs/api/" \;

echo -e "${GREEN}文档文件已整理${NC}"

# 5. 更新.gitignore文件
echo -e "${BLUE}正在更新.gitignore文件...${NC}"

# 备份原始.gitignore
cp "$PROJECT_DIR/.gitignore" "$PROJECT_DIR/.gitignore.bak"

# 添加新的忽略项
cat > "$PROJECT_DIR/.gitignore" << EOF
# 依赖目录
node_modules
server/node_modules
/dist

# 本地环境文件
.env.local
.env.*.local
.env.production
server/.env.production

# 日志文件
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
*.log

# 编辑器目录和文件
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 操作系统生成的文件
.DS_Store
Thumbs.db

# 构建临时文件
.temp
.cache

# 测试覆盖率报告
/coverage

# 本地证书文件
*.pem
*.key
*.crt

# 数据库文件
*.sqlite
*.db

# 备份文件
*.bak
*.backup
*~

# Vue-cli 生成的文件
.browserslistrc
.postcssrc

# 私有配置
config.private.js

# 临时文件目录
temp/*
!temp/.gitkeep
!temp/*/
temp/*/*
!temp/*/.gitkeep

# 服务器上传文件
server/uploads/*
!server/uploads/.gitkeep
!server/uploads/*/
server/uploads/*/*
!server/uploads/*/.gitkeep

# 忽略构建产物
server/public/js
server/public/css
server/public/img
server/public/fonts
EOF

echo -e "${GREEN}.gitignore文件已更新${NC}"

# 6. 创建符号链接确保兼容性
echo -e "${BLUE}正在创建符号链接...${NC}"

# 确保服务器静态文件目录存在
mkdir -p "$SERVER_DIR/public/js"
mkdir -p "$SERVER_DIR/public/css"
mkdir -p "$SERVER_DIR/public/img"

# 创建从dist到server/public的符号链接
ln -sf "$PROJECT_DIR/dist/assets" "$SERVER_DIR/public/assets"

echo -e "${GREEN}符号链接已创建${NC}"

# 7. 创建说明文件
echo -e "${BLUE}正在创建项目说明文件...${NC}"

cat > "$PROJECT_DIR/docs/PROJECT_STRUCTURE.md" << EOF
# 项目结构说明

## 目录结构

- **src/**: 前端源代码
  - **api/**: API请求函数
  - **components/**: Vue组件
  - **router/**: 路由配置
  - **stores/**: Pinia状态管理
  - **utils/**: 工具函数
  - **views/**: 页面视图组件

- **server/**: 后端源代码
  - **controllers/**: 控制器
  - **models/**: 数据模型
  - **routes/**: 路由定义
  - **services/**: 服务层
  - **middlewares/**: 中间件
  - **uploads/**: 文件上传目录（统一）
  - **utils/**: 工具函数

- **scripts/**: 脚本文件
  - **deploy/**: 部署相关脚本
  - **db/**: 数据库相关脚本
  - **utils/**: 实用工具脚本

- **docs/**: 文档
  - **api/**: API文档
  - **dev/**: 开发文档
  - **deploy/**: 部署文档

- **temp/**: 临时文件目录（开发环境使用）

- **public/**: 静态资源目录

- **dist/**: 构建输出目录

## 文件说明

- **package.json**: 前端依赖配置
- **server/package.json**: 后端依赖配置
- **vite.config.js**: Vite构建配置
- **server/server.js**: 后端入口文件

## 开发指南

1. 安装依赖：
   ```
   npm install
   cd server && npm install
   ```

2. 开发环境启动：
   ```
   ./scripts/restart.sh
   ```

3. 生产环境构建：
   ```
   npm run build
   ```

4. 生产环境部署：
   ```
   ./scripts/deploy/production-deploy.sh
   ```

## 注意事项

- 所有上传文件统一存储在 \`server/uploads\` 目录
- 临时文件请放在 \`temp\` 目录下对应子目录
- 新增脚本请放在 \`scripts\` 目录下对应子目录
EOF

echo -e "${GREEN}项目说明文件已创建${NC}"

# 8. 输出完成信息
echo -e "\n${GREEN}===== 项目结构优化完成 =====${NC}"
echo -e "${BLUE}原始文件已备份到: ${NC}$BACKUP_DIR"
echo -e "${BLUE}项目结构说明文件: ${NC}$PROJECT_DIR/docs/PROJECT_STRUCTURE.md"
echo -e "${YELLOW}请注意：此脚本已进行结构优化，但您可能需要更新项目中的相关路径引用。${NC}"
echo -e "${YELLOW}建议在执行后检查项目是否正常运行。${NC}" 