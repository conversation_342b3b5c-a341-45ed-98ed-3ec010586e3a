# 数据库开发工作流

## 📋 概述

本文档定义了项目中数据库相关操作的标准工作流程，确保所有数据库变更都能安全、可控地执行。

## 🔄 标准开发工作流

### 阶段1：开发前准备

#### 1.1 环境检查
```bash
# 检查当前数据库状态
npm run db:check

# 确认当前Git分支和提交
git status
git log --oneline -5
```

**检查点**：
- ✅ 数据库连接正常
- ✅ 当前表结构状态已知
- ✅ 最新备份已创建
- ✅ Git状态清晰

#### 1.2 创建功能分支
```bash
# 创建新的功能分支
git checkout -b feature/your-feature-name

# 记录当前数据库状态
npm run db:backup
```

### 阶段2：迁移文件开发

#### 2.1 生成迁移文件
```bash
cd server
npx sequelize-cli migration:generate --name descriptive-migration-name
```

**命名规范**：
- `create_table_name` - 创建新表
- `add_field_to_table` - 添加字段
- `modify_table_structure` - 修改表结构
- `remove_deprecated_fields` - 删除废弃字段

#### 2.2 编写迁移内容
```javascript
// 迁移文件模板
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // 正向迁移逻辑
      await queryInterface.createTable('new_table', {
        id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true
        },
        // 其他字段定义...
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
        }
      }, { transaction });

      await transaction.commit();
      console.log('✅ 迁移执行成功');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ 迁移执行失败:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // 回退逻辑
      await queryInterface.dropTable('new_table', { transaction });

      await transaction.commit();
      console.log('✅ 迁移回退成功');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ 迁移回退失败:', error);
      throw error;
    }
  }
};
```

### 阶段3：迁移测试

#### 3.1 本地测试流程
```bash
# 1. 执行迁移前检查
npm run db:check

# 2. 执行安全迁移
npm run migrate:safe

# 3. 验证迁移结果
mysql -u root -ppassword feishu_mall -e "DESCRIBE new_table;"

# 4. 测试回退功能
npm run migrate:undo

# 5. 验证回退结果
mysql -u root -ppassword feishu_mall -e "SHOW TABLES LIKE 'new_table';"

# 6. 重新执行迁移
npm run migrate:safe
```

**验证检查点**：
- ✅ 迁移执行无错误
- ✅ 表结构符合预期
- ✅ 数据完整性保持
- ✅ 回退功能正常
- ✅ 重新迁移成功

#### 3.2 数据验证
```sql
-- 检查新表结构
DESCRIBE new_table;

-- 验证索引创建
SHOW INDEX FROM new_table;

-- 检查外键约束
SELECT
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = 'feishu_mall'
  AND TABLE_NAME = 'new_table'
  AND REFERENCED_TABLE_NAME IS NOT NULL;

-- 验证迁移记录
SELECT * FROM SequelizeMeta ORDER BY name DESC LIMIT 5;
```

### 阶段4：代码集成

#### 4.1 更新模型文件
```javascript
// server/models/newModel.js
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const NewModel = sequelize.define('NewModel', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    // 字段定义与迁移文件保持一致
  }, {
    tableName: 'new_table',
    timestamps: true,
    // Sequelize字段映射（如果使用snake_case数据库字段）
    underscored: true
  });

  return NewModel;
};
```

#### 4.2 更新模型关联
```javascript
// server/models/index.js
// 添加新模型的关联关系
NewModel.belongsTo(User, { foreignKey: 'userId' });
User.hasMany(NewModel, { foreignKey: 'userId' });
```

### 阶段5：功能测试

#### 5.1 API测试
```bash
# 启动开发服务器
npm run dev

# 测试相关API接口
curl -X GET http://localhost:3000/api/new-endpoint
curl -X POST http://localhost:3000/api/new-endpoint -d '{"test": "data"}'
```

#### 5.2 集成测试
```javascript
// 测试数据库操作
const { NewModel } = require('../models');

// 创建测试
const newRecord = await NewModel.create({
  field1: 'test value',
  field2: 123
});

// 查询测试
const records = await NewModel.findAll();

// 更新测试
await newRecord.update({ field1: 'updated value' });

// 删除测试
await newRecord.destroy();
```

### 阶段6：提交和部署

#### 6.1 代码提交
```bash
# 添加所有变更文件
git add .

# 提交变更（包含迁移文件和相关代码）
git commit -m "feat: 添加新功能的数据库支持

- 新增 new_table 表结构
- 添加相关模型和API接口
- 包含完整的迁移和回退逻辑"

# 推送到远程仓库
git push origin feature/your-feature-name
```

#### 6.2 部署前准备
```bash
# 生产环境部署前检查
npm run db:check

# 创建部署前备份
npm run db:backup

# 记录当前状态
git log --oneline -1 > deployment_info.txt
date >> deployment_info.txt
```

#### 6.3 生产环境部署
```bash
# 1. 拉取最新代码
git pull origin main

# 2. 安装依赖
npm install

# 3. 执行安全迁移
npm run migrate:safe

# 4. 验证部署结果
npm run db:check

# 5. 启动服务
npm start
```

## 🚨 异常情况处理

### 迁移失败处理
```bash
# 1. 查看错误日志
tail -f logs/migration.log

# 2. 检查数据库状态
mysql -u root -ppassword feishu_mall -e "SELECT * FROM SequelizeMeta ORDER BY name DESC LIMIT 5;"

# 3. 如果需要回退
npm run migrate:undo

# 4. 恢复备份（紧急情况）
mysql -u root -ppassword feishu_mall < backups/latest_backup.sql
```

### 数据损坏恢复
```bash
# 1. 立即停止服务
pm2 stop all

# 2. 恢复最近备份
mysql -u root -ppassword feishu_mall < backups/feishu_mall_backup_YYYYMMDD_HHMMSS.sql

# 3. 验证数据完整性
npm run db:check

# 4. 重启服务
pm2 start all
```

### 回退操作失败
```bash
# 1. 检查回退脚本
cat server/migrations/YYYYMMDD_migration_name.js

# 2. 手动执行回退SQL
mysql -u root -ppassword feishu_mall

# 3. 清理迁移记录
DELETE FROM SequelizeMeta WHERE name = 'problematic_migration.js';

# 4. 验证状态
npm run db:check
```

## 📝 检查清单

### 开发阶段检查清单
- [ ] 执行了 `npm run db:check`
- [ ] 创建了功能分支
- [ ] 迁移文件命名规范
- [ ] 包含完整的up和down逻辑
- [ ] 使用了事务保护
- [ ] 本地测试通过
- [ ] 回退测试通过

### 部署阶段检查清单
- [ ] 代码已合并到主分支
- [ ] 执行了部署前备份
- [ ] 生产环境迁移成功
- [ ] API功能验证通过
- [ ] 服务启动正常
- [ ] 监控指标正常

### 紧急处理检查清单
- [ ] 服务已停止
- [ ] 备份已恢复
- [ ] 数据完整性已验证
- [ ] 问题原因已记录
- [ ] 修复方案已制定
- [ ] 服务已重启

## 🔗 相关文档

- [数据库开发规则](./数据库开发规则.md) - 强制性开发规范
- [数据库版本管理指南](./数据库版本管理指南.md) - 详细的版本管理说明
- [项目结构文档](./PROJECT_STRUCTURE.md) - 项目整体架构说明

## 📞 支持和帮助

### 常见问题
1. **Q: 迁移执行失败怎么办？**
   A: 首先查看错误日志，然后执行 `npm run migrate:undo` 回退，分析问题后重新编写迁移文件。

2. **Q: 如何处理数据迁移？**
   A: 创建专门的数据迁移脚本，在迁移文件中调用，确保数据完整性。

3. **Q: 生产环境迁移失败如何快速恢复？**
   A: 立即执行备份恢复，然后分析问题原因，制定修复方案。

### 技术支持
- 开发问题：查阅开发规则文档
- 紧急故障：按照应急处理流程操作
- 疑难问题：联系技术负责人
