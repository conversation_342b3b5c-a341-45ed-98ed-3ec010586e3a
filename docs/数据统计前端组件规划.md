# 数据统计前端组件规划

## 1. 组件架构设计

### 1.1 新增统计卡片组件
```vue
<!-- ConversionFunnelCard.vue -->
<template>
  <el-card class="funnel-card">
    <template #header>
      <div class="card-header">
        <h3>用户转化漏斗</h3>
        <el-button size="small" @click="refreshData">
          <el-icon><Refresh /></el-icon>
        </el-button>
      </div>
    </template>
    
    <div class="funnel-container">
      <div class="funnel-stage" v-for="stage in funnelData" :key="stage.stage">
        <div class="stage-bar" :style="{ width: stage.rate + '%' }">
          <span class="stage-label">{{ stage.stage }}</span>
          <span class="stage-count">{{ stage.count.toLocaleString() }}</span>
          <span class="stage-rate">{{ stage.rate }}%</span>
        </div>
      </div>
    </div>
    
    <div class="conversion-rates">
      <div class="rate-item" v-for="(rate, key) in conversionRates" :key="key">
        <span class="rate-label">{{ getRateLabel(key) }}</span>
        <span class="rate-value">{{ rate }}%</span>
      </div>
    </div>
  </el-card>
</template>
```

### 1.2 商品运营效率组件
```vue
<!-- ProductEfficiencyCard.vue -->
<template>
  <el-card class="efficiency-card">
    <template #header>
      <div class="card-header">
        <h3>商品运营效率</h3>
        <el-select v-model="selectedCategory" size="small">
          <el-option label="全部分类" value="all" />
          <el-option v-for="cat in categories" :key="cat.id" 
                     :label="cat.name" :value="cat.id" />
        </el-select>
      </div>
    </template>
    
    <!-- 概览指标 -->
    <div class="efficiency-overview">
      <div class="metric-item">
        <div class="metric-value">{{ overview.totalProducts }}</div>
        <div class="metric-label">总商品数</div>
      </div>
      <div class="metric-item warning">
        <div class="metric-value">{{ overview.lowStockProducts }}</div>
        <div class="metric-label">库存预警</div>
      </div>
      <div class="metric-item danger">
        <div class="metric-value">{{ overview.slowMovingProducts }}</div>
        <div class="metric-label">滞销商品</div>
      </div>
    </div>
    
    <!-- 周转率排行 -->
    <div class="turnover-ranking">
      <h4>商品周转率TOP10</h4>
      <el-table :data="turnoverTop10" size="small">
        <el-table-column prop="productName" label="商品名称" />
        <el-table-column prop="turnoverRate" label="周转率" 
                         :formatter="formatRate" />
        <el-table-column prop="exchangeCount" label="兑换量" />
      </el-table>
    </div>
  </el-card>
</template>
```

### 1.3 支付偏好分析组件
```vue
<!-- PaymentPreferenceCard.vue -->
<template>
  <el-card class="payment-card">
    <template #header>
      <div class="card-header">
        <h3>支付方式偏好</h3>
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button label="overview">概览</el-radio-button>
          <el-radio-button label="trend">趋势</el-radio-button>
          <el-radio-button label="category">分类</el-radio-button>
        </el-radio-group>
      </div>
    </template>
    
    <!-- 概览模式 -->
    <div v-if="viewMode === 'overview'" class="payment-overview">
      <div class="payment-stats">
        <div class="stat-item rmb">
          <div class="stat-icon">¥</div>
          <div class="stat-content">
            <div class="stat-value">{{ overview.rmbPercentage }}%</div>
            <div class="stat-label">人民币支付</div>
          </div>
        </div>
        <div class="stat-item ly">
          <div class="stat-icon">⭐</div>
          <div class="stat-content">
            <div class="stat-value">{{ overview.lyPercentage }}%</div>
            <div class="stat-label">光年币支付</div>
          </div>
        </div>
      </div>
      
      <!-- 双饼图 -->
      <div class="payment-charts">
        <v-chart class="chart" :option="paymentPieOption" />
      </div>
    </div>
    
    <!-- 趋势模式 -->
    <div v-if="viewMode === 'trend'" class="payment-trend">
      <v-chart class="chart" :option="paymentTrendOption" />
    </div>
    
    <!-- 分类模式 -->
    <div v-if="viewMode === 'category'" class="payment-category">
      <el-table :data="categoryPreference" size="small">
        <el-table-column prop="categoryName" label="商品分类" />
        <el-table-column label="人民币占比">
          <template #default="scope">
            <el-progress :percentage="scope.row.rmbPercentage" 
                         color="#f56c6c" :show-text="false" />
            <span class="percentage-text">{{ scope.row.rmbPercentage }}%</span>
          </template>
        </el-table-column>
        <el-table-column label="光年币占比">
          <template #default="scope">
            <el-progress :percentage="scope.row.lyPercentage" 
                         color="#e6a23c" :show-text="false" />
            <span class="percentage-text">{{ scope.row.lyPercentage }}%</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-card>
</template>
```

## 2. 图表配置

### 2.1 用户转化漏斗图配置
```javascript
const funnelChartOption = computed(() => ({
  title: {
    text: '用户转化漏斗',
    left: 'center'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} ({d}%)'
  },
  series: [{
    name: '转化漏斗',
    type: 'funnel',
    left: '10%',
    width: '80%',
    label: {
      show: true,
      position: 'inside'
    },
    data: funnelData.value.map(item => ({
      value: item.count,
      name: item.stage
    }))
  }]
}));
```

### 2.2 支付趋势双轴图配置
```javascript
const paymentTrendOption = computed(() => ({
  title: {
    text: '支付方式趋势分析'
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['人民币订单', '光年币订单', '人民币金额', '光年币数量']
  },
  xAxis: {
    type: 'category',
    data: trendData.value.map(item => item.date)
  },
  yAxis: [
    {
      type: 'value',
      name: '订单数量',
      position: 'left'
    },
    {
      type: 'value', 
      name: '金额/数量',
      position: 'right'
    }
  ],
  series: [
    {
      name: '人民币订单',
      type: 'bar',
      data: trendData.value.map(item => item.rmbCount),
      itemStyle: { color: '#f56c6c' }
    },
    {
      name: '光年币订单',
      type: 'bar',
      data: trendData.value.map(item => item.lyCount),
      itemStyle: { color: '#e6a23c' }
    },
    {
      name: '人民币金额',
      type: 'line',
      yAxisIndex: 1,
      data: trendData.value.map(item => item.rmbAmount),
      itemStyle: { color: '#409eff' }
    },
    {
      name: '光年币数量',
      type: 'line',
      yAxisIndex: 1,
      data: trendData.value.map(item => item.lyAmount),
      itemStyle: { color: '#67c23a' }
    }
  ]
}));
```

## 3. 响应式布局规划

### 3.1 桌面端布局（≥1200px）
```vue
<el-row :gutter="20">
  <!-- 第一行：转化漏斗 + 支付偏好 -->
  <el-col :span="12">
    <ConversionFunnelCard />
  </el-col>
  <el-col :span="12">
    <PaymentPreferenceCard />
  </el-col>
</el-row>

<el-row :gutter="20" style="margin-top: 20px;">
  <!-- 第二行：商品运营效率 + 用户洞察 -->
  <el-col :span="16">
    <ProductEfficiencyCard />
  </el-col>
  <el-col :span="8">
    <UserInsightsCard />
  </el-col>
</el-row>

<el-row style="margin-top: 20px;">
  <!-- 第三行：实时监控面板 -->
  <el-col :span="24">
    <RealtimeMonitorCard />
  </el-col>
</el-row>
```

### 3.2 平板端布局（768px-1199px）
```vue
<el-row :gutter="16">
  <el-col :span="24">
    <ConversionFunnelCard />
  </el-col>
</el-row>

<el-row :gutter="16" style="margin-top: 16px;">
  <el-col :span="12">
    <PaymentPreferenceCard />
  </el-col>
  <el-col :span="12">
    <UserInsightsCard />
  </el-col>
</el-row>
```

### 3.3 移动端布局（<768px）
```vue
<div class="mobile-dashboard">
  <ConversionFunnelCard class="mobile-card" />
  <PaymentPreferenceCard class="mobile-card" />
  <ProductEfficiencyCard class="mobile-card" />
  <UserInsightsCard class="mobile-card" />
  <RealtimeMonitorCard class="mobile-card" />
</div>
```

## 4. 性能优化策略

### 4.1 数据缓存
```javascript
// 使用Vue3的缓存机制
const cachedData = ref(new Map());
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟

const getCachedData = (key, fetchFn) => {
  const cached = cachedData.value.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  
  return fetchFn().then(data => {
    cachedData.value.set(key, {
      data,
      timestamp: Date.now()
    });
    return data;
  });
};
```

### 4.2 懒加载组件
```javascript
// 使用动态导入实现懒加载
const ConversionFunnelCard = defineAsyncComponent(() => 
  import('./components/ConversionFunnelCard.vue')
);

const ProductEfficiencyCard = defineAsyncComponent(() => 
  import('./components/ProductEfficiencyCard.vue')
);
```

### 4.3 虚拟滚动
```vue
<!-- 对于大数据量的表格使用虚拟滚动 -->
<el-table-v2
  :columns="columns"
  :data="largeDataset"
  :width="700"
  :height="400"
  fixed
/>
```

## 5. 交互设计

### 5.1 数据刷新策略
- 自动刷新：每5分钟自动更新数据
- 手动刷新：每个组件都有刷新按钮
- 实时数据：WebSocket推送关键指标更新

### 5.2 数据钻取
- 点击统计卡片可查看详细数据
- 图表支持点击钻取到具体维度
- 提供数据导出功能

### 5.3 个性化配置
- 用户可自定义显示的统计模块
- 支持拖拽调整组件位置
- 保存用户的布局偏好设置
