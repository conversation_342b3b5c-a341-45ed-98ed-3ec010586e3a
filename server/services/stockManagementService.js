const { sequelize } = require('../models');
const { ProductWorkplaceStock, StockOperationLog, Product, Workplace, User } = require('../models');

/**
 * 验证商品库存数据一致性
 * @param {number} productId - 商品ID
 * @param {object} transaction - 数据库事务
 * @returns {object} 验证结果
 */
async function validateStockConsistency(productId, transaction = null) {
  try {
    // 获取商品总库存
    const product = await Product.findByPk(productId, { transaction });
    if (!product) {
      return { isValid: false, error: '商品不存在' };
    }

    // 获取所有职场库存
    const workplaceStocks = await ProductWorkplaceStock.findAll({
      where: { productId },
      transaction
    });

    // 计算职场库存总和
    const totalWorkplaceStock = workplaceStocks.reduce((sum, ws) => sum + ws.stock, 0);
    const totalReservedStock = workplaceStocks.reduce((sum, ws) => sum + (ws.reservedStock || 0), 0);

    // 验证一致性
    const isValid = product.stock === totalWorkplaceStock;

    return {
      isValid,
      productStock: product.stock,
      totalWorkplaceStock,
      totalReservedStock,
      workplaceStocks: workplaceStocks.map(ws => ({
        workplaceId: ws.workplaceId,
        stock: ws.stock,
        reservedStock: ws.reservedStock || 0
      })),
      error: isValid ? null : `库存不一致：商品总库存=${product.stock}，职场库存总和=${totalWorkplaceStock}`
    };
  } catch (error) {
    return { isValid: false, error: error.message };
  }
}

/**
 * 库存管理服务
 * 提供商品职场库存的核心业务逻辑
 */
class StockManagementService {
  
  /**
   * 获取商品的职场库存分布
   * @param {number} productId - 商品ID
   * @returns {Object} 商品职场库存信息
   */
  static async getProductWorkplaceStocks(productId) {
    try {
      // 验证商品是否存在
      const product = await Product.findByPk(productId);
      if (!product) {
        throw new Error('商品不存在');
      }

      // 获取商品的职场库存信息
      const workplaceStocks = await ProductWorkplaceStock.findAll({
        where: { productId },
        include: [
          {
            model: Workplace,
            as: 'workplace',
            attributes: ['id', 'name', 'code', 'description', 'isActive']
          }
        ],
        order: [['workplace', 'id', 'ASC']]
      });

      // 计算总库存
      const totalStock = await ProductWorkplaceStock.getTotalStockByProduct(productId);

      // 格式化返回数据
      const formattedStocks = workplaceStocks.map(stock => ({
        workplaceId: stock.workplaceId,
        workplaceName: stock.workplace.name,
        workplaceCode: stock.workplace.code,
        stock: stock.stock,
        reservedStock: stock.reservedStock,
        availableStock: stock.availableStock,
        minStockAlert: stock.minStockAlert,
        maxStockLimit: stock.maxStockLimit,
        isLowStock: stock.isLowStock(),
        lastStockUpdate: stock.lastStockUpdate,
        isWorkplaceActive: stock.workplace.isActive
      }));

      return {
        productId: product.id,
        productName: product.name,
        stockManagementType: 'workplace',
        totalStock: totalStock.totalStock,
        totalReservedStock: totalStock.totalReservedStock,
        totalAvailableStock: totalStock.totalAvailableStock,
        workplaceStocks: formattedStocks
      };
    } catch (error) {
      console.error('获取商品职场库存失败:', error);
      throw error;
    }
  }

  /**
   * 批量更新商品在各职场的库存
   * @param {number} productId - 商品ID
   * @param {Array} stockUpdates - 库存更新数据 [{workplaceId, stock, minStockAlert?, maxStockLimit?}]
   * @param {Object} operator - 操作员信息 {id, name, ipAddress?, userAgent?}
   * @param {string} reason - 操作原因
   * @returns {Object} 更新结果
   */
  static async updateProductWorkplaceStocks(productId, stockUpdates, operator, reason = '批量更新库存') {
    const transaction = await sequelize.transaction();
    
    try {
      // 验证商品是否存在
      const product = await Product.findByPk(productId, { transaction });
      if (!product) {
        throw new Error('商品不存在');
      }

      // 验证职场是否存在且活跃
      const workplaceIds = stockUpdates.map(update => update.workplaceId);
      const workplaces = await Workplace.findAll({
        where: { 
          id: workplaceIds,
          isActive: true 
        },
        transaction
      });

      if (workplaces.length !== workplaceIds.length) {
        throw new Error('存在无效或非活跃的职场');
      }

      const updateResults = [];
      const batchId = `BATCH_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // 逐个更新职场库存
      for (const update of stockUpdates) {
        const { workplaceId, stock, minStockAlert, maxStockLimit } = update;

        // 验证库存数量
        if (stock < 0) {
          throw new Error(`职场 ${workplaceId} 的库存数量不能为负数`);
        }

        // 获取当前库存记录
        let workplaceStock = await ProductWorkplaceStock.findOne({
          where: { productId, workplaceId },
          transaction
        });

        const beforeStock = workplaceStock ? workplaceStock.stock : 0;

        // 检查最大库存限制
        const currentMaxLimit = workplaceStock ? workplaceStock.maxStockLimit : null;
        const effectiveMaxLimit = maxStockLimit !== undefined ? maxStockLimit : currentMaxLimit;

        if (effectiveMaxLimit && effectiveMaxLimit > 0 && stock > effectiveMaxLimit) {
          const workplace = workplaces.find(w => w.id === workplaceId);
          const workplaceName = workplace ? workplace.name : `职场${workplaceId}`;
          throw new Error(`${workplaceName}的库存数量(${stock})不能超过最大限制(${effectiveMaxLimit})`);
        }

        if (workplaceStock) {
          // 更新现有记录
          const updateData = { stock };
          if (minStockAlert !== undefined) updateData.minStockAlert = minStockAlert;
          if (maxStockLimit !== undefined) updateData.maxStockLimit = maxStockLimit;
          updateData.lastStockUpdate = new Date();

          await workplaceStock.update(updateData, { transaction });
        } else {
          // 创建新记录
          workplaceStock = await ProductWorkplaceStock.create({
            productId,
            workplaceId,
            stock,
            minStockAlert: minStockAlert || 10,
            maxStockLimit,
            lastStockUpdate: new Date()
          }, { transaction });
        }

        // 记录操作日志
        await StockOperationLog.createLog({
          productId,
          workplaceId,
          operationType: 'set',
          beforeStock,
          afterStock: stock,
          changeAmount: stock - beforeStock,
          operatorId: operator.id,
          operatorName: operator.name,
          reason,
          batchId,
          ipAddress: operator.ipAddress,
          userAgent: operator.userAgent,
          metadata: {
            operation_type: 'batch_update',
            min_stock_alert: workplaceStock.minStockAlert,
            max_stock_limit: workplaceStock.maxStockLimit
          }
        }, transaction);

        updateResults.push({
          workplaceId,
          beforeStock,
          afterStock: stock,
          changeAmount: stock - beforeStock,
          success: true
        });
      }

      // 更新商品总库存（兼容性）
      // 注意：需要在事务内重新计算总库存，确保读取到最新数据
      const workplaceStocksInTransaction = await ProductWorkplaceStock.findAll({
        where: { productId },
        attributes: ['stock'],
        transaction
      });

      const newTotalStock = workplaceStocksInTransaction.reduce((sum, ws) => sum + ws.stock, 0);

      await product.update({
        stock: newTotalStock
      }, { transaction });

      console.log(`📊 商品总库存已更新: 商品ID=${productId}, 新总库存=${newTotalStock}, 职场库存明细=${workplaceStocksInTransaction.map(ws => ws.stock).join('+')}`);

      // 验证更新结果和数据一致性
      await product.reload({ transaction });
      console.log(`✅ 验证商品库存更新: products.stock=${product.stock}`);

      // 验证库存一致性
      const consistencyCheck = await validateStockConsistency(productId, transaction);
      if (!consistencyCheck.isValid) {
        console.error(`❌ 库存一致性验证失败: ${consistencyCheck.error}`);
        throw new Error(`库存一致性验证失败: ${consistencyCheck.error}`);
      }
      console.log(`✅ 库存一致性验证通过: 商品库存=${consistencyCheck.productStock}, 职场库存总和=${consistencyCheck.totalWorkplaceStock}`);

      await transaction.commit();

      return {
        success: true,
        productId,
        batchId,
        totalUpdated: updateResults.length,
        newTotalStock: newTotalStock,
        updateResults
      };

    } catch (error) {
      await transaction.rollback();
      console.error('批量更新职场库存失败:', error);
      throw error;
    }
  }

  /**
   * 职场间库存转移
   * @param {number} productId - 商品ID
   * @param {number} fromWorkplaceId - 源职场ID
   * @param {number} toWorkplaceId - 目标职场ID
   * @param {number} quantity - 转移数量
   * @param {Object} operator - 操作员信息
   * @param {string} reason - 转移原因
   * @returns {Object} 转移结果
   */
  static async transferStock(productId, fromWorkplaceId, toWorkplaceId, quantity, operator, reason = '职场间库存转移') {
    const transaction = await sequelize.transaction();
    
    try {
      // 验证参数
      if (fromWorkplaceId === toWorkplaceId) {
        throw new Error('源职场和目标职场不能相同');
      }
      
      if (quantity <= 0) {
        throw new Error('转移数量必须大于0');
      }

      // 验证商品和职场
      const [product, fromWorkplace, toWorkplace] = await Promise.all([
        Product.findByPk(productId, { transaction }),
        Workplace.findOne({ where: { id: fromWorkplaceId, isActive: true }, transaction }),
        Workplace.findOne({ where: { id: toWorkplaceId, isActive: true }, transaction })
      ]);

      if (!product) throw new Error('商品不存在');
      if (!fromWorkplace) throw new Error('源职场不存在或未激活');
      if (!toWorkplace) throw new Error('目标职场不存在或未激活');

      // 获取源职场库存
      const fromStock = await ProductWorkplaceStock.findOne({
        where: { productId, workplaceId: fromWorkplaceId },
        transaction
      });

      if (!fromStock || fromStock.availableStock < quantity) {
        throw new Error('源职场可用库存不足');
      }

      // 获取或创建目标职场库存
      let toStock = await ProductWorkplaceStock.findOne({
        where: { productId, workplaceId: toWorkplaceId },
        transaction
      });

      if (!toStock) {
        toStock = await ProductWorkplaceStock.create({
          productId,
          workplaceId: toWorkplaceId,
          stock: 0,
          reservedStock: 0,
          minStockAlert: 10
        }, { transaction });
      }

      // 执行转移
      const fromBeforeStock = fromStock.stock;
      const toBeforeStock = toStock.stock;
      const newToStock = toStock.stock + quantity;

      // 检查目标职场的最大库存限制
      if (toStock.maxStockLimit && toStock.maxStockLimit > 0 && newToStock > toStock.maxStockLimit) {
        throw new Error(`目标职场${toWorkplace.name}的库存转移后(${newToStock})将超过最大限制(${toStock.maxStockLimit})`);
      }

      await fromStock.update({
        stock: fromStock.stock - quantity,
        lastStockUpdate: new Date()
      }, { transaction });

      await toStock.update({
        stock: newToStock,
        lastStockUpdate: new Date()
      }, { transaction });

      const batchId = `TRANSFER_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // 记录转移日志
      await Promise.all([
        // 源职场减少记录
        StockOperationLog.createLog({
          productId,
          workplaceId: fromWorkplaceId,
          operationType: 'transfer',
          beforeStock: fromBeforeStock,
          afterStock: fromStock.stock,
          changeAmount: -quantity,
          operatorId: operator.id,
          operatorName: operator.name,
          reason,
          batchId,
          ipAddress: operator.ipAddress,
          userAgent: operator.userAgent,
          metadata: {
            transfer_type: 'outbound',
            target_workplace_id: toWorkplaceId,
            target_workplace_name: toWorkplace.name
          }
        }, transaction),
        // 目标职场增加记录
        StockOperationLog.createLog({
          productId,
          workplaceId: toWorkplaceId,
          operationType: 'transfer',
          beforeStock: toBeforeStock,
          afterStock: toStock.stock,
          changeAmount: quantity,
          operatorId: operator.id,
          operatorName: operator.name,
          reason,
          batchId,
          ipAddress: operator.ipAddress,
          userAgent: operator.userAgent,
          metadata: {
            transfer_type: 'inbound',
            source_workplace_id: fromWorkplaceId,
            source_workplace_name: fromWorkplace.name
          }
        }, transaction)
      ]);

      await transaction.commit();

      return {
        success: true,
        productId,
        batchId,
        transferQuantity: quantity,
        fromWorkplace: {
          id: fromWorkplaceId,
          name: fromWorkplace.name,
          beforeStock: fromBeforeStock,
          afterStock: fromStock.stock
        },
        toWorkplace: {
          id: toWorkplaceId,
          name: toWorkplace.name,
          beforeStock: toBeforeStock,
          afterStock: toStock.stock
        }
      };

    } catch (error) {
      await transaction.rollback();
      console.error('库存转移失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有职场列表
   * @returns {Array} 职场列表
   */
  static async getWorkplaces() {
    try {
      const workplaces = await Workplace.findAll({
        attributes: ['id', 'name', 'code', 'isActive', 'createdAt', 'updatedAt'],
        order: [['name', 'ASC']]
      });

      return workplaces;
    } catch (error) {
      console.error('获取职场列表失败:', error);
      throw new Error('获取职场列表失败');
    }
  }

  /**
   * 获取职场库存统计
   * @param {number} workplaceId - 职场ID
   * @returns {Object} 职场库存统计信息
   */
  static async getWorkplaceStockSummary(workplaceId) {
    try {
      const workplace = await Workplace.findByPk(workplaceId);
      if (!workplace) {
        throw new Error('职场不存在');
      }

      const summary = await ProductWorkplaceStock.getWorkplaceStockSummary(workplaceId);
      
      return {
        workplaceId,
        workplaceName: workplace.name,
        workplaceCode: workplace.code,
        ...summary
      };
    } catch (error) {
      console.error('获取职场库存统计失败:', error);
      throw error;
    }
  }
}

module.exports = StockManagementService;
module.exports.validateStockConsistency = validateStockConsistency;
