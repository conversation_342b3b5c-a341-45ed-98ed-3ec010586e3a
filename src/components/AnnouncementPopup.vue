<template>
  <el-dialog
    v-model="dialogVisible"
    :title="announcement?.title || '公告'"
    width="600px"
    class="announcement-dialog"
    :close-on-click-modal="true"
    :before-close="closeAnnouncement"
    top="5vh"
  >
    <div class="announcement-content" v-if="announcement">
      <div class="announcement-meta">
        <el-tag :type="getTypeTagType(announcement.type)">
          {{ announcement.type }}
        </el-tag>
        <span class="publish-time">发布时间: {{ formatDate(announcement.createdAt) }}</span>
      </div>
      
      <!-- 多图片展示 -->
      <div v-if="announcement.imageUrls && announcement.imageUrls.length > 0" class="announcement-images">
        <el-carousel v-if="announcement.imageUrls.length > 1" height="300px" indicator-position="outside" arrow="always">
          <el-carousel-item v-for="(imageUrl, index) in announcement.imageUrls" :key="index">
            <img :src="formatImageUrl(imageUrl)" class="carousel-image" />
          </el-carousel-item>
        </el-carousel>
        
        <div v-else class="single-image">
          <img :src="formatImageUrl(announcement.imageUrls[0])" style="max-width: 100%; max-height: 300px;" />
        </div>
      </div>
      
      <!-- 向后兼容单张图片 -->
      <div v-else-if="announcement.imageUrl" class="announcement-image">
        <img :src="formatImageUrl(announcement.imageUrl)" style="max-width: 100%; max-height: 300px;" />
      </div>
      
      <div class="announcement-text">
        <div v-if="announcement.contentHtml" v-html="renderContent(announcement.contentHtml)" class="rich-content"></div>
        <p v-else v-html="formatContent(announcement.content)"></p>
      </div>
    </div>
    
    <div class="empty-announcement" v-else>
      <el-empty description="暂无公告" />
    </div>
    
    <template #footer>
      <el-checkbox v-model="dontShowAgain" label="不再显示此公告" />
      <el-button @click="closeAnnouncement">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, onMounted, watch } from 'vue';
import { ElMessageBox } from 'element-plus';
import { fixImageUrl, processHtmlImages } from '../utils/imageUtils';

const props = defineProps({
  announcement: {
    type: Object,
    default: null
  },
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'close']);

// 控制对话框显示
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 不再显示选项
const dontShowAgain = ref(false);

// 关闭公告
const closeAnnouncement = () => {
  emit('close', dontShowAgain.value);
  dialogVisible.value = false;
};

// 工具函数 - 格式化图片URL
const formatImageUrl = (url) => {
  return fixImageUrl(url);
};

// 处理HTML内容中的图片URL
const processHtmlContent = (html) => {
  return processHtmlImages(html);
};

// 新增：渲染内容，兼容JSON格式和HTML格式
const renderContent = (content) => {
  if (!content) return '';

  try {
    // 尝试解析为JSON
    if (typeof content === 'string' && (content.startsWith('[') || content.startsWith('{'))) {
      try {
        const jsonContent = JSON.parse(content);
        return convertJsonToHtml(jsonContent);
      } catch (e) {
        console.log('内容不是有效的JSON格式，尝试其他处理方式');
      }
    }

    // 如果不是JSON或解析失败，按HTML处理
    let processedContent = processHtmlContent(content);

    // 处理可能存在的字面量换行符（对于contentHtml字段）
    processedContent = processedContent
      .replace(/\\n/g, '<br>')
      .replace(/\n/g, '<br>');

    return processedContent;
  } catch (error) {
    console.error('渲染内容错误:', error);
    return content; // 出错时返回原始内容
  }
};

// 新增：将JSON格式转换为HTML
const convertJsonToHtml = (json) => {
  if (!json) return '';
  if (typeof json === 'string') return json;
  
  if (Array.isArray(json)) {
    return json.map(item => convertJsonToHtml(item)).join('');
  }
  
  if (typeof json === 'object') {
    // 处理不同类型的节点
    switch (json.type) {
      case 'paragraph':
        return `<p>${json.children ? json.children.map(child => convertJsonToHtml(child)).join('') : ''}</p>`;
        
      case 'text':
        let text = json.text || '';
        if (json.bold) text = `<strong>${text}</strong>`;
        if (json.italic) text = `<em>${text}</em>`;
        if (json.underline) text = `<u>${text}</u>`;
        return text;
        
      case 'image':
        const imgSrc = formatImageUrl(json.src || '');
        const alt = json.alt || '';
        const href = json.href || '';
        return `<img src="${imgSrc}" alt="${alt}" ${href ? `data-href="${href}"` : ''} style="${json.style || ''}" />`;
        
      case 'heading':
        const level = json.level || 1;
        return `<h${level}>${json.children ? json.children.map(child => convertJsonToHtml(child)).join('') : ''}</h${level}>`;
        
      case 'list':
        const tag = json.ordered ? 'ol' : 'ul';
        return `<${tag}>${json.children ? json.children.map(child => convertJsonToHtml(child)).join('') : ''}</${tag}>`;
        
      case 'list-item':
        return `<li>${json.children ? json.children.map(child => convertJsonToHtml(child)).join('') : ''}</li>`;
        
      default:
        if (json.children) {
          return json.children.map(child => convertJsonToHtml(child)).join('');
        }
        return json.text || '';
    }
  }
  
  return '';
};

// 工具函数 - 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).replace(/\//g, '-');
};

// 工具函数 - 格式化内容（简单处理换行等）
const formatContent = (content) => {
  if (!content) return '';
  return content
    // 首先处理字面量的\n字符串（反斜杠+n）
    .replace(/\\n/g, '<br>')
    // 然后处理真正的换行符
    .replace(/\n/g, '<br>')
    // 处理markdown格式的粗体
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // 处理markdown格式的斜体
    .replace(/\*(.*?)\*/g, '<em>$1</em>');
};

// 工具函数 - 根据公告类型返回标签类型
const getTypeTagType = (type) => {
  switch (type) {
    case '新品':
      return 'success';
    case '促销':
      return 'danger';
    case '系统更新':
    default:
      return 'info';
  }
};

// 监听公告变化，处理内容
watch(() => props.announcement, (newAnnouncement) => {
  if (newAnnouncement) {
    console.log('公告数据已更新:', newAnnouncement.title);
    if (newAnnouncement.contentHtml) {
      // 检查contentHtml是否为JSON字符串
      try {
        if (typeof newAnnouncement.contentHtml === 'string' && 
            (newAnnouncement.contentHtml.startsWith('[') || newAnnouncement.contentHtml.startsWith('{'))) {
          console.log('检测到JSON格式的contentHtml');
        }
      } catch (e) {
        console.log('非JSON格式的contentHtml');
      }
    }
  }
}, { immediate: true });

// 组件挂载完成后
onMounted(() => {
  console.log('公告组件已挂载，当前公告:', props.announcement);
});
</script>

<style>
.announcement-dialog .el-dialog__header {
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
}

.announcement-dialog .el-dialog__body {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .announcement-dialog {
    width: 95% !important;
    margin: 0 !important;
  }

  .announcement-dialog .el-dialog__body {
    padding: 15px;
    max-height: 75vh;
  }

  .announcement-text {
    max-height: 300px;
  }
}

.announcement-dialog .el-dialog__footer {
  padding: 10px 20px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.announcement-meta {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.publish-time {
  color: #909399;
  font-size: 14px;
}

.announcement-image, .announcement-images {
  margin: 15px 0;
  text-align: center;
}

.carousel-image {
  height: 300px;
  object-fit: contain;
  width: 100%;
}

.single-image {
  display: flex;
  justify-content: center;
}

.announcement-text {
  line-height: 1.6;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;
  margin-top: 15px;
}

/* 自定义滚动条样式 */
.announcement-text::-webkit-scrollbar {
  width: 6px;
}

.announcement-text::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.announcement-text::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.announcement-text::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 为了确保富文本内容正确显示，移除scoped */
.rich-content img {
  max-width: 100%;
  height: auto;
  margin: 10px 0;
  display: block;
  border-radius: 4px;
}

.rich-content p {
  margin-bottom: 10px;
}

.rich-content h1,
.rich-content h2,
.rich-content h3,
.rich-content h4,
.rich-content h5 {
  margin: 15px 0 10px;
  font-weight: bold;
}

.rich-content ul,
.rich-content ol {
  margin-left: 20px;
  margin-bottom: 10px;
}

.rich-content blockquote {
  border-left: 3px solid #dcdfe6;
  padding-left: 10px;
  color: #606266;
  margin: 10px 0;
}

.empty-announcement {
  padding: 30px;
}
</style> 