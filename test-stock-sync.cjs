/**
 * 测试库存同步功能
 * 直接通过数据库操作来验证库存同步
 */

const mysql = require('mysql2/promise');

const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'password',
  database: 'feishu_mall'
};

async function testStockSync() {
  console.log('🧪 测试库存同步功能...\n');

  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);

    // 1. 选择一个测试商品
    console.log('1️⃣ 选择测试商品...');
    const [products] = await connection.execute(
      'SELECT id, name, stock, stockManagementType FROM products WHERE stockManagementType = "single" LIMIT 1'
    );

    if (products.length === 0) {
      console.log('❌ 没有找到单一库存模式的商品');
      return;
    }

    const product = products[0];
    console.log(`✅ 测试商品: ${product.name} (ID: ${product.id})`);
    console.log(`   当前库存: ${product.stock}`);
    console.log(`   库存管理模式: ${product.stockManagementType}`);

    // 2. 检查当前职场库存
    console.log('\n2️⃣ 检查当前职场库存...');
    const [workplaceStocks] = await connection.execute(
      'SELECT stock FROM product_workplace_stocks WHERE productId = ? AND workplaceId = 1',
      [product.id]
    );

    if (workplaceStocks.length === 0) {
      console.log('❌ 没有找到职场库存记录');
      return;
    }

    const currentWorkplaceStock = workplaceStocks[0].stock;
    console.log(`✅ 当前职场库存: ${currentWorkplaceStock}`);

    // 3. 验证库存一致性
    if (product.stock === currentWorkplaceStock) {
      console.log('✅ 商品库存与职场库存一致');
    } else {
      console.log(`❌ 库存不一致: 商品库存=${product.stock}, 职场库存=${currentWorkplaceStock}`);
    }

    // 4. 模拟库存更新
    console.log('\n3️⃣ 模拟库存更新...');
    const originalStock = product.stock;
    const newStock = originalStock + 10;

    console.log(`更新库存: ${originalStock} → ${newStock}`);

    // 更新商品库存
    await connection.execute(
      'UPDATE products SET stock = ? WHERE id = ?',
      [newStock, product.id]
    );

    // 模拟后端同步逻辑：更新职场库存
    await connection.execute(
      'UPDATE product_workplace_stocks SET stock = ? WHERE productId = ? AND workplaceId = 1',
      [newStock, product.id]
    );

    // 5. 验证更新结果
    console.log('\n4️⃣ 验证更新结果...');
    const [updatedProduct] = await connection.execute(
      'SELECT stock FROM products WHERE id = ?',
      [product.id]
    );

    const [updatedWorkplaceStock] = await connection.execute(
      'SELECT stock FROM product_workplace_stocks WHERE productId = ? AND workplaceId = 1',
      [product.id]
    );

    const finalProductStock = updatedProduct[0].stock;
    const finalWorkplaceStock = updatedWorkplaceStock[0].stock;

    console.log(`商品库存: ${finalProductStock}`);
    console.log(`职场库存: ${finalWorkplaceStock}`);

    if (finalProductStock === newStock && finalWorkplaceStock === newStock) {
      console.log('✅ 库存更新和同步成功');
    } else {
      console.log('❌ 库存更新或同步失败');
    }

    // 6. 恢复原始库存
    console.log('\n5️⃣ 恢复原始库存...');
    await connection.execute(
      'UPDATE products SET stock = ? WHERE id = ?',
      [originalStock, product.id]
    );
    await connection.execute(
      'UPDATE product_workplace_stocks SET stock = ? WHERE productId = ? AND workplaceId = 1',
      [originalStock, product.id]
    );

    console.log(`✅ 已恢复原始库存: ${originalStock}`);

    console.log('\n🎉 库存同步功能测试完成');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行测试
testStockSync();
