-- 添加库存管理相关字段到products表
-- 执行时间：2025-07-30

-- 添加库存管理类型字段
ALTER TABLE products 
ADD COLUMN stockManagementType ENUM('single', 'workplace') NOT NULL DEFAULT 'single' COMMENT '库存管理类型：single-单一库存，workplace-职场分配';

-- 添加库存同步相关字段
ALTER TABLE products 
ADD COLUMN stockSyncedAt DATETIME NULL COMMENT '库存同步到职场的时间';

-- 添加索引
ALTER TABLE products 
ADD INDEX idx_stock_management_type (stockManagementType),
ADD INDEX idx_stock_synced_at (stockSyncedAt);

-- 更新现有商品的库存管理类型
-- 如果商品在product_workplace_stocks表中有记录，则设置为workplace模式
UPDATE products 
SET stockManagementType = 'workplace',
    stockSyncedAt = NOW()
WHERE id IN (SELECT DISTINCT productId FROM product_workplace_stocks);

-- 同步职场库存到商品主库存
UPDATE products p
SET stock = (
    SELECT COALESCE(SUM(pws.stock), 0)
    FROM product_workplace_stocks pws
    WHERE pws.productId = p.id
)
WHERE p.stockManagementType = 'workplace';

-- 验证数据一致性
SELECT 
    p.id,
    p.name,
    p.stock as product_stock,
    p.stockManagementType,
    COALESCE(SUM(pws.stock), 0) as workplace_stock_sum,
    CASE 
        WHEN p.stockManagementType = 'single' THEN 'OK'
        WHEN p.stockManagementType = 'workplace' AND p.stock = COALESCE(SUM(pws.stock), 0) THEN 'OK'
        ELSE 'ERROR'
    END as validation_status
FROM products p
LEFT JOIN product_workplace_stocks pws ON p.id = pws.productId
GROUP BY p.id, p.name, p.stock, p.stockManagementType
HAVING validation_status = 'ERROR';
