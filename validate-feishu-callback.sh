#!/bin/bash

# 飞书回调地址配置验证脚本
# 验证前后端飞书回调地址配置的一致性

set -e

echo "======================================"
echo "飞书回调地址配置验证脚本"
echo "======================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 验证函数
check_config() {
    local file=$1
    local env_var=$2
    local description=$3
    local expected_pattern=$4
    
    echo -n "检查 $description... "
    
    if [ ! -f "$file" ]; then
        echo -e "${RED}❌ 文件不存在${NC}"
        return 1
    fi
    
    # 提取配置值
    local config_value=$(grep "^$env_var=" "$file" 2>/dev/null | cut -d'=' -f2- | tr -d '"' | tr -d "'")
    
    if [ -z "$config_value" ]; then
        echo -e "${RED}❌ 配置项不存在或为空${NC}"
        return 1
    fi
    
    # 检查是否匹配预期模式
    if echo "$config_value" | grep -q "$expected_pattern"; then
        echo -e "${GREEN}✅ $config_value${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️ $config_value (不匹配预期模式)${NC}"
        return 1
    fi
}

# 提取配置值的函数
get_config_value() {
    local file=$1
    local env_var=$2
    
    if [ -f "$file" ]; then
        grep "^$env_var=" "$file" 2>/dev/null | cut -d'=' -f2- | tr -d '"' | tr -d "'"
    else
        echo ""
    fi
}

echo "🔍 检查生产环境配置..."

# 检查前端生产环境配置
FRONTEND_PROD_CALLBACK=$(get_config_value ".env.production" "VITE_FEISHU_CALLBACK_URL")
echo -n "前端生产环境回调地址... "
if [ -n "$FRONTEND_PROD_CALLBACK" ]; then
    echo -e "${GREEN}✅ $FRONTEND_PROD_CALLBACK${NC}"
else
    echo -e "${RED}❌ 未配置${NC}"
fi

# 检查后端生产环境配置
BACKEND_PROD_CALLBACK=$(get_config_value "server/.env.production" "FEISHU_REDIRECT_URI")
echo -n "后端生产环境回调地址... "
if [ -n "$BACKEND_PROD_CALLBACK" ]; then
    echo -e "${GREEN}✅ $BACKEND_PROD_CALLBACK${NC}"
else
    echo -e "${RED}❌ 未配置${NC}"
fi

echo ""
echo "🔍 检查开发环境配置..."

# 检查前端开发环境配置
FRONTEND_DEV_CALLBACK=$(get_config_value ".env" "VITE_FEISHU_CALLBACK_URL")
echo -n "前端开发环境回调地址... "
if [ -n "$FRONTEND_DEV_CALLBACK" ]; then
    echo -e "${GREEN}✅ $FRONTEND_DEV_CALLBACK${NC}"
else
    echo -e "${RED}❌ 未配置${NC}"
fi

# 检查后端开发环境配置
BACKEND_DEV_CALLBACK=$(get_config_value "server/.env" "FEISHU_REDIRECT_URI")
echo -n "后端开发环境回调地址... "
if [ -n "$BACKEND_DEV_CALLBACK" ]; then
    echo -e "${GREEN}✅ $BACKEND_DEV_CALLBACK${NC}"
else
    echo -e "${RED}❌ 未配置${NC}"
fi

echo ""
echo "🔍 验证配置一致性..."

# 验证生产环境配置一致性
echo -n "生产环境前后端配置一致性... "
if [ -n "$FRONTEND_PROD_CALLBACK" ] && [ -n "$BACKEND_PROD_CALLBACK" ]; then
    # 提取域名部分进行比较
    FRONTEND_DOMAIN=$(echo "$FRONTEND_PROD_CALLBACK" | sed 's|/feishu/callback.*||')
    BACKEND_DOMAIN=$(echo "$BACKEND_PROD_CALLBACK" | sed 's|/api/feishu/callback.*||')
    
    if [ "$FRONTEND_DOMAIN" = "$BACKEND_DOMAIN" ]; then
        echo -e "${GREEN}✅ 域名一致${NC}"
    else
        echo -e "${YELLOW}⚠️ 域名不一致: 前端($FRONTEND_DOMAIN) vs 后端($BACKEND_DOMAIN)${NC}"
    fi
else
    echo -e "${RED}❌ 配置不完整${NC}"
fi

# 验证开发环境配置一致性
echo -n "开发环境前后端配置一致性... "
if [ -n "$FRONTEND_DEV_CALLBACK" ] && [ -n "$BACKEND_DEV_CALLBACK" ]; then
    # 检查是否都使用localhost
    if echo "$FRONTEND_DEV_CALLBACK" | grep -q "localhost" && echo "$BACKEND_DEV_CALLBACK" | grep -q "localhost"; then
        echo -e "${GREEN}✅ 都使用localhost${NC}"
    else
        echo -e "${YELLOW}⚠️ 配置可能不一致${NC}"
    fi
else
    echo -e "${RED}❌ 配置不完整${NC}"
fi

echo ""
echo "🔍 检查路径一致性..."

# 检查路径格式
echo -n "前端回调路径格式... "
if echo "$FRONTEND_PROD_CALLBACK" | grep -q "/feishu/callback"; then
    echo -e "${GREEN}✅ 正确 (/feishu/callback)${NC}"
else
    echo -e "${RED}❌ 路径格式错误${NC}"
fi

echo -n "后端回调路径格式... "
if echo "$BACKEND_PROD_CALLBACK" | grep -q "/api/feishu/callback"; then
    echo -e "${GREEN}✅ 正确 (/api/feishu/callback)${NC}"
else
    echo -e "${RED}❌ 路径格式错误${NC}"
fi

echo ""
echo "🔍 检查HTTPS配置..."

# 检查生产环境是否使用HTTPS
echo -n "生产环境HTTPS配置... "
if echo "$BACKEND_PROD_CALLBACK" | grep -q "^https://"; then
    echo -e "${GREEN}✅ 使用HTTPS${NC}"
else
    echo -e "${YELLOW}⚠️ 未使用HTTPS${NC}"
fi

echo ""
echo "======================================"
echo "📋 配置摘要"
echo "======================================"
echo -e "${BLUE}生产环境配置:${NC}"
echo "  前端回调: $FRONTEND_PROD_CALLBACK"
echo "  后端回调: $BACKEND_PROD_CALLBACK"
echo ""
echo -e "${BLUE}开发环境配置:${NC}"
echo "  前端回调: $FRONTEND_DEV_CALLBACK"
echo "  后端回调: $BACKEND_DEV_CALLBACK"

echo ""
echo "======================================"
echo "📝 重要提醒"
echo "======================================"
echo "1. 确保飞书开放平台后台配置的回调地址与后端配置完全一致"
echo "2. 生产环境回调地址: $BACKEND_PROD_CALLBACK"
echo "3. 如果修改了配置，需要重新构建前端应用"
echo "4. 部署后需要测试飞书登录功能是否正常"

echo ""
echo "✨ 飞书回调地址配置验证完成！"
