#!/bin/bash

# 光年小卖部 - 代码格式化脚本
# 用于统一项目代码格式

echo "🚀 开始代码格式化..."

# 检查是否安装了必要的工具
check_tool() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 未安装，请先安装: npm install -g $1"
        exit 1
    fi
}

# 检查工具
echo "📋 检查必要工具..."
check_tool "prettier"

# 格式化前端代码
echo "🎨 格式化前端代码..."
npx prettier --write "src/**/*.{js,vue,css,scss,html,json,md}" --config .prettierrc

# 格式化后端代码
echo "🔧 格式化后端代码..."
cd server
npx prettier --write "**/*.{js,json,md}" --config ../.prettierrc
cd ..

# 运行ESLint修复
echo "🔍 运行ESLint自动修复..."
npm run lint:fix

# 格式化后端ESLint
echo "🔧 修复后端ESLint问题..."
cd server
npm run lint:fix
cd ..

# 格式化配置文件
echo "⚙️ 格式化配置文件..."
npx prettier --write "*.{json,md,yml,yaml}" --config .prettierrc

echo "✅ 代码格式化完成！"
echo ""
echo "📝 建议在提交前运行以下命令："
echo "  npm run lint        # 检查代码规范"
echo "  npm run format      # 格式化代码"
echo ""
echo "💡 提示：可以在IDE中配置保存时自动格式化"
