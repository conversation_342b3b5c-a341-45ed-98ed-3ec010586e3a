#!/bin/bash

# 光年小卖部 - 域名迁移部署脚本
# 从测试环境IP迁移到生产环境域名
# 前端域名: https://store.chongyangqisi.com
# 后端API域名: https://store-api.chongyangqisi.com

set -e  # 遇到错误立即退出

echo "======================================"
echo "光年小卖部 - 域名迁移部署脚本"
echo "======================================"
echo "前端域名: https://store.chongyangqisi.com"
echo "后端API域名: https://store-api.chongyangqisi.com"
echo "======================================"

# 检查当前目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在项目根目录下运行此脚本"
    exit 1
fi

# 创建备份目录
BACKUP_DIR="backup/domain_migration_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📦 创建配置文件备份..."
# 备份重要配置文件
cp .env.production "$BACKUP_DIR/" 2>/dev/null || echo "⚠️ 前端.env.production不存在"
cp server/.env.production "$BACKUP_DIR/" 2>/dev/null || echo "⚠️ 后端.env.production不存在"
cp nginx-production.conf "$BACKUP_DIR/" 2>/dev/null || echo "⚠️ nginx-production.conf不存在"
cp nginx-production-domain.conf "$BACKUP_DIR/" 2>/dev/null || echo "⚠️ nginx-production-domain.conf不存在"

echo "✅ 配置文件已备份到: $BACKUP_DIR"

# 验证环境变量配置
echo "🔍 验证环境变量配置..."

# 检查前端环境变量
if [ -f ".env.production" ]; then
    if grep -q "store-api.chongyangqisi.com" .env.production; then
        echo "✅ 前端环境变量配置正确"
    else
        echo "❌ 前端环境变量配置有误，请检查.env.production"
        exit 1
    fi
else
    echo "❌ 前端.env.production文件不存在"
    exit 1
fi

# 检查后端环境变量
if [ -f "server/.env.production" ]; then
    if grep -q "store-api.chongyangqisi.com" server/.env.production && grep -q "store.chongyangqisi.com" server/.env.production; then
        echo "✅ 后端环境变量配置正确"
    else
        echo "❌ 后端环境变量配置有误，请检查server/.env.production"
        exit 1
    fi
else
    echo "❌ 后端server/.env.production文件不存在"
    exit 1
fi

# 清理旧的构建文件
echo "🧹 清理旧的构建文件..."
rm -rf dist/*
rm -rf server/node_modules/.cache 2>/dev/null || true

# 安装依赖
echo "📦 安装前端依赖..."
npm install

echo "📦 安装后端依赖..."
cd server
npm install
cd ..

# 构建前端
echo "🏗️ 构建前端应用..."
npm run build

# 验证构建结果
if [ ! -f "dist/index.html" ]; then
    echo "❌ 前端构建失败"
    exit 1
fi

echo "✅ 前端构建完成"

# 检查构建文件中是否还有旧的IP地址
echo "🔍 检查构建文件中的URL配置..."
if grep -r "47\.122\.122\.245" dist/ 2>/dev/null; then
    echo "⚠️ 警告: 构建文件中仍包含旧的IP地址，这可能是缓存问题"
    echo "建议清理缓存后重新构建"
else
    echo "✅ 构建文件URL配置正确"
fi

# 显示部署说明
echo ""
echo "======================================"
echo "🎉 域名迁移配置完成！"
echo "======================================"
echo ""
echo "📋 接下来需要完成以下步骤："
echo ""
echo "1. 🌐 域名和SSL证书配置："
echo "   - 确保域名 store.chongyangqisi.com 和 store-api.chongyangqisi.com 已正确解析到服务器"
echo "   - 为两个域名申请并配置SSL证书"
echo "   - 更新nginx配置文件中的SSL证书路径"
echo ""
echo "2. 📁 服务器文件部署："
echo "   - 将整个项目目录上传到服务器 /www/wwwroot/workyy/"
echo "   - 或使用git拉取最新代码"
echo ""
echo "3. ⚙️ Nginx配置："
echo "   - 使用新的nginx配置文件: nginx-production-domain.conf"
echo "   - 更新SSL证书路径（第18、19、158、159行）"
echo "   - 重启nginx服务"
echo ""
echo "4. 🔄 服务重启："
echo "   - 在服务器上运行: ./restart.sh"
echo "   - 或使用PM2重启: pm2 restart feishu-mall-api"
echo ""
echo "5. 🧪 测试验证："
echo "   - 访问 https://store.chongyangqisi.com 测试前端"
echo "   - 访问 https://store-api.chongyangqisi.com/api/health 测试后端API"
echo "   - 测试飞书登录功能"
echo "   - 测试文件上传功能"
echo ""
echo "📝 重要提醒："
echo "   - 备份文件已保存在: $BACKUP_DIR"
echo "   - 如遇问题可使用备份文件恢复"
echo "   - 建议在低峰期进行域名切换"
echo ""
echo "======================================"

# 生成nginx配置检查脚本
cat > check-nginx-config.sh << 'EOF'
#!/bin/bash
echo "检查nginx配置文件..."
if [ -f "nginx-production-domain.conf" ]; then
    echo "✅ 找到域名nginx配置文件"
    
    # 检查SSL证书路径
    if grep -q "/path/to/" nginx-production-domain.conf; then
        echo "⚠️ 警告: 需要更新SSL证书路径"
        echo "请编辑 nginx-production-domain.conf 文件，更新以下行："
        grep -n "/path/to/" nginx-production-domain.conf
    else
        echo "✅ SSL证书路径已配置"
    fi
    
    # 测试nginx配置语法
    if command -v nginx >/dev/null 2>&1; then
        nginx -t -c $(pwd)/nginx-production-domain.conf
    else
        echo "ℹ️ nginx未安装，无法测试配置语法"
    fi
else
    echo "❌ 未找到nginx-production-domain.conf文件"
fi
EOF

chmod +x check-nginx-config.sh

echo "🔧 已生成nginx配置检查脚本: check-nginx-config.sh"
echo "   运行 ./check-nginx-config.sh 来检查nginx配置"
echo ""
echo "✨ 域名迁移准备完成！"
