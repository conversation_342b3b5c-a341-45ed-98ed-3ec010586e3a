# 光年小卖部 - 热门商品自动识别功能设计方案

## 📋 项目现状分析

### 技术架构
- **前端**: Vue 3 + Element Plus + Pinia + Vue Router
- **后端**: Node.js + Express + Sequelize + MySQL
- **数据库**: MySQL (feishu_mall)
- **认证**: JWT + 飞书OAuth集成

### 现有核心模型
- **Product**: 商品模型（支持光年币/人民币双价格）
- **User**: 用户模型（包含光年币积分系统）
- **Exchange**: 订单模型（支持双支付方式）
- **Category**: 分类模型
- **ProductPriceHistory**: 价格历史模型

### 现有商品标记功能
- 商品标签：`isHot`（热门）、`isNew`（新品）
- 手动设置热门商品功能
- 基础商品筛选和展示

---

## 🎯 热门商品自动识别模块设计

### 1. 数据库设计

#### 1.1 扩展商品表 (products)
```sql
ALTER TABLE products
ADD COLUMN isAutoHot BOOLEAN DEFAULT false COMMENT '是否自动识别的热门商品',
ADD COLUMN hotTimeRange ENUM('all', '30d', '7d', '1d') NULL COMMENT '热门时间维度',
ADD COLUMN hotScore DECIMAL(10,2) DEFAULT 0 COMMENT '热门度评分',
ADD COLUMN hotRank INT NULL COMMENT '热门排名',
ADD COLUMN lastHotUpdate DATETIME NULL COMMENT '最后热门状态更新时间';
```

#### 1.2 热门商品配置表 (hot_product_configs)
```sql
CREATE TABLE hot_product_configs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  timeRange ENUM('all', '30d', '7d', '1d') NOT NULL COMMENT '时间维度',
  maxHotProducts INT NOT NULL DEFAULT 10 COMMENT '最大热门商品数量',
  minExchangeCount INT NOT NULL DEFAULT 1 COMMENT '最小兑换数量要求',
  exchangeWeight DECIMAL(5,2) NOT NULL DEFAULT 1.0 COMMENT '兑换量权重',
  stockBonus DECIMAL(5,2) NOT NULL DEFAULT 0.2 COMMENT '库存奖励系数',
  isActive BOOLEAN NOT NULL DEFAULT true COMMENT '是否启用',
  updatedBy INT COMMENT '最后更新人ID',
  updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (updatedBy) REFERENCES users(id)
);
```

#### 1.3 热门商品历史记录表 (hot_product_history)
```sql
CREATE TABLE hot_product_history (
  id INT PRIMARY KEY AUTO_INCREMENT,
  productId INT NOT NULL COMMENT '商品ID',
  timeRange ENUM('all', '30d', '7d', '1d') NOT NULL COMMENT '时间维度',
  hotScore DECIMAL(10,2) NOT NULL COMMENT '热门度评分',
  hotRank INT NOT NULL COMMENT '热门排名',
  exchangeCount INT NOT NULL COMMENT '兑换数量',
  createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (productId) REFERENCES products(id)
);
```

### 2. 后端服务设计

#### 2.1 热门商品服务 (HotProductService)
```javascript
// server/services/hotProductService.js
class HotProductService {
  // 计算所有时间维度的热门商品
  async updateAllHotProducts() {...}
  
  // 计算特定时间维度的热门商品
  async updateHotProducts(timeRange) {...}
  
  // 计算热门度评分
  calculateHotScore(product, config, timeRange) {...}
  
  // 获取特定时间维度的热门商品
  async getHotProducts(timeRange, limit) {...}
  
  // 获取热门商品配置
  async getHotProductConfigs() {...}
  
  // 更新热门商品配置
  async updateHotProductConfig(configData) {...}
  
  // 记录热门商品历史
  async recordHotProductHistory(productId, timeRange, hotScore, hotRank, exchangeCount) {...}
}
```

#### 2.2 定时任务集成
```javascript
// server/services/scheduledTaskService.js
const cron = require('node-cron');
const hotProductService = require('./hotProductService');

// 每小时更新热门商品
cron.schedule('0 * * * *', async () => {
  console.log('开始定时更新热门商品...');
  try {
    await hotProductService.updateAllHotProducts();
    console.log('热门商品更新完成');
  } catch (error) {
    console.error('热门商品更新失败:', error);
  }
});
```

### 3. API设计

#### 3.1 热门商品API
```javascript
// server/routes/hotProducts.js
const express = require('express');
const router = express.Router();
const hotProductController = require('../controllers/hotProductController');
const { isAdmin } = require('../middlewares/auth');

// 获取指定时间维度的热门商品
router.get('/', hotProductController.getHotProducts);

// 获取所有时间维度的热门商品
router.get('/all', hotProductController.getAllHotProducts);

// 获取热门商品配置（管理员）
router.get('/configs', isAdmin, hotProductController.getHotProductConfigs);

// 更新热门商品配置（管理员）
router.put('/configs', isAdmin, hotProductController.updateHotProductConfig);

// 手动触发热门商品更新（管理员）
router.post('/update', isAdmin, hotProductController.triggerHotProductUpdate);

// 获取热门商品历史记录（管理员）
router.get('/history', isAdmin, hotProductController.getHotProductHistory);

// 获取热门商品统计信息（管理员）
router.get('/stats', isAdmin, hotProductController.getHotProductStats);

module.exports = router;
```

### 4. 前端组件设计

#### 4.1 热门商品管理页面
```vue
<!-- src/views/admin/HotProductManagement.vue -->
<template>
  <div class="hot-product-management">
    <h1>热门商品智能识别管理</h1>
    
    <!-- 热门商品配置表单 -->
    <el-tabs v-model="activeTab">
      <el-tab-pane label="累积热门" name="all">
        <hot-product-config-form :config="configs.all" @update="updateConfig" />
      </el-tab-pane>
      <el-tab-pane label="30天热门" name="30d">
        <hot-product-config-form :config="configs['30d']" @update="updateConfig" />
      </el-tab-pane>
      <el-tab-pane label="7天热门" name="7d">
        <hot-product-config-form :config="configs['7d']" @update="updateConfig" />
      </el-tab-pane>
      <el-tab-pane label="今日热门" name="1d">
        <hot-product-config-form :config="configs['1d']" @update="updateConfig" />
      </el-tab-pane>
    </el-tabs>
    
    <!-- 手动更新按钮 -->
    <el-button type="primary" @click="triggerUpdate" :loading="updating">
      手动更新热门商品
    </el-button>
    
    <!-- 热门商品列表 -->
    <hot-product-list :timeRange="activeTab" />
    
    <!-- 热门商品历史记录 -->
    <hot-product-history :timeRange="activeTab" />
  </div>
</template>
```

#### 4.2 热门商品排行榜组件
```vue
<!-- src/components/HotProductRanking.vue -->
<template>
  <div class="hot-product-ranking">
    <h3>热门商品排行榜</h3>
    
    <el-tabs v-model="activeTimeRange">
      <el-tab-pane label="累积热门" name="all"></el-tab-pane>
      <el-tab-pane label="30天热门" name="30d"></el-tab-pane>
      <el-tab-pane label="7天热门" name="7d"></el-tab-pane>
      <el-tab-pane label="今日热门" name="1d"></el-tab-pane>
    </el-tabs>
    
    <el-table :data="hotProducts" stripe>
      <el-table-column label="排名" width="80">
        <template #default="scope">
          <span class="rank">{{ scope.row.hotRank }}</span>
        </template>
      </el-table-column>
      <el-table-column label="商品" min-width="200">
        <template #default="scope">
          <div class="product-info">
            <img :src="scope.row.imageUrl" class="product-image" />
            <div>
              <div class="product-name">{{ scope.row.name }}</div>
              <div class="product-category">{{ scope.row.category.name }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="兑换量" prop="exchangeCount" width="100"></el-table-column>
      <el-table-column label="热门度" prop="hotScore" width="100"></el-table-column>
    </el-table>
  </div>
</template>
```

### 5. 算法设计

#### 5.1 热门度评分算法
```javascript
/**
 * 计算热门度评分
 * @param {Object} product - 商品对象
 * @param {Object} config - 热门商品配置
 * @param {String} timeRange - 时间维度
 * @returns {Number} 热门度评分
 */
function calculateHotScore(product, config, timeRange) {
  // 基础分数 = 兑换量 * 兑换权重
  let baseScore = product.exchangeCount * config.exchangeWeight;
  
  // 库存奖励 = 如果库存充足，给予额外加分
  let stockBonus = 0;
  if (product.stock > 0) {
    stockBonus = Math.min(product.stock / 10, 5) * config.stockBonus;
  }
  
  // 最终评分 = 基础分数 + 库存奖励
  return baseScore + stockBonus;
}
```

#### 5.2 时间维度筛选逻辑
```javascript
/**
 * 根据时间维度获取兑换记录
 * @param {String} timeRange - 时间维度 (all/30d/7d/1d)
 * @returns {Object} 查询条件
 */
function getTimeRangeCondition(timeRange) {
  const now = new Date();
  
  switch(timeRange) {
    case '30d':
      const thirtyDaysAgo = new Date(now);
      thirtyDaysAgo.setDate(now.getDate() - 30);
      return { createdAt: { [Op.gte]: thirtyDaysAgo } };
      
    case '7d':
      const sevenDaysAgo = new Date(now);
      sevenDaysAgo.setDate(now.getDate() - 7);
      return { createdAt: { [Op.gte]: sevenDaysAgo } };
      
    case '1d':
      const oneDayAgo = new Date(now);
      oneDayAgo.setDate(now.getDate() - 1);
      return { createdAt: { [Op.gte]: oneDayAgo } };
      
    case 'all':
    default:
      return {}; // 不限时间
  }
}
```

## 🚀 实施计划

### 阶段一：数据库准备
1. 创建数据库迁移脚本
2. 添加商品表新字段
3. 创建热门商品配置表和历史记录表
4. 初始化默认配置数据

### 阶段二：后端开发
1. 实现热门商品服务
2. 开发热门商品控制器
3. 配置API路由
4. 集成定时任务

### 阶段三：前端开发
1. 创建热门商品管理页面
2. 开发配置表单组件
3. 实现热门商品排行榜组件
4. 在商品卡片中显示热门标签

### 阶段四：测试与部署
1. 单元测试热门商品算法
2. 集成测试API接口
3. 前端组件测试
4. 生产环境部署

## 📊 预期效果

1. 系统能够自动识别并标记热门商品
2. 管理员可以配置热门商品识别规则
3. 用户可以查看不同时间维度的热门商品排行
4. 热门商品在首页和分类页面获得更高曝光
5. 提升用户购物体验和商品转化率
