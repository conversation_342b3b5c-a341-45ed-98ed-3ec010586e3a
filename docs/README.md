# 光年小卖部项目文档

## 文档目录

- [项目概述](./project-overview.md)
- [API文档](#api文档)
- [代码规范](./code-standard.md)
- [部署指南](./deployment.md)

## API文档

项目已集成Swagger API文档，可通过以下方式访问：

1. 启动后端服务器
2. 访问 `http://localhost:3000/api-docs` 查看完整API文档

### API文档特性

- 所有API接口详细说明
- 请求/响应示例
- 在线测试功能
- 权限要求说明

### 如何为新API添加文档

在路由文件中使用JSDoc格式注释来添加Swagger文档，示例：

```javascript
/**
 * @swagger
 * /products:
 *   get:
 *     summary: 获取商品列表
 *     tags: [Products]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *     responses:
 *       200:
 *         description: 返回商品列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 products:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Product'
 */
router.get('/', productController.getProducts);
```

## 代码规范

项目代码规范定义在[code-standard.md](./code-standard.md)文件中，所有团队成员必须遵循这些规范。

主要规范包括：

- 通用命名和编码规范
- 前端Vue和JavaScript规范
- 后端Express开发规范
- MongoDB数据库设计规范
- Git工作流程规范

## 如何贡献文档

1. 创建新的Markdown文件或更新现有文档
2. 遵循文档规范格式
3. 提交PR请求审核
4. 合并到主分支后，文档将自动更新 