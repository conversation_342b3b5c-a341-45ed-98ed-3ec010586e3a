#!/bin/bash

echo "🔄 库存转移API测试脚本"
echo "=========================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# API基础URL
API_BASE="http://localhost:3000/api"

echo -e "${BLUE}1. 测试用户登录...${NC}"
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "超管",
    "email": "<EMAIL>",
    "password": "654321"
  }')

# 检查登录是否成功
if echo "$LOGIN_RESPONSE" | grep -q "token"; then
    echo -e "${GREEN}✅ 登录成功${NC}"
    TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    echo "Token: ${TOKEN:0:50}..."
else
    echo -e "${RED}❌ 登录失败${NC}"
    echo "响应: $LOGIN_RESPONSE"
    exit 1
fi

echo ""
echo -e "${BLUE}2. 测试获取商品数据...${NC}"
PRODUCTS_RESPONSE=$(curl -s -X GET "$API_BASE/products?includeWorkplaceStocks=true&limit=5" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json")

if echo "$PRODUCTS_RESPONSE" | grep -q "success.*true"; then
    echo -e "${GREEN}✅ 获取商品数据成功${NC}"
    PRODUCT_COUNT=$(echo "$PRODUCTS_RESPONSE" | grep -o '"data":\[.*\]' | grep -o '{"id"' | wc -l)
    echo "获取到 $PRODUCT_COUNT 个商品"
else
    echo -e "${RED}❌ 获取商品数据失败${NC}"
    echo "响应: $PRODUCTS_RESPONSE"
fi

echo ""
echo -e "${BLUE}3. 测试获取职场数据...${NC}"
WORKPLACES_RESPONSE=$(curl -s -X GET "$API_BASE/workplaces" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json")

if echo "$WORKPLACES_RESPONSE" | grep -q "success.*true"; then
    echo -e "${GREEN}✅ 获取职场数据成功${NC}"
    WORKPLACE_COUNT=$(echo "$WORKPLACES_RESPONSE" | grep -o '"data":\[.*\]' | grep -o '{"id"' | wc -l)
    echo "获取到 $WORKPLACE_COUNT 个职场"
else
    echo -e "${RED}❌ 获取职场数据失败${NC}"
    echo "响应: $WORKPLACES_RESPONSE"
fi

echo ""
echo -e "${BLUE}4. 测试正常库存转移...${NC}"
TRANSFER_RESPONSE=$(curl -s -X POST "$API_BASE/stocks/transfer" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "productId": 62,
    "fromWorkplaceId": 2,
    "toWorkplaceId": 3,
    "quantity": 1,
    "reason": "API测试脚本转移"
  }')

if echo "$TRANSFER_RESPONSE" | grep -q "success.*true"; then
    echo -e "${GREEN}✅ 库存转移成功${NC}"
    echo "转移详情:"
    echo "$TRANSFER_RESPONSE" | grep -o '"fromWorkplace":{[^}]*}' | sed 's/,/\n  /g' | sed 's/{/  /g' | sed 's/}//g'
    echo "$TRANSFER_RESPONSE" | grep -o '"toWorkplace":{[^}]*}' | sed 's/,/\n  /g' | sed 's/{/  /g' | sed 's/}//g'
else
    echo -e "${RED}❌ 库存转移失败${NC}"
    echo "响应: $TRANSFER_RESPONSE"
fi

echo ""
echo -e "${BLUE}5. 测试边界情况 - 库存不足...${NC}"
INSUFFICIENT_RESPONSE=$(curl -s -X POST "$API_BASE/stocks/transfer" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "productId": 62,
    "fromWorkplaceId": 2,
    "toWorkplaceId": 3,
    "quantity": 9999,
    "reason": "库存不足测试"
  }')

if echo "$INSUFFICIENT_RESPONSE" | grep -q "success.*false"; then
    echo -e "${GREEN}✅ 库存不足检查正常${NC}"
    ERROR_MSG=$(echo "$INSUFFICIENT_RESPONSE" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
    echo "错误信息: $ERROR_MSG"
else
    echo -e "${YELLOW}⚠️  库存不足检查可能有问题${NC}"
    echo "响应: $INSUFFICIENT_RESPONSE"
fi

echo ""
echo -e "${BLUE}6. 测试边界情况 - 相同职场...${NC}"
SAME_WORKPLACE_RESPONSE=$(curl -s -X POST "$API_BASE/stocks/transfer" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "productId": 62,
    "fromWorkplaceId": 2,
    "toWorkplaceId": 2,
    "quantity": 1,
    "reason": "相同职场测试"
  }')

if echo "$SAME_WORKPLACE_RESPONSE" | grep -q "success.*false"; then
    echo -e "${GREEN}✅ 相同职场检查正常${NC}"
    ERROR_MSG=$(echo "$SAME_WORKPLACE_RESPONSE" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
    echo "错误信息: $ERROR_MSG"
else
    echo -e "${YELLOW}⚠️  相同职场检查可能有问题${NC}"
    echo "响应: $SAME_WORKPLACE_RESPONSE"
fi

echo ""
echo -e "${GREEN}=========================="
echo "🎉 API测试完成！"
echo "=========================="${NC}

echo ""
echo -e "${YELLOW}💡 提示：${NC}"
echo "- 如果所有测试都通过，说明后端API工作正常"
echo "- 如果前端仍有问题，可能是前端组件或CORS配置问题"
echo "- 可以通过浏览器访问 http://localhost:8080/test-stock-transfer-complete.html 进行前端测试"
