#!/bin/bash

# 数据库自动备份脚本
# 用途：在每次数据库结构更新前自动创建备份
# 作者：系统管理员
# 创建时间：2025-07-15

# 配置参数
DB_NAME="feishu_mall"
DB_USER="root"
DB_PASSWORD="password"
BACKUP_DIR="./backups"
LOG_FILE="./logs/backup.log"

# 创建备份目录（如果不存在）
mkdir -p "$BACKUP_DIR"
mkdir -p "./logs"

# 生成备份文件名（包含时间戳）
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="${BACKUP_DIR}/feishu_mall_backup_${TIMESTAMP}.sql"

# 记录开始时间
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 开始数据库备份..." >> "$LOG_FILE"

# 执行备份
mysqldump -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" > "$BACKUP_FILE" 2>> "$LOG_FILE"

# 检查备份是否成功
if [ $? -eq 0 ]; then
    # 获取备份文件大小
    BACKUP_SIZE=$(ls -lh "$BACKUP_FILE" | awk '{print $5}')
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 备份成功: $BACKUP_FILE (大小: $BACKUP_SIZE)" >> "$LOG_FILE"
    echo "✅ 数据库备份成功: $BACKUP_FILE"
    echo "📁 备份大小: $BACKUP_SIZE"
    
    # 记录到数据库日志表
    mysql -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "
        INSERT INTO logs (action, entityType, entityId, description, userId, username, createdAt, updatedAt) 
        VALUES ('DATABASE_BACKUP', 'database', 1, '自动备份完成: $BACKUP_FILE (大小: $BACKUP_SIZE)', 1, 'system', NOW(), NOW());
    " 2>> "$LOG_FILE"
    
else
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 备份失败!" >> "$LOG_FILE"
    echo "❌ 数据库备份失败，请检查日志: $LOG_FILE"
    exit 1
fi

# 清理旧备份（保留最近30个备份文件）
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 开始清理旧备份文件..." >> "$LOG_FILE"
cd "$BACKUP_DIR"
ls -t feishu_mall_backup_*.sql | tail -n +31 | xargs -r rm -f
REMAINING_COUNT=$(ls -1 feishu_mall_backup_*.sql 2>/dev/null | wc -l)
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 清理完成，当前保留 $REMAINING_COUNT 个备份文件" >> "$LOG_FILE"

echo "🗂️  当前保留 $REMAINING_COUNT 个备份文件"
echo "📝 详细日志: $LOG_FILE"
