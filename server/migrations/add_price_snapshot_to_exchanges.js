'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 为exchanges表添加价格快照字段
    await queryInterface.addColumn('exchanges', 'unitPrice', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: true,
      comment: '订单创建时的商品单价快照'
    });

    await queryInterface.addColumn('exchanges', 'priceType', {
      type: Sequelize.ENUM('ly', 'rmb'),
      allowNull: true,
      comment: '价格类型：ly(光年币价格)或rmb(人民币价格)'
    });

    // 为现有数据填充价格快照
    console.log('正在为现有订单填充价格快照...');
    
    // 更新人民币订单的价格快照
    await queryInterface.sequelize.query(`
      UPDATE exchanges e 
      JOIN products p ON e.productId = p.id
      SET 
        e.unitPrice = p.rmbPrice,
        e.priceType = 'rmb'
      WHERE e.paymentMethod = 'rmb' AND e.unitPrice IS NULL
    `);
    
    // 更新光年币订单的价格快照
    await queryInterface.sequelize.query(`
      UPDATE exchanges e 
      JOIN products p ON e.productId = p.id
      SET 
        e.unitPrice = p.lyPrice,
        e.priceType = 'ly'
      WHERE e.paymentMethod = 'ly' AND e.unitPrice IS NULL
    `);

    // 重新计算totalAmount确保数据一致性
    await queryInterface.sequelize.query(`
      UPDATE exchanges 
      SET totalAmount = unitPrice * quantity 
      WHERE unitPrice IS NOT NULL
    `);
    
    console.log('价格快照填充完成');

    // 将字段设为非空
    await queryInterface.changeColumn('exchanges', 'unitPrice', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      comment: '订单创建时的商品单价快照'
    });

    await queryInterface.changeColumn('exchanges', 'priceType', {
      type: Sequelize.ENUM('ly', 'rmb'),
      allowNull: false,
      comment: '价格类型：ly(光年币价格)或rmb(人民币价格)'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('exchanges', 'unitPrice');
    await queryInterface.removeColumn('exchanges', 'priceType');
  }
};
