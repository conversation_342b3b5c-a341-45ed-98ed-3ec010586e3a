# 光年小卖部 - 部署快速参考

## 服务器信息

| 项目 | 值 |
|------|-----|
| 服务器IP | ************** |
| 用户 | root |
| 密码 | Aa@123456 |
| 部署目录 | /www/wwwroot/workyy |
| 前端端口 | 80 (Nginx代理) |
| 后端端口 | 3000 |
| 数据库端口 | 3306 |
| PM2应用名 | feishu-mall-api |
| Git分支 | feat/reset |

## 快速部署命令

### 1. 连接服务器
```bash
ssh root@**************
```

### 2. 一键部署脚本
```bash
cd /www/wwwroot/workyy
chmod +x scripts/deploy/production-deploy-complete.sh
./scripts/deploy/production-deploy-complete.sh
```

### 3. 手动部署步骤
```bash
# 1. 克隆/更新代码
git clone -b feat/reset https://gitee.com/your-username/workyy.git /www/wwwroot/workyy
cd /www/wwwroot/workyy

# 2. 安装依赖
npm install
cd server && npm install --omit=optional && npm uninstall nodejieba

# 3. 构建前端
cd /www/wwwroot/workyy && npm run build

# 4. 启动后端
cd server && pm2 start server.js --name feishu-mall-api

# 5. 配置Nginx
nginx -t && systemctl restart nginx
```

## 重要配置文件

### 后端环境配置 (server/.env.production)
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=feishu_mall

# 应用配置
NODE_ENV=production
PORT=3000
SERVER_URL=http://**************:3000

# JWT配置
JWT_SECRET=your_secure_jwt_secret
JWT_EXPIRES_IN=1h
JWT_LONG_EXPIRES_IN=30d

# 飞书应用配置
FEISHU_APP_ID=cli_a66b3b2dcab8d013
FEISHU_APP_SECRET=5Fa8aatAGZ2Dv6K5VZhAWhbhjzE4lT2r
FEISHU_REDIRECT_URI=http://**************:3000/api/feishu/callback

# CORS配置
CORS_ORIGIN=http://**************,http://**************:80
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS
```

### 前端环境配置 (.env.production)
```env
VITE_API_URL=http://**************/api
VITE_APP_TITLE=光年小卖部
VITE_NODE_ENV=production
VITE_DEV_TOOLS=false
```

### PM2配置 (server/ecosystem.config.js)
```javascript
module.exports = {
  apps: [{
    name: 'feishu-mall-api',
    script: 'server.js',
    instances: 1,
    exec_mode: 'fork',
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    log_file: '../logs/combined.log',
    out_file: '../logs/out.log',
    error_file: '../logs/error.log'
  }]
};
```

## 常用运维命令

### PM2管理
```bash
# 查看应用状态
pm2 list

# 查看日志
pm2 logs feishu-mall-api --lines 50

# 重启应用
pm2 restart feishu-mall-api

# 停止应用
pm2 stop feishu-mall-api

# 删除应用
pm2 delete feishu-mall-api

# 监控应用
pm2 monit

# 保存配置
pm2 save
```

### Nginx管理
```bash
# 测试配置
nginx -t

# 重启服务
systemctl restart nginx

# 重新加载配置
systemctl reload nginx

# 查看状态
systemctl status nginx

# 查看日志
tail -f /www/wwwroot/workyy/logs/nginx_access.log
tail -f /www/wwwroot/workyy/logs/nginx_error.log
```

### MySQL管理
```bash
# 连接数据库
mysql -u root -p feishu_mall

# 查看数据库状态
systemctl status mysqld

# 重启数据库
systemctl restart mysqld

# 备份数据库
mysqldump -u root -p feishu_mall > backup_$(date +%Y%m%d).sql
```

### 系统监控
```bash
# 查看端口占用
netstat -tlnp | grep -E ':(80|3000|3306)'

# 查看系统资源
htop
df -h
free -h

# 查看进程
ps aux | grep node
ps aux | grep nginx
```

## 测试命令

### API测试
```bash
# 健康检查
curl http://**************/api/health

# 测试登录接口
curl -X POST http://**************/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# 测试文件上传
curl -X POST http://**************/api/upload \
  -F "file=@test.jpg"
```

### 前端测试
```bash
# 测试首页
curl -I http://**************/

# 测试静态资源
curl -I http://**************/assets/index.js
```

## 故障排除

### 端口占用
```bash
# 查看端口占用
lsof -i :3000

# 杀死进程
kill -9 <PID>

# 或使用fuser
fuser -k 3000/tcp
```

### 权限问题
```bash
# 设置项目权限
chown -R root:root /www/wwwroot/workyy
chmod -R 755 /www/wwwroot/workyy
chmod -R 777 /www/wwwroot/workyy/server/uploads
```

### 依赖问题
```bash
# 清除npm缓存
npm cache clean --force

# 重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

### 服务异常
```bash
# 查看PM2日志
pm2 logs feishu-mall-api --lines 100

# 查看系统日志
journalctl -u nginx -f
journalctl -u mysqld -f

# 重启所有服务
pm2 restart all
systemctl restart nginx
systemctl restart mysqld
```

## 重启脚本

### 完整重启
```bash
cd /www/wwwroot/workyy
./restart-production.sh
```

### 快速重启
```bash
cd /www/wwwroot/workyy
./restart-production-simple.sh
```

## 备份和恢复

### 创建备份
```bash
# 数据库备份
mysqldump -u root -p feishu_mall > backup_$(date +%Y%m%d).sql

# 项目文件备份
tar -czf workyy_backup_$(date +%Y%m%d).tar.gz /www/wwwroot/workyy

# PM2配置备份
pm2 save
cp ~/.pm2/dump.pm2 backup_pm2_$(date +%Y%m%d).json
```

### 恢复备份
```bash
# 恢复数据库
mysql -u root -p feishu_mall < backup_20250722.sql

# 恢复项目文件
tar -xzf workyy_backup_20250722.tar.gz -C /

# 恢复PM2配置
cp backup_pm2_20250722.json ~/.pm2/dump.pm2
pm2 resurrect
```

## 访问地址

| 服务 | 地址 |
|------|------|
| 前端页面 | http://************** |
| 后端API | http://**************/api |
| 健康检查 | http://**************/api/health |
| 上传文件 | http://**************/uploads/ |

## 重要提醒

1. **部署前备份**: 始终在部署前创建完整备份
2. **测试环境**: 建议先在测试环境验证
3. **维护窗口**: 在业务低峰期进行部署
4. **监控告警**: 部署后密切监控系统状态
5. **回滚准备**: 准备好快速回滚方案

## 联系信息

- **技术支持**: 开发团队
- **紧急联系**: 运维团队
- **文档更新**: 请及时更新本文档
