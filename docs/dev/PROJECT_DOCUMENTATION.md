# 项目文档

## 开发文档

### 项目架构
项目使用 Vue.js 作为前端框架，结合 Pinia 进行状态管理。主要组件包括商品详情对话框、商品卡片等。

### 技术栈
- Vue.js
- Pinia
- Element Plus

### 开发环境配置
1. 安装 Node.js 和 npm。
2. 克隆项目仓库。
3. 在项目根目录运行 `npm install` 安装依赖。

### 部署说明
项目可以通过 `npm run build` 进行构建，然后将生成的文件部署到服务器。

## 用户使用手册

### 功能介绍
- 商品浏览：用户可以浏览商品列表，并查看商品详情。
- 商品筛选：支持按类别和价格排序筛选商品。
- 商品搜索：用户可以通过商品名称进行搜索。

### 操作指南
1. 打开网站首页。
2. 使用筛选和搜索功能找到感兴趣的商品。
3. 点击商品查看详情。

### 常见问题解答
- 如何反馈意见？
  用户可以点击页面上的“意见反馈”按钮，填写反馈表单。

- 如何筛选商品？
  用户可以使用页面上的筛选选项，根据类别和价格进行筛选。