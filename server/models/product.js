const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Product = sequelize.define('Product', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true
    }
  },
  categoryId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'categories',
      key: 'id'
    }
  },
  lyPrice: {
    type: DataTypes.INTEGER,
    allowNull: false,
    validate: {
      min: 0
    }
  },
  rmbPrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  stock: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  exchangeCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  isHot: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  isNew: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  isAutoHot: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '自动识别的热门商品'
  },
  hotTimeRange: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '热门时间维度：all/30d/7d/1d'
  },
  hotScore: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
    comment: '热门度评分'
  },
  hotRank: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '热门排名'
  },
  lastHotUpdate: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '最后热门状态更新时间'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive'),
    allowNull: false,
    defaultValue: 'active'
  },
  stockManagementType: {
    type: DataTypes.ENUM('single', 'workplace'),
    allowNull: false,
    defaultValue: 'single',
    comment: '库存管理类型：single-单一库存，workplace-职场分配'
  },
  stockSyncedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '库存同步到职场的时间'
  }
}, {
  tableName: 'products',
  timestamps: true,
  hooks: {
    beforeUpdate: async (product, options) => {
      try {
        // 检查价格是否发生变化
        const changed = product.changed();
        const priceChanged = changed && (changed.includes('lyPrice') || changed.includes('rmbPrice'));

        if (priceChanged) {
          // 获取变更前的价格
          const oldValues = product._previousDataValues;
          const oldLyPrice = oldValues.lyPrice;
          const oldRmbPrice = oldValues.rmbPrice;

          // 记录价格变更历史
          const ProductPriceHistory = sequelize.models.ProductPriceHistory;
          if (ProductPriceHistory) {
            await ProductPriceHistory.create({
              productId: product.id,
              oldLyPrice: oldLyPrice,
              newLyPrice: product.lyPrice,
              oldRmbPrice: oldRmbPrice,
              newRmbPrice: product.rmbPrice,
              changeReason: options.changeReason || '价格更新',
              changedBy: options.userId || null,
              effectiveDate: new Date()
            });

            console.log(`商品 ${product.name} 价格变更已记录: 光年币 ${oldLyPrice} -> ${product.lyPrice}, 人民币 ${oldRmbPrice} -> ${product.rmbPrice}`);
          }
        }
      } catch (error) {
        console.error('记录商品价格变更历史失败:', error);
      }
    }
  }
});

module.exports = Product;
