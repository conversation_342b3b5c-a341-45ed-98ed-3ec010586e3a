<template>
  <el-card class="payment-preference-card">
    <template #header>
      <div class="card-header">
        <h3>支付方式偏好分析</h3>
        <div class="card-controls">
          <el-radio-group v-model="viewMode" size="small" @change="handleViewModeChange">
            <el-radio-button label="overview">概览</el-radio-button>
            <el-radio-button label="trend">趋势</el-radio-button>
            <el-radio-button label="category">分类</el-radio-button>
          </el-radio-group>
          <el-select v-model="selectedPeriod" size="small" @change="handlePeriodChange" style="margin-left: 10px; width: 100px;">
            <el-option label="周" value="week" />
            <el-option label="月" value="month" />
            <el-option label="季" value="quarter" />
            <el-option label="年" value="year" />
          </el-select>
          <el-tooltip content="刷新数据" placement="top">
            <el-button size="small" circle @click="refreshData" :loading="isLoading" style="margin-left: 10px;">
              <el-icon><component :is="ElementPlusIcons.Refresh" /></el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </template>

    <div v-loading="isLoading" class="card-content">
      <!-- 概览模式 -->
      <div v-if="viewMode === 'overview'" class="payment-overview">
        <div class="payment-stats">
          <div class="stat-item rmb">
            <div class="stat-icon">¥</div>
            <div class="stat-content">
              <div class="stat-value">{{ paymentData.overview.rmbPercentage }}%</div>
              <div class="stat-label">人民币支付</div>
              <div class="stat-detail">{{ paymentData.overview.rmbOrders }} 单</div>
            </div>
          </div>
          <div class="stat-item ly">
            <div class="stat-icon">⭐</div>
            <div class="stat-content">
              <div class="stat-value">{{ paymentData.overview.lyPercentage }}%</div>
              <div class="stat-label">光年币支付</div>
              <div class="stat-detail">{{ paymentData.overview.lyOrders }} 单</div>
            </div>
          </div>
        </div>

        <!-- 数据说明 -->
        <div class="data-explanation">
          <el-alert
            title="数据说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <div class="explanation-content">
                <p><strong>支付方式占比</strong>：基于订单数量统计，显示不同支付方式的使用比例</p>
                <p><strong>用户分层</strong>：根据用户历史订单的支付方式进行分类</p>
                <ul>
                  <li><span class="label-rmb">仅人民币用户</span>：所有订单都使用人民币支付的用户</li>
                  <li><span class="label-ly">仅光年币用户</span>：所有订单都使用光年币支付的用户</li>
                  <li><span class="label-mixed">支付多样化用户</span>：在不同订单中使用过不同支付方式的用户</li>
                </ul>
              </div>
            </template>
          </el-alert>
        </div>



        <!-- 数据有效性提示 -->
        <div v-if="paymentData.overview.totalOrders === 0" class="data-validity-warning">
          <el-alert
            title="暂无数据"
            type="warning"
            :closable="false"
            show-icon
          >
            <template #default>
              当前时间周期内暂无有效订单数据，请尝试切换到更长的时间周期或检查订单状态。
            </template>
          </el-alert>
        </div>

        <div v-else-if="paymentData.overview.totalOrders < 10" class="data-validity-info">
          <el-alert
            title="数据量较少"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              当前时间周期内订单数量较少（{{ paymentData.overview.totalOrders }}单），分析结果仅供参考。建议选择更长的时间周期以获得更准确的分析结果。
            </template>
          </el-alert>
        </div>

        <!-- 单一支付方式提示 -->
        <div v-else-if="isSinglePaymentMethod" class="single-payment-info">
          <el-alert
            title="支付方式单一"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              当前时间周期内所有订单都使用{{ dominantPaymentMethod }}支付。这可能反映了用户的支付偏好或系统的支付配置情况。
            </template>
          </el-alert>
        </div>

        <!-- 双饼图 -->
        <div v-if="paymentData.overview.totalOrders > 0" class="payment-charts">
          <div class="chart-container">
            <h4>订单数量占比</h4>
            <div v-if="isSinglePaymentMethod" class="single-payment-display">
              <div class="single-payment-card">
                <div class="payment-icon" :class="dominantPaymentMethod === '光年币' ? 'ly-icon' : 'rmb-icon'">
                  {{ dominantPaymentMethod === '光年币' ? '⭐' : '¥' }}
                </div>
                <div class="payment-info">
                  <div class="payment-name">{{ dominantPaymentMethod }}支付</div>
                  <div class="payment-count">{{ paymentData.overview.totalOrders }}单 (100%)</div>
                  <div class="payment-note">当前唯一支付方式</div>
                </div>
              </div>
            </div>
            <v-chart v-else class="chart" :option="orderPieOption" autoresize />
          </div>
          <div class="chart-container">
            <h4>用户分层分布</h4>
            <div v-if="isSinglePaymentMethod" class="single-payment-display">
              <div class="single-payment-card">
                <div class="payment-icon" :class="dominantPaymentMethod === '光年币' ? 'ly-icon' : 'rmb-icon'">
                  👥
                </div>
                <div class="payment-info">
                  <div class="payment-name">仅{{ dominantPaymentMethod }}用户</div>
                  <div class="payment-count">{{ getTotalUsers() }}人 (100%)</div>
                  <div class="payment-note">所有用户都使用相同支付方式</div>
                </div>
              </div>
            </div>
            <v-chart v-else class="chart" :option="userSegmentPieOption" autoresize />
          </div>
        </div>
      </div>

      <!-- 趋势模式 -->
      <div v-if="viewMode === 'trend'" class="payment-trend">
        <div v-if="isSinglePaymentMethod" class="single-payment-trend-info">
          <el-alert
            :title="`${dominantPaymentMethod}支付趋势`"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              当前时间周期内所有订单都使用{{ dominantPaymentMethod }}支付，无法进行支付方式对比分析。
              建议查看订单数量趋势或切换到更长的时间周期。
            </template>
          </el-alert>
        </div>
        <v-chart class="trend-chart" :option="paymentTrendOption" autoresize />
      </div>

      <!-- 分类模式 -->
      <div v-if="viewMode === 'category'" class="payment-category">
        <div class="category-header">
          <h4 class="category-title">商品分类支付偏好分析</h4>
          <div class="category-summary" v-if="paymentData.categoryPreference.length > 0">
            共 {{ paymentData.categoryPreference.length }} 个分类
          </div>
        </div>

        <div class="table-wrapper">
          <el-table
            :data="paymentData.categoryPreference"
            size="default"
            :max-height="calculateTableHeight()"
            stripe
            border
            :header-cell-style="{
              background: '#f8f9fa',
              color: '#303133',
              fontWeight: '600',
              fontSize: '14px',
              padding: '12px 8px'
            }"
            :cell-style="{ padding: '16px 8px' }"
            :row-style="{ minHeight: '60px' }"
            empty-text="暂无分类数据"
            class="category-table"
            style="width: 100%"
          >
            <el-table-column
              prop="categoryName"
              label="商品分类"
              min-width="140"
              show-overflow-tooltip
              sortable
            >
              <template #default="scope">
                <div class="category-name">
                  <el-icon class="category-icon"><Box /></el-icon>
                  <span class="category-text">{{ scope.row.categoryName }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column
              label="总订单"
              width="100"
              align="center"
              sortable
              :sort-method="(a, b) => a.totalOrders - b.totalOrders"
            >
              <template #default="scope">
                <div class="order-count">
                  <el-tag size="default" type="info" effect="light">
                    {{ scope.row.totalOrders }}单
                  </el-tag>
                </div>
              </template>
            </el-table-column>

            <el-table-column
              label="人民币支付"
              min-width="280"
              sortable
              :sort-method="(a, b) => a.rmbPercentage - b.rmbPercentage"
            >
              <template #default="scope">
                <div class="progress-container rmb-progress">
                  <div class="progress-wrapper">
                    <el-progress
                      :percentage="scope.row.rmbPercentage"
                      :color="rmbProgressColors"
                      :show-text="false"
                      :stroke-width="16"
                      striped
                      striped-flow
                    />
                  </div>
                  <div class="progress-info">
                    <span class="percentage-text">{{ scope.row.rmbPercentage }}%</span>
                    <span class="order-text">{{ Math.round(scope.row.totalOrders * scope.row.rmbPercentage / 100) }}单</span>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column
              label="光年币支付"
              min-width="280"
              sortable
              :sort-method="(a, b) => a.lyPercentage - b.lyPercentage"
            >
              <template #default="scope">
                <div class="progress-container ly-progress">
                  <div class="progress-wrapper">
                    <el-progress
                      :percentage="scope.row.lyPercentage"
                      :color="lyProgressColors"
                      :show-text="false"
                      :stroke-width="16"
                      striped
                      striped-flow
                    />
                  </div>
                  <div class="progress-info">
                    <span class="percentage-text">{{ scope.row.lyPercentage }}%</span>
                    <span class="order-text">{{ Math.round(scope.row.totalOrders * scope.row.lyPercentage / 100) }}单</span>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div v-if="paymentData.categoryPreference.length === 0" class="empty-data">
          <el-empty description="暂无分类数据" :image-size="80">
            <template #description>
              <div class="empty-description">
                <p>当前时间周期内暂无商品分类的支付偏好数据</p>
                <p v-if="isSinglePaymentMethod">所有订单都使用{{ dominantPaymentMethod }}支付</p>
                <p>请尝试切换到更长的时间周期</p>
              </div>
            </template>
          </el-empty>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-if="hasError" class="error-state">
        <el-empty description="数据加载失败" :image-size="80">
          <el-button type="primary" @click="refreshData">重新加载</el-button>
        </el-empty>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Box } from '@element-plus/icons-vue';
import * as ElementPlusIcons from '@element-plus/icons-vue';
import VChart from 'vue-echarts';
import { getPaymentPreference } from '../api/dashboard';

// 响应式数据
const isLoading = ref(false);
const hasError = ref(false);
const viewMode = ref('overview');
const selectedPeriod = ref('month');

const paymentData = reactive({
  overview: {
    totalOrders: 0,
    rmbOrders: 0,
    lyOrders: 0,
    rmbPercentage: 0,
    lyPercentage: 0,
    rmbAmount: 0,
    lyAmount: 0
  },
  paymentTrend: [],
  categoryPreference: [],
  userSegments: {
    rmbOnly: 0,
    lyOnly: 0,
    mixed: 0,
    preferences: {
      highValueRmb: 0,
      frequentLy: 0
    }
  }
});

// 计算属性：检测是否为单一支付方式
const isSinglePaymentMethod = computed(() => {
  return (paymentData.overview.rmbOrders === 0 && paymentData.overview.lyOrders > 0) ||
         (paymentData.overview.lyOrders === 0 && paymentData.overview.rmbOrders > 0);
});

// 计算属性：获取主导支付方式
const dominantPaymentMethod = computed(() => {
  if (paymentData.overview.rmbOrders === 0 && paymentData.overview.lyOrders > 0) {
    return '光年币';
  } else if (paymentData.overview.lyOrders === 0 && paymentData.overview.rmbOrders > 0) {
    return '人民币';
  }
  return '';
});

// 计算表格高度
const calculateTableHeight = () => {
  const dataLength = paymentData.categoryPreference.length;
  if (dataLength === 0) return 300;

  // 基础高度：表头(50px) + 边距(20px)
  const baseHeight = 70;
  // 每行高度：60px
  const rowHeight = 60;
  // 最小高度：300px，最大高度：600px
  const calculatedHeight = baseHeight + (dataLength * rowHeight);

  return Math.min(Math.max(calculatedHeight, 300), 600);
};

// 人民币进度条颜色配置
const rmbProgressColors = computed(() => [
  { color: '#ff9a9e', percentage: 20 },
  { color: '#ff7875', percentage: 40 },
  { color: '#f56c6c', percentage: 60 },
  { color: '#e55a5a', percentage: 80 },
  { color: '#d4444c', percentage: 100 }
]);

// 光年币进度条颜色配置
const lyProgressColors = computed(() => [
  { color: '#fff566', percentage: 20 },
  { color: '#ffd93d', percentage: 40 },
  { color: '#e6a23c', percentage: 60 },
  { color: '#d4941f', percentage: 80 },
  { color: '#b8860b', percentage: 100 }
]);

// 订单数量饼图配置
const orderPieOption = computed(() => {
  const totalOrders = paymentData.overview.totalOrders;

  return {
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        const percentage = totalOrders > 0 ? ((params.value / totalOrders) * 100).toFixed(1) : 0;
        return `${params.name}: ${params.value}单 (${percentage}%)`;
      }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      formatter: function(name) {
        const data = {
          '人民币支付': paymentData.overview.rmbOrders,
          '光年币支付': paymentData.overview.lyOrders
        };
        return `${name} (${data[name] || 0}单)`;
      }
    },
    series: [
      {
        name: '支付方式',
        type: 'pie',
        radius: '50%',
        data: [
          {
            value: paymentData.overview.rmbOrders,
            name: '人民币支付',
            itemStyle: { color: '#f56c6c' }
          },
          {
            value: paymentData.overview.lyOrders,
            name: '光年币支付',
            itemStyle: { color: '#e6a23c' }
          }
        ].filter(item => item.value > 0), // 过滤掉值为0的数据
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
});

// 用户分层饼图配置
const userSegmentPieOption = computed(() => {
  const totalUsers = paymentData.userSegments.rmbOnly + paymentData.userSegments.lyOnly + paymentData.userSegments.mixed;

  return {
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        const percentage = totalUsers > 0 ? ((params.value / totalUsers) * 100).toFixed(1) : 0;
        return `${params.name}: ${params.value}人 (${percentage}%)`;
      }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      formatter: function(name) {
        const data = {
          '仅人民币用户': paymentData.userSegments.rmbOnly,
          '仅光年币用户': paymentData.userSegments.lyOnly,
          '支付多样化用户': paymentData.userSegments.mixed
        };
        return `${name} (${data[name] || 0}人)`;
      }
    },
    series: [
      {
        name: '用户分层',
        type: 'pie',
        radius: '50%',
        data: [
          {
            value: paymentData.userSegments.rmbOnly,
            name: '仅人民币用户',
            itemStyle: { color: '#f56c6c' }
          },
          {
            value: paymentData.userSegments.lyOnly,
            name: '仅光年币用户',
            itemStyle: { color: '#e6a23c' }
          },
          {
            value: paymentData.userSegments.mixed,
            name: '支付多样化用户',
            itemStyle: { color: '#409eff' }
          }
        ].filter(item => item.value > 0), // 过滤掉值为0的数据
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
});

// 支付趋势图配置
const paymentTrendOption = computed(() => ({
  title: {
    text: '支付方式趋势分析',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  legend: {
    data: ['人民币订单', '光年币订单'],
    top: 30
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: paymentData.paymentTrend.map(item => item.date)
  },
  yAxis: {
    type: 'value',
    name: '订单数量'
  },
  series: [
    {
      name: '人民币订单',
      type: 'line',
      smooth: true,
      data: paymentData.paymentTrend.map(item => item.rmbCount),
      itemStyle: { color: '#f56c6c' },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(245, 108, 108, 0.3)' },
            { offset: 1, color: 'rgba(245, 108, 108, 0.1)' }
          ]
        }
      }
    },
    {
      name: '光年币订单',
      type: 'line',
      smooth: true,
      data: paymentData.paymentTrend.map(item => item.lyCount),
      itemStyle: { color: '#e6a23c' },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(230, 162, 60, 0.3)' },
            { offset: 1, color: 'rgba(230, 162, 60, 0.1)' }
          ]
        }
      }
    }
  ]
}));

// 获取支付偏好数据
const fetchPaymentPreference = async () => {
  try {
    isLoading.value = true;
    hasError.value = false;

    console.log('获取支付偏好数据，周期:', selectedPeriod.value);

    const data = await getPaymentPreference({
      period: selectedPeriod.value,
      groupBy: 'time'
    });

    // 更新数据
    Object.assign(paymentData.overview, data.overview);
    paymentData.paymentTrend = data.paymentTrend || [];
    paymentData.categoryPreference = data.categoryPreference || [];
    Object.assign(paymentData.userSegments, data.userSegments);

    console.log('支付偏好数据加载成功');
  } catch (error) {
    console.error('获取支付偏好数据失败:', error);
    hasError.value = true;
    ElMessage.error('获取支付偏好数据失败');
  } finally {
    isLoading.value = false;
  }
};

// 刷新数据
const refreshData = () => {
  fetchPaymentPreference();
};

// 处理视图模式变化
const handleViewModeChange = (mode) => {
  console.log('切换视图模式:', mode);
};

// 处理周期变化
const handlePeriodChange = (period) => {
  console.log('切换时间周期:', period);
  fetchPaymentPreference();
};

// 获取用户总数
const getTotalUsers = () => {
  return paymentData.userSegments.rmbOnly + paymentData.userSegments.lyOnly + paymentData.userSegments.mixed;
};

// 监听周期变化
watch(selectedPeriod, () => {
  fetchPaymentPreference();
});

// 组件挂载时加载数据
onMounted(() => {
  fetchPaymentPreference();
});
</script>

<style scoped>
.payment-preference-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.card-content {
  min-height: 300px;
}

.payment-overview {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.payment-stats {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  background: #f8f9fa;
  min-width: 200px;
}

.stat-item.rmb {
  border-left: 4px solid #f56c6c;
}

.stat-item.ly {
  border-left: 4px solid #e6a23c;
}

.stat-icon {
  font-size: 32px;
  font-weight: bold;
  margin-right: 15px;
  color: #666;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 2px;
}

.stat-detail {
  font-size: 12px;
  color: #909399;
}

.payment-charts {
  display: flex;
  gap: 20px;
  justify-content: space-around;
}

.chart-container {
  flex: 1;
  text-align: center;
}

.chart-container h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #606266;
}

.chart {
  width: 100%;
  height: 250px;
}

.trend-chart {
  width: 100%;
  height: 400px;
}

/* 分类模式样式 */
.payment-category {
  padding: 20px 0;
  min-height: 400px;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 4px;
}

.category-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  position: relative;
  padding-left: 12px;
}

.category-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #409eff, #66b3ff);
  border-radius: 2px;
}

.category-summary {
  font-size: 14px;
  color: #909399;
  background: #f0f9ff;
  padding: 6px 12px;
  border-radius: 12px;
  border: 1px solid #e1f5fe;
  font-weight: 500;
}

.table-wrapper {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #ebeef5;
}

.category-table {
  border-radius: 12px;
  width: 100%;
}

.category-table .el-table__header {
  background: #f8f9fa;
}

.category-table .el-table__row:hover {
  background-color: #f8f9fa;
}

.category-table .el-table__body-wrapper {
  overflow-x: auto;
}

.category-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.category-icon {
  color: #409eff;
  font-size: 16px;
  flex-shrink: 0;
}

.category-text {
  color: #303133;
  font-size: 14px;
}

.order-count {
  display: flex;
  justify-content: center;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 0;
  min-width: 240px;
}

.progress-wrapper {
  flex: 1;
  min-width: 160px;
}

.progress-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  min-width: 70px;
  flex-shrink: 0;
}

.percentage-text {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
  margin-bottom: 2px;
}

.order-text {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
}

.rmb-progress .percentage-text {
  color: #f56c6c;
}

.ly-progress .percentage-text {
  color: #e6a23c;
}

/* 进度条动画效果 */
.progress-container .el-progress {
  transition: all 0.3s ease;
}

.progress-container:hover .el-progress {
  transform: scale(1.02);
}

.empty-data {
  text-align: center;
  padding: 40px 0;
}

.error-state {
  text-align: center;
  padding: 40px 0;
}

.data-explanation {
  margin: 20px 0;
}

.explanation-content {
  font-size: 14px;
  line-height: 1.6;
}

.explanation-content p {
  margin: 8px 0;
}

.explanation-content ul {
  margin: 8px 0;
  padding-left: 20px;
}

.explanation-content li {
  margin: 4px 0;
}

.label-rmb {
  color: #f56c6c;
  font-weight: 600;
}

.label-ly {
  color: #e6a23c;
  font-weight: 600;
}

.label-mixed {
  color: #409eff;
  font-weight: 600;
}

.data-validity-warning,
.data-validity-info,
.single-payment-info {
  margin: 20px 0;
}

.single-payment-display {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 250px;
}

.single-payment-card {
  display: flex;
  align-items: center;
  padding: 30px;
  border-radius: 12px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid #dee2e6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.single-payment-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.payment-icon {
  font-size: 48px;
  margin-right: 20px;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-weight: bold;
}

.payment-icon.rmb-icon {
  background: linear-gradient(135deg, #f56c6c, #ff8a80);
  color: white;
}

.payment-icon.ly-icon {
  background: linear-gradient(135deg, #e6a23c, #ffb74d);
  color: white;
}

.payment-info {
  text-align: left;
}

.payment-name {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.payment-count {
  font-size: 16px;
  color: #606266;
  margin-bottom: 4px;
}

.payment-note {
  font-size: 12px;
  color: #909399;
  font-style: italic;
}

.single-payment-trend-info {
  margin-bottom: 20px;
}

.empty-description {
  color: #909399;
  font-size: 14px;
  line-height: 1.6;
}

.empty-description p {
  margin: 4px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: stretch;
  }

  .card-controls {
    justify-content: center;
  }

  .payment-stats {
    flex-direction: column;
  }

  .payment-charts {
    flex-direction: column;
  }

  .chart {
    height: 200px;
  }

  .trend-chart {
    height: 300px;
  }

  /* 分类模式响应式 */
  .category-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 20px;
  }

  .category-title {
    font-size: 16px;
  }

  .category-summary {
    font-size: 12px;
    padding: 4px 8px;
  }

  .table-wrapper {
    overflow-x: auto;
    border-radius: 8px;
  }

  .category-table {
    min-width: 700px;
  }

  .progress-container {
    gap: 12px;
    min-width: 200px;
  }

  .progress-wrapper {
    min-width: 120px;
  }

  .progress-info {
    min-width: 60px;
  }

  .percentage-text {
    font-size: 13px;
  }

  .order-text {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .category-title {
    font-size: 14px;
  }

  .category-summary {
    font-size: 11px;
    padding: 3px 6px;
  }

  .category-table {
    min-width: 600px;
  }

  .progress-container {
    gap: 8px;
    min-width: 180px;
  }

  .progress-wrapper {
    min-width: 100px;
  }

  .progress-info {
    min-width: 50px;
  }

  .percentage-text {
    font-size: 12px;
  }

  .order-text {
    font-size: 10px;
  }

  .category-name {
    gap: 6px;
  }

  .category-icon {
    font-size: 14px;
  }

  .category-text {
    font-size: 13px;
  }
}
</style>
