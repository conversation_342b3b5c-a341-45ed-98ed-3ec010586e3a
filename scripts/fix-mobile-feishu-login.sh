#!/bin/bash

# 手机端飞书登录修复部署脚本
# 解决手机端飞书登录ERR_CONNECTION_REFUSED问题

set -e  # 遇到错误立即退出

# 设置颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

echo -e "${YELLOW}===== 手机端飞书登录修复部署 =====${NC}"
echo -e "${BLUE}修复目标: 解决手机端ERR_CONNECTION_REFUSED错误${NC}"
echo -e "${BLUE}修复方案: 统一使用80端口，优化移动端重定向逻辑${NC}"

# 检查当前目录
if [ ! -f "package.json" ]; then
    echo -e "${RED}错误: 请在项目根目录运行此脚本${NC}"
    exit 1
fi

# 显示修复内容
echo -e "\n${BLUE}=== 修复内容概览 ===${NC}"
echo -e "1. ✅ 修改飞书回调URL: 3000端口 → 80端口"
echo -e "2. ✅ 优化后端移动端检测和重定向逻辑"
echo -e "3. ✅ 更新前端回调页面支持移动端直接重定向"
echo -e "4. ✅ 增强CORS配置支持移动端访问"
echo -e "5. 🔄 部署到生产环境"

# 确认部署
read -p "是否继续部署修复？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}部署已取消${NC}"
    exit 0
fi

echo -e "\n${BLUE}=== 开始部署修复 ===${NC}"

# 1. 验证配置文件
echo -e "${BLUE}1. 验证配置文件...${NC}"
if [ ! -f "server/.env.production" ]; then
    echo -e "${RED}错误: 生产环境配置文件不存在${NC}"
    exit 1
fi

# 检查飞书回调URL配置
if grep -q "FEISHU_REDIRECT_URI=http://**************/api/feishu/callback" server/.env.production; then
    echo -e "${GREEN}✅ 飞书回调URL配置正确${NC}"
else
    echo -e "${RED}❌ 飞书回调URL配置错误${NC}"
    exit 1
fi

# 2. 构建前端
echo -e "${BLUE}2. 构建前端项目...${NC}"
npm run build
if [ $? -ne 0 ]; then
    echo -e "${RED}前端构建失败${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 前端构建完成${NC}"

# 3. 部署到服务器
echo -e "${BLUE}3. 部署到生产服务器...${NC}"

# 服务器信息
SERVER_IP="**************"
SERVER_USER="root"
SERVER_PATH="/www/wwwroot/workyy"

echo -e "${BLUE}正在连接服务器 ${SERVER_IP}...${NC}"

# 上传文件到服务器
echo -e "${BLUE}上传修复文件...${NC}"
scp -r dist/ ${SERVER_USER}@${SERVER_IP}:${SERVER_PATH}/
scp server/.env.production ${SERVER_USER}@${SERVER_IP}:${SERVER_PATH}/server/.env
scp server/controllers/feishuController.js ${SERVER_USER}@${SERVER_IP}:${SERVER_PATH}/server/controllers/
scp src/views/FeishuCallback.vue ${SERVER_USER}@${SERVER_IP}:${SERVER_PATH}/src/views/

# 在服务器上执行部署命令
echo -e "${BLUE}在服务器上重启服务...${NC}"
ssh ${SERVER_USER}@${SERVER_IP} << 'ENDSSH'
    cd /www/wwwroot/workyy
    
    echo "=== 备份当前配置 ==="
    cp server/.env server/.env.backup.$(date +%Y%m%d_%H%M%S)
    
    echo "=== 应用新配置 ==="
    # 配置已通过scp上传
    
    echo "=== 重启PM2应用 ==="
    pm2 restart feishu-mall-api
    
    echo "=== 检查服务状态 ==="
    pm2 list | grep feishu-mall-api
    
    echo "=== 检查服务健康状态 ==="
    sleep 3
    curl -s http://localhost:3000/api/health || echo "健康检查失败"
ENDSSH

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 部署成功完成${NC}"
else
    echo -e "${RED}❌ 部署过程中出现错误${NC}"
    exit 1
fi

# 4. 验证修复效果
echo -e "\n${BLUE}=== 修复验证 ===${NC}"
echo -e "${BLUE}请在手机端测试以下流程:${NC}"
echo -e "1. 打开浏览器访问: http://**************"
echo -e "2. 点击登录按钮"
echo -e "3. 选择飞书登录"
echo -e "4. 完成飞书授权"
echo -e "5. 检查是否能正常跳转回首页"

echo -e "\n${GREEN}=== 修复部署完成 ===${NC}"
echo -e "${YELLOW}重要提醒:${NC}"
echo -e "1. 需要在飞书开放平台更新回调地址为: http://**************/api/feishu/callback"
echo -e "2. 如果仍有问题，请检查Nginx配置是否正确代理/api路径到3000端口"
echo -e "3. 可以通过 'pm2 logs feishu-mall-api' 查看详细日志"

echo -e "\n${BLUE}修复完成时间: $(date '+%Y-%m-%d %H:%M:%S')${NC}"
