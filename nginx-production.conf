# 光年小卖部 - 生产环境Nginx配置
# 服务器IP: **************
# 前端端口: 80 (通过nginx代理)
# 后端端口: 3000
# 部署目录: /www/wwwroot/workyy

server {
    listen 80;
    server_name **************;
    
    # 项目根目录
    root /www/wwwroot/workyy;
    index index.html index.htm;
    
    # 设置客户端请求体大小限制（用于文件上传）
    client_max_body_size 50M;
    
    # 设置超时时间
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    
    # 缓冲区设置
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;
    proxy_busy_buffers_size 8k;
    
    # Gzip压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml
        application/x-font-ttf
        application/vnd.ms-fontobject
        font/opentype;
    
    # 前端静态文件服务 - Vue构建后的dist目录
    location / {
        root /www/wwwroot/workyy/dist;
        try_files $uri $uri/ /index.html;
        
        # HTML文件不缓存，确保更新及时
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
        }
        
        # 静态资源长期缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
            
            # 添加CORS头部，解决跨域问题
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range' always;
        }
    }
    
    # API代理 - 代理到后端Node.js服务
    location /api/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_cache_bypass $http_upgrade;
        
        # CORS配置 - 解决跨域问题
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Origin' always;
        add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;
        
        # 处理OPTIONS预检请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH' always;
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Origin' always;
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }
    
    # 上传文件静态服务 - 关键配置，解决图片显示问题
    location /uploads/ {
        alias /www/wwwroot/workyy/server/uploads/;
        
        # 设置正确的MIME类型
        location ~* \.(jpg|jpeg|png|gif|webp|svg)$ {
            expires 30d;
            add_header Cache-Control "public, no-transform";
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS' always;
            
            # 确保图片文件存在，否则返回404
            try_files $uri =404;
        }
        
        # 其他上传文件类型
        location ~* \.(pdf|doc|docx|xls|xlsx|zip|rar)$ {
            expires 7d;
            add_header Cache-Control "public";
            add_header 'Access-Control-Allow-Origin' '*' always;
            
            # 强制下载某些文件类型
            add_header Content-Disposition "attachment";
        }
        
        # 禁止访问敏感文件
        location ~* \.(php|jsp|asp|sh|py|pl)$ {
            deny all;
        }
    }
    
    # 健康检查端点
    location /health {
        proxy_pass http://127.0.0.1:3000/api/health;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        access_log off;
    }
    
    # 禁止访问隐藏文件和敏感目录
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ /(\.git|\.svn|\.env|node_modules|server\.js|package\.json) {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 安全头部配置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http: https:; img-src 'self' data: blob: http: https:; font-src 'self' data: http: https:;" always;
    
    # 隐藏Nginx版本信息
    server_tokens off;
    
    # 日志配置
    access_log /www/wwwroot/workyy/logs/nginx_access.log combined;
    error_log /www/wwwroot/workyy/logs/nginx_error.log warn;
    
    # 错误页面配置
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        root /www/wwwroot/workyy/dist;
        internal;
    }
    
    location = /50x.html {
        root /www/wwwroot/workyy/dist;
        internal;
    }
}

# 可选：重定向www域名到非www（如果有域名的话）
# server {
#     listen 80;
#     server_name www.yourdomain.com;
#     return 301 http://yourdomain.com$request_uri;
# }

# 可选：HTTPS配置模板（当有SSL证书时启用）
# server {
#     listen 443 ssl http2;
#     server_name **************;
#     
#     ssl_certificate /path/to/your/certificate.crt;
#     ssl_certificate_key /path/to/your/private.key;
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     
#     # 其他配置与HTTP版本相同...
# }
