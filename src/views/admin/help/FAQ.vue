<template>
  <div class="help-doc faq">
    <h2>常见问题解答</h2>

    <div class="search-container">
      <el-input
        v-model="searchText"
        placeholder="搜索常见问题..."
        prefix-icon="Search"
        clearable
        @input="handleSearch"
      />
    </div>

    <el-card class="help-card" v-if="filteredQuestions.length > 0">
      <template #header>
        <div class="card-header">
          <h3>常见问题</h3>
        </div>
      </template>
      <div class="card-content">
        <el-collapse v-model="activeNames">
          <el-collapse-item
            v-for="(item, index) in filteredQuestions"
            :key="index"
            :title="item.title"
            :name="index.toString()"
          >
            <div v-html="item.answer"></div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>

    <el-empty v-else description="没有找到匹配的问题，请尝试其他关键词" />

    <el-divider content-position="center">
      <span class="divider-text">没有找到您的问题？</span>
    </el-divider>

    <div class="contact-container">
      <p>如果您的问题在常见问题中没有找到解答，您可以通过以下方式联系我们：</p>
      <el-row :gutter="20" class="contact-ways">
        <el-col :span="8">
          <div class="contact-item">
            <el-icon size="40" color="#409EFF"><ChatDotRound /></el-icon>
            <h4>在线客服</h4>
            <p>工作时间: 周一至周五 9:00-18:00</p>
            <el-button type="primary">在线咨询</el-button>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="contact-item">
            <el-icon size="40" color="#67C23A"><Message /></el-icon>
            <h4>电子邮件</h4>
            <p>我们会在24小时内回复您的邮件</p>
            <el-button type="success">发送邮件</el-button>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="contact-item">
            <el-icon size="40" color="#E6A23C"><Document /></el-icon>
            <h4>提交工单</h4>
            <p>详细描述您的问题，获得技术支持</p>
            <el-button type="warning">创建工单</el-button>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { Search, ChatDotRound, Message, Document } from '@element-plus/icons-vue';

// 常见问题数据
const questions = [
  {
    title: '如何使用飞书登录系统？',
    answer: '您可以通过以下步骤使用飞书登录：<ol><li>在登录页面点击"飞书登录"按钮</li><li>跳转到飞书授权页面</li><li>使用您的飞书账号登录并授权</li><li>系统会自动创建或更新您的账户信息</li><li>登录成功后即可使用系统功能</li></ol><strong>注意：</strong>您的邮箱必须以@guanghe.tv结尾才能成功注册。'
  },
  {
    title: '光年币和人民币支付有什么区别？',
    answer: '系统支持两种支付方式：<ul><li><strong>光年币支付</strong>：使用企业内部积分，兑换时自动扣除，无需额外操作</li><li><strong>人民币支付</strong>：需要先完成实际支付，然后上传支付凭证，管理员审核通过后发货</li></ul>建议优先使用光年币支付，流程更简单快捷。'
  },
  {
    title: '如何查看和处理订单？',
    answer: '处理订单的步骤如下：<ol><li>进入"订单管理"页面查看所有订单</li><li>使用筛选功能查找特定状态的订单</li><li>点击订单查看详细信息，包括用户信息、商品信息、支付凭证等</li><li>对于人民币订单，需要验证支付凭证</li><li>确认无误后，将订单状态更新为"处理中"</li><li>准备商品并发货，更新状态为"已发货"</li><li>用户确认收货后，订单自动完成</li></ol>'
  },
  {
    title: '如何添加新用户并设置权限？',
    answer: '添加新用户并设置权限的步骤：<ol><li>进入"用户管理"页面</li><li>点击"添加用户"按钮</li><li>填写用户基本信息（姓名、邮箱、手机等）</li><li>在"角色权限"区域，为用户分配适当的角色</li><li>如需更细粒度的权限控制，可点击"自定义权限"</li><li>设置完成后点击"保存"按钮</li></ol>'
  },
  {
    title: '系统支持哪些浏览器？',
    answer: '我们的系统支持以下现代浏览器的最新版本：<ul><li>Google Chrome 100+</li><li>Mozilla Firefox 95+</li><li>Microsoft Edge 100+</li><li>Safari 15+</li></ul>为获得最佳体验，我们建议使用Chrome或Firefox浏览器。'
  },
  {
    title: '如何设置系统通知？',
    answer: '设置系统通知的步骤：<ol><li>点击右上角用户头像，选择"系统设置"</li><li>切换到"通知设置"选项卡</li><li>您可以选择启用或禁用不同类型的通知</li><li>对于重要通知，您可以设置额外的提醒方式（如邮件、短信）</li><li>完成设置后点击"保存"按钮</li></ol>'
  },
  {
    title: '如何批量处理数据？',
    answer: '系统支持多种批量处理方式：<ol><li>在列表页面，使用左侧的复选框选择需要处理的项目</li><li>选择完成后，点击上方的批量操作按钮</li><li>根据不同数据类型，可用的批量操作包括：<ul><li>批量修改状态</li><li>批量删除</li><li>批量导出</li><li>批量分类/标记</li></ul></li><li>确认操作后，系统会处理所有选中的项目</li></ol>'
  },
  {
    title: '遇到系统错误怎么办？',
    answer: '如果您遇到系统错误，请尝试以下步骤：<ol><li>刷新页面，看问题是否仍然存在</li><li>清除浏览器缓存并重新登录</li><li>尝试使用不同的浏览器</li><li>记录错误发生的时间和操作步骤</li><li>联系系统管理员或技术支持，提供详细的错误信息</li></ol>大多数情况下，简单的刷新或重新登录可以解决临时性问题。'
  },
  {
    title: '数据安全如何保障？',
    answer: '我们采取多层次的措施保障数据安全：<ul><li><strong>数据传输安全</strong>：所有数据通过HTTPS加密传输</li><li><strong>数据存储安全</strong>：数据库采用强加密存储敏感信息</li><li><strong>访问控制</strong>：严格的权限管理和身份验证</li><li><strong>数据备份</strong>：定期自动备份，确保数据不会丢失</li><li><strong>安全审计</strong>：系统自动记录所有关键操作的日志</li></ul>'
  },
  {
    title: '如何使用数据分析功能？',
    answer: '使用数据分析功能的基本步骤：<ol><li>进入"数据仪表盘"页面</li><li>选择您感兴趣的数据类型（销售、用户、商品等）</li><li>设置日期范围和其他筛选条件</li><li>系统会自动生成数据图表和关键指标</li><li>您可以通过点击图表上的数据点查看更详细的信息</li><li>对于特定分析需求，可以使用"自定义报表"功能</li></ol>'
  },
  {
    title: '如何使用多语言功能？',
    answer: '使用系统多语言功能的步骤：<ol><li>点击页面右上角的语言切换图标</li><li>在下拉菜单中选择您需要的语言</li><li>系统界面将立即切换为所选语言</li><li>如果您是管理员，还可以在"系统设置"中配置默认语言</li><li>添加商品时，您可以为每种支持的语言输入商品名称和描述</li></ol>目前系统支持简体中文、英文、日文和韩文。'
  },
  {
    title: '如何在移动设备上获得最佳体验？',
    answer: '在移动设备上使用系统的建议：<ol><li>使用最新版本的移动浏览器（Chrome、Safari等）</li><li>保持设备系统更新到最新版本</li><li>使用设备的横屏模式可以获得更好的操作体验</li><li>对于复杂的数据分析和批量操作，建议使用平板电脑而非手机</li><li>可以将系统添加到设备主屏幕，获得类似App的使用体验</li></ol>系统会自动适应您的屏幕大小，提供最佳的移动端体验。'
  },
  {
    title: '如何配置飞书机器人推送？',
    answer: '配置飞书机器人推送的步骤：<ol><li>进入"系统设置"页面</li><li>在"通知配置"区域找到要配置的通知类型</li><li>使用开关控制是否启用该类型通知</li><li>设置推送时间（对于定时推送）</li><li>配置Webhook地址（如果使用自定义群聊）</li><li>点击保存应用配置</li></ol>系统支持每日报告、每周报告、新用户欢迎、里程碑庆祝等多种推送类型。'
  },
  {
    title: '为什么飞书群收不到推送消息？',
    answer: '如果飞书群收不到推送消息，请检查以下几点：<ol><li>确认通知配置中对应类型的推送已启用</li><li>检查Webhook地址是否正确配置</li><li>确认机器人已添加到目标飞书群</li><li>查看系统日志中是否有错误信息</li><li>使用"高级诊断工具"测试消息发送功能</li></ol>如果问题仍然存在，请联系系统管理员。'
  },
  {
    title: '如何导出订单和商品数据？',
    answer: '导出数据的步骤：<ol><li>进入对应的管理页面（订单管理或商品管理）</li><li>使用筛选条件选择要导出的数据范围</li><li>点击页面右上角的"导出"按钮</li><li>选择导出格式（Excel或CSV）</li><li>系统会生成文件并自动下载</li></ol>导出的数据包含所有相关字段，便于进一步分析和处理。'
  },
  {
    title: '系统设置页面出现空白怎么办？',
    answer: '如果系统设置页面出现空白，这通常是前端渲染问题，请尝试以下解决方案：<ol><li><strong>刷新页面</strong>：按F5或Ctrl+R刷新当前页面</li><li><strong>清除缓存</strong>：清除浏览器缓存后重新访问</li><li><strong>检查网络</strong>：确保网络连接稳定</li><li><strong>更换浏览器</strong>：尝试使用Chrome或Firefox浏览器</li><li><strong>重新登录</strong>：退出系统后重新登录</li></ol><strong>注意：</strong>系统已在v1.4.1版本中修复了此问题，如果仍然遇到，请联系技术支持。'
  },
  {
    title: '高级诊断工具如何使用？',
    answer: '高级诊断工具提供了系统健康检查功能：<ol><li>进入"系统设置" > "高级功能" > "高级诊断工具"</li><li>选择要执行的诊断项目：<ul><li><strong>系统健康检查</strong>：检测CPU、内存使用率</li><li><strong>Webhook连接测试</strong>：测试飞书连接状态</li><li><strong>消息发送分析</strong>：分析消息发送成功率</li></ul></li><li>点击对应的诊断按钮开始检查</li><li>查看诊断结果和建议</li><li>根据建议进行系统优化</li></ol>建议定期使用诊断工具检查系统状态。'
  },
  {
    title: '智能发送时间控制如何配置？',
    answer: '智能发送时间控制可以优化消息推送体验：<ol><li>进入"系统设置" > "高级功能" > "智能发送时间控制"</li><li>点击"新建调度"创建新的时间控制规则</li><li>选择调度类型：<ul><li><strong>固定时间</strong>：在指定时间发送</li><li><strong>智能调度</strong>：根据用户活跃时间自动选择</li><li><strong>条件触发</strong>：满足特定条件时发送</li></ul></li><li>配置具体的时间参数和条件</li><li>启用调度规则</li></ol>智能调度可以显著提高消息的阅读率和用户体验。'
  }
];

// 当前激活的折叠面板
const activeNames = ref(['0']);
// 搜索文本
const searchText = ref('');

// 根据搜索文本过滤问题
const filteredQuestions = computed(() => {
  if (!searchText.value) return questions;

  const search = searchText.value.toLowerCase();
  return questions.filter(q =>
    q.title.toLowerCase().includes(search) ||
    q.answer.toLowerCase().includes(search)
  );
});

// 处理搜索
const handleSearch = () => {
  // 如果只有一个结果，自动展开
  if (filteredQuestions.value.length === 1) {
    activeNames.value = ['0'];
  }
};
</script>

<style scoped>
.faq h2 {
  margin-bottom: 20px;
  font-weight: bold;
  color: #303133;
}

.search-container {
  margin-bottom: 20px;
}

.help-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.card-content {
  line-height: 1.6;
}

.divider-text {
  font-size: 14px;
  color: #909399;
}

.contact-container {
  margin-top: 20px;
  text-align: center;
}

.contact-container p {
  margin-bottom: 20px;
  color: #606266;
}

.contact-ways {
  margin-top: 20px;
}

.contact-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  height: 100%;
  background-color: #fafafa;
  transition: all 0.3s;
}

.contact-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.contact-item h4 {
  margin: 12px 0;
  color: #303133;
}

.contact-item p {
  margin-bottom: 15px;
  color: #606266;
  font-size: 14px;
}

.contact-item .el-button {
  width: 100%;
}
</style>
