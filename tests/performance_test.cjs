/**
 * 核心功能性能测试
 * 测试订单编号生成、商品调价、订单导出的性能表现
 */

const { sequelize } = require('../server/config/database');
const { Product, Exchange, ProductPriceHistory, User, Category } = require('../server/models');

// 测试配置
const TEST_CONFIG = {
  CONCURRENT_ORDERS: 10,      // 并发订单数量
  BATCH_PRODUCTS: 5,          // 批量商品数量
  EXPORT_ORDERS: 100          // 导出订单数量
};

// 测试数据
let testUser, testCategory, testProducts = [], testExchanges = [];

async function setupPerformanceTestData() {
  console.log('=== 设置性能测试数据 ===');

  // 创建测试用户
  testUser = await User.create({
    username: 'perf_test_user',
    email: '<EMAIL>',
    password: 'test123',
    role: 'admin',
    points: 10000
  });

  // 创建测试分类
  testCategory = await Category.create({
    name: '性能测试分类',
    description: '用于性能测试的分类'
  });

  // 创建多个测试商品
  for (let i = 1; i <= TEST_CONFIG.BATCH_PRODUCTS; i++) {
    const product = await Product.create({
      name: `性能测试商品_${i}`,
      categoryId: testCategory.id,
      lyPrice: 100 + i * 10,
      rmbPrice: 25.00 + i * 5,
      description: `用于性能测试的商品 ${i}`,
      stock: 100,
      status: 'active'
    });
    testProducts.push(product);
  }

  console.log(`性能测试数据创建完成: 用户ID=${testUser.id}, 商品数量=${testProducts.length}`);
}

async function testOrderNumberGenerationPerformance() {
  console.log('\n=== 测试订单编号生成性能 ===');

  const startTime = Date.now();
  const promises = [];

  // 并发创建订单测试
  for (let i = 0; i < TEST_CONFIG.CONCURRENT_ORDERS; i++) {
    const productIndex = i % testProducts.length;
    const paymentMethod = i % 2 === 0 ? 'ly' : 'rmb';
    const product = testProducts[productIndex];
    
    const promise = Exchange.create({
      userId: testUser.id,
      productId: product.id,
      quantity: 1,
      unitPrice: paymentMethod === 'ly' ? product.lyPrice : parseFloat(product.rmbPrice),
      priceType: paymentMethod,
      paymentMethod: paymentMethod,
      contactInfo: `性能测试联系方式_${i}`,
      location: `性能测试地址_${i}`,
      status: 'pending'
    });
    
    promises.push(promise);
  }

  try {
    const exchanges = await Promise.all(promises);
    testExchanges = exchanges;
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`✅ 并发创建 ${TEST_CONFIG.CONCURRENT_ORDERS} 个订单完成`);
    console.log(`⏱️  总耗时: ${duration}ms`);
    console.log(`📊 平均每个订单: ${(duration / TEST_CONFIG.CONCURRENT_ORDERS).toFixed(2)}ms`);
    
    // 验证订单编号唯一性
    const orderNumbers = exchanges.map(e => e.orderNumber).filter(Boolean);
    const uniqueNumbers = new Set(orderNumbers);
    
    if (orderNumbers.length === uniqueNumbers.size) {
      console.log('✅ 所有订单编号唯一');
    } else {
      console.log('❌ 发现重复订单编号');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 并发订单创建失败:', error.message);
    return false;
  }
}

async function testBatchPriceUpdatePerformance() {
  console.log('\n=== 测试批量价格调整性能 ===');

  const startTime = Date.now();

  try {
    // 模拟批量价格更新
    const productIds = testProducts.map(p => p.id);
    const priceUpdates = {
      lyPrice: 200,
      rmbPrice: 50.00
    };

    // 开启事务
    const transaction = await sequelize.transaction();

    try {
      let successCount = 0;
      let totalAffectedOrders = 0;

      for (const product of testProducts) {
        // 分析价格变更影响
        const affectedOrders = await Exchange.count({
          where: {
            productId: product.id,
            status: ['pending', 'approved', 'shipped']
          },
          transaction
        });

        totalAffectedOrders += affectedOrders;

        // 更新商品价格
        await product.update(priceUpdates, {
          userId: testUser.id,
          changeReason: '性能测试批量价格更新',
          transaction
        });

        successCount++;
      }

      await transaction.commit();

      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log(`✅ 批量更新 ${successCount} 个商品价格完成`);
      console.log(`📊 影响订单数量: ${totalAffectedOrders}`);
      console.log(`⏱️  总耗时: ${duration}ms`);
      console.log(`📊 平均每个商品: ${(duration / successCount).toFixed(2)}ms`);

      return true;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('❌ 批量价格更新失败:', error.message);
    return false;
  }
}

async function testExportPerformance() {
  console.log('\n=== 测试订单导出性能 ===');

  // 创建更多测试订单以测试导出性能
  console.log(`创建 ${TEST_CONFIG.EXPORT_ORDERS} 个额外订单用于导出测试...`);
  
  const startCreateTime = Date.now();
  const createPromises = [];

  for (let i = 0; i < TEST_CONFIG.EXPORT_ORDERS; i++) {
    const productIndex = i % testProducts.length;
    const paymentMethod = i % 2 === 0 ? 'ly' : 'rmb';
    const product = testProducts[productIndex];
    
    const promise = Exchange.create({
      userId: testUser.id,
      productId: product.id,
      quantity: 1,
      unitPrice: paymentMethod === 'ly' ? product.lyPrice : parseFloat(product.rmbPrice),
      priceType: paymentMethod,
      paymentMethod: paymentMethod,
      contactInfo: `导出测试联系方式_${i}`,
      location: `导出测试地址_${i}`,
      status: 'pending'
    });
    
    createPromises.push(promise);
  }

  const additionalExchanges = await Promise.all(createPromises);
  testExchanges = testExchanges.concat(additionalExchanges);
  
  const createDuration = Date.now() - startCreateTime;
  console.log(`订单创建完成，耗时: ${createDuration}ms`);

  // 测试查询性能
  const startQueryTime = Date.now();
  
  const totalCount = await Exchange.count({
    where: { userId: testUser.id }
  });

  const queryDuration = Date.now() - startQueryTime;
  console.log(`✅ 查询 ${totalCount} 条订单，耗时: ${queryDuration}ms`);

  // 测试分批查询性能
  const startBatchTime = Date.now();
  const batchSize = 50;
  let offset = 0;
  let processedCount = 0;

  while (offset < totalCount) {
    const exchanges = await Exchange.findAll({
      where: { userId: testUser.id },
      include: [
        {
          model: Product,
          attributes: ['id', 'name', 'lyPrice', 'rmbPrice'],
          required: false
        },
        {
          model: User,
          attributes: ['id', 'username', 'email', 'department'],
          required: false
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: batchSize,
      offset: offset
    });

    processedCount += exchanges.length;
    offset += batchSize;
  }

  const batchDuration = Date.now() - startBatchTime;
  console.log(`✅ 分批查询 ${processedCount} 条订单完成，耗时: ${batchDuration}ms`);
  console.log(`📊 平均每批(${batchSize}条): ${(batchDuration / Math.ceil(totalCount / batchSize)).toFixed(2)}ms`);

  return true;
}

async function cleanupPerformanceTestData() {
  console.log('\n=== 清理性能测试数据 ===');

  try {
    // 删除测试订单
    await Exchange.destroy({
      where: { userId: testUser.id }
    });

    // 删除价格历史
    const productIds = testProducts.map(p => p.id);
    await ProductPriceHistory.destroy({
      where: { productId: productIds }
    });

    // 删除测试商品
    await Product.destroy({
      where: { id: productIds }
    });

    // 删除测试分类
    await testCategory.destroy();

    // 删除测试用户
    await testUser.destroy();

    console.log('✅ 性能测试数据清理完成');

  } catch (error) {
    console.error('清理性能测试数据失败:', error);
  }
}

async function runPerformanceTests() {
  try {
    console.log('=== 商品管理系统核心功能性能测试 ===\n');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功\n');

    // 设置测试数据
    await setupPerformanceTestData();

    // 运行性能测试
    const orderNumberTest = await testOrderNumberGenerationPerformance();
    const priceUpdateTest = await testBatchPriceUpdatePerformance();
    const exportTest = await testExportPerformance();

    // 清理测试数据
    await cleanupPerformanceTestData();

    // 汇总结果
    const allTestsPassed = orderNumberTest && priceUpdateTest && exportTest;

    if (allTestsPassed) {
      console.log('\n🎉 所有性能测试通过！');
      console.log('✅ 订单编号生成性能良好');
      console.log('✅ 批量价格调整性能良好');
      console.log('✅ 订单导出性能良好');
      process.exit(0);
    } else {
      console.log('\n❌ 部分性能测试失败！');
      console.log(`订单编号生成: ${orderNumberTest ? '✅' : '❌'}`);
      console.log(`批量价格调整: ${priceUpdateTest ? '✅' : '❌'}`);
      console.log(`订单导出: ${exportTest ? '✅' : '❌'}`);
      process.exit(1);
    }

  } catch (error) {
    console.error('\n❌ 性能测试运行失败:', error);
    await cleanupPerformanceTestData();
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runPerformanceTests();
}

module.exports = {
  setupPerformanceTestData,
  testOrderNumberGenerationPerformance,
  testBatchPriceUpdatePerformance,
  testExportPerformance,
  cleanupPerformanceTestData,
  runPerformanceTests
};
