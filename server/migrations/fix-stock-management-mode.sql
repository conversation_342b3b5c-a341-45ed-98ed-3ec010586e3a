-- 修正库存管理模式判断逻辑
-- 基于职场数量来正确设置库存管理模式
-- 执行时间：2025-07-30

-- 1. 检查当前职场数量
SELECT 
    '当前职场数量检查' as check_type,
    COUNT(*) as workplace_count,
    CASE 
        WHEN COUNT(*) <= 1 THEN '应使用单一库存模式'
        ELSE '应使用职场分配模式'
    END as recommended_mode
FROM workplaces;

-- 2. 显示当前商品的库存管理模式分布
SELECT 
    '当前模式分布' as check_type,
    stockManagementType,
    COUNT(*) as product_count
FROM products 
GROUP BY stockManagementType;

-- 3. 由于系统只有1个职场，将所有商品改为单一库存模式
UPDATE products 
SET stockManagementType = 'single',
    stockSyncedAt = NULL
WHERE stockManagementType = 'workplace';

-- 4. 验证更新结果
SELECT 
    '更新后模式分布' as check_type,
    stockManagementType,
    COUNT(*) as product_count
FROM products 
GROUP BY stockManagementType;

-- 5. 检查是否有商品的总库存与职场库存不一致（单一库存模式下应该一致）
SELECT 
    '库存一致性检查' as check_type,
    p.id,
    p.name,
    p.stock as product_stock,
    COALESCE(pws.stock, 0) as workplace_stock,
    CASE 
        WHEN p.stock = COALESCE(pws.stock, 0) THEN 'OK'
        ELSE 'NEED_SYNC'
    END as sync_status
FROM products p
LEFT JOIN product_workplace_stocks pws ON p.id = pws.productId AND pws.workplaceId = 1
WHERE p.stock != COALESCE(pws.stock, 0)
ORDER BY p.id;

-- 6. 同步总库存到职场库存（确保北京职场的库存与商品总库存一致）
UPDATE product_workplace_stocks pws
JOIN products p ON pws.productId = p.id
SET pws.stock = p.stock,
    pws.lastStockUpdate = NOW()
WHERE pws.workplaceId = 1;

-- 7. 为没有职场库存记录的商品创建记录
INSERT INTO product_workplace_stocks (productId, workplaceId, stock, reservedStock, minStockAlert)
SELECT
    p.id,
    1 as workplaceId,
    p.stock,
    0 as reservedStock,
    5 as minStockAlert
FROM products p
LEFT JOIN product_workplace_stocks pws ON p.id = pws.productId AND pws.workplaceId = 1
WHERE pws.id IS NULL;

-- 8. 最终验证：确保所有商品的总库存与北京职场库存一致
SELECT 
    '最终验证' as check_type,
    p.id,
    p.name,
    p.stock as product_stock,
    p.stockManagementType,
    pws.stock as beijing_stock,
    CASE 
        WHEN p.stock = pws.stock THEN 'OK'
        ELSE 'ERROR'
    END as final_status
FROM products p
LEFT JOIN product_workplace_stocks pws ON p.id = pws.productId AND pws.workplaceId = 1
WHERE p.stock != COALESCE(pws.stock, 0)
ORDER BY p.id;

-- 9. 统计最终结果
SELECT 
    '修复完成统计' as summary_type,
    COUNT(*) as total_products,
    SUM(CASE WHEN stockManagementType = 'single' THEN 1 ELSE 0 END) as single_mode_count,
    SUM(CASE WHEN stockManagementType = 'workplace' THEN 1 ELSE 0 END) as workplace_mode_count,
    SUM(stock) as total_stock
FROM products;
