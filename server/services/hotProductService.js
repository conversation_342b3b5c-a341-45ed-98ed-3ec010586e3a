const { Op } = require('sequelize');
const { Product, Exchange, HotProductConfig, HotProductHistory, sequelize } = require('../models');

/**
 * 热门商品计算服务
 */
class HotProductService {
  constructor() {
    this.isUpdating = false; // 防止并发更新
  }

  /**
   * 计算商品在指定时间范围内的热门度评分
   * @param {number} productId - 商品ID
   * @param {string} timeRange - 时间范围 (all/30d/7d/1d)
   * @param {Object} config - 配置对象
   * @returns {Promise<Object>} 包含评分和兑换量的对象
   */
  async calculateHotScore(productId, timeRange, config) {
    try {
      // 获取商品信息
      const product = await Product.findByPk(productId);
      if (!product || product.status !== 'active') {
        return { hotScore: 0, exchangeCount: 0 };
      }

      let exchangeCount = 0;

      if (timeRange === 'all') {
        // 使用商品表中的累积兑换量
        exchangeCount = product.exchangeCount || 0;
      } else {
        // 计算指定时间范围内的兑换量
        const timeCondition = this.getTimeRangeCondition(timeRange);
        if (timeCondition) {
          const result = await Exchange.sum('quantity', {
            where: {
              productId,
              status: ['approved', 'shipped', 'completed'],
              ...timeCondition
            }
          });
          exchangeCount = result || 0;
        }
      }

      // 检查是否满足最小兑换量要求
      if (exchangeCount < config.minExchangeCount) {
        return { hotScore: 0, exchangeCount };
      }

      // 计算库存奖励分
      const stockBonus = product.stock > 0 ? 10 : 0;

      // 计算热门度评分
      const hotScore = (exchangeCount * config.exchangeWeight) + (stockBonus * config.stockWeight);

      return { hotScore: parseFloat(hotScore.toFixed(2)), exchangeCount };
    } catch (error) {
      console.error(`计算商品 ${productId} 热门度评分失败:`, error);
      return { hotScore: 0, exchangeCount: 0 };
    }
  }

  /**
   * 更新指定时间维度的热门商品
   * @param {string} timeRange - 时间范围
   * @returns {Promise<Object>} 更新结果
   */
  async updateHotProducts(timeRange) {
    try {
      console.log(`🔥 开始更新 ${timeRange} 时间维度的热门商品...`);

      // 获取配置
      const config = await HotProductConfig.findOne({ where: { timeRange } });
      if (!config || !config.enabled) {
        console.log(`⏭️ ${timeRange} 时间维度未启用，跳过更新`);
        return { success: false, message: '时间维度未启用' };
      }

      // 获取所有活跃商品
      const products = await Product.findAll({
        where: { status: 'active' },
        attributes: ['id', 'name', 'exchangeCount', 'stock']
      });

      if (products.length === 0) {
        console.log(`⚠️ 没有找到活跃商品`);
        return { success: false, message: '没有活跃商品' };
      }

      // 计算所有商品的热门度评分
      const productScores = [];
      for (const product of products) {
        const scoreData = await this.calculateHotScore(product.id, timeRange, config);
        if (scoreData.hotScore > 0) {
          productScores.push({
            productId: product.id,
            productName: product.name,
            hotScore: scoreData.hotScore,
            exchangeCount: scoreData.exchangeCount
          });
        }
      }

      // 按热门度评分排序
      productScores.sort((a, b) => b.hotScore - a.hotScore);

      // 取前N名作为热门商品
      const hotProducts = productScores.slice(0, config.maxCount);

      // 开启事务更新
      const transaction = await sequelize.transaction();
      try {
        // 清除该时间维度的旧热门标记
        await Product.update(
          {
            isAutoHot: false,
            hotTimeRange: null,
            hotScore: 0,
            hotRank: null
          },
          {
            where: { hotTimeRange: timeRange },
            transaction
          }
        );

        // 设置新的热门商品
        const updatePromises = hotProducts.map((item, index) => {
          const rank = index + 1;
          return Product.update(
            {
              isAutoHot: true,
              hotTimeRange: timeRange,
              hotScore: item.hotScore,
              hotRank: rank,
              lastHotUpdate: new Date()
            },
            {
              where: { id: item.productId },
              transaction
            }
          );
        });

        await Promise.all(updatePromises);

        // 记录历史数据
        const historyRecords = hotProducts.map((item, index) => ({
          productId: item.productId,
          timeRange,
          hotScore: item.hotScore,
          rank: index + 1,
          exchangeCount: item.exchangeCount
        }));

        await HotProductHistory.bulkCreate(historyRecords, { transaction });

        await transaction.commit();

        console.log(`✅ ${timeRange} 时间维度热门商品更新完成，共 ${hotProducts.length} 个商品`);

        return {
          success: true,
          timeRange,
          hotProductsCount: hotProducts.length,
          totalProductsEvaluated: productScores.length,
          hotProducts: hotProducts.map(item => ({
            productId: item.productId,
            productName: item.productName,
            rank: hotProducts.indexOf(item) + 1,
            hotScore: item.hotScore,
            exchangeCount: item.exchangeCount
          }))
        };

      } catch (error) {
        await transaction.rollback();
        throw error;
      }

    } catch (error) {
      console.error(`❌ 更新 ${timeRange} 时间维度热门商品失败:`, error);
      return {
        success: false,
        message: error.message,
        timeRange
      };
    }
  }

  /**
   * 更新所有启用的时间维度
   * @returns {Promise<Array>} 所有更新结果
   */
  async updateAllTimeRanges() {
    if (this.isUpdating) {
      console.log('⚠️ 热门商品更新正在进行中，跳过本次更新');
      return [];
    }

    this.isUpdating = true;

    try {
      console.log('🚀 开始更新所有时间维度的热门商品...');

      // 获取所有启用的配置
      const configs = await HotProductConfig.findAll({
        where: {
          enabled: true,
          autoUpdateEnabled: true
        },
        order: [['timeRange', 'ASC']]
      });

      if (configs.length === 0) {
        console.log('⚠️ 没有启用的时间维度配置');
        return [];
      }

      const results = [];
      for (const config of configs) {
        const result = await this.updateHotProducts(config.timeRange);
        results.push(result);

        // 添加延迟避免数据库压力
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      console.log('✅ 所有时间维度热门商品更新完成');
      return results;

    } catch (error) {
      console.error('❌ 更新所有时间维度热门商品失败:', error);
      return [];
    } finally {
      this.isUpdating = false;
    }
  }

  /**
   * 获取指定时间维度的热门商品
   * @param {string} timeRange - 时间范围
   * @param {number} limit - 返回数量限制
   * @returns {Promise<Array>} 热门商品列表
   */
  async getHotProductsByTimeRange(timeRange, limit = 10) {
    try {
      return await Product.findAll({
        where: {
          isAutoHot: true,
          hotTimeRange: timeRange,
          status: 'active'
        },
        order: [['hotRank', 'ASC']],
        limit,
        include: [
          {
            model: require('../models/category'),
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: require('../models/productImage'),
            attributes: ['id', 'imageUrl'],
            required: false,
            limit: 1,
            order: [['sortOrder', 'ASC']]
          }
        ]
      });
    } catch (error) {
      console.error(`获取 ${timeRange} 热门商品失败:`, error);
      return [];
    }
  }

  /**
   * 获取时间范围的SQL条件
   * @param {string} timeRange - 时间范围
   * @returns {Object|null} SQL条件对象
   */
  getTimeRangeCondition(timeRange) {
    const now = new Date();

    switch (timeRange) {
      case 'all':
        return null; // 不限制时间
      case '30d':
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        return { createdAt: { [Op.gte]: thirtyDaysAgo } };
      case '7d':
        const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        return { createdAt: { [Op.gte]: sevenDaysAgo } };
      case '1d':
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        return {
          createdAt: {
            [Op.between]: [today, tomorrow]
          }
        };
      default:
        return null;
    }
  }

  /**
   * 清理过期的历史记录
   * @param {number} daysToKeep - 保留天数
   * @returns {Promise<number>} 清理的记录数
   */
  async cleanupHistory(daysToKeep = 90) {
    try {
      return await HotProductHistory.cleanupOldRecords(daysToKeep);
    } catch (error) {
      console.error('清理热门商品历史记录失败:', error);
      return 0;
    }
  }
}

module.exports = new HotProductService();
