<template>
  <div class="advanced-settings">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <el-icon class="title-icon"><Star /></el-icon>
            高级功能
          </h1>
          <p class="page-description">飞书群管理高级功能和工具</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 高级功能网格 -->
    <div class="advanced-grid">
      <!-- 自定义消息模板 -->
      <router-link to="/admin/system/message-templates" class="feature-link">
        <el-card class="advanced-card" shadow="hover">
          <div class="advanced-content">
            <div class="advanced-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="advanced-info">
              <h3 class="advanced-title">自定义消息模板</h3>
              <p class="advanced-desc">创建和管理飞书消息模板，支持变量替换和多种消息类型</p>
              <div class="advanced-features">
                <el-tag size="small">模板管理</el-tag>
                <el-tag size="small" type="success">变量替换</el-tag>
                <el-tag size="small" type="info">多种类型</el-tag>
              </div>
            </div>
            <div class="advanced-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </el-card>
      </router-link>

      <!-- 智能发送时间控制 -->
      <router-link to="/admin/system/intelligent-schedule" class="feature-link">
        <el-card class="advanced-card" shadow="hover">
          <div class="advanced-content">
            <div class="advanced-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="advanced-info">
              <h3 class="advanced-title">智能发送时间控制</h3>
              <p class="advanced-desc">智能控制消息发送时间，避免在非工作时间打扰用户</p>
              <div class="advanced-features">
                <el-tag size="small">时间控制</el-tag>
                <el-tag size="small" type="warning">智能调度</el-tag>
                <el-tag size="small" type="success">用户友好</el-tag>
              </div>
            </div>
            <div class="advanced-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </el-card>
      </router-link>

      <!-- 高级诊断工具 -->
      <router-link to="/admin/system/diagnostic-tools" class="feature-link">
        <el-card class="advanced-card" shadow="hover">
          <div class="advanced-content">
            <div class="advanced-icon">
              <el-icon><Tools /></el-icon>
            </div>
            <div class="advanced-info">
              <h3 class="advanced-title">高级诊断工具</h3>
              <p class="advanced-desc">系统诊断和故障排查工具，帮助快速定位和解决问题</p>
              <div class="advanced-features">
                <el-tag size="small">系统诊断</el-tag>
                <el-tag size="small" type="danger">故障排查</el-tag>
                <el-tag size="small" type="info">性能监控</el-tag>
              </div>
            </div>
            <div class="advanced-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </el-card>
      </router-link>

      <!-- API管理 -->
      <el-card class="advanced-card" shadow="hover">
        <div class="advanced-content">
          <div class="advanced-icon">
            <el-icon><Connection /></el-icon>
          </div>
          <div class="advanced-info">
            <h3 class="advanced-title">API管理</h3>
            <p class="advanced-desc">管理系统API接口，配置访问权限和限流策略</p>
            <div class="advanced-features">
              <el-tag size="small">接口管理</el-tag>
              <el-tag size="small" type="warning">权限控制</el-tag>
              <el-tag size="small" type="info">限流策略</el-tag>
            </div>
            <div class="feature-status">
              <el-tag type="info">开发中</el-tag>
            </div>
          </div>
          <div class="advanced-arrow disabled">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </el-card>

      <!-- 数据分析 -->
      <el-card class="advanced-card" shadow="hover">
        <div class="advanced-content">
          <div class="advanced-icon">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="advanced-info">
            <h3 class="advanced-title">高级数据分析</h3>
            <p class="advanced-desc">深度数据分析和报表生成，提供业务洞察</p>
            <div class="advanced-features">
              <el-tag size="small">数据挖掘</el-tag>
              <el-tag size="small" type="success">报表生成</el-tag>
              <el-tag size="small" type="warning">业务洞察</el-tag>
            </div>
            <div class="feature-status">
              <el-tag type="info">规划中</el-tag>
            </div>
          </div>
          <div class="advanced-arrow disabled">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </el-card>

      <!-- 自动化工作流 -->
      <el-card class="advanced-card" shadow="hover">
        <div class="advanced-content">
          <div class="advanced-icon">
            <el-icon><Operation /></el-icon>
          </div>
          <div class="advanced-info">
            <h3 class="advanced-title">自动化工作流</h3>
            <p class="advanced-desc">创建自动化工作流程，提升运营效率</p>
            <div class="advanced-features">
              <el-tag size="small">流程自动化</el-tag>
              <el-tag size="small" type="success">效率提升</el-tag>
              <el-tag size="small" type="info">智能触发</el-tag>
            </div>
            <div class="feature-status">
              <el-tag type="info">规划中</el-tag>
            </div>
          </div>
          <div class="advanced-arrow disabled">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 功能统计 -->
    <div class="feature-stats">
      <el-card>
        <template #header>
          <h3>功能使用统计</h3>
        </template>
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.messageTemplates }}</div>
                <div class="stat-label">消息模板数量</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.scheduledMessages }}</div>
                <div class="stat-label">定时消息数量</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon><Tools /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.diagnosticRuns }}</div>
                <div class="stat-label">诊断运行次数</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 使用指南 -->
    <div class="usage-guide">
      <el-card>
        <template #header>
          <h3>使用指南</h3>
        </template>
        <div class="guide-content">
          <el-timeline>
            <el-timeline-item timestamp="第一步" placement="top">
              <h4>配置消息模板</h4>
              <p>创建自定义消息模板，支持变量替换和多种消息类型</p>
            </el-timeline-item>
            <el-timeline-item timestamp="第二步" placement="top">
              <h4>设置发送时间</h4>
              <p>配置智能发送时间控制，避免在非工作时间打扰用户</p>
            </el-timeline-item>
            <el-timeline-item timestamp="第三步" placement="top">
              <h4>系统诊断</h4>
              <p>使用诊断工具监控系统状态，及时发现和解决问题</p>
            </el-timeline-item>
            <el-timeline-item timestamp="第四步" placement="top">
              <h4>持续优化</h4>
              <p>根据使用情况和反馈，持续优化配置和流程</p>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Star, Refresh, Document, Clock, Tools, ArrowRight,
  Connection, TrendCharts, Operation
} from '@element-plus/icons-vue';

// 响应式数据
const loading = ref(false);

const stats = reactive({
  messageTemplates: 12,
  scheduledMessages: 45,
  diagnosticRuns: 128
});

// 方法
const refreshData = async () => {
  loading.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 更新统计数据
    Object.assign(stats, {
      messageTemplates: Math.floor(Math.random() * 50) + 10,
      scheduledMessages: Math.floor(Math.random() * 100) + 20,
      diagnosticRuns: Math.floor(Math.random() * 200) + 100
    });
    
    ElMessage.success('数据刷新成功');
  } catch (error) {
    ElMessage.error('数据刷新失败');
  } finally {
    loading.value = false;
  }
};

// 生命周期
onMounted(() => {
  // 初始化数据
});
</script>

<style scoped>
.advanced-settings {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  color: #3b82f6;
}

.page-description {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

.advanced-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.feature-link {
  text-decoration: none;
  color: inherit;
}

.advanced-card {
  height: 100%;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  cursor: pointer;
}

.advanced-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.advanced-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 24px;
  position: relative;
}

.advanced-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  flex-shrink: 0;
}

.advanced-info {
  flex: 1;
}

.advanced-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.advanced-desc {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 12px 0;
}

.advanced-features {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.feature-status {
  position: absolute;
  top: 16px;
  right: 16px;
}

.advanced-arrow {
  color: #9ca3af;
  font-size: 20px;
  transition: all 0.2s;
}

.advanced-arrow.disabled {
  color: #d1d5db;
}

.advanced-card:hover .advanced-arrow:not(.disabled) {
  color: #3b82f6;
  transform: translateX(4px);
}

.feature-stats {
  margin-bottom: 24px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #3b82f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  color: #6b7280;
  font-size: 14px;
}

.usage-guide .guide-content {
  padding: 16px 0;
}

.usage-guide h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.usage-guide p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .advanced-grid {
    grid-template-columns: 1fr;
  }
  
  .advanced-content {
    flex-direction: column;
    text-align: center;
  }
  
  .advanced-arrow {
    align-self: center;
  }
}
</style>
