#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

echo -e "${YELLOW}===== 项目依赖优化脚本 =====${NC}"

# 项目路径
PROJECT_DIR="$(pwd)"
SERVER_DIR="$PROJECT_DIR/server"

# 检查工具是否安装
check_tool() {
  if ! command -v $1 &> /dev/null; then
    echo -e "${RED}错误: 未找到 $1 命令${NC}"
    echo -e "${YELLOW}请先安装 $1: npm install -g $1${NC}"
    exit 1
  fi
}

# 检查依赖分析工具
check_tool depcheck
check_tool npm-check

# 前端依赖分析
echo -e "${BLUE}正在分析前端依赖...${NC}"
cd "$PROJECT_DIR"

echo -e "${YELLOW}检查未使用的依赖...${NC}"
depcheck --json > "$PROJECT_DIR/temp/frontend-depcheck.json"
echo -e "${GREEN}依赖分析报告已保存到 temp/frontend-depcheck.json${NC}"

echo -e "${YELLOW}检查过时的依赖...${NC}"
npm-check -u --no-emoji > "$PROJECT_DIR/temp/frontend-npmcheck.txt"
echo -e "${GREEN}依赖更新报告已保存到 temp/frontend-npmcheck.txt${NC}"

# 提取未使用的依赖
UNUSED_DEPS=$(cat "$PROJECT_DIR/temp/frontend-depcheck.json" | grep -o '"dependencies": \[.*\]' | sed 's/"dependencies": \[\(.*\)\]/\1/' | sed 's/"//g' | sed 's/,/ /g')

if [ ! -z "$UNUSED_DEPS" ]; then
  echo -e "${YELLOW}发现可能未使用的前端依赖:${NC}"
  echo $UNUSED_DEPS
  
  echo -e "${BLUE}是否移除这些依赖? [y/N]${NC}"
  read -r response
  if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    for dep in $UNUSED_DEPS; do
      echo -e "${BLUE}移除 $dep...${NC}"
      npm uninstall $dep
    done
    echo -e "${GREEN}未使用的前端依赖已移除${NC}"
  else
    echo -e "${YELLOW}跳过移除未使用的前端依赖${NC}"
  fi
else
  echo -e "${GREEN}未发现未使用的前端依赖${NC}"
fi

# 后端依赖分析
echo -e "${BLUE}正在分析后端依赖...${NC}"
cd "$SERVER_DIR"

echo -e "${YELLOW}检查未使用的依赖...${NC}"
depcheck --json > "$PROJECT_DIR/temp/backend-depcheck.json"
echo -e "${GREEN}依赖分析报告已保存到 temp/backend-depcheck.json${NC}"

echo -e "${YELLOW}检查过时的依赖...${NC}"
npm-check -u --no-emoji > "$PROJECT_DIR/temp/backend-npmcheck.txt"
echo -e "${GREEN}依赖更新报告已保存到 temp/backend-npmcheck.txt${NC}"

# 提取未使用的依赖
UNUSED_DEPS=$(cat "$PROJECT_DIR/temp/backend-depcheck.json" | grep -o '"dependencies": \[.*\]' | sed 's/"dependencies": \[\(.*\)\]/\1/' | sed 's/"//g' | sed 's/,/ /g')

if [ ! -z "$UNUSED_DEPS" ]; then
  echo -e "${YELLOW}发现可能未使用的后端依赖:${NC}"
  echo $UNUSED_DEPS
  
  echo -e "${BLUE}是否移除这些依赖? [y/N]${NC}"
  read -r response
  if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    for dep in $UNUSED_DEPS; do
      echo -e "${BLUE}移除 $dep...${NC}"
      npm uninstall $dep
    done
    echo -e "${GREEN}未使用的后端依赖已移除${NC}"
  else
    echo -e "${YELLOW}跳过移除未使用的后端依赖${NC}"
  fi
else
  echo -e "${GREEN}未发现未使用的后端依赖${NC}"
fi

# 返回项目根目录
cd "$PROJECT_DIR"

# 优化脚本
echo -e "${BLUE}正在优化npm脚本...${NC}"

# 创建新的生产环境优化脚本
cat > "$PROJECT_DIR/scripts/optimize-production.sh" << EOF
#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

echo -e "\${YELLOW}===== 生产环境优化脚本 =====${NC}"

# 项目路径
PROJECT_DIR="\$(pwd)"
SERVER_DIR="\$PROJECT_DIR/server"

# 构建前端
echo -e "\${BLUE}正在构建前端...${NC}"
npm run build

# 清理node_modules
echo -e "\${BLUE}正在清理开发依赖...${NC}"
cd "\$PROJECT_DIR"
npm prune --production

cd "\$SERVER_DIR"
npm prune --production

# 移除开发工具和配置
echo -e "\${BLUE}正在移除开发配置文件...${NC}"
rm -rf "\$PROJECT_DIR/.vscode"
rm -rf "\$PROJECT_DIR/temp"
rm -f "\$PROJECT_DIR/.env"
rm -f "\$SERVER_DIR/.env"

# 压缩静态资源
echo -e "\${BLUE}正在压缩静态资源...${NC}"
find "\$PROJECT_DIR/dist" -name "*.js" -exec gzip -9 -k {} \;
find "\$PROJECT_DIR/dist" -name "*.css" -exec gzip -9 -k {} \;
find "\$PROJECT_DIR/dist" -name "*.html" -exec gzip -9 -k {} \;

echo -e "\${GREEN}生产环境优化完成${NC}"
EOF

chmod +x "$PROJECT_DIR/scripts/optimize-production.sh"
echo -e "${GREEN}生产环境优化脚本已创建${NC}"

# 创建开发环境初始化脚本
cat > "$PROJECT_DIR/scripts/dev-setup.sh" << EOF
#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

echo -e "\${YELLOW}===== 开发环境初始化脚本 =====${NC}"

# 项目路径
PROJECT_DIR="\$(pwd)"
SERVER_DIR="\$PROJECT_DIR/server"

# 安装前端依赖
echo -e "\${BLUE}正在安装前端依赖...${NC}"
cd "\$PROJECT_DIR"
npm install

# 安装后端依赖
echo -e "\${BLUE}正在安装后端依赖...${NC}"
cd "\$SERVER_DIR"
npm install

# 初始化开发环境配置
echo -e "\${BLUE}正在初始化开发环境配置...${NC}"
cd "\$PROJECT_DIR"

# 如果.env文件不存在，从示例创建
if [ ! -f ".env" ]; then
  cp ".env.example" ".env" 2>/dev/null || echo -e "\${YELLOW}未找到.env.example文件${NC}"
fi

cd "\$SERVER_DIR"
if [ ! -f ".env" ]; then
  cp ".env.example" ".env" 2>/dev/null || echo -e "\${YELLOW}未找到.env.example文件${NC}"
fi

# 创建必要的目录
echo -e "\${BLUE}正在创建必要的目录...${NC}"
mkdir -p "\$SERVER_DIR/uploads/images"
mkdir -p "\$SERVER_DIR/uploads/payment"
mkdir -p "\$PROJECT_DIR/temp/logs"

echo -e "\${GREEN}开发环境初始化完成${NC}"
echo -e "\${BLUE}可以使用以下命令启动开发环境:${NC}"
echo -e "  ./scripts/restart.sh"
EOF

chmod +x "$PROJECT_DIR/scripts/dev-setup.sh"
echo -e "${GREEN}开发环境初始化脚本已创建${NC}"

# 输出完成信息
echo -e "\n${GREEN}===== 依赖优化完成 =====${NC}"
echo -e "${BLUE}依赖分析报告:${NC}"
echo -e "  前端: temp/frontend-depcheck.json, temp/frontend-npmcheck.txt"
echo -e "  后端: temp/backend-depcheck.json, temp/backend-npmcheck.txt"
echo -e "${BLUE}新增脚本:${NC}"
echo -e "  生产环境优化: scripts/optimize-production.sh"
echo -e "  开发环境初始化: scripts/dev-setup.sh"
echo -e "${YELLOW}提示: 请检查依赖分析报告，确认移除的依赖确实不再使用${NC}" 