/**
 * 测试操作日志API接口
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:3000/api';

// 获取管理员token
async function getAdminToken() {
  try {
    const loginData = {
      username: '超管',
      email: 'ch<PERSON><PERSON><PERSON>@guanghe.tv',
      password: '654321',
      userType: 'admin'
    };
    
    const response = await axios.post(`${BASE_URL}/auth/login`, loginData);
    return response.data.token;
  } catch (error) {
    console.error('❌ 获取token失败:', error.response?.data?.message || error.message);
    return null;
  }
}

// 测试操作日志API
async function testOperationLogsAPI(token) {
  try {
    const config = {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };
    
    console.log('📊 测试操作日志API...');
    
    // 1. 测试获取所有日志
    console.log('\n1. 获取所有日志（前10条）:');
    const allLogsResponse = await axios.get(`${BASE_URL}/stocks/operation-logs`, {
      ...config,
      params: {
        page: 1,
        limit: 10
      }
    });
    
    console.log('API响应状态:', allLogsResponse.status);
    console.log('响应数据结构:', {
      success: allLogsResponse.data.success,
      message: allLogsResponse.data.message,
      logsCount: allLogsResponse.data.data?.logs?.length || 0,
      totalCount: allLogsResponse.data.data?.pagination?.total || 0
    });
    
    if (allLogsResponse.data.data?.logs?.length > 0) {
      const firstLog = allLogsResponse.data.data.logs[0];
      console.log('第一条日志示例:', {
        id: firstLog.id,
        productId: firstLog.productId,
        workplaceId: firstLog.workplaceId,
        operationType: firstLog.operationType,
        beforeStock: firstLog.beforeStock,
        afterStock: firstLog.afterStock,
        changeAmount: firstLog.changeAmount,
        reason: firstLog.reason,
        operatorName: firstLog.operatorName,
        createdAt: firstLog.createdAt,
        hasProduct: !!firstLog.product,
        hasWorkplace: !!firstLog.workplace,
        hasOperator: !!firstLog.operator
      });
    }
    
    // 2. 测试按操作类型筛选
    console.log('\n2. 按操作类型筛选（transfer）:');
    const transferLogsResponse = await axios.get(`${BASE_URL}/stocks/operation-logs`, {
      ...config,
      params: {
        operationType: 'transfer',
        page: 1,
        limit: 5
      }
    });
    
    console.log('转移操作日志数量:', transferLogsResponse.data.data?.logs?.length || 0);
    
    // 3. 测试按商品ID筛选
    console.log('\n3. 按商品ID筛选（哪吒捏捏乐，ID=15）:');
    const productLogsResponse = await axios.get(`${BASE_URL}/stocks/operation-logs`, {
      ...config,
      params: {
        productId: 15,
        page: 1,
        limit: 5
      }
    });
    
    console.log('哪吒捏捏乐操作日志数量:', productLogsResponse.data.data?.logs?.length || 0);
    
    // 4. 测试按职场ID筛选
    console.log('\n4. 按职场ID筛选（北京，ID=1）:');
    const workplaceLogsResponse = await axios.get(`${BASE_URL}/stocks/operation-logs`, {
      ...config,
      params: {
        workplaceId: 1,
        page: 1,
        limit: 5
      }
    });
    
    console.log('北京职场操作日志数量:', workplaceLogsResponse.data.data?.logs?.length || 0);
    
    return true;
  } catch (error) {
    console.error('❌ 测试操作日志API失败:', error.response?.data || error.message);
    return false;
  }
}

// 主测试函数
async function runOperationLogsTest() {
  console.log('🧪 开始测试操作日志API...\n');
  
  // 1. 获取token
  const token = await getAdminToken();
  if (!token) {
    console.log('❌ 无法获取token，测试终止');
    return;
  }
  console.log('✅ 获取token成功\n');
  
  // 2. 测试API
  const success = await testOperationLogsAPI(token);
  
  if (success) {
    console.log('\n🎉 操作日志API测试成功！');
    console.log('💡 现在前端操作日志对话框应该能正常显示数据了');
  } else {
    console.log('\n❌ 操作日志API测试失败');
  }
  
  console.log('\n📋 测试完成');
}

// 运行测试
runOperationLogsTest().catch(error => {
  console.error('测试执行失败:', error);
});
