import api from './index';
import { useAuthStore } from '../stores/auth';

/**
 * 获取当前用户资料
 * @returns {Promise<Object>} 当前用户的资料信息
 */
export function getUserProfile() {
  // 使用本地存储的用户数据，避免额外的API请求
  const authStore = useAuthStore();

  // 如果本地已有用户数据，直接返回
  if (authStore.user) {
    return Promise.resolve(authStore.user);
  }

  // 否则尝试从服务器获取
  return api.get('/users/profile');
}

/**
 * 修改密码
 * @param {Object} passwordData - 密码数据
 * @param {string} passwordData.oldPassword - 旧密码
 * @param {string} passwordData.newPassword - 新密码
 * @returns {Promise<Object>} 操作结果
 */
export function updatePassword(passwordData) {
  return api.put('/users/password', passwordData);
}

/**
 * 获取用户列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.search - 姓名搜索关键词
 * @returns {Promise<Object>} 用户列表数据和分页信息
 */
export function getUsers(params = {}) {
  return api.get('/users', { params });
}

/**
 * 创建新用户
 * @param {Object} userData - 用户数据
 * @param {string} userData.username - 姓名
 * @param {string} userData.password - 密码
 * @param {string} userData.email - 邮箱（可选）
 * @param {string} userData.role - 角色（'admin' 或 'user'）
 * @param {string} userData.department - 部门（可选）
 * @returns {Promise<Object>} 创建的用户数据
 */
export function createUser(userData) {
  return api.post('/users', userData);
}

/**
 * 删除用户
 * @param {number} userId - 用户ID
 * @returns {Promise<Object>} 操作结果
 */
export function deleteUser(userId) {
  return api.delete(`/users/${userId}`);
}

/**
 * 更新用户部门
 * @param {number} userId - 用户ID
 * @param {string} department - 部门名称
 * @returns {Promise<Object>} 更新后的用户数据
 */
export function updateUserDepartment(userId, department) {
  return api.put(`/users/${userId}/department`, { department });
}

/**
 * 更新用户职场
 * @param {number} userId - 用户ID
 * @param {Object} workplaceData - 职场数据
 * @param {string} workplaceData.workplace - 职场名称
 * @param {number} workplaceData.workplaceId - 职场ID
 * @returns {Promise<Object>} 更新后的用户数据
 */
export function updateUserWorkplace(userId, workplaceData) {
  return api.put(`/users/${userId}/workplace`, workplaceData);
}

/**
 * 更新用户邮箱
 * @param {number} userId - 用户ID
 * @param {string} email - 邮箱地址
 * @returns {Promise<Object>} 更新后的用户数据
 */
export function updateUserEmail(userId, email) {
  return api.put(`/users/${userId}/email`, { email });
}

/**
 * 更新用户手机号码
 * @param {number} userId - 用户ID
 * @param {string} mobile - 手机号码
 * @returns {Promise<Object>} 更新后的用户数据
 */
export function updateUserMobile(userId, mobile) {
  return api.put(`/users/${userId}/mobile`, { mobile });
}

/**
 * 更新用户角色
 * @param {number} userId - 用户ID
 * @param {string} role - 新的角色值 ('admin' 或 'user')
 * @returns {Promise<Object>} 更新后的用户数据
 */
export function updateUserRole(userId, role) {
  return api.put(`/users/${userId}/role`, { role });
}

/**
 * 重置用户密码
 * @param {number} userId - 用户ID
 * @param {string} password - 新密码
 * @returns {Promise<Object>} 操作结果
 */
export function resetUserPassword(userId, password) {
  return api.put(`/users/${userId}/reset-password`, { password });
}

/**
 * 导入用户（CSV文件）
 * @param {File} file - CSV文件
 * @returns {Promise<Object>} 导入结果
 */
export function importUsers(file) {
  if (!(file instanceof File)) {
    console.error('导入用户失败：无效的文件对象', file);
    return Promise.reject(new Error('无效的文件对象'));
  }

  console.log('准备导入用户文件:', {
    名称: file.name,
    类型: file.type,
    大小: `${(file.size / 1024).toFixed(2)}KB`
  });

  const formData = new FormData();
  formData.append('file', file);

  // 打印FormData内容（调试用）
  console.log('FormData对象创建完成');

  return api.post('/users/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    timeout: 60000, // 增加超时时间到60秒，处理大文件
    onUploadProgress: (progressEvent) => {
      const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
      console.log(`上传进度: ${percentCompleted}%`);
    }
  }).catch(error => {
    console.error('导入用户API错误:', error);
    if (error.response) {
      console.error('错误响应状态:', error.response.status);
      console.error('错误响应数据:', error.response.data);
    }
    throw error;
  });
}

/**
 * 导出用户列表（CSV文件）
 * @param {Object} params - 查询参数
 * @param {string} params.search - 姓名搜索关键词
 * @returns {Promise<Blob>} CSV文件Blob数据
 */
export function exportUsers(params = {}) {
  return api.get('/users/export', {
    params,
    responseType: 'blob'
  });
}

/**
 * 下载用户导入模板
 * @param {string} format - 模板格式，'csv'或'xlsx'
 * @returns {Promise<Blob>} 模板文件Blob数据
 */
export function downloadUserTemplate(format = 'csv') {
  return api.get(`/users/template`, {
    params: { format },
    responseType: 'blob'
  });
}

/**
 * 批量删除用户
 * @param {Array<number>} userIds - 用户ID数组
 * @returns {Promise<Object>} 操作结果
 */
export function batchDeleteUsers(userIds) {
  return api.post('/users/batch-delete', { userIds });
}

/**
 * 切换用户状态（启用/禁用）
 * @param {number} userId - 用户ID
 * @param {boolean} isActive - 是否启用用户，true为启用，false为禁用
 * @returns {Promise<Object>} 操作结果
 */
export function toggleUserStatus(userId, isActive) {
  return api.put(`/users/${userId}/status`, { isActive });
}

/**
 * 批量修改用户状态（启用/禁用）
 * @param {Array<number>} userIds - 用户ID数组
 * @param {boolean} isActive - 是否启用
 * @returns {Promise<Object>} 操作结果
 */
export function batchUpdateUserStatus(userIds, isActive) {
  return api.post('/users/batch-status', { userIds, isActive });
}

/**
 * 批量修改用户部门
 * @param {Array<number>} userIds - 用户ID数组
 * @param {string} department - 部门名称
 * @returns {Promise<Object>} 操作结果
 */
export function batchUpdateUserDepartment(userIds, department) {
  return api.post('/users/batch-department', { userIds, department });
}

/**
 * 批量修改用户职场
 * @param {Array<number>} userIds - 用户ID数组
 * @param {string} workplace - 职场名称
 * @param {number} workplaceId - 职场ID
 * @returns {Promise<Object>} 操作结果
 */
export function batchUpdateUserWorkplace(userIds, workplace, workplaceId) {
  return api.post('/users/batch-workplace', { userIds, workplace, workplaceId });
}
