/**
 * 修复前端缓存问题的脚本
 * 问题：库存转移成功但前端显示的数据没有更新
 * 原因：可能的缓存机制阻止了数据刷新
 */

import { readFileSync, writeFileSync } from 'fs';
import { join } from 'path';

console.log('🔧 开始修复前端缓存问题...\n');

// 1. 检查并修复 axios 请求缓存问题
function fixAxiosCache() {
  console.log('📝 修复 axios 请求缓存问题...');
  
  const apiIndexPath = 'src/api/index.js';
  let content = readFileSync(apiIndexPath, 'utf8');
  
  // 检查是否已经有缓存控制头
  if (!content.includes('Cache-Control')) {
    // 在请求拦截器中添加缓存控制
    const interceptorMatch = content.match(/(api\.interceptors\.request\.use\(\s*config => \{[^}]+)/);
    if (interceptorMatch) {
      const newInterceptor = interceptorMatch[1] + `
    
    // 添加缓存控制头，防止浏览器缓存API响应
    config.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate';
    config.headers['Pragma'] = 'no-cache';
    config.headers['Expires'] = '0';
    
    // 为GET请求添加时间戳参数，确保每次请求都是唯一的
    if (config.method === 'get' || config.method === 'GET') {
      config.params = config.params || {};
      config.params._t = Date.now();
    }`;
      
      content = content.replace(interceptorMatch[1], newInterceptor);
      writeFileSync(apiIndexPath, content);
      console.log('✅ 已添加缓存控制头到 axios 请求拦截器');
    }
  } else {
    console.log('✅ axios 缓存控制已存在');
  }
}

// 2. 修复库存管理页面的数据刷新机制
function fixStockManagementRefresh() {
  console.log('📝 修复库存管理页面数据刷新机制...');
  
  const stockManagementPath = 'src/views/admin/StockManagement.vue';
  let content = readFileSync(stockManagementPath, 'utf8');
  
  // 检查 handleStockTransfer 方法中的 loadData 调用
  const handleStockTransferMatch = content.match(/(const handleStockTransfer = async \(transferData\) => \{[\s\S]*?showTransferDialog\.value = false\s*loadData\(\))/);
  
  if (handleStockTransferMatch) {
    // 确保在成功后强制刷新数据
    const newHandleStockTransfer = handleStockTransferMatch[1].replace(
      'showTransferDialog.value = false\n        loadData()',
      `showTransferDialog.value = false
        
        // 强制刷新数据，清除可能的缓存
        console.log('🔄 库存转移成功，强制刷新页面数据...')
        await loadData()
        
        // 额外等待一下确保数据更新完成
        setTimeout(() => {
          console.log('🔄 延迟刷新确保数据同步...')
          loadData()
        }, 1000)`
    );
    
    content = content.replace(handleStockTransferMatch[1], newHandleStockTransfer);
    writeFileSync(stockManagementPath, content);
    console.log('✅ 已优化库存转移后的数据刷新机制');
  }
}

// 3. 添加强制刷新功能到库存管理页面
function addForceRefreshButton() {
  console.log('📝 添加强制刷新按钮...');
  
  const stockManagementPath = 'src/views/admin/StockManagement.vue';
  let content = readFileSync(stockManagementPath, 'utf8');
  
  // 在操作按钮区域添加强制刷新按钮
  const buttonAreaMatch = content.match(/(<div class="actions"[\s\S]*?<\/div>)/);
  if (buttonAreaMatch && !content.includes('强制刷新')) {
    const newButtonArea = buttonAreaMatch[1].replace(
      '</div>',
      `        <el-button 
          type="warning" 
          @click="forceRefreshData"
          :loading="loading"
          title="清除缓存并强制刷新数据"
        >
          <i class="fas fa-sync-alt"></i>
          强制刷新
        </el-button>
      </div>`
    );
    
    content = content.replace(buttonAreaMatch[1], newButtonArea);
    
    // 添加 forceRefreshData 方法
    const methodsMatch = content.match(/(const refreshData = \(\) => \{[\s\S]*?\})/);
    if (methodsMatch) {
      const newMethods = methodsMatch[1] + `

    const forceRefreshData = async () => {
      console.log('🔄 执行强制刷新...')
      
      // 清除浏览器缓存
      if ('caches' in window) {
        try {
          const cacheNames = await caches.keys()
          for (const cacheName of cacheNames) {
            await caches.delete(cacheName)
          }
          console.log('✅ 已清除浏览器缓存')
        } catch (error) {
          console.warn('清除缓存失败:', error)
        }
      }
      
      // 重置页面状态
      currentPage.value = 1
      
      // 强制重新加载数据
      await loadData()
      
      ElMessage.success('数据已强制刷新')
    }`;
      
      content = content.replace(methodsMatch[1], newMethods);
      
      // 在 return 语句中添加新方法
      const returnMatch = content.match(/(return \{[\s\S]*?refreshData,)/);
      if (returnMatch) {
        content = content.replace(returnMatch[1], returnMatch[1] + '\n      forceRefreshData,');
      }
    }
    
    writeFileSync(stockManagementPath, content);
    console.log('✅ 已添加强制刷新按钮和功能');
  }
}

// 4. 修复 getProductsWithStocks API 调用
function fixGetProductsWithStocksAPI() {
  console.log('📝 修复 getProductsWithStocks API 调用...');
  
  const stockManagementApiPath = 'src/api/stockManagement.js';
  let content = readFileSync(stockManagementApiPath, 'utf8');
  
  // 修改 getProductsWithStocks 函数，添加缓存破坏参数
  const getProductsMatch = content.match(/(export const getProductsWithStocks = \(params = \{\}\) => \{[\s\S]*?\})/);
  if (getProductsMatch) {
    const newGetProducts = `export const getProductsWithStocks = (params = {}) => {
  // 添加时间戳参数防止缓存
  const requestParams = {
    ...params,
    includeWorkplaceStocks: true,
    _timestamp: Date.now() // 缓存破坏参数
  }
  
  console.log('🔍 调用 getProductsWithStocks API，参数:', requestParams)
  
  return request({
    url: '/products',
    method: 'GET',
    params: requestParams
  }).then(response => {
    console.log('✅ getProductsWithStocks API 响应:', {
      dataLength: response.data?.length || 0,
      total: response.total,
      timestamp: new Date().toISOString()
    })
    return response
  }).catch(error => {
    console.error('❌ getProductsWithStocks API 失败:', error)
    throw error
  })
}`;
    
    content = content.replace(getProductsMatch[1], newGetProducts);
    writeFileSync(stockManagementApiPath, content);
    console.log('✅ 已优化 getProductsWithStocks API 调用');
  }
}

// 5. 创建调试工具
function createDebugTool() {
  console.log('📝 创建前端调试工具...');
  
  const debugToolContent = `/**
 * 前端库存数据调试工具
 * 在浏览器控制台中使用
 */

// 全局调试对象
window.StockDebugger = {
  // 检查当前页面的库存数据
  checkCurrentData() {
    console.log('🔍 检查当前页面库存数据...')
    
    // 查找哪吒捏捏乐的数据
    const tableRows = document.querySelectorAll('table tbody tr')
    console.log(\`📋 找到 \${tableRows.length} 个商品行\`)
    
    tableRows.forEach((row, index) => {
      const nameCell = row.querySelector('td:nth-child(2)')
      if (nameCell && nameCell.textContent.includes('哪吒捏捏乐')) {
        console.log(\`✅ 找到哪吒捏捏乐，行索引: \${index}\`)
        
        // 获取职场库存信息
        const stockCells = row.querySelectorAll('.workplace-stock')
        stockCells.forEach(cell => {
          console.log(\`📍 职场库存: \${cell.textContent}\`)
        })
      }
    })
  },
  
  // 强制刷新页面数据
  async forceRefresh() {
    console.log('🔄 强制刷新页面数据...')
    
    // 清除所有缓存
    if ('caches' in window) {
      const cacheNames = await caches.keys()
      for (const cacheName of cacheNames) {
        await caches.delete(cacheName)
      }
      console.log('✅ 已清除浏览器缓存')
    }
    
    // 清除 localStorage 和 sessionStorage
    localStorage.clear()
    sessionStorage.clear()
    console.log('✅ 已清除本地存储')
    
    // 建议用户刷新页面
    console.log('💡 请按 Ctrl+F5 (Windows) 或 Cmd+Shift+R (Mac) 强制刷新页面')
  },
  
  // 检查网络请求
  monitorRequests() {
    console.log('🔍 开始监控网络请求...')
    
    const originalFetch = window.fetch
    window.fetch = function(...args) {
      console.log('🌐 Fetch 请求:', args[0])
      return originalFetch.apply(this, args).then(response => {
        console.log('📥 Fetch 响应:', response.status, response.url)
        return response
      })
    }
    
    console.log('✅ 网络请求监控已启用')
  }
}

console.log('🛠️ 库存调试工具已加载')
console.log('使用方法:')
console.log('- StockDebugger.checkCurrentData() // 检查当前数据')
console.log('- StockDebugger.forceRefresh() // 强制刷新')
console.log('- StockDebugger.monitorRequests() // 监控请求')`;

  writeFileSync('public/stock-debugger.js', debugToolContent);
  console.log('✅ 已创建调试工具文件: public/stock-debugger.js');
}

// 执行所有修复
async function runAllFixes() {
  try {
    fixAxiosCache();
    fixStockManagementRefresh();
    addForceRefreshButton();
    fixGetProductsWithStocksAPI();
    createDebugTool();
    
    console.log('\n🎉 所有修复完成！');
    console.log('\n📋 修复内容总结:');
    console.log('1. ✅ 添加了 axios 缓存控制头');
    console.log('2. ✅ 优化了库存转移后的数据刷新机制');
    console.log('3. ✅ 添加了强制刷新按钮');
    console.log('4. ✅ 优化了 getProductsWithStocks API 调用');
    console.log('5. ✅ 创建了前端调试工具');
    
    console.log('\n🔧 使用说明:');
    console.log('1. 重启前端开发服务器');
    console.log('2. 在浏览器中打开库存管理页面');
    console.log('3. 如果数据仍然不更新，点击"强制刷新"按钮');
    console.log('4. 在浏览器控制台中使用 StockDebugger 工具进行调试');
    
  } catch (error) {
    console.error('❌ 修复过程中出现错误:', error);
  }
}

runAllFixes();
