#!/bin/bash

# 数据库迁移前预检查脚本
# 用途：在执行数据库迁移前进行安全检查和自动备份
# 作者：系统管理员
# 创建时间：2025-07-15

# 配置参数
DB_NAME="feishu_mall"
DB_USER="root"
DB_PASSWORD="password"
LOG_FILE="./logs/migration.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 创建日志目录
mkdir -p "./logs"

echo -e "${BLUE}🔍 开始数据库迁移前预检查...${NC}"
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 开始迁移前预检查" >> "$LOG_FILE"

# 1. 检查数据库连接
echo -e "${YELLOW}1. 检查数据库连接...${NC}"
mysql -u "$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 数据库连接正常${NC}"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 数据库连接检查通过" >> "$LOG_FILE"
else
    echo -e "${RED}❌ 数据库连接失败${NC}"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 数据库连接失败" >> "$LOG_FILE"
    exit 1
fi

# 2. 检查数据库是否存在
echo -e "${YELLOW}2. 检查目标数据库...${NC}"
mysql -u "$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 数据库 $DB_NAME 存在${NC}"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 数据库 $DB_NAME 检查通过" >> "$LOG_FILE"
else
    echo -e "${RED}❌ 数据库 $DB_NAME 不存在${NC}"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 数据库 $DB_NAME 不存在" >> "$LOG_FILE"
    exit 1
fi

# 3. 检查当前数据库状态
echo -e "${YELLOW}3. 检查数据库当前状态...${NC}"
TABLE_COUNT=$(mysql -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = '$DB_NAME';" -s -N)
echo -e "${GREEN}📊 当前表数量: $TABLE_COUNT${NC}"
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 当前数据库表数量: $TABLE_COUNT" >> "$LOG_FILE"

# 4. 检查SequelizeMeta表
echo -e "${YELLOW}4. 检查迁移记录表...${NC}"
mysql -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SELECT COUNT(*) FROM SequelizeMeta;" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    MIGRATION_COUNT=$(mysql -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SELECT COUNT(*) FROM SequelizeMeta;" -s -N)
    echo -e "${GREEN}✅ SequelizeMeta表存在，已执行迁移: $MIGRATION_COUNT 个${NC}"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] SequelizeMeta表检查通过，迁移记录: $MIGRATION_COUNT" >> "$LOG_FILE"
else
    echo -e "${RED}❌ SequelizeMeta表不存在${NC}"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] SequelizeMeta表不存在" >> "$LOG_FILE"
    exit 1
fi

# 5. 执行自动备份
echo -e "${YELLOW}5. 执行迁移前自动备份...${NC}"
./scripts/db/auto_backup.sh
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 自动备份完成${NC}"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 迁移前自动备份完成" >> "$LOG_FILE"
else
    echo -e "${RED}❌ 自动备份失败，迁移中止${NC}"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 自动备份失败，迁移中止" >> "$LOG_FILE"
    exit 1
fi

# 6. 显示当前Git状态
echo -e "${YELLOW}6. 检查Git状态...${NC}"
if [ -d ".git" ]; then
    CURRENT_BRANCH=$(git branch --show-current)
    CURRENT_COMMIT=$(git rev-parse --short HEAD)
    echo -e "${GREEN}📝 当前分支: $CURRENT_BRANCH${NC}"
    echo -e "${GREEN}📝 当前提交: $CURRENT_COMMIT${NC}"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Git状态 - 分支: $CURRENT_BRANCH, 提交: $CURRENT_COMMIT" >> "$LOG_FILE"
else
    echo -e "${YELLOW}⚠️  不在Git仓库中${NC}"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 不在Git仓库中" >> "$LOG_FILE"
fi

echo -e "${GREEN}🎉 预检查完成，可以安全执行数据库迁移${NC}"
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 预检查完成" >> "$LOG_FILE"

# 记录到数据库
mysql -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "
    INSERT INTO logs (action, entityType, entityId, description, userId, username, createdAt, updatedAt) 
    VALUES ('PRE_MIGRATION_CHECK', 'database', 1, '迁移前预检查完成 - 表数量: $TABLE_COUNT, 迁移记录: $MIGRATION_COUNT', 1, 'system', NOW(), NOW());
" 2>> "$LOG_FILE"

echo -e "${BLUE}💡 现在可以安全执行: npm run migrate${NC}"
