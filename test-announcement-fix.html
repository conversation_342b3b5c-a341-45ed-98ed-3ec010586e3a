<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公告格式修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            display: flex;
            gap: 20px;
        }
        .test-section {
            flex: 1;
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .content-display {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            white-space: pre-wrap;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .html-display {
            background: #fff;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 3px;
            min-height: 200px;
            line-height: 1.6;
        }
        .api-test {
            margin: 20px 0;
            padding: 15px;
            background: #e8f4fd;
            border-radius: 5px;
        }
        .button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #337ecc;
        }
        .success {
            color: #67c23a;
            font-weight: bold;
        }
        .error {
            color: #f56c6c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>公告格式修复测试</h1>
    
    <div class="api-test">
        <h3>实时API测试</h3>
        <button class="button" onclick="fetchRealAnnouncement()">获取最新公告</button>
        <button class="button" onclick="clearLocalStorage()">清除浏览器缓存</button>
        <div id="api-status"></div>
    </div>
    
    <div class="test-container">
        <div class="test-section">
            <div class="test-title">原始API数据</div>
            <div class="content-display" id="raw-content">点击"获取最新公告"按钮加载数据</div>
        </div>
        
        <div class="test-section">
            <div class="test-title">修复后的显示效果</div>
            <div class="html-display" id="formatted-content">等待数据加载...</div>
        </div>
    </div>

    <div class="test-container">
        <div class="test-section">
            <div class="test-title">模拟测试数据</div>
            <div class="content-display" id="sample-raw"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">模拟修复效果</div>
            <div class="html-display" id="sample-formatted"></div>
        </div>
    </div>

    <script>
        // 修复后的格式化函数
        function formatContent(content) {
            if (!content) return '';
            return content
                // 首先处理字面量的\n字符串（反斜杠+n）
                .replace(/\\n/g, '<br>')
                // 然后处理真正的换行符
                .replace(/\n/g, '<br>')
                // 处理markdown格式的粗体
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                // 处理markdown格式的斜体
                .replace(/\*(.*?)\*/g, '<em>$1</em>');
        }

        // 获取真实的公告数据
        async function fetchRealAnnouncement() {
            const statusDiv = document.getElementById('api-status');
            statusDiv.innerHTML = '正在获取数据...';
            
            try {
                const response = await fetch('http://localhost:3000/api/announcements?page=1&limit=1&sort=newest&status=active');
                const data = await response.json();
                
                if (data.data && data.data.length > 0) {
                    const announcement = data.data[0];
                    
                    // 显示原始数据
                    document.getElementById('raw-content').textContent = JSON.stringify(announcement, null, 2);
                    
                    // 显示格式化后的内容
                    const formattedContent = formatContent(announcement.content);
                    document.getElementById('formatted-content').innerHTML = `
                        <h4>${announcement.title}</h4>
                        <div>${formattedContent}</div>
                    `;
                    
                    statusDiv.innerHTML = '<span class="success">✓ 数据获取成功</span>';
                } else {
                    statusDiv.innerHTML = '<span class="error">✗ 没有找到公告数据</span>';
                }
            } catch (error) {
                console.error('获取公告失败:', error);
                statusDiv.innerHTML = '<span class="error">✗ 获取数据失败: ' + error.message + '</span>';
            }
        }

        // 清除localStorage
        function clearLocalStorage() {
            localStorage.removeItem('lastClosedAnnouncementId');
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith('announcement_') && key.endsWith('_read')) {
                    localStorage.removeItem(key);
                }
            });
            document.getElementById('api-status').innerHTML = '<span class="success">✓ 浏览器缓存已清除</span>';
        }

        // 初始化模拟数据测试
        function initSampleTest() {
            const sampleContent = " (2025年6月9日) - 热门商品智能识别系统上线\\n- 🚀 **重大功能**: 全新热门商品自动识别系统正式上线\\n- ✨ **智能识别算法**:\\n  - 基于兑换量的自动热门商品识别机制\\n  - 支持四个时间维度：累积/30天/7天/今日热门";
            
            document.getElementById('sample-raw').textContent = sampleContent;
            document.getElementById('sample-formatted').innerHTML = formatContent(sampleContent);
        }

        // 页面加载时初始化
        window.onload = function() {
            initSampleTest();
        };
    </script>
</body>
</html>
