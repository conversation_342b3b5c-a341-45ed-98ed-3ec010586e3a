const StockManagementService = require('../services/stockManagementService');
const { validateStockConsistency } = require('../services/stockManagementService');
const { StockOperationLog } = require('../models');

/**
 * 库存管理控制器
 * 处理商品职场库存相关的HTTP请求
 */
class StockManagementController {

  /**
   * 获取指定商品的职场库存分布
   * GET /api/products/:id/workplace-stocks
   */
  static async getProductWorkplaceStocks(req, res) {
    try {
      const { id: productId } = req.params;
      
      // 验证商品ID
      if (!productId || isNaN(productId)) {
        return res.status(400).json({
          success: false,
          message: '无效的商品ID'
        });
      }

      const stockInfo = await StockManagementService.getProductWorkplaceStocks(parseInt(productId));
      
      res.json({
        success: true,
        message: '获取商品职场库存成功',
        data: stockInfo
      });

    } catch (error) {
      console.error('获取商品职场库存失败:', error);
      
      if (error.message === '商品不存在') {
        return res.status(404).json({
          success: false,
          message: error.message
        });
      }

      res.status(500).json({
        success: false,
        message: '获取商品职场库存失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * 批量更新商品在各职场的库存
   * PUT /api/products/:id/workplace-stocks
   * Body: {
   *   stockUpdates: [
   *     {
   *       workplaceId: number,
   *       stock: number,
   *       minStockAlert?: number,
   *       maxStockLimit?: number
   *     }
   *   ],
   *   reason?: string
   * }
   */
  static async updateProductWorkplaceStocks(req, res) {
    try {
      const { id: productId } = req.params;
      const { stockUpdates, reason = '管理员更新库存' } = req.body;
      
      // 验证商品ID
      if (!productId || isNaN(productId)) {
        return res.status(400).json({
          success: false,
          message: '无效的商品ID'
        });
      }

      // 验证请求体
      if (!stockUpdates || !Array.isArray(stockUpdates) || stockUpdates.length === 0) {
        return res.status(400).json({
          success: false,
          message: '库存更新数据不能为空'
        });
      }

      // 验证每个更新项
      for (const update of stockUpdates) {
        if (!update.workplaceId || isNaN(update.workplaceId)) {
          return res.status(400).json({
            success: false,
            message: '无效的职场ID'
          });
        }
        
        if (update.stock === undefined || isNaN(update.stock) || update.stock < 0) {
          return res.status(400).json({
            success: false,
            message: '库存数量必须为非负数'
          });
        }

        if (update.minStockAlert !== undefined && (isNaN(update.minStockAlert) || update.minStockAlert < 0)) {
          return res.status(400).json({
            success: false,
            message: '最低库存告警阈值必须为非负数'
          });
        }

        if (update.maxStockLimit !== undefined && update.maxStockLimit !== null && (isNaN(update.maxStockLimit) || update.maxStockLimit <= 0)) {
          return res.status(400).json({
            success: false,
            message: '最大库存限制必须为正数或留空表示无限制'
          });
        }
      }

      // 获取操作员信息
      const operator = {
        id: req.user?.id || null,
        name: req.user?.username || '系统管理员',
        ipAddress: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent')
      };

      const result = await StockManagementService.updateProductWorkplaceStocks(
        parseInt(productId),
        stockUpdates,
        operator,
        reason
      );

      res.json({
        success: true,
        message: '批量更新职场库存成功',
        data: result
      });

    } catch (error) {
      console.error('批量更新职场库存失败:', error);
      
      if (error.message.includes('不存在') || error.message.includes('无效')) {
        return res.status(400).json({
          success: false,
          message: error.message
        });
      }

      res.status(500).json({
        success: false,
        message: '批量更新职场库存失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * 职场间库存转移
   * POST /api/stocks/transfer
   * Body: {
   *   productId: number,
   *   fromWorkplaceId: number,
   *   toWorkplaceId: number,
   *   quantity: number,
   *   reason?: string
   * }
   */
  static async transferStock(req, res) {
    try {
      const { 
        productId, 
        fromWorkplaceId, 
        toWorkplaceId, 
        quantity, 
        reason = '职场间库存调拨' 
      } = req.body;

      // 验证必填参数
      if (!productId || isNaN(productId)) {
        return res.status(400).json({
          success: false,
          message: '无效的商品ID'
        });
      }

      if (!fromWorkplaceId || isNaN(fromWorkplaceId)) {
        return res.status(400).json({
          success: false,
          message: '无效的源职场ID'
        });
      }

      if (!toWorkplaceId || isNaN(toWorkplaceId)) {
        return res.status(400).json({
          success: false,
          message: '无效的目标职场ID'
        });
      }

      if (!quantity || isNaN(quantity) || quantity <= 0) {
        return res.status(400).json({
          success: false,
          message: '转移数量必须为正数'
        });
      }

      // 获取操作员信息
      const operator = {
        id: req.user?.id || null,
        name: req.user?.username || '系统管理员',
        ipAddress: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent')
      };

      const result = await StockManagementService.transferStock(
        parseInt(productId),
        parseInt(fromWorkplaceId),
        parseInt(toWorkplaceId),
        parseInt(quantity),
        operator,
        reason
      );

      res.json({
        success: true,
        message: '库存转移成功',
        data: result
      });

    } catch (error) {
      console.error('库存转移失败:', error);
      
      if (error.message.includes('不存在') || 
          error.message.includes('不足') || 
          error.message.includes('不能相同') ||
          error.message.includes('必须')) {
        return res.status(400).json({
          success: false,
          message: error.message
        });
      }

      res.status(500).json({
        success: false,
        message: '库存转移失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * 获取所有职场列表
   * GET /api/workplaces
   */
  static async getWorkplaces(req, res) {
    try {
      const workplaces = await StockManagementService.getWorkplaces();

      res.json({
        success: true,
        message: '获取职场列表成功',
        data: workplaces
      });
    } catch (error) {
      console.error('获取职场列表失败:', error);
      res.status(500).json({
        success: false,
        message: '获取职场列表失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * 获取职场库存统计
   * GET /api/workplaces/:id/stock-summary
   */
  static async getWorkplaceStockSummary(req, res) {
    try {
      const { id: workplaceId } = req.params;
      
      // 验证职场ID
      if (!workplaceId || isNaN(workplaceId)) {
        return res.status(400).json({
          success: false,
          message: '无效的职场ID'
        });
      }

      const summary = await StockManagementService.getWorkplaceStockSummary(parseInt(workplaceId));
      
      res.json({
        success: true,
        message: '获取职场库存统计成功',
        data: summary
      });

    } catch (error) {
      console.error('获取职场库存统计失败:', error);
      
      if (error.message === '职场不存在') {
        return res.status(404).json({
          success: false,
          message: error.message
        });
      }

      res.status(500).json({
        success: false,
        message: '获取职场库存统计失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * 获取库存操作日志
   * GET /api/stocks/operation-logs
   * Query: {
   *   productId?: number,
   *   workplaceId?: number,
   *   operationType?: string,
   *   startDate?: string,
   *   endDate?: string,
   *   page?: number,
   *   limit?: number
   * }
   */
  static async getStockOperationLogs(req, res) {
    try {
      const {
        productId,
        productName,
        workplaceId,
        operationType,
        startDate,
        endDate,
        page = 1,
        limit = 20
      } = req.query;

      // 验证分页参数
      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      
      if (isNaN(pageNum) || pageNum < 1) {
        return res.status(400).json({
          success: false,
          message: '无效的页码'
        });
      }

      if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
        return res.status(400).json({
          success: false,
          message: '每页数量必须在1-100之间'
        });
      }

      const options = {
        limit: limitNum,
        offset: (pageNum - 1) * limitNum
      };

      // 添加筛选条件
      if (productId && !isNaN(productId)) {
        options.productId = parseInt(productId);
      }

      if (productName) {
        options.productName = productName.trim();
      }

      if (workplaceId && !isNaN(workplaceId)) {
        options.workplaceId = parseInt(workplaceId);
      }

      if (operationType) {
        options.operationType = operationType;
      }

      if (startDate) {
        options.startDate = new Date(startDate);
      }

      if (endDate) {
        options.endDate = new Date(endDate);
      }

      const result = await StockOperationLog.getProductHistory(
        options.productId || null,
        {
          workplaceId: options.workplaceId,
          operationType: options.operationType,
          productName: options.productName,
          startDate: options.startDate,
          endDate: options.endDate,
          limit: limitNum,
          offset: (pageNum - 1) * limitNum
        }
      );

      res.json({
        success: true,
        message: '获取库存操作日志成功',
        data: {
          logs: result.rows,
          pagination: {
            total: result.count,
            page: pageNum,
            limit: limitNum,
            totalPages: Math.ceil(result.count / limitNum)
          }
        }
      });

    } catch (error) {
      console.error('获取库存操作日志失败:', error);
      
      res.status(500).json({
        success: false,
        message: '获取库存操作日志失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * 保存操作记录
   * POST /api/stocks/operation-records
   * Body: {
   *   productId: number,
   *   workplaceId?: number,
   *   type: string,
   *   operation: string,
   *   quantity?: number,
   *   oldStock?: number,
   *   newStock?: number,
   *   reason?: string,
   *   details?: object
   * }
   */
  static async saveOperationRecord(req, res) {
    try {
      const {
        productId,
        workplaceId,
        type,
        operation,
        quantity,
        oldStock,
        newStock,
        reason,
        details
      } = req.body;

      // 验证必需字段
      if (!productId || !type || !operation) {
        return res.status(400).json({
          success: false,
          message: '缺少必需字段：productId, type, operation'
        });
      }

      // 映射操作类型
      const operationTypeMap = {
        'quick_adjust': operation === 'add' ? 'add' : 'subtract',
        'stock_update': 'set',
        'stock_transfer': 'transfer',
        'batch_quick_adjust': operation === 'add' ? 'add' : 'subtract',
        'batch_edit': 'set'
      };

      const operationType = operationTypeMap[type] || 'set';

      // 计算变化量
      let changeAmount = quantity;
      if (oldStock !== null && newStock !== null) {
        changeAmount = newStock - oldStock;
      }

      // 创建操作日志
      const logData = {
        productId: parseInt(productId),
        workplaceId: workplaceId ? parseInt(workplaceId) : null,
        operationType,
        beforeStock: oldStock,
        afterStock: newStock,
        changeAmount,
        operatorId: req.user?.id || null,
        operatorName: req.user?.username || '未知用户',
        reason: reason || '',
        ipAddress: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent'),
        metadata: details ? JSON.stringify(details) : null
      };

      const operationLog = await StockOperationLog.createLog(logData);

      res.json({
        success: true,
        message: '操作记录保存成功',
        data: {
          id: operationLog.id,
          timestamp: operationLog.createdAt
        }
      });

    } catch (error) {
      console.error('保存操作记录失败:', error);

      res.status(500).json({
        success: false,
        message: '保存操作记录失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * 获取商品库存趋势数据
   * GET /api/stocks/product-trend
   * Query: {
   *   productId: number,
   *   startDate: string (YYYY-MM-DD),
   *   endDate: string (YYYY-MM-DD),
   *   workplaceId?: number
   * }
   */
  static async getProductStockTrend(req, res) {
    try {
      const {
        productId,
        startDate,
        endDate,
        workplaceId
      } = req.query;

      // 验证必需参数
      if (!productId) {
        return res.status(400).json({
          success: false,
          message: '商品ID不能为空'
        });
      }

      if (!startDate || !endDate) {
        return res.status(400).json({
          success: false,
          message: '开始日期和结束日期不能为空'
        });
      }

      // 验证日期格式
      const startDateTime = new Date(startDate);
      const endDateTime = new Date(endDate);

      if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
        return res.status(400).json({
          success: false,
          message: '日期格式不正确，请使用 YYYY-MM-DD 格式'
        });
      }

      if (startDateTime > endDateTime) {
        return res.status(400).json({
          success: false,
          message: '开始日期不能晚于结束日期'
        });
      }

      console.log('🔍 查询商品库存趋势参数:', {
        productId: parseInt(productId),
        startDate,
        endDate,
        workplaceId: workplaceId ? parseInt(workplaceId) : null
      });

      // 获取趋势数据
      const trendData = await StockOperationLog.getProductStockTrend(
        parseInt(productId),
        {
          startDate: startDate,
          endDate: endDate,
          workplaceId: workplaceId ? parseInt(workplaceId) : null
        }
      );

      console.log('📊 获取到的趋势数据:', trendData);

      res.json({
        success: true,
        message: '获取商品库存趋势成功',
        data: {
          productId: parseInt(productId),
          startDate,
          endDate,
          workplaceId: workplaceId ? parseInt(workplaceId) : null,
          trendData: trendData || []
        }
      });

    } catch (error) {
      console.error('获取商品库存趋势失败:', error);

      res.status(500).json({
        success: false,
        message: '获取商品库存趋势失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * 检查商品库存一致性
   * @param {object} req - 请求对象
   * @param {object} res - 响应对象
   */
  async checkStockConsistency(req, res) {
    try {
      const { productId } = req.params;

      if (!productId) {
        return res.status(400).json({
          success: false,
          message: '商品ID不能为空'
        });
      }

      const consistencyCheck = await validateStockConsistency(parseInt(productId));

      res.json({
        success: true,
        data: consistencyCheck
      });
    } catch (error) {
      console.error('检查库存一致性失败:', error);
      res.status(500).json({
        success: false,
        message: '检查库存一致性失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * 批量检查所有商品库存一致性
   * @param {object} req - 请求对象
   * @param {object} res - 响应对象
   */
  async checkAllStockConsistency(req, res) {
    try {
      const { Product } = require('../models');

      // 获取所有活跃商品
      const products = await Product.findAll({
        where: { status: 'active' },
        attributes: ['id', 'name', 'stock']
      });

      const results = [];
      let inconsistentCount = 0;

      for (const product of products) {
        const consistencyCheck = await validateStockConsistency(product.id);
        results.push({
          productId: product.id,
          productName: product.name,
          ...consistencyCheck
        });

        if (!consistencyCheck.isValid) {
          inconsistentCount++;
        }
      }

      res.json({
        success: true,
        data: {
          totalProducts: products.length,
          inconsistentCount,
          consistencyRate: ((products.length - inconsistentCount) / products.length * 100).toFixed(2) + '%',
          results
        }
      });
    } catch (error) {
      console.error('批量检查库存一致性失败:', error);
      res.status(500).json({
        success: false,
        message: '批量检查库存一致性失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
}

module.exports = StockManagementController;
