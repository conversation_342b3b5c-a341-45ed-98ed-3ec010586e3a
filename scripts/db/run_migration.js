// 手动执行的迁移脚本 - 添加contentHtml字段到announcements表
const { Sequelize } = require('sequelize');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

async function runMigration() {
  console.log('开始执行迁移...');
  
  // 使用SQLite数据库
  const sequelize = new Sequelize({
    dialect: 'sqlite',
    storage: path.join(__dirname, '../database.sqlite')
  });
  
  try {
    // 验证数据库连接
    await sequelize.authenticate();
    console.log('数据库连接已建立');
    
    // 检查announcements表是否存在
    const [tables] = await sequelize.query(`
      SELECT name FROM sqlite_master WHERE type='table' AND name='announcements'
    `);
    
    if (tables.length === 0) {
      console.log('announcements表不存在，请先创建表');
      return;
    }
    
    // 检查contentHtml字段是否已存在
    const [columns] = await sequelize.query(`
      PRAGMA table_info(announcements)
    `);
    
    const contentHtmlExists = columns.some(column => column.name === 'contentHtml');
    
    if (contentHtmlExists) {
      console.log('contentHtml字段已存在，无需迁移');
      return;
    }
    
    // 执行ALTER TABLE语句，添加contentHtml字段
    await sequelize.query(`
      ALTER TABLE announcements 
      ADD COLUMN contentHtml TEXT
    `);
    
    console.log('成功添加contentHtml字段到announcements表');
    
    // 更新现有数据，将content字段值复制到contentHtml字段
    await sequelize.query(`
      UPDATE announcements 
      SET contentHtml = content 
      WHERE contentHtml IS NULL
    `);
    
    console.log('成功更新现有数据，迁移完成');
    
  } catch (error) {
    console.error('迁移失败:', error);
  } finally {
    // 关闭连接
    await sequelize.close();
    console.log('迁移脚本执行完毕');
  }
}

// 执行迁移
runMigration(); 