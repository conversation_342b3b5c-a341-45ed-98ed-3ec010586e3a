{"semi": true, "trailingComma": "none", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "vueIndentScriptAndStyle": true, "htmlWhitespaceSensitivity": "ignore", "overrides": [{"files": "*.vue", "options": {"parser": "vue"}}, {"files": "*.json", "options": {"parser": "json", "trailingComma": "none"}}, {"files": "*.md", "options": {"parser": "markdown", "printWidth": 80, "proseWrap": "preserve"}}]}