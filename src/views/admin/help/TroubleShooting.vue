<template>
  <div class="help-doc troubleshooting">
    <h2>故障排查指南</h2>
    
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>常见问题快速诊断</h3>
        </div>
      </template>
      <div class="card-content">
        <p>本指南帮助您快速诊断和解决系统使用过程中遇到的常见问题。</p>
        
        <el-alert
          title="紧急提示"
          type="warning"
          description="如果遇到系统无法访问或数据丢失等严重问题，请立即联系技术支持团队。"
          show-icon
          :closable="false"
        />
      </div>
    </el-card>

    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>🖥️ 前端页面问题</h3>
        </div>
      </template>
      <div class="card-content">
        <el-collapse v-model="activeNames">
          <el-collapse-item title="页面显示空白或加载失败" name="1">
            <div class="problem-solution">
              <h4>可能原因：</h4>
              <ul>
                <li>浏览器缓存问题</li>
                <li>JavaScript错误</li>
                <li>网络连接问题</li>
                <li>组件渲染错误</li>
              </ul>
              
              <h4>解决步骤：</h4>
              <ol>
                <li><strong>刷新页面</strong>：按 F5 或 Ctrl+R 强制刷新</li>
                <li><strong>清除缓存</strong>：
                  <ul>
                    <li>Chrome: Ctrl+Shift+Delete</li>
                    <li>Firefox: Ctrl+Shift+Delete</li>
                    <li>Safari: Cmd+Option+E</li>
                  </ul>
                </li>
                <li><strong>检查控制台</strong>：按 F12 打开开发者工具，查看 Console 标签页的错误信息</li>
                <li><strong>尝试无痕模式</strong>：使用浏览器的无痕/隐私模式访问</li>
                <li><strong>更换浏览器</strong>：尝试使用 Chrome 或 Firefox 最新版本</li>
              </ol>
              
              <el-alert
                title="特别说明"
                type="success"
                description="系统设置模块的页面空白问题已在 v1.4.1 版本中修复，如仍遇到此问题请联系技术支持。"
                show-icon
              />
            </div>
          </el-collapse-item>

          <el-collapse-item title="系统设置页面功能异常" name="2">
            <div class="problem-solution">
              <h4>症状描述：</h4>
              <ul>
                <li>点击高级诊断工具后页面变空白</li>
                <li>智能发送时间控制无法正常加载</li>
                <li>系统设置页面间切换失败</li>
              </ul>
              
              <h4>解决方案：</h4>
              <ol>
                <li><strong>确认版本</strong>：检查系统版本是否为 v1.4.1 或更高版本</li>
                <li><strong>重新登录</strong>：退出系统后重新登录</li>
                <li><strong>检查权限</strong>：确认当前用户具有系统设置访问权限</li>
                <li><strong>使用诊断工具</strong>：进入"高级诊断工具"运行系统健康检查</li>
              </ol>
            </div>
          </el-collapse-item>

          <el-collapse-item title="数据加载缓慢或失败" name="3">
            <div class="problem-solution">
              <h4>排查步骤：</h4>
              <ol>
                <li><strong>检查网络</strong>：确认网络连接稳定</li>
                <li><strong>查看数据量</strong>：大量数据可能需要更长加载时间</li>
                <li><strong>调整筛选条件</strong>：缩小数据查询范围</li>
                <li><strong>检查服务器状态</strong>：联系管理员确认服务器运行状态</li>
              </ol>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>

    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>🔐 登录认证问题</h3>
        </div>
      </template>
      <div class="card-content">
        <el-collapse v-model="activeNames">
          <el-collapse-item title="飞书登录失败" name="4">
            <div class="problem-solution">
              <h4>检查项目：</h4>
              <ul>
                <li>确认邮箱以 @guanghe.tv 结尾</li>
                <li>检查飞书账号是否正常</li>
                <li>确认网络可以访问飞书服务</li>
                <li>清除浏览器 Cookie 后重试</li>
              </ul>
            </div>
          </el-collapse-item>

          <el-collapse-item title="权限不足或访问被拒绝" name="5">
            <div class="problem-solution">
              <h4>解决方法：</h4>
              <ol>
                <li>联系系统管理员检查用户权限设置</li>
                <li>确认用户账号状态为"启用"</li>
                <li>检查角色权限配置是否正确</li>
              </ol>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>

    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>📱 飞书集成问题</h3>
        </div>
      </template>
      <div class="card-content">
        <el-collapse v-model="activeNames">
          <el-collapse-item title="飞书群收不到推送消息" name="6">
            <div class="problem-solution">
              <h4>诊断步骤：</h4>
              <ol>
                <li><strong>检查配置</strong>：确认通知配置中对应推送已启用</li>
                <li><strong>验证 Webhook</strong>：检查 Webhook 地址是否正确</li>
                <li><strong>测试连接</strong>：使用"高级诊断工具"测试飞书连接</li>
                <li><strong>检查机器人</strong>：确认机器人已添加到目标群聊</li>
                <li><strong>查看日志</strong>：检查系统日志中的错误信息</li>
              </ol>
            </div>
          </el-collapse-item>

          <el-collapse-item title="消息发送频率异常" name="7">
            <div class="problem-solution">
              <h4>可能原因：</h4>
              <ul>
                <li>智能发送时间控制配置错误</li>
                <li>定时任务设置问题</li>
                <li>系统时区配置不正确</li>
              </ul>
              
              <h4>解决方案：</h4>
              <ol>
                <li>检查"智能发送时间控制"配置</li>
                <li>确认系统时区设置正确</li>
                <li>重新配置发送频率规则</li>
              </ol>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>

    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>🛠️ 系统诊断工具</h3>
        </div>
      </template>
      <div class="card-content">
        <p>系统提供了多种诊断工具帮助您快速定位问题：</p>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="tool-item">
              <el-icon :size="40" color="#409EFF"><Monitor /></el-icon>
              <h4>系统健康检查</h4>
              <p>检测 CPU、内存使用率和系统运行状态</p>
              <el-button type="primary" size="small">立即检查</el-button>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="tool-item">
              <el-icon :size="40" color="#67C23A"><Connection /></el-icon>
              <h4>连接测试</h4>
              <p>测试数据库连接和飞书 API 连接状态</p>
              <el-button type="success" size="small">测试连接</el-button>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="tool-item">
              <el-icon :size="40" color="#E6A23C"><DataAnalysis /></el-icon>
              <h4>消息发送分析</h4>
              <p>分析消息发送成功率和失败原因</p>
              <el-button type="warning" size="small">开始分析</el-button>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <h3>📞 技术支持联系方式</h3>
        </div>
      </template>
      <div class="card-content">
        <p>如果以上方法无法解决您的问题，请通过以下方式联系技术支持：</p>
        
        <div class="contact-info">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="contact-item">
                <h4>🚨 紧急支持</h4>
                <p>系统无法访问、数据丢失等紧急问题</p>
                <p><strong>响应时间：</strong>15分钟内</p>
                <el-button type="danger">紧急联系</el-button>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="contact-item">
                <h4>💬 常规支持</h4>
                <p>功能使用问题、配置咨询等</p>
                <p><strong>响应时间：</strong>2小时内</p>
                <el-button type="primary">提交工单</el-button>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { Monitor, Connection, DataAnalysis } from '@element-plus/icons-vue';

const activeNames = ref(['1']);
</script>

<style scoped>
.troubleshooting h2 {
  margin-bottom: 20px;
  font-weight: bold;
  color: #303133;
}

.help-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.card-content {
  line-height: 1.6;
}

.problem-solution h4 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-size: 16px;
  color: #303133;
}

.problem-solution ul,
.problem-solution ol {
  padding-left: 20px;
  margin-bottom: 16px;
}

.problem-solution li {
  margin-bottom: 6px;
  color: #606266;
}

.tool-item {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  height: 100%;
  background-color: #fafafa;
  transition: all 0.3s;
}

.tool-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.tool-item h4 {
  margin: 12px 0;
  color: #303133;
  font-size: 16px;
}

.tool-item p {
  margin-bottom: 15px;
  color: #606266;
  font-size: 14px;
}

.contact-info {
  margin-top: 20px;
}

.contact-item {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  height: 100%;
  background-color: #fafafa;
}

.contact-item h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.contact-item p {
  margin-bottom: 10px;
  color: #606266;
  font-size: 14px;
}

.contact-item .el-button {
  width: 100%;
  margin-top: 10px;
}
</style>
