-- 热门商品功能数据库更新脚本
-- 执行前请确保备份数据库

USE feishu_mall;

-- 1. 扩展products表，添加自动热门商品相关字段
-- 检查字段是否存在，如果不存在则添加
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
   WHERE table_name = 'products'
   AND table_schema = 'feishu_mall'
   AND column_name = 'isAutoHot') > 0,
  'SELECT "isAutoHot column already exists"',
  'ALTER TABLE products ADD COLUMN isAutoHot BOOLEAN DEFAULT FALSE COMMENT "自动识别的热门商品"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
   WHERE table_name = 'products'
   AND table_schema = 'feishu_mall'
   AND column_name = 'hotTimeRange') > 0,
  'SELECT "hotTimeRange column already exists"',
  'ALTER TABLE products ADD COLUMN hotTimeRange VARCHAR(20) DEFAULT NULL COMMENT "热门时间维度：all/30d/7d/1d"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
   WHERE table_name = 'products'
   AND table_schema = 'feishu_mall'
   AND column_name = 'hotScore') > 0,
  'SELECT "hotScore column already exists"',
  'ALTER TABLE products ADD COLUMN hotScore DECIMAL(10,2) DEFAULT 0 COMMENT "热门度评分"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
   WHERE table_name = 'products'
   AND table_schema = 'feishu_mall'
   AND column_name = 'hotRank') > 0,
  'SELECT "hotRank column already exists"',
  'ALTER TABLE products ADD COLUMN hotRank INT DEFAULT NULL COMMENT "热门排名"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
   WHERE table_name = 'products'
   AND table_schema = 'feishu_mall'
   AND column_name = 'lastHotUpdate') > 0,
  'SELECT "lastHotUpdate column already exists"',
  'ALTER TABLE products ADD COLUMN lastHotUpdate DATETIME COMMENT "最后热门状态更新时间"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 创建热门商品配置表
CREATE TABLE IF NOT EXISTS hot_product_configs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  timeRange VARCHAR(20) NOT NULL COMMENT '时间维度：all/30d/7d/1d',
  enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用该时间维度',
  maxCount INT DEFAULT 10 COMMENT '该时间维度的热门商品数量上限',
  minExchangeCount INT DEFAULT 1 COMMENT '最小兑换量要求',
  exchangeWeight DECIMAL(3,2) DEFAULT 1.0 COMMENT '兑换量权重',
  stockWeight DECIMAL(3,2) DEFAULT 0.1 COMMENT '库存权重',
  autoUpdateEnabled BOOLEAN DEFAULT TRUE COMMENT '是否自动更新',
  updateFrequency VARCHAR(50) DEFAULT '0 */1 * * *' COMMENT '更新频率(cron表达式)',
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_time_range (timeRange)
);

-- 3. 创建热门商品历史记录表
CREATE TABLE IF NOT EXISTS hot_product_history (
  id INT AUTO_INCREMENT PRIMARY KEY,
  productId INT NOT NULL,
  timeRange VARCHAR(20) NOT NULL COMMENT '时间维度',
  hotScore DECIMAL(10,2) NOT NULL COMMENT '热门度评分',
  rank INT NOT NULL COMMENT '排名',
  exchangeCount INT NOT NULL COMMENT '该时间段内兑换量',
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE,
  INDEX idx_product_time (productId, timeRange),
  INDEX idx_time_rank (timeRange, rank),
  INDEX idx_created_at (createdAt)
);

-- 4. 添加products表的索引
-- 检查索引是否存在，如果不存在则添加
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
   WHERE table_name = 'products'
   AND table_schema = 'feishu_mall'
   AND index_name = 'idx_is_auto_hot') > 0,
  'SELECT "idx_is_auto_hot index already exists"',
  'ALTER TABLE products ADD INDEX idx_is_auto_hot (isAutoHot)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
   WHERE table_name = 'products'
   AND table_schema = 'feishu_mall'
   AND index_name = 'idx_hot_time_range') > 0,
  'SELECT "idx_hot_time_range index already exists"',
  'ALTER TABLE products ADD INDEX idx_hot_time_range (hotTimeRange)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 插入默认配置数据
INSERT IGNORE INTO hot_product_configs (timeRange, enabled, maxCount, minExchangeCount, exchangeWeight, stockWeight, autoUpdateEnabled, updateFrequency) VALUES
('all', TRUE, 15, 1, 1.0, 0.1, TRUE, '0 */2 * * *'),
('30d', TRUE, 10, 1, 1.0, 0.1, TRUE, '0 */1 * * *'),
('7d', TRUE, 8, 1, 1.0, 0.1, TRUE, '0 */1 * * *'),
('1d', TRUE, 5, 1, 1.0, 0.1, TRUE, '0 */1 * * *');

-- 6. 验证表结构
DESCRIBE products;
DESCRIBE hot_product_configs;
DESCRIBE hot_product_history;

-- 7. 显示配置数据
SELECT * FROM hot_product_configs;

COMMIT;
