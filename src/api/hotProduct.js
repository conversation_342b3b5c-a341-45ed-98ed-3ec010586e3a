import api from './index'

/**
 * 热门商品API
 */
export const hotProductApi = {
  /**
   * 获取热门商品配置
   */
  getConfigs() {
    return api.get('/hot-products/configs')
  },

  /**
   * 更新热门商品配置
   * @param {Array} configs - 配置数组
   */
  updateConfigs(configs) {
    return api.put('/hot-products/configs', { configs })
  },

  /**
   * 手动触发热门商品更新
   * @param {string} timeRange - 时间范围，可选
   */
  triggerUpdate(timeRange = null) {
    return api.post('/hot-products/update', timeRange ? { timeRange } : {})
  },

  /**
   * 获取热门商品历史记录
   * @param {Object} params - 查询参数
   */
  getHistory(params = {}) {
    return api.get('/hot-products/history', { params })
  },

  /**
   * 获取热门商品统计信息
   */
  getStats() {
    return api.get('/hot-products/stats')
  },

  /**
   * 清理历史记录
   * @param {number} daysToKeep - 保留天数
   */
  cleanupHistory(daysToKeep = 90) {
    return api.post('/hot-products/cleanup', { daysToKeep })
  },

  /**
   * 获取指定时间维度的热门商品
   * @param {string} timeRange - 时间范围
   * @param {number} limit - 数量限制
   */
  getHotProducts(timeRange = 'all', limit = 10) {
    return api.get('/hot-products', { params: { timeRange, limit } })
  },

  /**
   * 获取所有时间维度的热门商品
   * @param {number} limit - 数量限制
   */
  getAllHotProducts(limit = 10) {
    return api.get('/hot-products/all', { params: { limit } })
  }
}

export default hotProductApi
