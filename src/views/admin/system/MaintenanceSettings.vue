<template>
  <div class="maintenance-settings">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <el-icon class="title-icon"><Tools /></el-icon>
            系统维护
          </h1>
          <p class="page-description">系统维护工具和操作</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="refreshSystemInfo" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新系统信息
          </el-button>
        </div>
      </div>
    </div>

    <!-- 系统信息 -->
    <div class="system-info">
      <el-card>
        <template #header>
          <h3>系统信息</h3>
        </template>
        <el-row :gutter="24">
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">系统版本：</span>
              <span class="info-value">{{ systemInfo.version }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">运行时间：</span>
              <span class="info-value">{{ systemInfo.uptime }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">数据库状态：</span>
              <el-tag :type="systemInfo.dbStatus === 'connected' ? 'success' : 'danger'">
                {{ systemInfo.dbStatus === 'connected' ? '已连接' : '连接失败' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">服务器时间：</span>
              <span class="info-value">{{ systemInfo.serverTime }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">内存使用：</span>
              <span class="info-value">{{ systemInfo.memoryUsage }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">磁盘使用：</span>
              <span class="info-value">{{ systemInfo.diskUsage }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 维护工具 -->
    <div class="maintenance-tools">
      <el-row :gutter="24">
        <!-- 缓存管理 -->
        <el-col :span="8">
          <el-card class="tool-card">
            <template #header>
              <div class="tool-header">
                <el-icon><DataBoard /></el-icon>
                <span>缓存管理</span>
              </div>
            </template>
            <div class="tool-content">
              <p class="tool-description">清理系统缓存，提升系统性能</p>
              <div class="tool-stats">
                <div class="stat-item">
                  <span class="stat-label">缓存大小：</span>
                  <span class="stat-value">{{ cacheInfo.size }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">缓存项数：</span>
                  <span class="stat-value">{{ cacheInfo.count }}</span>
                </div>
              </div>
              <div class="tool-actions">
                <el-button type="warning" @click="clearCache" :loading="clearingCache">
                  <el-icon><Delete /></el-icon>
                  清理缓存
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 数据库维护 -->
        <el-col :span="8">
          <el-card class="tool-card">
            <template #header>
              <div class="tool-header">
                <el-icon><Coin /></el-icon>
                <span>数据库维护</span>
              </div>
            </template>
            <div class="tool-content">
              <p class="tool-description">优化数据库性能，清理冗余数据</p>
              <div class="tool-stats">
                <div class="stat-item">
                  <span class="stat-label">数据库大小：</span>
                  <span class="stat-value">{{ dbInfo.size }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">表数量：</span>
                  <span class="stat-value">{{ dbInfo.tableCount }}</span>
                </div>
              </div>
              <div class="tool-actions">
                <el-button type="primary" @click="optimizeDatabase" :loading="optimizingDb">
                  <el-icon><Tools /></el-icon>
                  优化数据库
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 日志管理 -->
        <el-col :span="8">
          <el-card class="tool-card">
            <template #header>
              <div class="tool-header">
                <el-icon><Document /></el-icon>
                <span>日志管理</span>
              </div>
            </template>
            <div class="tool-content">
              <p class="tool-description">管理系统日志文件</p>
              <div class="tool-stats">
                <div class="stat-item">
                  <span class="stat-label">日志大小：</span>
                  <span class="stat-value">{{ logInfo.size }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">日志文件：</span>
                  <span class="stat-value">{{ logInfo.fileCount }}</span>
                </div>
              </div>
              <div class="tool-actions">
                <el-button type="danger" @click="clearLogs" :loading="clearingLogs">
                  <el-icon><Delete /></el-icon>
                  清理日志
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 备份与恢复 -->
    <div class="backup-section">
      <el-card>
        <template #header>
          <h3>数据备份与恢复</h3>
        </template>
        <el-row :gutter="24">
          <el-col :span="12">
            <div class="backup-panel">
              <h4>创建备份</h4>
              <p>创建系统数据的完整备份</p>
              <el-form :model="backupForm" label-width="100px">
                <el-form-item label="备份名称">
                  <el-input v-model="backupForm.name" placeholder="请输入备份名称" />
                </el-form-item>
                <el-form-item label="备份类型">
                  <el-radio-group v-model="backupForm.type">
                    <el-radio label="full">完整备份</el-radio>
                    <el-radio label="incremental">增量备份</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="备份说明">
                  <el-input
                    v-model="backupForm.description"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入备份说明（可选）"
                  />
                </el-form-item>
              </el-form>
              <el-button type="primary" @click="createBackup" :loading="creatingBackup">
                <el-icon><FolderAdd /></el-icon>
                创建备份
              </el-button>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="backup-list">
              <h4>备份历史</h4>
              <div class="backup-items">
                <div
                  v-for="backup in backupList"
                  :key="backup.id"
                  class="backup-item"
                >
                  <div class="backup-info">
                    <div class="backup-name">{{ backup.name }}</div>
                    <div class="backup-meta">
                      <span>{{ backup.type === 'full' ? '完整备份' : '增量备份' }}</span>
                      <span>{{ backup.size }}</span>
                      <span>{{ backup.createdAt }}</span>
                    </div>
                  </div>
                  <div class="backup-actions">
                    <el-button size="small" @click="downloadBackup(backup)">
                      <el-icon><Download /></el-icon>
                      下载
                    </el-button>
                    <el-button size="small" type="warning" @click="restoreBackup(backup)">
                      <el-icon><RefreshLeft /></el-icon>
                      恢复
                    </el-button>
                    <el-button size="small" type="danger" @click="deleteBackup(backup)">
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 系统监控 -->
    <div class="monitoring-section">
      <el-card>
        <template #header>
          <h3>系统监控</h3>
        </template>
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="monitor-item">
              <div class="monitor-title">CPU使用率</div>
              <el-progress
                :percentage="monitoring.cpu"
                :color="getProgressColor(monitoring.cpu)"
                :stroke-width="8"
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="monitor-item">
              <div class="monitor-title">内存使用率</div>
              <el-progress
                :percentage="monitoring.memory"
                :color="getProgressColor(monitoring.memory)"
                :stroke-width="8"
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="monitor-item">
              <div class="monitor-title">磁盘使用率</div>
              <el-progress
                :percentage="monitoring.disk"
                :color="getProgressColor(monitoring.disk)"
                :stroke-width="8"
              />
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Tools, Refresh, DataBoard, Delete, Coin, Document,
  FolderAdd, Download, RefreshLeft
} from '@element-plus/icons-vue';

// 响应式数据
const loading = ref(false);
const clearingCache = ref(false);
const optimizingDb = ref(false);
const clearingLogs = ref(false);
const creatingBackup = ref(false);

const systemInfo = reactive({
  version: 'v1.0.0',
  uptime: '7天 12小时 30分钟',
  dbStatus: 'connected',
  serverTime: '',
  memoryUsage: '2.1GB / 8GB',
  diskUsage: '45.2GB / 100GB'
});

const cacheInfo = reactive({
  size: '156MB',
  count: 1234
});

const dbInfo = reactive({
  size: '2.3GB',
  tableCount: 28
});

const logInfo = reactive({
  size: '89MB',
  fileCount: 15
});

const backupForm = reactive({
  name: '',
  type: 'full',
  description: ''
});

const backupList = ref([]);

const monitoring = reactive({
  cpu: 45,
  memory: 62,
  disk: 38
});

let monitoringInterval = null;

// 方法
const refreshSystemInfo = async () => {
  loading.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    systemInfo.serverTime = new Date().toLocaleString();
    ElMessage.success('系统信息刷新成功');
  } catch (error) {
    ElMessage.error('刷新失败');
  } finally {
    loading.value = false;
  }
};

const clearCache = async () => {
  try {
    await ElMessageBox.confirm('确定要清理系统缓存吗？', '确认操作', {
      type: 'warning'
    });
    
    clearingCache.value = true;
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    cacheInfo.size = '0MB';
    cacheInfo.count = 0;
    ElMessage.success('缓存清理成功');
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('缓存清理失败');
    }
  } finally {
    clearingCache.value = false;
  }
};

const optimizeDatabase = async () => {
  try {
    await ElMessageBox.confirm('数据库优化可能需要较长时间，确定要继续吗？', '确认操作', {
      type: 'warning'
    });
    
    optimizingDb.value = true;
    await new Promise(resolve => setTimeout(resolve, 3000));
    ElMessage.success('数据库优化完成');
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('数据库优化失败');
    }
  } finally {
    optimizingDb.value = false;
  }
};

const clearLogs = async () => {
  try {
    await ElMessageBox.confirm('确定要清理系统日志吗？', '确认操作', {
      type: 'warning'
    });
    
    clearingLogs.value = true;
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    logInfo.size = '0MB';
    logInfo.fileCount = 0;
    ElMessage.success('日志清理成功');
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('日志清理失败');
    }
  } finally {
    clearingLogs.value = false;
  }
};

const createBackup = async () => {
  if (!backupForm.name) {
    ElMessage.warning('请输入备份名称');
    return;
  }
  
  creatingBackup.value = true;
  try {
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const newBackup = {
      id: Date.now(),
      name: backupForm.name,
      type: backupForm.type,
      description: backupForm.description,
      size: '1.2GB',
      createdAt: new Date().toLocaleString()
    };
    
    backupList.value.unshift(newBackup);
    
    // 重置表单
    Object.assign(backupForm, {
      name: '',
      type: 'full',
      description: ''
    });
    
    ElMessage.success('备份创建成功');
  } catch (error) {
    ElMessage.error('备份创建失败');
  } finally {
    creatingBackup.value = false;
  }
};

const downloadBackup = (backup) => {
  ElMessage.info(`开始下载备份: ${backup.name}`);
  // 实际项目中这里会触发文件下载
};

const restoreBackup = async (backup) => {
  try {
    await ElMessageBox.confirm(
      `确定要恢复备份 "${backup.name}" 吗？此操作将覆盖当前数据！`,
      '确认恢复',
      { type: 'warning' }
    );
    
    // 模拟恢复过程
    await new Promise(resolve => setTimeout(resolve, 2000));
    ElMessage.success('备份恢复成功');
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('备份恢复失败');
    }
  }
};

const deleteBackup = async (backup) => {
  try {
    await ElMessageBox.confirm(`确定要删除备份 "${backup.name}" 吗？`, '确认删除', {
      type: 'warning'
    });
    
    const index = backupList.value.findIndex(b => b.id === backup.id);
    if (index > -1) {
      backupList.value.splice(index, 1);
    }
    ElMessage.success('备份删除成功');
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('备份删除失败');
    }
  }
};

const getProgressColor = (percentage) => {
  if (percentage < 50) return '#67c23a';
  if (percentage < 80) return '#e6a23c';
  return '#f56c6c';
};

const updateMonitoring = () => {
  monitoring.cpu = Math.floor(Math.random() * 100);
  monitoring.memory = Math.floor(Math.random() * 100);
  monitoring.disk = Math.floor(Math.random() * 100);
};

// 生命周期
onMounted(() => {
  systemInfo.serverTime = new Date().toLocaleString();
  
  // 模拟备份历史数据
  backupList.value = [
    {
      id: 1,
      name: '系统完整备份_20240801',
      type: 'full',
      description: '月度完整备份',
      size: '2.1GB',
      createdAt: '2024-08-01 02:00:00'
    },
    {
      id: 2,
      name: '增量备份_20240803',
      type: 'incremental',
      description: '日常增量备份',
      size: '156MB',
      createdAt: '2024-08-03 02:00:00'
    }
  ];
  
  // 启动监控数据更新
  monitoringInterval = setInterval(updateMonitoring, 5000);
});

onUnmounted(() => {
  if (monitoringInterval) {
    clearInterval(monitoringInterval);
  }
});
</script>

<style scoped>
.maintenance-settings {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  color: #3b82f6;
}

.page-description {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

.system-info {
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.info-label {
  font-weight: 600;
  color: #374151;
  min-width: 100px;
}

.info-value {
  color: #6b7280;
}

.maintenance-tools {
  margin-bottom: 24px;
}

.tool-card {
  height: 100%;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.tool-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.tool-description {
  color: #6b7280;
  margin-bottom: 16px;
}

.tool-stats {
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.stat-label {
  color: #6b7280;
}

.stat-value {
  font-weight: 600;
  color: #1f2937;
}

.backup-section {
  margin-bottom: 24px;
}

.backup-panel h4,
.backup-list h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.backup-items {
  max-height: 300px;
  overflow-y: auto;
}

.backup-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 8px;
}

.backup-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.backup-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #6b7280;
}

.backup-actions {
  display: flex;
  gap: 8px;
}

.monitoring-section .monitor-item {
  text-align: center;
}

.monitor-title {
  margin-bottom: 16px;
  font-weight: 600;
  color: #1f2937;
}
</style>
