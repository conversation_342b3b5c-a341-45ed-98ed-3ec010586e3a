-- 数据库回退脚本：回退到版本c3fa2fb（新增热门商品智能识别系统）
-- 执行时间：2025-07-15
-- 目标：删除活动促销功能相关的表和字段，保留热门商品功能

-- 开始事务
START TRANSACTION;

-- 记录回退操作
INSERT INTO logs (action, entityType, entityId, description, userId, username, createdAt, updatedAt)
VALUES ('DATABASE_ROLLBACK', 'database', 1, '开始数据库回退操作：回退到版本c3fa2fb，删除活动促销功能', 1, 'system', NOW(), NOW());

-- 1. 删除活动促销相关的表（按依赖关系顺序删除）
-- 删除统计表
DROP TABLE IF EXISTS promotion_statistics;

-- 删除参与记录表
DROP TABLE IF EXISTS promotion_participations;

-- 删除活动商品关联表
DROP TABLE IF EXISTS promotion_products;

-- 删除活动主表
DROP TABLE IF EXISTS promotions;

-- 2. 删除products表中的活动促销相关字段
-- 检查并删除has_promotion字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.columns
     WHERE table_schema = 'feishu_mall' AND table_name = 'products' AND column_name = 'has_promotion') > 0,
    'ALTER TABLE products DROP COLUMN has_promotion',
    'SELECT "has_promotion字段不存在，跳过删除" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并删除promotion_price字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.columns
     WHERE table_schema = 'feishu_mall' AND table_name = 'products' AND column_name = 'promotion_price') > 0,
    'ALTER TABLE products DROP COLUMN promotion_price',
    'SELECT "promotion_price字段不存在，跳过删除" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 清理SequelizeMeta表中不应该存在的迁移记录
-- 注意：这些迁移记录可能是手动添加的，需要清理
DELETE FROM SequelizeMeta WHERE name LIKE '%promotion%';

-- 4. 验证热门商品功能相关的表和字段是否完整
-- 检查hot_product_configs表是否存在
SELECT 'hot_product_configs表检查' as check_item,
       CASE WHEN COUNT(*) > 0 THEN '存在' ELSE '不存在' END as status
FROM information_schema.tables
WHERE table_schema = 'feishu_mall' AND table_name = 'hot_product_configs';

-- 检查hot_product_history表是否存在
SELECT 'hot_product_history表检查' as check_item,
       CASE WHEN COUNT(*) > 0 THEN '存在' ELSE '不存在' END as status
FROM information_schema.tables
WHERE table_schema = 'feishu_mall' AND table_name = 'hot_product_history';

-- 检查products表中的热门商品字段
SELECT 'products表热门商品字段检查' as check_item,
       CASE WHEN COUNT(*) = 5 THEN '完整' ELSE '不完整' END as status
FROM information_schema.columns
WHERE table_schema = 'feishu_mall'
  AND table_name = 'products'
  AND column_name IN ('isAutoHot', 'hotTimeRange', 'hotScore', 'hotRank', 'lastHotUpdate');

-- 5. 记录回退完成
INSERT INTO logs (action, entityType, entityId, description, userId, username, createdAt, updatedAt)
VALUES ('DATABASE_ROLLBACK', 'database', 1, '数据库回退操作完成：成功回退到版本c3fa2fb', 1, 'system', NOW(), NOW());

-- 提交事务
COMMIT;

-- 显示最终的表列表
SHOW TABLES;

-- 显示products表结构确认
DESCRIBE products;
