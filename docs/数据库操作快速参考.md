# 数据库操作快速参考卡片

## 🚀 常用命令速查

### 基础检查命令
```bash
# 数据库状态检查
npm run db:check

# 手动备份
npm run db:backup

# 安全迁移（推荐）
npm run migrate:safe

# 迁移回退
npm run migrate:undo
```

### 开发流程命令
```bash
# 1. 开发前检查
npm run db:check

# 2. 创建迁移文件
cd server
npx sequelize-cli migration:generate --name your-migration-name

# 3. 执行迁移
npm run migrate:safe

# 4. 测试回退
npm run migrate:undo

# 5. 重新执行
npm run migrate:safe
```

## 📝 迁移文件模板

### 基础模板
```javascript
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // 你的迁移逻辑
      
      await transaction.commit();
      console.log('✅ 迁移执行成功');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ 迁移执行失败:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // 你的回退逻辑
      
      await transaction.commit();
      console.log('✅ 迁移回退成功');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ 迁移回退失败:', error);
      throw error;
    }
  }
};
```

### 创建表模板
```javascript
await queryInterface.createTable('table_name', {
  id: {
    type: Sequelize.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },
  name: {
    type: Sequelize.STRING(255),
    allowNull: false,
    comment: '名称'
  },
  status: {
    type: Sequelize.ENUM('active', 'inactive'),
    allowNull: false,
    defaultValue: 'active',
    comment: '状态'
  },
  createdAt: {
    type: Sequelize.DATE,
    allowNull: false,
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
  },
  updatedAt: {
    type: Sequelize.DATE,
    allowNull: false,
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
  }
}, { transaction });
```

### 添加字段模板
```javascript
await queryInterface.addColumn('table_name', 'new_field', {
  type: Sequelize.STRING(255),
  allowNull: true,
  comment: '新字段说明'
}, { transaction });
```

### 添加索引模板
```javascript
await queryInterface.addIndex('table_name', ['field_name'], {
  name: 'idx_table_field',
  unique: false,
  transaction
});
```

## ⚠️ 安全检查清单

### 迁移前必检项
- [ ] 执行了 `npm run db:check`
- [ ] 创建了备份
- [ ] 迁移文件包含事务
- [ ] 编写了回退逻辑
- [ ] 本地测试通过

### 部署前必检项
- [ ] 代码已合并
- [ ] 执行了部署前备份
- [ ] 迁移在测试环境验证
- [ ] 回退方案已准备
- [ ] 监控已设置

## 🚨 紧急处理流程

### 迁移失败
```bash
# 1. 立即回退
npm run migrate:undo

# 2. 如果回退失败，恢复备份
mysql -u root -ppassword feishu_mall < backups/latest_backup.sql

# 3. 验证状态
npm run db:check
```

### 数据损坏
```bash
# 1. 停止服务
pm2 stop all

# 2. 恢复备份
mysql -u root -ppassword feishu_mall < backups/feishu_mall_backup_YYYYMMDD_HHMMSS.sql

# 3. 验证完整性
npm run db:check

# 4. 重启服务
pm2 start all
```

## 📋 常用SQL查询

### 检查表结构
```sql
-- 查看表结构
DESCRIBE table_name;

-- 查看所有表
SHOW TABLES;

-- 查看索引
SHOW INDEX FROM table_name;
```

### 检查迁移状态
```sql
-- 查看已执行的迁移
SELECT * FROM SequelizeMeta ORDER BY name;

-- 查看最近的迁移
SELECT * FROM SequelizeMeta ORDER BY name DESC LIMIT 5;
```

### 检查数据完整性
```sql
-- 检查表数量
SELECT COUNT(*) as table_count 
FROM information_schema.tables 
WHERE table_schema = 'feishu_mall';

-- 检查字段是否存在
SELECT COUNT(*) 
FROM information_schema.columns 
WHERE table_schema = 'feishu_mall' 
  AND table_name = 'your_table' 
  AND column_name = 'your_field';
```

## 🔧 故障排除

### 常见错误及解决方案

**错误**: `Unknown column 'field' in 'field list'`
**解决**: 检查字段名拼写，确认迁移是否执行成功

**错误**: `Table 'table_name' already exists`
**解决**: 检查迁移是否重复执行，使用 `IF NOT EXISTS`

**错误**: `Cannot add foreign key constraint`
**解决**: 确认引用的表和字段存在，检查数据类型匹配

**错误**: `Duplicate entry for key`
**解决**: 检查唯一约束冲突，清理重复数据

### 调试技巧
```bash
# 查看详细错误日志
tail -f logs/migration.log

# 检查MySQL错误日志
sudo tail -f /var/log/mysql/error.log

# 验证数据库连接
mysql -u root -ppassword feishu_mall -e "SELECT 1;"
```

## 📞 快速联系

### 紧急情况
1. **数据丢失**: 立即停止服务，恢复备份
2. **迁移失败**: 执行回退，分析错误
3. **服务异常**: 检查数据库连接和表结构

### 支持资源
- [数据库开发工作流](./数据库开发工作流.md)
- [数据库开发规则](./数据库开发规则.md)
- [数据库版本管理指南](./数据库版本管理指南.md)

---

**💡 提示**: 将此文档加入书签，开发时随时查阅！
