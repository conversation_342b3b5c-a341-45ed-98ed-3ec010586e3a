/**
 * 验证命名冲突修复的测试脚本
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:3000/api';

// 获取管理员token
async function getAdminToken() {
  try {
    const loginData = {
      username: '超管',
      email: 'ch<PERSON><PERSON><PERSON>@guanghe.tv',
      password: '654321',
      userType: 'admin'
    };
    
    const response = await axios.post(`${BASE_URL}/auth/login`, loginData);
    return response.data.token;
  } catch (error) {
    console.error('❌ 获取token失败:', error.response?.data?.message || error.message);
    return null;
  }
}

// 获取哪吒捏捏乐的当前库存
async function getCurrentStock(token) {
  try {
    const config = {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };
    
    const response = await axios.get(`${BASE_URL}/products`, {
      ...config,
      params: {
        includeWorkplaceStocks: true,
        limit: 1000
      }
    });
    
    const nezhaProduct = response.data?.data?.find(p => p.name && p.name.includes('哪吒捏捏乐'));
    
    if (nezhaProduct && nezhaProduct.workplaceStocks) {
      const stockData = {};
      nezhaProduct.workplaceStocks.forEach(stock => {
        stockData[stock.workplaceName] = {
          stock: stock.stock,
          reservedStock: stock.reservedStock,
          availableStock: stock.availableStock || (stock.stock - stock.reservedStock),
          lastUpdate: stock.lastStockUpdate
        };
      });
      return stockData;
    }
    
    return null;
  } catch (error) {
    console.error('❌ 获取库存数据失败:', error.response?.data?.message || error.message);
    return null;
  }
}

// 执行库存转移
async function performTransfer(token, fromWorkplaceId, toWorkplaceId, quantity, reason) {
  try {
    const config = {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };
    
    const transferData = {
      productId: 15, // 哪吒捏捏乐
      fromWorkplaceId,
      toWorkplaceId,
      quantity,
      reason
    };
    
    console.log('📤 发送转移请求:', transferData);
    
    const response = await axios.post(`${BASE_URL}/stocks/transfer`, transferData, config);
    
    console.log('📥 API响应:', response.data);
    
    return response.data.success;
  } catch (error) {
    console.error('❌ 库存转移失败:', error.response?.data?.message || error.message);
    return false;
  }
}

// 主测试函数
async function runFixVerificationTest() {
  console.log('🔧 开始验证命名冲突修复...\n');
  
  // 1. 获取token
  const token = await getAdminToken();
  if (!token) {
    console.log('❌ 无法获取token，测试终止');
    return;
  }
  console.log('✅ 获取token成功\n');
  
  // 2. 获取转移前数据
  console.log('📊 步骤1: 获取转移前库存状态');
  const beforeData = await getCurrentStock(token);
  if (!beforeData) {
    console.log('❌ 无法获取转移前数据');
    return;
  }
  
  console.log('转移前库存状态:');
  Object.entries(beforeData).forEach(([workplace, data]) => {
    console.log(`  ${workplace}: ${data.availableStock}个 (总库存: ${data.stock}, 预留: ${data.reservedStock})`);
  });
  console.log('');
  
  // 3. 执行库存转移（从武汉转移1个到北京）
  console.log('🔄 步骤2: 执行库存转移（武汉 → 北京，1个）');
  const transferSuccess = await performTransfer(token, 14, 1, 1, '验证命名冲突修复');
  
  if (!transferSuccess) {
    console.log('❌ 库存转移失败，可能仍存在问题');
    return;
  }
  console.log('✅ 库存转移API调用成功\n');
  
  // 4. 等待1秒后获取转移后数据
  console.log('⏳ 等待1秒后获取转移后数据...');
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  console.log('📊 步骤3: 获取转移后库存状态');
  const afterData = await getCurrentStock(token);
  
  if (afterData) {
    console.log('转移后库存状态:');
    Object.entries(afterData).forEach(([workplace, data]) => {
      console.log(`  ${workplace}: ${data.availableStock}个 (总库存: ${data.stock}, 预留: ${data.reservedStock})`);
    });
    
    // 检查数据是否已更新
    const wuhanBefore = beforeData['武汉']?.availableStock || 0;
    const beijingBefore = beforeData['北京']?.availableStock || 0;
    const wuhanAfter = afterData['武汉']?.availableStock || 0;
    const beijingAfter = afterData['北京']?.availableStock || 0;
    
    const wuhanChange = wuhanAfter - wuhanBefore;
    const beijingChange = beijingAfter - beijingBefore;
    
    console.log('\n📈 数据变化分析:');
    console.log(`武汉: ${wuhanBefore} → ${wuhanAfter} (变化: ${wuhanChange})`);
    console.log(`北京: ${beijingBefore} → ${beijingAfter} (变化: ${beijingChange})`);
    
    if (wuhanChange === -1 && beijingChange === 1) {
      console.log('\n🎉 修复验证成功！');
      console.log('✅ 命名冲突问题已解决');
      console.log('✅ 库存转移API正常工作');
      console.log('✅ 数据库正确更新');
      console.log('\n💡 现在前端页面的库存转移功能应该能正常工作了！');
    } else {
      console.log('\n❌ 修复验证失败');
      console.log('期望：武汉-1，北京+1');
      console.log(`实际：武汉${wuhanChange}，北京${beijingChange}`);
      console.log('可能仍存在其他问题需要进一步排查');
    }
  } else {
    console.log('❌ 无法获取转移后数据');
  }
  
  console.log('\n📋 测试完成');
}

// 运行测试
runFixVerificationTest().catch(error => {
  console.error('测试执行失败:', error);
});
